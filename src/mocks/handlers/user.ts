import { http, HttpResponse } from "msw";

export const userHandlers = [
  // 获取用户详情
  http.post("/muyan/api/role/permission/userDetail", async () => {
    return HttpResponse.json({
      success: true,
      message: "请求成功",
      detail: null,
      data: {
        userId: "user-l355mouaaiv5fmermuhh7gzbz22gcf6p",
        username: "慕思-超管-测试环境",
        creator: null,
        createdAt: "2025-01-16 10:49:55",
        updatedAt: null,
        roleList: [
          {
            createdAt: null,
            updatedAt: null,
            id: "1",
            name: "超级管理员",
            roleCode: "SUPER_ADMIN",
          },
        ],
        permissionList: [
          {
            createdAt: null,
            updatedAt: null,
            id: "1935577610244427777",
            parentId: null,
            name: "牧言客户端",
            permissionCode: "client",
            type: 3,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935612329300430850",
            parentId: null,
            name: "工作台",
            permissionCode: "workbench",
            type: 3,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935612380160561154",
            parentId: null,
            name: "会话",
            permissionCode: "conversation",
            type: 3,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935612579859763201",
            parentId: null,
            name: "消息",
            permissionCode: "message",
            type: 3,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935614718854139905",
            parentId: null,
            name: "客服质检",
            permissionCode: "quality:inspection",
            type: 3,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935615916948688897",
            parentId: null,
            name: "知识学习",
            permissionCode: "knowledge",
            type: 3,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935617935251968001",
            parentId: null,
            name: "智能体",
            permissionCode: "client:agent",
            type: 3,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935625008798863362",
            parentId: null,
            name: "启用智能体",
            permissionCode: "client:agent:enable",
            type: 1,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935625169214214146",
            parentId: null,
            name: "转人工任务",
            permissionCode: "workbench:transfer:task",
            type: 3,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935625925103292418",
            parentId: null,
            name: "转人工任务",
            permissionCode: "workbench:transfer:task:enable",
            type: 1,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935626883271069697",
            parentId: null,
            name: "会话管理",
            permissionCode: "conversations:conversation",
            type: 3,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935626986207678466",
            parentId: null,
            name: "模拟会话",
            permissionCode: "conversations:simulation",
            type: 3,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935627189782417410",
            parentId: null,
            name: "会话查看",
            permissionCode: "conversations:conversation:view",
            type: 1,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935627610739544066",
            parentId: null,
            name: "会话-转人工任务会话筛选",
            permissionCode: "conversations:conversation:transfer:task:filter",
            type: 1,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935633824168906753",
            parentId: null,
            name: "消息-日志",
            permissionCode: "message:log",
            type: 3,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935635818120056833",
            parentId: null,
            name: "日志-信息",
            permissionCode: "message:log:info",
            type: 1,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935635883161128962",
            parentId: null,
            name: "日志-过程",
            permissionCode: "message:log:process",
            type: 1,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935635936630116354",
            parentId: null,
            name: "日志-数据",
            permissionCode: "message:log:data",
            type: 1,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935637949401108482",
            parentId: null,
            name: "模拟会话查看/筛选/创建",
            permissionCode: "conversations:simulation:view",
            type: 1,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935638021052403714",
            parentId: null,
            name: "模拟预设",
            permissionCode: "conversations:conversation:preset",
            type: 1,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935639550304030721",
            parentId: null,
            name: "质检概览",
            permissionCode: "quality:inspection:overview",
            type: 3,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935639628750098434",
            parentId: null,
            name: "质检概览查看",
            permissionCode: "quality:inspection:overview:view",
            type: 1,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935639748744941570",
            parentId: null,
            name: "质检明细",
            permissionCode: "quality:inspection:detail",
            type: 3,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935639907272855554",
            parentId: null,
            name: "质检明细 CRUD",
            permissionCode: "quality:inspection:detail:crud",
            type: 1,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935640680652181506",
            parentId: null,
            name: "知识库",
            permissionCode: "knowledge:base",
            type: 3,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935640787141365761",
            parentId: null,
            name: "知识库-查看",
            permissionCode: "knowledge:base:view",
            type: 1,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935640856645177345",
            parentId: null,
            name: "知识库-编辑",
            permissionCode: "knowledge:base:edit",
            type: 1,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935640964094857218",
            parentId: null,
            name: "意图编辑",
            permissionCode: "knowledge:base:intent",
            type: 1,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935641065039171585",
            parentId: null,
            name: "商品库",
            permissionCode: "good:library",
            type: 3,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935641376583684097",
            parentId: null,
            name: "商品查看",
            permissionCode: "good:library:view",
            type: 1,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935641412411428866",
            parentId: null,
            name: "商品编辑",
            permissionCode: "good:library:edit",
            type: 1,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935644654516146177",
            parentId: null,
            name: "活动库",
            permissionCode: "activity:library",
            type: 3,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935644742613307394",
            parentId: null,
            name: "活动主题-查看/搜索",
            permissionCode: "activity:library:view",
            type: 1,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935644793997725697",
            parentId: null,
            name: "活动主题-活动主题增删改",
            permissionCode: "activity:library:edit",
            type: 1,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935645970952982529",
            parentId: null,
            name: "活动主题-添加活动商品",
            permissionCode: "activity:library:good:add",
            type: 1,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935646051563311106",
            parentId: null,
            name: "活动商品-查看/搜索",
            permissionCode: "activity:library:good:view",
            type: 1,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935646256073379841",
            parentId: null,
            name: "活动商品删改",
            permissionCode: "activity:library:good:edit",
            type: 1,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935646390492434433",
            parentId: null,
            name: "店铺",
            permissionCode: "shop",
            type: 3,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935646477239029761",
            parentId: null,
            name: "店铺配置",
            permissionCode: "shop:config",
            type: 3,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935646871822372866",
            parentId: null,
            name: "店铺配置修改/发布",
            permissionCode: "shop:config:edit",
            type: 1,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935646926897778689",
            parentId: null,
            name: "代码查看/编辑",
            permissionCode: "shop:config:code:edit",
            type: 1,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935647020892131330",
            parentId: null,
            name: "店铺配置版本",
            permissionCode: "shop:config:version",
            type: 3,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935647124340445186",
            parentId: null,
            name: "版本查看",
            permissionCode: "shop:config:version:view",
            type: 1,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935647413688700929",
            parentId: null,
            name: "代码查看",
            permissionCode: "shop:config:version:code",
            type: 1,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935647785211760642",
            parentId: null,
            name: "版本发布",
            permissionCode: "shop:config:version:publish",
            type: 1,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935648065961693185",
            parentId: null,
            name: "版本删除",
            permissionCode: "shop:config:version:delete",
            type: 1,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935648137059340290",
            parentId: null,
            name: "智能体",
            permissionCode: "agent",
            type: 3,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935648303761952769",
            parentId: null,
            name: "智能体配置",
            permissionCode: "agent:config",
            type: 3,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935648423228313602",
            parentId: null,
            name: "智能体编辑",
            permissionCode: "agent:config:edit",
            type: 1,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935648658168057857",
            parentId: null,
            name: "编辑代码",
            permissionCode: "agent:config:code:edit",
            type: 1,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935648719291650049",
            parentId: null,
            name: "选择版本",
            permissionCode: "agent:config:version:edit",
            type: 1,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935648823943729153",
            parentId: null,
            name: "提示词",
            permissionCode: "prompt",
            type: 3,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935649121353437186",
            parentId: null,
            name: "提示词管理",
            permissionCode: "prompt:management",
            type: 3,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935653539067039745",
            parentId: null,
            name: "能力",
            permissionCode: "prompt:management:ability",
            type: 1,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935654629762240513",
            parentId: null,
            name: "模块内容",
            permissionCode: "prompt:management:module",
            type: 1,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935655190272249858",
            parentId: null,
            name: "共享变量",
            permissionCode: "prompt:management:shared:variable",
            type: 1,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935655268118532098",
            parentId: null,
            name: "变量管理",
            permissionCode: "prompt:variable",
            type: 3,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935655383117959169",
            parentId: null,
            name: "系统变量&店铺变量",
            permissionCode: "prompt:variable:crud",
            type: 1,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935655470028132354",
            parentId: null,
            name: "提示词版本",
            permissionCode: "prompt:version",
            type: 3,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935655564479664130",
            parentId: null,
            name: "提示词版本发布",
            permissionCode: "prompt:version:publish",
            type: 1,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935655639708700673",
            parentId: null,
            name: "提示词版本查看",
            permissionCode: "prompt:version:view",
            type: 1,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935655963144065026",
            parentId: null,
            name: "效果评测",
            permissionCode: "effect:evaluation",
            type: 3,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935656446315302914",
            parentId: null,
            name: "样本管理",
            permissionCode: "effect:evaluation:sample",
            type: 3,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935656509259223042",
            parentId: null,
            name: "效果调试",
            permissionCode: "effect:evaluation:tuning",
            type: 3,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935656591891206145",
            parentId: null,
            name: "评测管理",
            permissionCode: "effect:evaluation:management",
            type: 3,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935672432720449538",
            parentId: null,
            name: "查看样本",
            permissionCode: "effect:evaluation:sample:view",
            type: 1,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935672550521671681",
            parentId: null,
            name: "管理样本",
            permissionCode: "effect:evaluation:sample:edit",
            type: 1,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935672636152582146",
            parentId: null,
            name: "效果调试 CRUD",
            permissionCode: "effect:evaluation:tuning:crud",
            type: 1,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935672706700775426",
            parentId: null,
            name: "查看评测任务",
            permissionCode: "effect:evaluation:management:view",
            type: 1,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935672772261941250",
            parentId: null,
            name: "管理评测任务",
            permissionCode: "effect:evaluation:management:edit",
            type: 1,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935672899823308802",
            parentId: null,
            name: "系统配置",
            permissionCode: "system:setting",
            type: 3,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935673017708417026",
            parentId: null,
            name: "字段配置",
            permissionCode: "system:setting:field:configure",
            type: 3,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935673129373372418",
            parentId: null,
            name: "标注配置",
            permissionCode: "system:setting:annotate:configure",
            type: 3,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935673429551321090",
            parentId: null,
            name: "系统管理",
            permissionCode: "system:management",
            type: 3,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935673859689779202",
            parentId: null,
            name: "模型配置项",
            permissionCode: "system:setting:field:configure:model",
            type: 1,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935674275311751170",
            parentId: null,
            name: "消息日志过滤字段",
            permissionCode: "system:setting:field:configure:message:field",
            type: 1,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935674654942400513",
            parentId: null,
            name: "归因配置",
            permissionCode: "system:setting:annotate:configure:ascribe",
            type: 1,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935674742112620545",
            parentId: null,
            name: "场景标签配置",
            permissionCode: "system:setting:annotate:configure:scene",
            type: 1,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935674828519477249",
            parentId: null,
            name: "意图列表配置",
            permissionCode: "system:setting:annotate:configure:intent",
            type: 1,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935675289452515330",
            parentId: null,
            name: "账号管理",
            permissionCode: "system:management:account",
            type: 3,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935675388157071361",
            parentId: null,
            name: "账号增删改",
            permissionCode: "system:management:account:crud",
            type: 1,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935675517018673154",
            parentId: null,
            name: "品牌管理",
            permissionCode: "system:management:brand",
            type: 3,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935675604998393858",
            parentId: null,
            name: "角色管理",
            permissionCode: "system:management:role",
            type: 3,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935675709377843201",
            parentId: null,
            name: "权限管理",
            permissionCode: "system:management:permission",
            type: 3,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935675865619861506",
            parentId: null,
            name: "品牌查看",
            permissionCode: "system:management:brand:view",
            type: 1,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935675942073634817",
            parentId: null,
            name: "品牌增删改",
            permissionCode: "system:management:brand:edit",
            type: 1,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935676278544896001",
            parentId: null,
            name: "店铺查看",
            permissionCode: "system:management:shop:view",
            type: 1,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935676355392933890",
            parentId: null,
            name: "店铺编辑",
            permissionCode: "system:management:shop:edit",
            type: 1,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935676518714937346",
            parentId: null,
            name: "角色增删改",
            permissionCode: "system:management:role:crud",
            type: 1,
            url: "",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "1935676564671926274",
            parentId: null,
            name: "权限增删改",
            permissionCode: "system:management:permission:crud",
            type: 1,
            url: "",
          },
        ],
        brandList: [
          {
            createdAt: null,
            updatedAt: null,
            id: "6868810118162432",
            name: "慕思",
            description: "慕思",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "6868810118162433",
            name: "HOKA",
            description: "HOKA",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "6868810118162434",
            name: "iconslab",
            description: "iconslab",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "6868810118162435",
            name: "GoLong",
            description: "GoLong",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "6868810118162436",
            name: "凤凰自行车",
            description: "凤凰自行车",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "6868810118162437",
            name: "彩棠",
            description: "彩棠",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "6868810118162438",
            name: "星客猫",
            description: "星客猫",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "6868810118162439",
            name: "梓晨",
            description: "梓晨",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "6868810118162440",
            name: "泰兰尼斯",
            description: "泰兰尼斯",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "6868810118162441",
            name: "域涵轻奢女装",
            description: "域涵轻奢女装",
          },
        ],
        shopList: [
          {
            createdAt: null,
            updatedAt: null,
            id: "6868840236607488",
            name: "慕思旗舰店",
            brandId: "6868810118162432",
            platformId: null,
            status: 1,
            description: "淘宝-慕思",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "6868840236607489",
            name: "慕思旗舰店_test",
            brandId: "6868810118162432",
            platformId: null,
            status: 1,
            description: "淘宝-慕思",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "6868840236607490",
            name: "hokaoneone官方旗舰店",
            brandId: "6868810118162433",
            platformId: null,
            status: 1,
            description: "淘宝-HOKA",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "6868840236607491",
            name: "hokaoneone官方旗舰店_test",
            brandId: "6868810118162433",
            platformId: null,
            status: 1,
            description: "淘宝-HOKA",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "6868840236607492",
            name: "iconslab旗舰店",
            brandId: "6868810118162434",
            platformId: null,
            status: 1,
            description: "淘宝-iconslab",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "6868840236607493",
            name: "bblaboratories旗舰店",
            brandId: "6868810118162435",
            platformId: null,
            status: 1,
            description: "淘宝-golong",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "6868840236607494",
            name: "凤凰官方旗舰店",
            brandId: "6868810118162436",
            platformId: null,
            status: 1,
            description: "淘宝-凤凰自行车",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "6868840236607495",
            name: "彩棠旗舰店",
            brandId: "6868810118162437",
            platformId: null,
            status: 1,
            description: "淘宝-彩棠",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "6868840236607496",
            name: "派大星星星686868",
            brandId: "6868810118162438",
            platformId: null,
            status: 1,
            description: "淘宝-星客猫",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "6868840236607497",
            name: "梓晨旗舰店",
            brandId: "6868810118162439",
            platformId: null,
            status: 1,
            description: "淘宝-梓晨",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "6868840236607498",
            name: "泰兰尼斯童鞋旗舰店",
            brandId: "6868810118162440",
            platformId: null,
            status: 1,
            description: "淘宝-泰兰尼斯",
          },
          {
            createdAt: null,
            updatedAt: null,
            id: "6868840236607499",
            name: "域涵轻奢女装",
            brandId: "6868810118162441",
            platformId: null,
            status: 1,
            description: "淘宝-muyan",
          },
        ],
      },
    });
  }),
];
