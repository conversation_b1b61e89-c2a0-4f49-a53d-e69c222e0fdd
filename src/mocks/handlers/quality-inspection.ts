import { http } from "msw";
import { faker } from "@faker-js/faker";
import { generatePagerResponse } from "./utils";

// 生成模拟数据
const generateMockItems = (count = 10) => {
  return Array(count)
    .fill(null)
    .map(() => ({
      qualityInspectionItemId: faker.string.uuid(),
      qualityInspectionItemList: [
        faker.lorem.sentence(),
        faker.lorem.sentence(),
      ],
      qualityInspectionItemName: faker.commerce.productName(),
      qualityInspectionItemScore: faker.number
        .int({ min: 0, max: 100 })
        .toString(),
      qualityInspectionItemType: faker.helpers.arrayElement([
        "语言",
        "专业度",
        "效率",
        "其他",
      ]),
    }));
};

const generateMockQualityDetail = () => {
  return generatePagerResponse(
    Array.from({ length: 10 }, () => ({
      conversationId: faker.string.uuid(),
      dialogueContent: faker.lorem.sentence(),
      dialogueQualityId: faker.string.uuid(),
      dialogueQualityItemList: [faker.lorem.sentence()],
      dialogueQuestionId: faker.string.uuid(),
      isReQuality: faker.string.uuid(),
      qualityAnalyse: faker.lorem.sentence(),
      qualityDate: faker.date.recent().toISOString(),
      qualityScore: faker.number.int({ min: 0, max: 100 }).toString(),
    })),
  );
};

export const qualityInspectionHandlers = [
  // 获取质检项列表
  http.post(
    "/message/api/conversationQuality/list/quality/inspection/item",
    () => {
      return Response.json({
        data: generateMockItems(),
        message: "操作成功",
        success: true,
      });
    },
  ),
  // 获取质检项列表
  http.post(
    "/message/api/conversationQuality/page/dialogue/quality/detail",
    () => {
      return Response.json({
        data: generateMockQualityDetail(),
        message: "操作成功",
        success: true,
      });
    },
  ),
];
