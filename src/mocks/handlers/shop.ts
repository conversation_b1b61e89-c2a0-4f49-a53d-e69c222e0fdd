import { Category } from "@/types/api/shop";
import { http, HttpResponse } from "msw";

// 模拟类目数据
const mockCategories: Category[] = [
  {
    categoryId: "cat_1",
    categoryName: "服装鞋帽",
    parentName: "",
    child: [
      {
        categoryId: "cat_1_1",
        categoryName: "女装",
        parentName: "服装鞋帽",
        child: [
          {
            categoryId: "cat_1_1_1",
            categoryName: "连衣裙",
            parentName: "女装",
            child: [],
          },
          {
            categoryId: "cat_1_1_2",
            categoryName: "上衣",
            parentName: "女装",
            child: [
              {
                categoryId: "cat_1_1_2_1",
                categoryName: "T恤",
                parentName: "上衣",
                child: [],
              },
              {
                categoryId: "cat_1_1_2_2",
                categoryName: "衬衫",
                parentName: "上衣",
                child: [],
              },
            ],
          },
        ],
      },
      {
        categoryId: "cat_1_2",
        categoryName: "男装",
        parentName: "服装鞋帽",
        child: [
          {
            categoryId: "cat_1_2_1",
            categoryName: "上衣",
            parentName: "男装",
            child: [
              {
                categoryId: "cat_1_2_1_1",
                categoryName: "T恤",
                parentName: "上衣",
                child: [],
              },
            ],
          },
        ],
      },
    ],
  },
  {
    categoryId: "cat_2",
    categoryName: "数码家电",
    parentName: "",
    child: [
      {
        categoryId: "cat_2_1",
        categoryName: "手机通讯",
        parentName: "数码家电",
        child: [
          {
            categoryId: "cat_2_1_1",
            categoryName: "智能手机",
            parentName: "手机通讯",
            child: [],
          },
        ],
      },
      {
        categoryId: "cat_2_2",
        categoryName: "电脑办公",
        parentName: "数码家电",
        child: [
          {
            categoryId: "cat_2_2_1",
            categoryName: "笔记本电脑",
            parentName: "电脑办公",
            child: [],
          },
        ],
      },
    ],
  },
  {
    categoryId: "cat_3",
    categoryName: "家居生活",
    parentName: "",
    child: [
      {
        categoryId: "cat_3_1",
        categoryName: "家具",
        parentName: "家居生活",
        child: [
          {
            categoryId: "cat_3_1_1",
            categoryName: "沙发",
            parentName: "家具",
            child: [],
          },
        ],
      },
      {
        categoryId: "cat_3_3",
        categoryName: "厨具",
        parentName: "厨具",
        child: [
          {
            categoryId: "cat_3_3_1",
            categoryName: "锅具",
            parentName: "厨具",
            child: [],
          },
        ],
      },
    ],
  },
  {
    categoryId: "cat_4",
    categoryName: "美妆个护",
    parentName: "",
    child: [
      {
        categoryId: "cat_4_1",
        categoryName: "护肤",
        parentName: "美妆个护",
        child: [
          {
            categoryId: "cat_4_1_1",
            categoryName: "洁面",
            parentName: "护肤",
            child: [],
          },
        ],
      },
    ],
  },
];

export const shopHandlers = [
  // 获取类目数据
  http.post("/muyan/api/category/selectCategoryList", async () => {
    return HttpResponse.json({
      success: true,
      message: null,
      data: mockCategories,
    });
  }),
];
