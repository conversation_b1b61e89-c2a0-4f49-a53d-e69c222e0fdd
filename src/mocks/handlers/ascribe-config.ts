import { http, HttpResponse } from "msw";

export const ascribeConfigHandlers = [
  // 获取归因标签列表
  http.get("/message/api/ascribeConfig/getAscribeItemConfig", () => {
    return HttpResponse.json({
      success: true,
      message: null,
      data: [
        {
          ascribeItemId: "97",
          userId: "user-buls7i5fuw4jpxkjg25ewd2vz6mcvguq",
          itemName: "iur解析错误",
          itemCode: "iur_analysis_error",
          sort: 0,
        },
        {
          ascribeItemId: "98",
          userId: "user-buls7i5fuw4jpxkjg25ewd2vz6mcvguq",
          itemName: "动作命中错误",
          itemCode: "action_hit_error",
          sort: 1,
        },
        {
          ascribeItemId: "99",
          userId: "user-buls7i5fuw4jpxkjg25ewd2vz6mcvguq",
          itemName: "内容瞎编",
          itemCode: "content_trump_up",
          sort: 2,
        },
        {
          ascribeItemId: "100",
          userId: "user-buls7i5fuw4jpxkjg25ewd2vz6mcvguq",
          itemName: "推荐错误",
          itemCode: "recommend_error",
          sort: 3,
        },
        {
          ascribeItemId: "101",
          userId: "user-buls7i5fuw4jpxkjg25ewd2vz6mcvguq",
          itemName: "追问逻辑问题",
          itemCode: "ask_logical_question",
          sort: 4,
        },
        {
          ascribeItemId: "102",
          userId: "user-buls7i5fuw4jpxkjg25ewd2vz6mcvguq",
          itemName: "消息回复慢",
          itemCode: "message_reply_slow",
          sort: 5,
        },
        {
          ascribeItemId: "103",
          userId: "user-buls7i5fuw4jpxkjg25ewd2vz6mcvguq",
          itemName: "重复回复",
          itemCode: "repeat_reply",
          sort: 6,
        },
        {
          ascribeItemId: "104",
          userId: "user-buls7i5fuw4jpxkjg25ewd2vz6mcvguq",
          itemName: "意图排序错误",
          itemCode: "intent_order_error",
          sort: 7,
        },
        {
          ascribeItemId: "105",
          userId: "user-buls7i5fuw4jpxkjg25ewd2vz6mcvguq",
          itemName: "其他",
          itemCode: "other",
          sort: 8,
        },
      ],
    });
  }),
];
