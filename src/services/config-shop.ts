import { muyanServe } from "@/api";
import {
  GetShopConfigResponse,
  SaveShopConfigParams,
  SaveShopConfigResponse,
  SelectShopVariableParams,
  ShopVariableListResponse,
  GetIntentConfigParams,
  GetIntentConfigResponse,
  SaveIntentConfigParams,
  SaveIntentConfigResponse,
} from "@/types/api/config";
export const ShopConfigService = {
  /**
   * 获取店铺配置
   * @returns 店铺配置
   */
  async getShopConfig(shopId: string) {
    return muyanServe
      .get("config/shop", {
        searchParams: {
          shopId,
        },
      })
      .json<GetShopConfigResponse>()
      .then((response) => response.data)
      .catch(() => null);
  },
  async saveShopConfig(params: SaveShopConfigParams) {
    return muyanServe
      .post(`config/shop?shopId=${params.shopId}`, {
        json: params,
      })
      .json<SaveShopConfigResponse>()
      .then((response) => ({
        message: response.message,
        success: response.success,
      }))
      .catch(() => ({
        success: false,
      }));
  },
  async selectShopVariableList(params: SelectShopVariableParams) {
    return muyanServe
      .post(`variable/selectShopVariableList?shopId=${params.shopId}`, {
        json: params,
      })
      .json<ShopVariableListResponse>()
      .then((response) => response.data)
      .catch(() => []);
  },
  /**
   * 获取意图配置
   */
  async getIntentConfig(params: GetIntentConfigParams) {
    return muyanServe
      .get(`config/intent?shopId=${params.shopId}`)
      .json<GetIntentConfigResponse>()
      .then((response) => {
        if (response.data) {
          return {
            ...response.data,
            intentConfig: JSON.parse(response.data.intentConfig),
          };
        }
        return null;
      })
      .catch(() => null);
  },
  async saveIntentConfig(params: SaveIntentConfigParams) {
    return muyanServe
      .post(`config/intent?shopId=${params.shopId}`, {
        json: params,
      })
      .json<SaveIntentConfigResponse>()
      .then((response) => ({
        message: response.message,
        success: response.success,
      }))
      .catch(() => ({
        success: false,
      }));
  },
};
