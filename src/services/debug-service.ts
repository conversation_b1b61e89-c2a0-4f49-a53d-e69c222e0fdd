import { muyanServe } from "@/api";
import {
  EvaluationDebugParams,
  EvaluationDebugResponse,
  EvaluationDebugResultResponse,
} from "@/types/api/evaluation-tuning";

export const DebugService = {
  /**
   * 调试配置
   *
   * @param params
   * @returns
   */
  evaluationDebug: async (params: EvaluationDebugParams) => {
    return muyanServe
      .post("debug", {
        json: params,
      })
      .json<EvaluationDebugResponse>()
      .then((response) => response.data);
  },
  /**
   * 查询调试结果
   *
   * @param debugId
   * @returns
   */
  getEvaluationDebugResult: async (debugId: string) => {
    return muyanServe
      .get(`debug/${debugId}/status`)
      .json<EvaluationDebugResultResponse>()
      .then((response) => response.data);
  },
};
