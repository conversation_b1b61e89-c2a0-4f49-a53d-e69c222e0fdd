import { muyanServe } from "@/api";
import {
  AddOrUpdateSharedVariableParams,
  AddOrUpdateSharedVariableResponse,
  DeleteSharedVariableParams,
  DeleteSharedVariableResponse,
  SelectSharedVariableListParams,
  SelectSharedVariableListResponse,
  SharedVariable,
} from "@/types/api/shared-variable";
import { serializeApiResponse } from "@/utils";
import { HTTPError } from "ky";

export const SharedVariableService = {
  /**
   * 添加或更新共享变量
   *
   * @param params
   * @returns
   */
  addOrUpdateSharedVariable: async (
    params: AddOrUpdateSharedVariableParams,
  ) => {
    return await muyanServe
      .post("variable/addOrUpdateSharedVariable", {
        json: params,
      })
      .json<AddOrUpdateSharedVariableResponse>()
      .catch(async (error: HTTPError) => {
        throw new Error((await serializeApiResponse(error.response)).message);
      });
  },

  /**
   * 删除共享变量
   *
   * @param sharedVariableId
   * @returns
   */
  deleteSharedVariable: async (params: DeleteSharedVariableParams) => {
    return await muyanServe
      .post("variable/deleteSharedVariable", {
        json: params,
      })
      .json<DeleteSharedVariableResponse>();
  },

  /**
   * 共享变量列表
   */
  selectSharedVariableList: async (params: SelectSharedVariableListParams) => {
    return muyanServe
      .post("variable/selectSharedVariableList", {
        json: params,
      })
      .json<SelectSharedVariableListResponse>()
      .then((response) => response.data)
      .catch(() => [] as SharedVariable[]);
  },
};
