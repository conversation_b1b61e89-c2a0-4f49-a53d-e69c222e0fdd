import { muyanServe } from "@/api";
import {
  brandTableData,
  BrandSaveParams,
  ShopDetailDTO,
  PlatformData,
  EditAddShopParams,
} from "@/types/api/brand";
import { RoleUserService } from "@/types/api/role";
import { BaseResponse } from "@/types/api/base";
export const BrandService = {
  getBrandList(params: { brandId?: string }) {
    return muyanServe
      .post("shop/brand/list", {
        json: params,
      })
      .json<BaseResponse<brandTableData[]>>()
      .then((response) => {
        return {
          data: response.data,
        };
      })
      .catch(() => ({
        data: [] as brandTableData[],
      }));
  },
  // 保存品牌
  saveBrand(params: BrandSaveParams) {
    return muyanServe
      .post("shop/brand/save/brand", {
        json: params,
      })
      .json<BaseResponse<any>>()
      .then((response) => response.data);
  },
  // 更新品牌
  upDataBrand(params: BrandSaveParams) {
    return muyanServe
      .post("shop/brand/update/brand", {
        json: params,
      })
      .json<BaseResponse<any>>()
      .then((response) => response.data);
  },
  // 删除品牌
  deleteBrand(brandId: string) {
    return muyanServe
      .post("shop/brand/delete/brand", {
        json: {
          brandId: brandId,
        },
      })
      .json<BaseResponse<any>>()
      .then((response) => response.data);
  },
  // 删除店铺
  deleteShop(shopId: string) {
    return muyanServe
      .post("shop/brand/delete/shop", {
        json: {
          shopId: shopId,
        },
      })
      .json<BaseResponse<any>>()
      .then((response) => response.data);
  },
  // 查询品牌详情
  detailBrand(brandId: string) {
    return muyanServe
      .post("shop/brand/detail/brand", {
        json: {
          brandId: brandId,
        },
      })
      .json<BaseResponse<BrandSaveParams>>()
      .then((response) => response.data);
  },
  // 查询店铺详情
  detailShop(shopId: string) {
    return muyanServe
      .post("shop/brand/detail/shop", {
        json: {
          shopId: shopId,
        },
      })
      .json<BaseResponse<ShopDetailDTO>>()
      .then((response) => response.data);
  },
  // 平台列表
  platformList() {
    return muyanServe
      .post("shop/brand/platform/list", {
        json: {},
      })
      .json<BaseResponse<PlatformData[]>>()
      .then((response) => response.data);
  },
  // 根据角色用户列表
  roleUserList(brandId: string) {
    return muyanServe
      .post("role/permission/listRoleUserWithRole", {
        json: {
          brandId: brandId,
        },
      })
      .json<BaseResponse<RoleUserService[]>>()
      .then((response) => response.data);
  },
  // 保存 店铺
  saveShop(params: EditAddShopParams) {
    return muyanServe
      .post("shop/brand/save/shop", {
        json: params,
      })
      .json<BaseResponse<any>>()
      .then((response) => response.data);
  },
  // 更新店铺
  upDataShop(params: EditAddShopParams) {
    return muyanServe
      .post("shop/brand/update/shop", {
        json: params,
      })
      .json<BaseResponse<any>>()
      .then((response) => response.data);
  },
  // 删除客服账号
  deleteCustomer(id: string) {
    return muyanServe
      .post("shop/brand/delete/customer", {
        json: {
          id: id,
        },
      })
      .json<BaseResponse<any>>()
      .then((response) => response.data);
  },
};
