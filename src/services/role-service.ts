import { muyanServe } from "@/api";
import { BaseResponse } from "@/types/api/base";
import {
  AddPermissionParams,
  AddPermissionResponse,
  MenuPermissionResponse,
  PermissionListResponse,
} from "@/types/api/permission";
import {
  AddUserParams,
  AddUserResponse,
  DeleteUserParams,
  DeleteUserResponse,
  RoleListResponse,
  RolePermissionListResponse,
  RoleUser,
  RoleUserListParams,
  RoleUserListResponse,
  RoleWithPermission,
  SetOwnPasswordParams,
  SetPasswordParams,
  SetPasswordResponse,
  UpdateUserParams,
  UpdateUserResponse,
} from "@/types/api/role";
import { UserDetailResponse } from "@/types/api/user";

export const RoleService = {
  /**
   * 获取权限列表
   */
  getPermissionList: () =>
    muyanServe
      .post("role/permission/listPermission")
      .json<PermissionListResponse>()
      .then((res) => res.data)
      .catch(() => null),
  /**
   * 添加权限
   */
  savePermission: (params: AddPermissionParams) =>
    muyanServe
      .post("role/permission/saveMenuPermission", {
        json: params,
      })
      .json<AddPermissionResponse>()
      .then((res) => res.data),
  /**
   * 获取角色权限列表
   * @returns 角色权限列表
   */
  getRolePermissionList: () =>
    muyanServe
      .post("role/permission/list")
      .json<RolePermissionListResponse>()
      .then((res) => res.data)
      .catch(() => null),
  /**
   * 获取用户详情
   * @returns 用户详情
   */
  getUserDetail: () =>
    muyanServe
      .post("role/permission/userDetail", {
        json: {},
      })
      .json<UserDetailResponse>()
      .then((res) => res.data),

  /**
   * 获取角色列表
   * @returns 角色列表
   */
  getRoleList: () =>
    muyanServe
      .post("role/permission/listRole")
      .json<RoleListResponse>()
      .then((res) => res.data),

  /**
   * 获取角色权限列表
   * @param roleId 角色ID
   * @returns 角色权限列表
   */
  getRolePermissions: () =>
    muyanServe
      .post("role/permission/rolePermissionList")
      .json<RolePermissionListResponse>()
      .then((res) => res.data),

  /**
   * 获取所有权限列表
   * @returns 所有权限列表
   */
  getAllPermissions: () =>
    muyanServe
      .post("role/permission/listPermission")
      .json<BaseResponse<MenuPermissionResponse>>()
      .then((res) => res.data),

  /**
   * 保存角色权限
   * @param roleId 角色ID
   * @param permissionCodes 权限编码列表
   * @returns 保存结果
   */
  saveRolePermissions: (role: RoleWithPermission) =>
    muyanServe
      .post("role/permission/bind", {
        json: {
          role,
        },
      })
      .json<AddPermissionResponse>()
      .then((res) => res.data)
      .catch(() => null),
  /**
   * 获取角色用户列表
   */
  getRoleUserList: (params: RoleUserListParams) =>
    muyanServe
      .post("role/permission/listRoleUser", {
        json: params,
      })
      .json<RoleUserListResponse>()
      .then((response) => ({
        data: response.data.results,
        totalPage: response.data.totalPage,
        totalSize: response.data.totalSize,
      }))
      .catch(() => ({
        data: [] as RoleUser[],
        totalPage: 0,
        totalSize: 0,
      })),
  async deleteUser(params: DeleteUserParams) {
    return muyanServe
      .delete("role/permission/deleteUser", {
        json: params,
      })
      .json<DeleteUserResponse>()
      .then((response) => ({
        message: response.message,
        success: response.success,
      }))
      .catch(() => ({
        success: false,
      }));
  },
  async setPassword(params: SetPasswordParams) {
    return muyanServe
      .put(`role/permission/password/${params.userId}`, {
        json: {
          password: params.password,
        },
      })
      .json<SetPasswordResponse>()
      .then((response) => ({
        message: response.message,
        success: response.success,
      }))
      .catch(() => ({
        success: false,
      }));
  },

  async setOwnPassword(params: SetOwnPasswordParams) {
    return muyanServe
      .put(`role/permission/password`, {
        json: {
          password: params.password,
        },
      })
      .json<SetPasswordResponse>()
      .then((response) => ({
        message: response.message,
        success: response.success,
      }))
      .catch(() => ({
        success: false,
      }));
  },
  async addUser(params: AddUserParams) {
    return muyanServe
      .post("role/permission/addUser", {
        json: params,
      })
      .json<AddUserResponse>()
      .then((response) => ({
        message: response.message,
        success: response.success,
      }))
      .catch(() => ({
        success: false,
      }));
  },
  async updateUser(params: UpdateUserParams) {
    return muyanServe
      .post("role/permission/updateUser", {
        json: params,
      })
      .json<UpdateUserResponse>()
      .then((response) => ({
        message: response.message,
        success: response.success,
      }))
      .catch(() => ({
        success: false,
      }));
  },
};
