import { muyanServe } from "@/api";
import {
  AddOrUpdateModuleLabelParams,
  AddOrUpdateModuleLabelResponse,
  AddOrUpdateModuleParams,
  AddOrUpdateModuleResponse,
  DeleteModuleLabelParams,
  DeleteModuleLabelResponse,
  DeleteModuleParams,
  DeleteModuleResponse,
  SelectModuleLabelListParams,
  SelectModuleLabelListResponse,
  SelectModuleLabelNameListParams,
  SelectModuleLabelNameListResponse,
  SelectModuleListParams,
  SelectModuleListResponse,
  SelectModuleNameListParams,
  SelectModuleNameListResponse,
} from "@/types/api/module";

export const ModuleService = {
  /**
   * 添加或更新模块
   *
   * @param params
   * @returns
   */
  addOrUpdateModule: async (params: AddOrUpdateModuleParams) => {
    return muyanServe
      .post("module/addOrUpdateModule", {
        json: params,
      })
      .json<AddOrUpdateModuleResponse>();
  },

  /**
   * 添加或更新模块标签
   *
   * @param params
   * @returns
   */
  addOrUpdateModuleLabel: async (params: AddOrUpdateModuleLabelParams) => {
    return muyanServe
      .post("module/addOrUpdateModuleLabel", {
        json: params,
      })
      .json<AddOrUpdateModuleLabelResponse>();
  },

  /**
   * 删除模块
   *
   * @param params
   * @returns
   */
  deleteModule: async (params: DeleteModuleParams) => {
    return muyanServe
      .post("module/delModule", {
        json: params,
      })
      .json<DeleteModuleResponse>();
  },

  /**
   * 删除模块标签
   *
   * @param params
   * @returns
   */
  deleteModuleLabel: async (params: DeleteModuleLabelParams) => {
    return muyanServe
      .post("module/delModuleLabel", {
        json: params,
      })
      .json<DeleteModuleLabelResponse>();
  },

  /**
   * 查询模块列表
   *
   * @param params
   * @returns
   */
  selectModuleLabelList: async (params: SelectModuleLabelListParams) => {
    return muyanServe
      .post("module/selectModuleLabelList", {
        json: params,
      })
      .json<SelectModuleLabelListResponse>()
      .then((response) => response.data)
      .catch(() => null);
  },

  /**
   * 查询模块列表
   *
   * @param params
   * @returns
   */
  selectModuleList: async (params: SelectModuleListParams) => {
    return muyanServe
      .post("module/selectModuleList", {
        json: params,
      })
      .json<SelectModuleListResponse>()
      .then((response) => response.data)
      .catch(() => null);
  },

  /**
   * 模块标签名称列表
   *
   * @param params
   * @returns
   */
  selectModuleLabelNameList: async (
    params: SelectModuleLabelNameListParams,
  ) => {
    return muyanServe
      .post("module/selectModuleLabelNameList", {
        json: params,
      })
      .json<SelectModuleLabelNameListResponse>()
      .then((response) => response.data)
      .catch(() => null);
  },

  /**
   * 模块名称列表
   *
   * @param params
   * @returns
   */
  selectModuleNameList: async (params: SelectModuleNameListParams) => {
    return muyanServe
      .post("module/selectModuleNameList", {
        json: params,
      })
      .json<SelectModuleNameListResponse>()
      .then((response) => response.data)
      .catch(() => null);
  },
};
