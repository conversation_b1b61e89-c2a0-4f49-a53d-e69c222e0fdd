import { muyanServe } from "@/api";
import { LoginParams, LoginResponse, LogoutResponse } from "@/types/api/user";

export const AuthService = {
  login: (params: LoginParams) =>
    muyanServe
      .post("login", {
        json: params,
      })
      .json<LoginResponse>()
      .then((res) => res.data)
      .catch((err) => {
        console.log("err", err);

        throw err;
      }),
  logout: () =>
    muyanServe
      .post("logout")
      .json<LogoutResponse>()
      .then((res) => res.data)
      .catch((err) => {
        throw err;
      }),
};
