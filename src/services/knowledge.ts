import { muyanServe } from "@/api";
import {
  getKnowledgeListParams,
  getKnowledgeResponse,
  KnowledgeItem,
  KnowledgeCopyParams,
  KnowledgeCopyResponse,
  DeleteKnowledgeParams,
  DeleteKnowledgeResponse,
  KnowledgeDownload,
  KnowledgeDownloadResponse,
  KnowledgeAddParams,
  KnowledgeAddResponse,
  KnowledgeUpdateParams,
  KnowledgeUpdateResponse,
  KnowledgeImportParams,
  KnowledgeImportResponse,
  GetConversationQuestionsResponse,
  GetConversationQuestionsParams,
} from "@/types/api/knowledge-library";

export const KnowledgeService = {
  async getKnowledgeList(params: getKnowledgeListParams) {
    return muyanServe
      .post(`knowledge/page?shopId=${params.shopId}`, {
        json: params,
      })
      .json<getKnowledgeResponse>()
      .then((response) => ({
        data: response.data.results,
        totalPage: response.data.totalPage,
        totalSize: response.data.totalSize,
      }))
      .catch(() => ({
        data: [] as KnowledgeItem[],
        totalPage: 0,
        totalSize: 0,
      }));
  },
  async knowledgeCopy(params: KnowledgeCopyParams) {
    return muyanServe
      .post(
        `knowledge/${params.qaKnowledgeId}/copy?shopId=${params.shopId}`,
        {},
      )
      .json<KnowledgeCopyResponse>()
      .then((response) => ({
        message: response.message,
        success: response.success,
      }))
      .catch(() => ({
        success: false,
      }));
  },
  async deleteKnowledge(params: DeleteKnowledgeParams) {
    return muyanServe
      .delete(`knowledge/${params.qaKnowledgeId}?shopId=${params.shopId}`, {
        json: params,
      })
      .json<DeleteKnowledgeResponse>()
      .then((response) => ({
        message: response.message,
        success: response.success,
      }))
      .catch(() => ({
        success: false,
      }));
  },
  async knowledgeDownload(params: KnowledgeDownload) {
    return muyanServe
      .get(`knowledge/download?shopId=${params.shopId}`)
      .json<KnowledgeDownloadResponse>()
      .then((response) => ({
        message: response.message,
        success: response.success,
        data: response.data,
      }))
      .catch(() => ({
        success: false,
        data: { url: "" },
      }));
  },
  async knowledgeAdd(params: KnowledgeAddParams) {
    return muyanServe
      .post(`knowledge?shopId=${params.shopId}`, {
        json: params,
      })
      .json<KnowledgeAddResponse>()
      .then((response) => ({
        message: response.message,
        success: response.success,
      }))
      .catch(() => ({
        success: false,
      }));
  },
  async knowledgeEdit(params: KnowledgeUpdateParams) {
    return muyanServe
      .put(`knowledge/${params.qaKnowledgeId}?shopId=${params.shopId}`, {
        json: params,
      })
      .json<KnowledgeUpdateResponse>()
      .then((response) => ({
        message: response.message,
        success: response.success,
      }))
      .catch(() => ({
        success: false,
      }));
  },
  async knowledgeImport(params: KnowledgeImportParams) {
    return muyanServe
      .post(`knowledge/import?shopId=${params.shopId}`, {
        json: params,
      })
      .json<KnowledgeImportResponse>()
      .then((response) => ({
        message: response.message,
        success: response.success,
      }))
      .catch(() => ({
        success: false,
      }));
  },

  /**
   * 获取聊天问法
   * @param params 请求参数
   * @returns
   */
  async getConversationQuestions(params: GetConversationQuestionsParams) {
    return muyanServe
      .post(`knowledge/random/question?shopId=${params.shopId}`)
      .json<GetConversationQuestionsResponse>();
  },
};
