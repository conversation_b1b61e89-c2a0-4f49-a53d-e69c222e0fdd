import { muyanServe } from "@/api";
import {
  getAgentConfigParams,
  AgentListResponse,
  UpdateAgentParams,
  getUserBandParams,
  BandListType,
  delAiAgentConfig,
  CustomerListType,
  AgentConfig,
  ChooseVersionParams,
  CodeEditParams,
} from "@/types/api/agent-config";
import { BaseResponse } from "@/types/api/base";
export const AiAgentService = {
  // 获取智能体配置列表
  getAgentList(params: getAgentConfigParams) {
    return muyanServe
      .post("aiAgentConfig/selectAiAgentConfigPage", {
        json: params,
      })
      .json<AgentListResponse>()
      .then((res) => {
        if (res.data) {
          return {
            data: res.data.results,
            totalPage: res.data.totalPage,
            totalSize: res.data.totalSize,
          };
        } else {
          return {
            data: [],
            totalPage: 0,
            totalSize: 0,
          };
        }
      })
      .catch(() => ({
        data: [] as AgentConfig[],
        totalPage: 0,
        totalSize: 0,
      }));
  },
  //   新增修改智能体
  upDataAgent(params: UpdateAgentParams) {
    return muyanServe
      .post("aiAgentConfig/addOrUpdateAiAgentConfig", {
        json: params,
      })
      .json<BaseResponse<any>>()
      .then((response) => response.data);
  },

  //   获取品牌客服列表
  getSelectUserList(params: getUserBandParams) {
    return muyanServe
      .post("aiAgentConfig/selectUserList", {
        json: params,
      })
      .json<BaseResponse<BandListType[]>>()
      .then((response) => {
        return response.data;
      });
  },
  //   获取接待客服列表
  getSelectCustomerList(params: getUserBandParams) {
    return muyanServe
      .post("aiAgentConfig/selectCustomerList", {
        json: params,
      })
      .json<BaseResponse<CustomerListType[]>>()
      .then((response) => response.data);
  },
  //   删除智能体
  delAiAgentConfig(params: delAiAgentConfig) {
    return muyanServe
      .post("aiAgentConfig/delAiAgentConfig", {
        json: params,
      })
      .json<BaseResponse<any>>()
      .then((response) => response.data);
  },
  //   克隆智能体
  copyAiAgentConfig(params: delAiAgentConfig) {
    return muyanServe
      .post("aiAgentConfig/copyAiAgentConfig", {
        json: params,
      })
      .json<BaseResponse<any>>()
      .then((response) => response.data);
  },
  // 选择版本
  putChooseVersion(params: ChooseVersionParams) {
    return muyanServe
      .post("aiAgentConfig/chooseVersion", {
        json: params,
      })
      .json<BaseResponse<any>>()
      .then((response) => response.data);
  },

  updateAiAgentConfigContent(params: CodeEditParams) {
    return muyanServe
      .post("aiAgentConfig/updateAiAgentConfigContent", {
        json: params,
      })
      .json<BaseResponse<any>>()
      .then((response) => response.data);
  },
};
