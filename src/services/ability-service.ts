import { muyanServe } from "@/api";
import {
  AddOrUpdateAbilityParams,
  AddOrUpdateAbilityResponse,
  DeleteAbilityParams,
  DeleteAbilityResponse,
  GetAbilityParams,
  GetAbilityResponse,
  SelectAbilityListParams,
  SelectAbilityListResponse,
  SelectAbilityNameListParams,
  SelectAbilityNameListResponse,
} from "@/types/api/ability";

export const AbilityService = {
  /**
   * 添加或更新能力
   *
   * @param params
   * @returns
   */
  addOrUpdateAbility: async (params: AddOrUpdateAbilityParams) => {
    return muyanServe
      .post("prompt/ability/addOrUpdateAbility", {
        json: params,
      })
      .json<AddOrUpdateAbilityResponse>();
  },

  /**
   * 删除能力
   *
   * @param params
   * @returns
   */
  deleteAbility: async (params: DeleteAbilityParams) => {
    return muyanServe
      .post("prompt/ability/delAbility", {
        json: params,
      })
      .json<DeleteAbilityResponse>();
  },

  /**
   * 获取能力详情
   *
   * @param params
   * @returns
   */
  getAbilityDetail: async (params: GetAbilityParams) => {
    return muyanServe
      .get("prompt/ability/getAbilityDetail", {
        json: params,
      })
      .json<GetAbilityResponse>()
      .then((response) => response.data)
      .catch(() => null);
  },

  /**
   * 查询能力列表
   *
   * @param params
   * @returns
   */
  selectAbilityList: async (params: SelectAbilityListParams) => {
    return muyanServe
      .post("prompt/ability/selectAbilityList", {
        json: params,
      })
      .json<SelectAbilityListResponse>()
      .then((response) => response.data)
      .catch(() => null);
  },

  /**
   * 能力名称列表
   *
   * @param params
   * @returns
   */
  selectAbilityNameList: async (params: SelectAbilityNameListParams) => {
    return muyanServe
      .post("prompt/ability/selectAbilityNameList", {
        json: params,
      })
      .json<SelectAbilityNameListResponse>()
      .then((response) => response.data)
      .catch(() => null);
  },
};
