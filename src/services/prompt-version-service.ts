import { muyanServe } from "@/api";
import {
  AddOrUpdatePromptVersionParams,
  AddOrUpdatePromptVersionResponse,
  DeletePromptVersionParams,
  DeletePromptVersionResponse,
  GetPromptVersionParams,
  GetPromptVersionResponse,
  PromptVersion,
  PublishPromptVersionParams,
  PublishPromptVersionResponse,
  SelectPromptVersionListParams,
  SelectPromptVersionListResponse,
} from "@/types/api/prompt-version";

export const PromptVersionService = {
  /**
   * 添加或更新提示词版本
   *
   * @param params
   * @returns
   */
  addOrUpdatePromptVersion: async (params: AddOrUpdatePromptVersionParams) => {
    return muyanServe
      .post("prompt/version/addOrUpdatePromptVersion", {
        json: params,
      })
      .json<AddOrUpdatePromptVersionResponse>();
  },

  /**
   * 删除提示词版本
   *
   * @param promptVersionId
   * @returns
   */
  deletePromptVersion: async (params: DeletePromptVersionParams) => {
    return muyanServe
      .post("prompt/version/delPromptVersion", {
        json: params,
      })
      .json<DeletePromptVersionResponse>();
  },

  /**
   * 获取提示词版本详情
   *
   * @param params
   * @returns
   */
  getPromptVersionDetail: async (params: GetPromptVersionParams) => {
    return muyanServe
      .post("prompt/version/getPromptVersionDetail", {
        json: params,
      })
      .json<GetPromptVersionResponse>()
      .then((response) => response.data)
      .catch(() => null);
  },

  /**
   * 发布提示词版本
   *
   * @param params
   * @returns
   */
  publishPromptVersion: async (params: PublishPromptVersionParams) => {
    return muyanServe
      .post("prompt/version/publishPromptVersion", {
        json: params,
      })
      .json<PublishPromptVersionResponse>();
  },

  /**
   * 查询提示词版本列表
   *
   * @param params
   * @returns
   */
  selectPromptVersionList: async (params: SelectPromptVersionListParams) => {
    return muyanServe
      .post("prompt/version/selectPromptVersionList", {
        json: params,
      })
      .json<SelectPromptVersionListResponse>()
      .then((response) => response.data)
      .catch(() => [] as PromptVersion[]);
  },
};
