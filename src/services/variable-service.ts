import { muyanServe } from "@/api";
import {
  AddOrUpdateVariableParams,
  AddOrUpdateVariableResponse,
  DeleteVariableParams,
  DeleteVariableResponse,
  SelectVariableListParams,
  SelectVariableListResponse,
  Variable,
} from "@/types/api/variable";

export const variableService = {
  /**
   * 添加或更新变量
   *
   * @param params
   * @returns
   */
  addOrUpdateVariable: async (params: AddOrUpdateVariableParams) => {
    return muyanServe
      .post("variable/addOrUpdateVariable", {
        json: params,
      })
      .json<AddOrUpdateVariableResponse>();
  },

  /**
   * 删除变量
   *
   * @param variableId
   * @returns
   */
  deleteVariable: async (params: DeleteVariableParams) => {
    return muyanServe
      .post("variable/deleteVariable", {
        json: params,
      })
      .json<DeleteVariableResponse>();
  },

  /**
   * 变量列表
   */
  selectVariableList: async (params: SelectVariableListParams) => {
    return muyanServe
      .post("variable/selectVariableList", {
        json: params,
      })
      .json<SelectVariableListResponse>()
      .then((response) => response.data)
      .catch(() => [] as Variable[]);
  },
};
