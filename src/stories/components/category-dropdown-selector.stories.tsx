import { CategoryDropdownSelector } from "@/components/common/category-dropdown-selector";
import type { Meta, StoryObj } from "@storybook/react";
import { useState } from "react";

const meta: Meta<typeof CategoryDropdownSelector> = {
  title: "组件/类目下拉组件",
  component: CategoryDropdownSelector,
  argTypes: {
    value: {
      control: false,
      description: "选中的类目ID数组",
    },
    onChange: {
      control: false,
      description: "选择变化回调函数",
    },
    triggerClassName: {
      control: { type: "text" },
      description: "触发器的CSS类名",
    },
    placeholder: {
      control: { type: "text" },
      description: "占位符文本",
    },
    disabled: {
      control: { type: "boolean" },
      description: "是否禁用",
    },
    align: {
      control: { type: "select" },
      options: ["start", "end", "center"],
      description: "对齐方式",
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// 基础示例
export const Default: Story = {
  render: (args) => {
    const [selectedCategories, setSelectedCategories] = useState<string[]>([]);

    return (
      <div className="w-96">
        <CategoryDropdownSelector
          {...args}
          value={selectedCategories}
          onChange={setSelectedCategories}
        />
        <div className="mt-4 p-3 bg-gray-50 rounded">
          <p className="text-sm font-medium">已选择的类目ID:</p>
          <pre className="text-xs mt-1">
            {JSON.stringify(selectedCategories, null, 2)}
          </pre>
        </div>
      </div>
    );
  },
  args: {
    placeholder: "请选择类目",
  },
};

// 预设选中值
export const WithPreselected: Story = {
  render: (args) => {
    const [selectedCategories, setSelectedCategories] = useState<string[]>([
      "cat_1_1_1", // 连衣裙
      "cat_2_1_1", // 智能手机
    ]);

    return (
      <div className="w-96">
        <CategoryDropdownSelector
          {...args}
          value={selectedCategories}
          onChange={setSelectedCategories}
        />
        <div className="mt-4 p-3 bg-gray-50 rounded">
          <p className="text-sm font-medium">已选择的类目ID:</p>
          <pre className="text-xs mt-1">
            {JSON.stringify(selectedCategories, null, 2)}
          </pre>
        </div>
      </div>
    );
  },
  args: {
    placeholder: "请选择类目",
  },
};

// 自定义样式
export const CustomStyle: Story = {
  render: (args) => {
    const [selectedCategories, setSelectedCategories] = useState<string[]>([]);

    return (
      <div className="w-80">
        <CategoryDropdownSelector
          {...args}
          value={selectedCategories}
          onChange={setSelectedCategories}
        />
        <div className="mt-4 p-3 bg-gray-50 rounded">
          <p className="text-sm font-medium">已选择的类目ID:</p>
          <pre className="text-xs mt-1">
            {JSON.stringify(selectedCategories, null, 2)}
          </pre>
        </div>
      </div>
    );
  },
  args: {
    triggerClassName: "border-2 border-green-300 focus-within:border-green-500",
    placeholder: "选择您需要的类目",
  },
};

// 禁用状态
export const Disabled: Story = {
  render: (args) => {
    const [selectedCategories, setSelectedCategories] = useState<string[]>([
      "cat_3_1_1", // 沙发
      "cat_4_1_1", // 洁面
    ]);

    return (
      <div className="w-80">
        <CategoryDropdownSelector
          {...args}
          value={selectedCategories}
          onChange={setSelectedCategories}
        />
        <div className="mt-4 p-3 bg-gray-50 rounded">
          <p className="text-sm font-medium">已选择的类目ID:</p>
          <pre className="text-xs mt-1">
            {JSON.stringify(selectedCategories, null, 2)}
          </pre>
        </div>
      </div>
    );
  },
  args: {
    placeholder: "请选择类目",
    disabled: true,
  },
};

// 父子同步演示
export const ParentChildSync: Story = {
  render: () => {
    const [selectedCategories, setSelectedCategories] = useState<string[]>([]);

    const handleSelectAllChildren = () => {
      // 选择"服装"下的所有子类目，测试父类目自动选中
      setSelectedCategories([
        "cat_1_1_1", // 连衣裙
        "cat_1_1_2", // 上衣
        "cat_1_2_1", // T恤
        "cat_1_2_2", // 衬衫
      ]);
    };

    const handleSelectPartialChildren = () => {
      // 只选择部分子类目，测试父类目不会被选中
      setSelectedCategories([
        "cat_1_1_1", // 连衣裙
        "cat_2_1_1", // 智能手机
      ]);
    };

    return (
      <div className="w-96 space-y-4">
        <CategoryDropdownSelector
          value={selectedCategories}
          onChange={setSelectedCategories}
          placeholder="测试父子同步功能"
        />

        <div className="space-y-2">
          <div className="flex gap-2">
            <button
              onClick={() => setSelectedCategories([])}
              className="px-3 py-1 text-sm bg-red-500 text-white rounded hover:bg-red-600"
            >
              清空选择
            </button>
            <button
              onClick={handleSelectAllChildren}
              className="px-3 py-1 text-sm bg-green-500 text-white rounded hover:bg-green-600"
            >
              选择服装所有子类目
            </button>
            <button
              onClick={handleSelectPartialChildren}
              className="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              选择部分子类目
            </button>
          </div>
        </div>

        <div className="p-3 bg-gray-50 rounded">
          <p className="text-sm font-medium">测试说明:</p>
          <ul className="text-xs mt-1 space-y-1">
            <li>• 点击"选择服装所有子类目"应该自动选中"服装"父类目</li>
            <li>• 点击"选择部分子类目"不会自动选中父类目</li>
            <li>• 在下拉菜单中取消部分子类目会自动取消父类目</li>
            <li>• 选中父类目会自动选中所有子类目</li>
          </ul>
          <p className="text-xs mt-2">
            已选择类目数量: {selectedCategories.length}
          </p>
          <pre className="text-xs mt-1">
            {JSON.stringify(selectedCategories, null, 2)}
          </pre>
        </div>
      </div>
    );
  },
};

// 交互式演示
export const Interactive: Story = {
  render: () => {
    const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
    const [disabled, setDisabled] = useState(false);
    const [placeholder, setPlaceholder] = useState("请选择类目");

    const handleClear = () => {
      setSelectedCategories([]);
    };

    const handleSelectSample = () => {
      setSelectedCategories([
        "cat_1_1_2_1", // T恤
        "cat_2_2_1", // 笔记本电脑
        "cat_3_3_1", // 锅具
      ]);
    };

    return (
      <div className="w-96 space-y-4">
        <CategoryDropdownSelector
          value={selectedCategories}
          onChange={setSelectedCategories}
          placeholder={placeholder}
          disabled={disabled}
        />

        <div className="space-y-2">
          <div className="flex gap-2">
            <button
              onClick={handleClear}
              className="px-3 py-1 text-sm bg-red-500 text-white rounded hover:bg-red-600"
            >
              清空选择
            </button>
            <button
              onClick={handleSelectSample}
              className="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              选择示例
            </button>
            <button
              onClick={() => setDisabled(!disabled)}
              className="px-3 py-1 text-sm bg-gray-500 text-white rounded hover:bg-gray-600"
            >
              {disabled ? "启用" : "禁用"}
            </button>
          </div>

          <div>
            <label className="block text-xs font-medium mb-1">
              占位符文本:
            </label>
            <input
              type="text"
              value={placeholder}
              onChange={(e) => setPlaceholder(e.target.value)}
              className="w-full px-2 py-1 text-xs border rounded"
            />
          </div>
        </div>

        <div className="p-3 bg-gray-50 rounded">
          <p className="text-sm font-medium">状态信息:</p>
          <ul className="text-xs mt-1 space-y-1">
            <li>已选择类目数量: {selectedCategories.length}</li>
            <li>是否禁用: {disabled ? "是" : "否"}</li>
            <li>选中的类目ID: {selectedCategories.join(", ") || "无"}</li>
          </ul>
        </div>
      </div>
    );
  },
};
