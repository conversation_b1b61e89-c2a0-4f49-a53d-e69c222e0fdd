import { InitiativeMessage } from "@/components/common/message/initiative-message";
import type { Meta, StoryObj } from "@storybook/react";

const meta = {
  title: "组件/消息/主动触发消息",
  component: InitiativeMessage,
  parameters: {
    layout: "centered",
  },
  decorators: [
    (Story) => (
      <div className="p-8 max-w-2xl bg-background rounded-lg border">
        <Story />
      </div>
    ),
  ],
  tags: ["autodocs"],
} satisfies Meta<typeof InitiativeMessage>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    messageId: "initiative-message-1",
    content: JSON.stringify([
      {
        type: "text",
        text: [
          {
            value: "系统检测到您的订单可能需要跟进，已为您安排专属客服处理",
            meta_data: null,
          },
        ],
      },
    ]),
    compact: false,
  },
};

export const Compact: Story = {
  args: {
    messageId: "initiative-message-2",
    content: JSON.stringify([
      {
        type: "text",
        text: [
          {
            value: "追单提醒：订单处理中，请耐心等待",
            meta_data: null,
          },
        ],
      },
    ]),
    compact: true,
  },
};

export const LongText: Story = {
  args: {
    messageId: "initiative-message-3",
    content: JSON.stringify([
      {
        type: "text",
        text: [
          {
            value:
              "系统智能分析发现您的订单存在异常情况，已自动触发人工客服介入处理，我们将在24小时内为您提供解决方案",
            meta_data: null,
          },
        ],
      },
    ]),
    compact: false,
  },
};

export const PlainText: Story = {
  args: {
    messageId: "initiative-message-4",
    content: "系统主动提醒：您的订单状态已更新",
    compact: false,
  },
};

export const MultipleMessages: Story = {
  args: {
    messageId: "multiple-messages",
    content: "多个消息示例",
    compact: false,
  },
  render: () => (
    <div className="space-y-4">
      <InitiativeMessage
        messageId="initiative-1"
        content="订单跟进提醒"
        compact={false}
      />
      <InitiativeMessage
        messageId="initiative-2"
        content="系统检测到异常，已安排处理"
        compact={true}
      />
      <InitiativeMessage
        messageId="initiative-3"
        content="智能客服已为您匹配最佳解决方案"
        compact={false}
      />
    </div>
  ),
};
