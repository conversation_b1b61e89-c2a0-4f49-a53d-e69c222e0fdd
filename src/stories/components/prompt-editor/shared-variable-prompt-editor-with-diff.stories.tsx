import { SharedVariablePromptEditor } from "@/components/common/prompt-editor";
import type { Meta, StoryObj } from "@storybook/react";
import { JSONContent } from "@tiptap/react";

const sharedVariableEditorMeta: Meta<typeof SharedVariablePromptEditor> = {
  title: "组件/提示词编辑器/共享变量编辑器（带对比）",
  component: SharedVariablePromptEditor,
  parameters: {
    layout: "padded",
    docs: {
      description: {
        component: "带有版本对比功能的共享变量提示词编辑器组件",
      },
    },
  },
  argTypes: {
    content: {
      description: "当前内容",
      control: { type: "object" },
    },
    previousContent: {
      description: "之前版本的内容",
      control: { type: "object" },
    },
    editable: {
      description: "是否可编辑",
      control: { type: "boolean" },
    },
    enableDiff: {
      description: "是否启用 diff 模式",
      control: { type: "boolean" },
    },
    className: {
      description: "自定义样式类名",
      control: { type: "text" },
    },
  },
};

export default sharedVariableEditorMeta;
type SharedVariableStory = StoryObj<typeof SharedVariablePromptEditor>;

const sharedVariableBaseContent: JSONContent = {
  type: "doc",
  content: [
    {
      type: "tableVariable",
      content: [
        {
          type: "tableVariableRow",
          content: [
            {
              type: "tableVariableHeader",
              content: [
                {
                  type: "paragraph",
                  content: [
                    {
                      type: "text",
                      text: "变量名",
                    },
                  ],
                },
              ],
            },
            {
              type: "tableVariableHeader",
              content: [
                {
                  type: "paragraph",
                  content: [
                    {
                      type: "text",
                      text: "变量值",
                    },
                  ],
                },
              ],
            },
          ],
        },
        {
          type: "tableVariableRow",
          content: [
            {
              type: "tableVariableCell",
              content: [
                {
                  type: "paragraph",
                  content: [
                    {
                      type: "text",
                      text: "用户名",
                    },
                  ],
                },
              ],
            },
            {
              type: "tableVariableCell",
              content: [
                {
                  type: "paragraph",
                  content: [
                    {
                      type: "text",
                      text: "张三",
                    },
                  ],
                },
              ],
            },
          ],
        },
      ],
    },
  ],
};

const sharedVariableModifiedContent: JSONContent = {
  type: "doc",
  content: [
    {
      type: "tableVariable",
      content: [
        {
          type: "tableVariableRow",
          content: [
            {
              type: "tableVariableHeader",
              content: [
                {
                  type: "paragraph",
                  content: [
                    {
                      type: "text",
                      text: "变量名称",
                    },
                  ],
                },
              ],
            },
            {
              type: "tableVariableHeader",
              content: [
                {
                  type: "paragraph",
                  content: [
                    {
                      type: "text",
                      text: "变量数值",
                    },
                  ],
                },
              ],
            },
            {
              type: "tableVariableHeader",
              content: [
                {
                  type: "paragraph",
                  content: [
                    {
                      type: "text",
                      text: "描述",
                    },
                  ],
                },
              ],
            },
          ],
        },
        {
          type: "tableVariableRow",
          content: [
            {
              type: "tableVariableCell",
              content: [
                {
                  type: "paragraph",
                  content: [
                    {
                      type: "text",
                      text: "用户姓名",
                    },
                  ],
                },
              ],
            },
            {
              type: "tableVariableCell",
              content: [
                {
                  type: "paragraph",
                  content: [
                    {
                      type: "text",
                      text: "李四",
                    },
                  ],
                },
              ],
            },
            {
              type: "tableVariableCell",
              content: [
                {
                  type: "paragraph",
                  content: [
                    {
                      type: "text",
                      text: "客户真实姓名",
                    },
                  ],
                },
              ],
            },
          ],
        },
      ],
    },
  ],
};

export const Default: SharedVariableStory = {
  args: {
    content: sharedVariableModifiedContent,
    previousContent: sharedVariableBaseContent,
    enableDiff: true,
    className: "min-h-[300px] border rounded-lg p-4",
  },
};

export const ReadOnly: SharedVariableStory = {
  args: {
    content: sharedVariableModifiedContent,
    previousContent: sharedVariableBaseContent,
    enableDiff: true,
    className: "min-h-[300px] border rounded-lg p-4",
  },
};
export const NoPreviousContent: SharedVariableStory = {
  args: {
    content: sharedVariableModifiedContent,
    enableDiff: true,
    className: "min-h-[300px] border rounded-lg p-4",
  },
};
