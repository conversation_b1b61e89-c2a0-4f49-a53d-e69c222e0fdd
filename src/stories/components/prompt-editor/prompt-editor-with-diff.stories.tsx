import {
  ModulePromptEditor,
  VariableProvider,
} from "@/components/common/prompt-editor";
import { Level, VariableType } from "@/types/api/prompt";
import { Variable } from "@/types/api/variable";
import type { Meta, StoryObj } from "@storybook/react";
import { JSONContent } from "@tiptap/react";

// ModulePromptEditor 的故事
const moduleEditorMeta: Meta<typeof ModulePromptEditor> = {
  title: "组件/提示词编辑器/模块编辑器（带对比）",
  component: ModulePromptEditor,
  parameters: {
    layout: "padded",
    docs: {
      description: {
        component: "带有版本对比功能的模块提示词编辑器组件",
      },
    },
  },
  argTypes: {
    content: {
      description: "当前内容",
      control: { type: "object" },
    },
    previousContent: {
      description: "之前版本的内容",
      control: { type: "object" },
    },
    editable: {
      description: "是否可编辑",
      control: { type: "boolean" },
    },
    enableDiff: {
      description: "是否启用 diff 模式",
      control: { type: "boolean" },
    },
    className: {
      description: "自定义样式类名",
      control: { type: "text" },
    },
  },
};

export default moduleEditorMeta;
type ModuleStory = StoryObj<typeof ModulePromptEditor>;

// ModuleDoc 基础内容（符合 ModuleDoc schema: content: "prompt+"）
const moduleBaseContent: JSONContent = {
  type: "doc",
  content: [
    {
      type: "prompt",
      content: [
        {
          type: "paragraph",
          content: [
            {
              type: "text",
              text: "你是一个专业的客服助手，请帮助用户解决问题。",
            },
          ],
        },
      ],
    },
  ],
};

// ModuleDoc 修改后的内容
const moduleModifiedContent: JSONContent = {
  type: "doc",
  content: [
    {
      type: "prompt",
      content: [
        {
          type: "paragraph",
          content: [
            {
              type: "text",
              text: "你是一个专业的AI客服助手，请耐心帮助用户解决各种问题。",
            },
          ],
        },
      ],
    },
  ],
};

// 复杂内容示例（符合 ModuleDoc schema）
const complexPreviousContent: JSONContent = {
  type: "doc",
  content: [
    {
      type: "prompt",
      content: [
        {
          type: "paragraph",
          content: [
            {
              type: "text",
              text: "欢迎来到我们的在线商店！我是您的购物助手。",
            },
          ],
        },
      ],
    },
    {
      type: "prompt",
      content: [
        {
          type: "paragraph",
          content: [
            {
              type: "text",
              text: "我可以帮助您：",
            },
          ],
        },
      ],
    },
    {
      type: "prompt",
      content: [
        {
          type: "paragraph",
          content: [
            {
              type: "text",
              text: "1. 查找商品信息",
            },
          ],
        },
      ],
    },
    {
      type: "prompt",
      content: [
        {
          type: "paragraph",
          content: [
            {
              type: "text",
              text: "2. 处理订单问题",
            },
          ],
        },
      ],
    },
    {
      type: "prompt",
      content: [
        {
          type: "paragraph",
          content: [
            {
              type: "text",
              text: "3. 解答售后疑问",
            },
          ],
        },
      ],
    },
  ],
};

const complexCurrentContent: JSONContent = {
  type: "doc",
  content: [
    {
      type: "prompt",
      content: [
        {
          type: "paragraph",
          content: [
            {
              type: "text",
              text: "欢迎来到我们的在线商店！我是您专业的购物顾问。",
            },
          ],
        },
      ],
    },
    {
      type: "prompt",
      content: [
        {
          type: "paragraph",
          content: [
            {
              type: "text",
              text: "我可以为您提供以下服务：",
            },
          ],
        },
      ],
    },
    {
      type: "prompt",
      content: [
        {
          type: "paragraph",
          content: [
            {
              type: "text",
              text: "1. 商品推荐和详细信息查询",
            },
          ],
        },
      ],
    },
    {
      type: "prompt",
      content: [
        {
          type: "paragraph",
          content: [
            {
              type: "text",
              text: "2. 订单状态跟踪和处理",
            },
          ],
        },
      ],
    },
    {
      type: "prompt",
      content: [
        {
          type: "paragraph",
          content: [
            {
              type: "text",
              text: "3. 售后服务和退换货指导",
            },
          ],
        },
      ],
    },
    {
      type: "prompt",
      content: [
        {
          type: "paragraph",
          content: [
            {
              type: "text",
              text: "4. 优惠活动和会员权益介绍",
            },
          ],
        },
      ],
    },
  ],
};

export const Default: ModuleStory = {
  args: {
    content: moduleModifiedContent,
    previousContent: moduleBaseContent,
    editable: true,
    className: "min-h-[300px] border rounded-lg p-4",
  },
};

export const ReadOnly: ModuleStory = {
  args: {
    content: moduleModifiedContent,
    previousContent: moduleBaseContent,
    editable: false,
    className: "min-h-[300px] border rounded-lg p-4",
  },
};

export const WithoutDiff: ModuleStory = {
  args: {
    content: moduleModifiedContent,
    previousContent: moduleBaseContent,
    editable: true,
    enableDiff: false,
    className: "min-h-[300px] border rounded-lg p-4",
  },
};

export const NoPreviousContent: ModuleStory = {
  args: {
    content: moduleModifiedContent,
    editable: true,
    className: "min-h-[300px] border rounded-lg p-4",
  },
};

export const ComplexDiff: ModuleStory = {
  args: {
    content: complexCurrentContent,
    previousContent: complexPreviousContent,
    editable: true,
    className: "min-h-[400px] border rounded-lg p-4",
  },
};

export const LargeDiff: ModuleStory = {
  args: {
    content: {
      type: "doc",
      content: [
        {
          type: "prompt",
          content: [
            {
              type: "paragraph",
              content: [
                {
                  type: "text",
                  text: "这是一个包含大量文本的模块，用于测试大型差异对比的性能和显示效果。在这个版本中，我们对原有的内容进行了大幅度的修改和扩展，增加了更多的功能描述和使用说明。新版本不仅保留了原有的核心功能，还新增了许多实用的特性，比如智能推荐、个性化服务、多语言支持等等。",
                },
              ],
            },
          ],
        },
      ],
    },
    previousContent: {
      type: "doc",
      content: [
        {
          type: "prompt",
          content: [
            {
              type: "paragraph",
              content: [
                {
                  type: "text",
                  text: "这是一个简单的模块，包含基本的功能说明。原版本的功能相对简单，主要提供基础的服务支持。",
                },
              ],
            },
          ],
        },
      ],
    },
    editable: true,
    className: "min-h-[400px] border rounded-lg p-4",
  },
};

// 测试用的变量数据
const mockVariables: Variable[] = [
  {
    variableId: "var-1",
    name: "用户名称",
    promptName: "userName",
    level: Level.Product,
    type: VariableType.System,
    definition: "当前用户的姓名",
    example: "张三",
  },
  {
    variableId: "var-2",
    name: "商品名称",
    promptName: "productName",
    level: Level.Product,
    type: VariableType.System,
    definition: "当前商品的名称",
    example: "iPhone 15 Pro",
  },
  {
    variableId: "var-3",
    name: "店铺名称",
    promptName: "shopName",
    level: Level.Shop,
    type: VariableType.Shop,
    definition: "店铺的名称",
    example: "苹果官方旗舰店",
    scopeDetail: '["shop1"]',
  },
];

// 包含 StringVariable 节点的内容（旧版本）
const contentWithVariablesPrevious: JSONContent = {
  type: "doc",
  content: [
    {
      type: "prompt",
      content: [
        {
          type: "paragraph",
          content: [
            {
              type: "text",
              text: "你好，",
            },
            {
              type: "stringVariable",
              attrs: {
                id: "var-1",
              },
            },
            {
              type: "text",
              text: "！欢迎来到我们的店铺。",
            },
          ],
        },
      ],
    },
  ],
};

// 包含 StringVariable 节点的内容（新版本 - 增删了变量）
const contentWithVariablesCurrent: JSONContent = {
  type: "doc",
  content: [
    {
      type: "prompt",
      content: [
        {
          type: "paragraph",
          content: [
            {
              type: "text",
              text: "你好，",
            },
            {
              type: "text",
              text: "！欢迎来到",
            },
            {
              type: "stringVariable",
              attrs: {
                id: "var-3",
              },
            },
            {
              type: "text",
              text: "，我们为您推荐",
            },
            {
              type: "stringVariable",
              attrs: {
                id: "var-2",
              },
            },
            {
              type: "text",
              text: "。",
            },
          ],
        },
      ],
    },
  ],
};

export const StringVariableDiff: ModuleStory = {
  render: (args) => (
    <VariableProvider
      dataSources={[
        {
          variableType: "system",
          data: mockVariables.filter((v) => v.type === VariableType.System),
        },
        {
          variableType: "shop",
          data: mockVariables.filter((v) => v.type === VariableType.Shop),
        },
      ]}
    >
      <ModulePromptEditor {...args} />
    </VariableProvider>
  ),
  args: {
    content: contentWithVariablesCurrent,
    previousContent: contentWithVariablesPrevious,
    editable: true,
    className: "min-h-[300px] border rounded-lg p-4",
  },
};
