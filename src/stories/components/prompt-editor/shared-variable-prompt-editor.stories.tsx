import { SharedVariablePromptEditor } from "@/components/common/prompt-editor";
import { VariableType } from "@/types/api/prompt";
import type { Meta, StoryObj } from "@storybook/react";

const meta = {
  title: "组件/提示词编辑器/共享变量提示词编辑器",
  component: SharedVariablePromptEditor,
  tags: ["autodocs"],
  decorators: [
    (Story) => (
      <div className="p-8">
        <Story />
      </div>
    ),
  ],
} satisfies Meta<typeof SharedVariablePromptEditor>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    content: {
      type: "doc",
      content: [
        {
          type: "tableVariable",
          attrs: {
            name: "意图类型表",
            level: "product",
            scope: "系统",
            variableName: "acts",
            definition: "意图识别的动作类型定义表",
            example: "问候|问候语|无",
            type: VariableType.Shop,
          },
          content: [
            {
              type: "tableVariableRow",
              content: [
                {
                  type: "tableVariableHeader",
                  content: [
                    {
                      type: "paragraph",
                      content: [
                        {
                          type: "text",
                          text: "动作类型",
                        },
                      ],
                    },
                  ],
                },
                {
                  type: "tableVariableHeader",
                  content: [
                    {
                      type: "paragraph",
                      content: [
                        {
                          type: "text",
                          text: "动作描述",
                        },
                      ],
                    },
                  ],
                },
                {
                  type: "tableVariableHeader",
                  content: [
                    {
                      type: "paragraph",
                      content: [
                        {
                          type: "text",
                          text: "动作参数",
                        },
                      ],
                    },
                  ],
                },
              ],
            },
            {
              type: "tableVariableRow",
              content: [
                {
                  type: "tableVariableCell",
                  content: [
                    {
                      type: "paragraph",
                      content: [
                        {
                          type: "text",
                          text: "问候",
                        },
                      ],
                    },
                  ],
                },
                {
                  type: "tableVariableCell",
                  content: [
                    {
                      type: "paragraph",
                      content: [
                        {
                          type: "text",
                          text: "用户问候语，如你好、hi等",
                        },
                      ],
                    },
                  ],
                },
                {
                  type: "tableVariableCell",
                  content: [
                    {
                      type: "paragraph",
                      content: [
                        {
                          type: "text",
                          text: "无",
                        },
                      ],
                    },
                  ],
                },
              ],
            },
          ],
        },
      ],
    },
  },
};
