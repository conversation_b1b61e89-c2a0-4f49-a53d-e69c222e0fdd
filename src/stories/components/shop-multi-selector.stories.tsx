import type { Meta, StoryObj } from "@storybook/react";
import { useState } from "react";
import { ShopMultiSelector } from "@/components/common/shop-multi-selector";

const meta: Meta<typeof ShopMultiSelector> = {
  title: "组件/店铺多选下拉组件",
  component: ShopMultiSelector,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    value: {
      control: { type: "object" },
      description: "选中的店铺ID数组",
    },
    onChange: {
      action: "onChange",
      description: "选择变化时的回调函数",
    },
    placeholder: {
      control: { type: "text" },
      description: "占位符文本",
    },
    disabled: {
      control: { type: "boolean" },
      description: "是否禁用",
    },
    triggerClassName: {
      control: { type: "text" },
      description: "触发器的CSS类名",
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// 基础示例
export const Default: Story = {
  render: (args) => {
    const [selectedShops, setSelectedShops] = useState<string[]>([]);

    return (
      <div className="w-80">
        <ShopMultiSelector
          {...args}
          value={selectedShops}
          onChange={setSelectedShops}
        />
        <div className="mt-4 p-3 bg-gray-50 rounded">
          <p className="text-sm font-medium">已选择的店铺ID:</p>
          <pre className="text-xs mt-1">
            {JSON.stringify(selectedShops, null, 2)}
          </pre>
        </div>
      </div>
    );
  },
  args: {
    placeholder: "请选择店铺",
  },
};

// 预设选中值
export const WithPreselected: Story = {
  render: (args) => {
    const [selectedShops, setSelectedShops] = useState<string[]>([
      "shop1",
      "shop2",
    ]);

    return (
      <div className="w-80">
        <ShopMultiSelector
          {...args}
          value={selectedShops}
          onChange={setSelectedShops}
        />
        <div className="mt-4 p-3 bg-gray-50 rounded">
          <p className="text-sm font-medium">已选择的店铺ID:</p>
          <pre className="text-xs mt-1">
            {JSON.stringify(selectedShops, null, 2)}
          </pre>
        </div>
      </div>
    );
  },
  args: {
    placeholder: "请选择店铺",
  },
};

// 禁用状态
export const Disabled: Story = {
  render: (args) => {
    const [selectedShops, setSelectedShops] = useState<string[]>(["shop1"]);

    return (
      <div className="w-80">
        <ShopMultiSelector
          {...args}
          value={selectedShops}
          onChange={setSelectedShops}
        />
        <div className="mt-4 p-3 bg-gray-50 rounded">
          <p className="text-sm font-medium">已选择的店铺ID:</p>
          <pre className="text-xs mt-1">
            {JSON.stringify(selectedShops, null, 2)}
          </pre>
        </div>
      </div>
    );
  },
  args: {
    placeholder: "请选择店铺",
    disabled: true,
  },
};

// 自定义样式
export const CustomStyle: Story = {
  render: (args) => {
    const [selectedShops, setSelectedShops] = useState<string[]>([]);

    return (
      <div className="w-96">
        <ShopMultiSelector
          {...args}
          value={selectedShops}
          onChange={setSelectedShops}
        />
        <div className="mt-4 p-3 bg-gray-50 rounded">
          <p className="text-sm font-medium">已选择的店铺ID:</p>
          <pre className="text-xs mt-1">
            {JSON.stringify(selectedShops, null, 2)}
          </pre>
        </div>
      </div>
    );
  },
  args: {
    placeholder: "选择您的店铺",
    triggerClassName: "w-full border-2 border-blue-300 focus:border-blue-500",
  },
};

// 多选项显示测试
export const ManySelections: Story = {
  render: (args) => {
    const [selectedShops, setSelectedShops] = useState<string[]>([
      "shop1",
      "shop2",
      "shop3",
      "shop4",
      "shop5",
      "shop6",
    ]);

    return (
      <div className="w-80">
        <ShopMultiSelector
          {...args}
          value={selectedShops}
          onChange={setSelectedShops}
        />
        <div className="mt-4 p-3 bg-gray-50 rounded">
          <p className="text-sm font-medium">测试说明:</p>
          <ul className="text-xs mt-1 space-y-1">
            <li>• 选择超过3个店铺时，只显示前3个</li>
            <li>• 剩余的店铺数量用 +数字 表示</li>
            <li>• 当前选择了 {selectedShops.length} 个店铺</li>
          </ul>
          <p className="text-sm font-medium mt-2">已选择的店铺ID:</p>
          <pre className="text-xs mt-1">
            {JSON.stringify(selectedShops, null, 2)}
          </pre>
        </div>
      </div>
    );
  },
  args: {
    placeholder: "请选择店铺",
  },
};

// 交互式演示
export const Interactive: Story = {
  render: () => {
    const [selectedShops, setSelectedShops] = useState<string[]>([]);
    const [isDisabled, setIsDisabled] = useState(false);

    const handleClear = () => {
      setSelectedShops([]);
    };

    const handleSelectAll = () => {
      // 这里需要根据实际的店铺数据来设置
      setSelectedShops(["shop1", "shop2", "shop3"]);
    };

    return (
      <div className="w-80 space-y-4">
        <ShopMultiSelector
          value={selectedShops}
          onChange={setSelectedShops}
          placeholder="请选择店铺"
          disabled={isDisabled}
        />

        <div className="flex gap-2">
          <button
            onClick={handleClear}
            className="px-3 py-1 text-sm bg-red-500 text-white rounded hover:bg-red-600"
          >
            清空选择
          </button>
          <button
            onClick={handleSelectAll}
            className="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            全选
          </button>
          <button
            onClick={() => setIsDisabled(!isDisabled)}
            className="px-3 py-1 text-sm bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            {isDisabled ? "启用" : "禁用"}
          </button>
        </div>

        <div className="p-3 bg-gray-50 rounded">
          <p className="text-sm font-medium">状态信息:</p>
          <ul className="text-xs mt-1 space-y-1">
            <li>已选择店铺数量: {selectedShops.length}</li>
            <li>是否禁用: {isDisabled ? "是" : "否"}</li>
            <li>选中的店铺ID: {selectedShops.join(", ") || "无"}</li>
          </ul>
        </div>
      </div>
    );
  },
};
