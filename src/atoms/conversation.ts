import { atom } from "jotai";
import { atomWithImmer } from "jotai-immer";
import { atomWithQuery } from "jotai-tanstack-query";
import { atomEffect } from "jotai-effect";
import { getQueryClient } from "@/services/query-client";
import { MessageService } from "@/services/message-service";
import { MessageSource } from "@/types/api/message";
import { Sample } from "@/types/api/effect-samples";

// Types
export type ReplyMessageRole =
  | "all"
  | "vitoCustomer"
  | "customer"
  | "assistant";

// 消息工具类型
export type MessageToolkitAction = {
  type: "view-log";
  data: Sample;
} | null;

// 商品信息类型
export interface ProductInfo {
  code: string;
  title: string;
  [key: string]: any;
}

// Preset info form data (no longer persisted to localStorage)
export type PresetFormData = {
  masterId?: string[];
  orderType?: string;
  orderId?: string[];
  configId?: string;
  promptVersion?: string;
  entryProduct?: ProductInfo;
  orderProduct?: ProductInfo;
};

// 不再使用localStorage持久化存储，改为普通的atom
export const persistedPresetInfoFormAtom = atom<PresetFormData>({});

// 添加assistantAtom和threadAtom用于消息重跑功能
export const assistantAtom = atom<{ currentAssistantId: string | null }>({
  currentAssistantId: null,
});

export const threadAtom = atom<{ currentThreadId: string | null }>({
  currentThreadId: null,
});

// Basic atoms
export const currentThreadIdAtom = atom<string | null>(null);
export const currentMessageIdAtom = atom<string | null>(null);
export const replyMessageRoleAtom = atom<ReplyMessageRole>("all");
export const isRunningAtom = atom<boolean>(false);
export const isRunningConversationIdAtom = atom<string | null>(null);
export const highlightMessageIdAtom = atom<string | null>(null);
export const tableActionAtom = atom<MessageToolkitAction>(null);

// Message data atom with immer for easier updates
export const messageDataAtom = atomWithImmer<{ [key: string]: any }>({});

// Query atoms
export const messagesAtom = atomWithQuery((get) => {
  const threadId = get(currentThreadIdAtom);
  const role = get(replyMessageRoleAtom);

  return {
    queryKey: ["messages", threadId, role],
    queryFn: async () => {
      if (!threadId) return { messageDTOS: [] };

      return await MessageService.getConversation({
        conversationId: threadId,
        replyMessageRole: role as MessageSource,
      });
    },
    enabled: !!threadId,
  };
}, getQueryClient);

export const clearConversationAtom = atom(null, (get, set) => {
  set(currentThreadIdAtom, null);
  set(currentMessageIdAtom, null);
  set(replyMessageRoleAtom, "all");
  set(isRunningAtom, false);
  set(isRunningConversationIdAtom, null);
  set(highlightMessageIdAtom, null);
  set(tableActionAtom, null);
});

// 重置预设信息的atom
export const resetPresetInfoAtom = atom(null, (get, set) => {
  set(persistedPresetInfoFormAtom, {});
});

// 监听店铺切换，重置预设信息
export const shopChangeEffect = atomEffect((get, set) => {
  // 当店铺发生变化时，重置预设信息
  set(resetPresetInfoAtom);
});

// 监听会话切换，重置预设信息
export const threadChangeEffect = atomEffect((get, set) => {
  // 当会话发生变化时，重置预设信息
  set(resetPresetInfoAtom);
});
