/**
 * 根据文件名的扩展名推断MIME类型。
 *
 * @param fileName 需要推断MIME类型的文件名，可以包含路径，但函数只关注文件名的扩展部分。
 * @returns 返回对应于文件扩展名的MIME类型字符串。如果找不到匹配的MIME类型，则返回默认的未知类型 'application/octet-stream'。
 */
export function inferMimeTypeFromExtension(fileName: string): string {
  // 验证输入是否有效
  if (typeof fileName !== "string" || fileName.trim() === "") {
    throw new Error("Invalid file name provided.");
  }

  const extensionToMimeType = new Map([
    // 文本文件
    ["txt", "text/plain"],
    ["csv", "text/csv"],
    ["json", "application/json"],
    ["xml", "application/xml"],
    // 图像文件
    ["jpg", "image/jpeg"],
    ["jpeg", "image/jpeg"],
    ["png", "image/png"],
    ["gif", "image/gif"],
    ["bmp", "image/bmp"],
    // 文档文件
    ["pdf", "application/pdf"],
    ["doc", "application/msword"],
    [
      "docx",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    ],
    ["xls", "application/vnd.ms-excel"],
    [
      "xlsx",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    ],
    // HTML, CSS, JavaScript
    ["html", "text/html"],
    ["css", "text/css"],
    ["js", "application/javascript"],
    // 压缩文件
    ["zip", "application/zip"],
    ["gz", "application/gzip"],
    // 其他
    ["mp3", "audio/mpeg"],
    ["mp4", "video/mp4"],
    ["avi", "video/x-msvideo"],
  ]);

  // 获取文件扩展名，包括点（.）
  const extension = getExtension(fileName).toLowerCase();
  // 查找映射表中对应的MIME类型
  return extensionToMimeType.get(extension) || "application/octet-stream"; // 默认未知类型
}

/**
 * 辅助函数：获取文件名的扩展名。
 * 这个函数将文件名分割成基础名称和扩展名，并返回扩展名部分。
 *
 * @param fileName 需要解析的文件名。
 * @returns 文件名的扩展名，不包括点（.）。
 */
function getExtension(fileName: string) {
  const parts = fileName.split(".");
  return parts.length > 1 ? parts[parts.length - 1] : "";
}
