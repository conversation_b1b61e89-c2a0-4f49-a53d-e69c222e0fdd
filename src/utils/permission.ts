import { routePermissionMap } from "@/constants/routes-permission";
import { RootContext } from "@/router";
import {
  FileRouteTypes,
  ParsedLocation,
  redirect,
} from "@tanstack/react-router";

// 找到匹配的路由模式
function findMatchingRoute(
  pathname: string,
  routes: Partial<Record<FileRouteTypes["to"], string>>,
) {
  // 精确匹配
  if (routes[pathname]) return pathname;

  // 参数路由匹配
  for (const route in routes) {
    // 将路由模式转换为正则表达式
    // 例如: /users/$id/profile => /users/([^/]+)/profile
    const regexPattern = route
      .replace(/\$\w+/g, "([^/]+)")
      .replace(/\//g, "\\/");

    const regex = new RegExp(`^${regexPattern}$`);

    if (regex.test(pathname)) {
      return route;
    }
  }

  return null;
}

export const beforeLoadPermissionValid = (
  context: RootContext,
  location: ParsedLocation,
) => {
  const { hasPermission } = context;
  const matchedRoute = findMatchingRoute(location.pathname, routePermissionMap);

  if (!matchedRoute || !hasPermission(routePermissionMap[matchedRoute])) {
    throw redirect({
      to: "/404",
    });
  }
};
