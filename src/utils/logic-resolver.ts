import dayjs from "dayjs";
import jsonLogic from "json-logic-js";
import { flatten } from "lodash-es";

// 注册自定义操作
jsonLogic.add_operation("parse_json", (value) => {
  try {
    return JSON.parse(value);
  } catch (error) {
    return value;
  }
});

jsonLogic.add_operation("values", (value) => Object.values(value || {}));

jsonLogic.add_operation("keys", (value) => Object.keys(value || {}));

jsonLogic.add_operation("entries", (value) => Object.entries(value || {}));

jsonLogic.add_operation("flatten", flatten);

jsonLogic.add_operation("make_options", (value) =>
  Object.entries(value || {}).map(([label, value]) => ({
    label,
    value,
  })),
);

/**
 * 计算数组数量
 */
jsonLogic.add_operation("array_length", (value) =>
  Array.isArray(value) ? value.length : 0,
);

/**
 * 生成对象的操作符
 * @param value 对象的键值对数组
 * @param data 数据源
 * @returns 生成的对象
 * @example
 * {
 *  "make_object": [
 *    "name",
 *    { "var": "user.name" },
 *    "age",
 *    { "var": "user.age" }
 *  ]
 * }
 */
jsonLogic.add_operation("make_object", (...value: unknown[]) => {
  const result: Record<string, unknown> = {};

  for (let i = 0; i < value.length; i += 2) {
    const key = value[i];
    const val = value[i + 1];

    if (typeof key === "string") {
      result[key] = val;
    }
  }

  return result;
});

/**
 * 格式化时间
 */
jsonLogic.add_operation("format_date", (value, format) => {
  try {
    return dayjs(value).format(format);
  } catch (error) {
    return value;
  }
});

/**
 * 解析 JSON 逻辑表达式并从数据中提取值
 * @param logic 逻辑表达式对象
 * @param data 数据源
 * @returns 解析后的值
 */
export const resolveLogicValue = (logic: any, data: any): any => {
  try {
    const result = jsonLogic.apply(logic, data);

    return result;
  } catch (error) {
    return undefined;
  }
};
