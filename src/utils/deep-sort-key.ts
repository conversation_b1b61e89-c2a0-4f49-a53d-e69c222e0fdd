type DeepSortKeys<T> = T extends object
  ? T extends any[]
    ? T
    : { [K in keyof T]: DeepSortKeys<T[K]> }
  : T;

export const deepSortKeys = <T>(obj: T): DeepSortKeys<T> => {
  if (!obj || typeof obj !== "object" || Array.isArray(obj)) {
    return obj as DeepSortKeys<T>;
  }

  return Object.fromEntries(
    Object.entries(obj)
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([key, value]) => [key, deepSortKeys(value)]),
  ) as DeepSortKeys<T>;
};
