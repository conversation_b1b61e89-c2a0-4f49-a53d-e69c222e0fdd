/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import type { CreateFileRoute, FileRoutesByPath } from '@tanstack/react-router'

import { Route as rootRouteImport } from './routes/__root'
import { Route as AppRouteImport } from './routes/_app'
import { Route as AppIndexRouteImport } from './routes/_app/index'
import { Route as AuthLoginRouteImport } from './routes/_auth/login'
import { Route as AppWorkbenchRouteImport } from './routes/_app/workbench'
import { Route as AppSystemSettingRouteImport } from './routes/_app/system-setting'
import { Route as AppShopRouteImport } from './routes/_app/shop'
import { Route as AppPromptManagementRouteImport } from './routes/_app/prompt-management'
import { Route as AppManagementRouteImport } from './routes/_app/management'
import { Route as AppEffectEvaluationRouteImport } from './routes/_app/effect-evaluation'
import { Route as AppChatRouteImport } from './routes/_app/chat'
import { Route as App404RouteImport } from './routes/_app/404'
import { Route as AppWorkbenchTransferTaskRouteImport } from './routes/_app/workbench/transfer-task'
import { Route as AppSystemSettingFieldConfigureRouteImport } from './routes/_app/system-setting/field-configure'
import { Route as AppSystemSettingAnnotateConfigureRouteImport } from './routes/_app/system-setting/annotate-configure'
import { Route as AppShopVersionManagementRouteImport } from './routes/_app/shop/version-management'
import { Route as AppShopSimulationConversationRouteImport } from './routes/_app/shop/simulation-conversation'
import { Route as AppShopShopConfigRouteImport } from './routes/_app/shop/shop-config'
import { Route as AppShopQualityInspectionRouteImport } from './routes/_app/shop/quality-inspection'
import { Route as AppShopKnowledgeLibraryRouteImport } from './routes/_app/shop/knowledge-library'
import { Route as AppShopGoodLibraryRouteImport } from './routes/_app/shop/good-library'
import { Route as AppShopConversationsRouteImport } from './routes/_app/shop/conversations'
import { Route as AppShopAgentConfigRouteImport } from './routes/_app/shop/agent-config'
import { Route as AppShopActivityLibraryRouteImport } from './routes/_app/shop/activity-library'
import { Route as AppPromptManagementVersionRouteImport } from './routes/_app/prompt-management/version'
import { Route as AppPromptManagementVariableRouteImport } from './routes/_app/prompt-management/variable'
import { Route as AppPromptManagementPromptRouteImport } from './routes/_app/prompt-management/prompt'
import { Route as AppManagementRoleRouteImport } from './routes/_app/management/role'
import { Route as AppManagementPermissionRouteImport } from './routes/_app/management/permission'
import { Route as AppManagementBrandRouteImport } from './routes/_app/management/brand'
import { Route as AppManagementAccountRouteImport } from './routes/_app/management/account'
import { Route as AppEffectEvaluationTuningRouteImport } from './routes/_app/effect-evaluation/tuning'
import { Route as AppEffectEvaluationSamplesRouteImport } from './routes/_app/effect-evaluation/samples'
import { Route as AppEffectEvaluationManagementRouteImport } from './routes/_app/effect-evaluation/management'
import { Route as AppShopQualityInspectionIndexRouteImport } from './routes/_app/shop/quality-inspection/index'
import { Route as AppEffectEvaluationManagementIndexRouteImport } from './routes/_app/effect-evaluation/management/index'
import { Route as AppShopQualityInspectionOverviewRouteImport } from './routes/_app/shop/quality-inspection/overview'
import { Route as AppShopQualityInspectionDetailRouteImport } from './routes/_app/shop/quality-inspection/detail'
import { Route as AppPromptManagementPromptSharedVariableRouteImport } from './routes/_app/prompt-management/prompt/shared-variable'
import { Route as AppPromptManagementPromptModuleRouteImport } from './routes/_app/prompt-management/prompt/module'
import { Route as AppPromptManagementPromptAbilityRouteImport } from './routes/_app/prompt-management/prompt/ability'
import { Route as AppEffectEvaluationTuningIdRouteImport } from './routes/_app/effect-evaluation/tuning/$id'
import { Route as AppEffectEvaluationManagementDetailRouteImport } from './routes/_app/effect-evaluation/management/detail'
import { Route as AppEffectEvaluationManagementContrastResultRouteImport } from './routes/_app/effect-evaluation/management/contrast-result'

const AppRoute = AppRouteImport.update({
  id: '/_app',
  getParentRoute: () => rootRouteImport,
} as any)
const AppIndexRoute = AppIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AppRoute,
} as any)
const AuthLoginRoute = AuthLoginRouteImport.update({
  id: '/_auth/login',
  path: '/login',
  getParentRoute: () => rootRouteImport,
} as any)
const AppWorkbenchRoute = AppWorkbenchRouteImport.update({
  id: '/workbench',
  path: '/workbench',
  getParentRoute: () => AppRoute,
} as any)
const AppSystemSettingRoute = AppSystemSettingRouteImport.update({
  id: '/system-setting',
  path: '/system-setting',
  getParentRoute: () => AppRoute,
} as any)
const AppShopRoute = AppShopRouteImport.update({
  id: '/shop',
  path: '/shop',
  getParentRoute: () => AppRoute,
} as any)
const AppPromptManagementRoute = AppPromptManagementRouteImport.update({
  id: '/prompt-management',
  path: '/prompt-management',
  getParentRoute: () => AppRoute,
} as any)
const AppManagementRoute = AppManagementRouteImport.update({
  id: '/management',
  path: '/management',
  getParentRoute: () => AppRoute,
} as any)
const AppEffectEvaluationRoute = AppEffectEvaluationRouteImport.update({
  id: '/effect-evaluation',
  path: '/effect-evaluation',
  getParentRoute: () => AppRoute,
} as any)
const AppChatRoute = AppChatRouteImport.update({
  id: '/chat',
  path: '/chat',
  getParentRoute: () => AppRoute,
} as any)
const App404Route = App404RouteImport.update({
  id: '/404',
  path: '/404',
  getParentRoute: () => AppRoute,
} as any)
const AppWorkbenchTransferTaskRoute =
  AppWorkbenchTransferTaskRouteImport.update({
    id: '/transfer-task',
    path: '/transfer-task',
    getParentRoute: () => AppWorkbenchRoute,
  } as any)
const AppSystemSettingFieldConfigureRoute =
  AppSystemSettingFieldConfigureRouteImport.update({
    id: '/field-configure',
    path: '/field-configure',
    getParentRoute: () => AppSystemSettingRoute,
  } as any)
const AppSystemSettingAnnotateConfigureRoute =
  AppSystemSettingAnnotateConfigureRouteImport.update({
    id: '/annotate-configure',
    path: '/annotate-configure',
    getParentRoute: () => AppSystemSettingRoute,
  } as any)
const AppShopVersionManagementRoute =
  AppShopVersionManagementRouteImport.update({
    id: '/version-management',
    path: '/version-management',
    getParentRoute: () => AppShopRoute,
  } as any)
const AppShopSimulationConversationRoute =
  AppShopSimulationConversationRouteImport.update({
    id: '/simulation-conversation',
    path: '/simulation-conversation',
    getParentRoute: () => AppShopRoute,
  } as any)
const AppShopShopConfigRoute = AppShopShopConfigRouteImport.update({
  id: '/shop-config',
  path: '/shop-config',
  getParentRoute: () => AppShopRoute,
} as any)
const AppShopQualityInspectionRoute =
  AppShopQualityInspectionRouteImport.update({
    id: '/quality-inspection',
    path: '/quality-inspection',
    getParentRoute: () => AppShopRoute,
  } as any)
const AppShopKnowledgeLibraryRoute = AppShopKnowledgeLibraryRouteImport.update({
  id: '/knowledge-library',
  path: '/knowledge-library',
  getParentRoute: () => AppShopRoute,
} as any)
const AppShopGoodLibraryRoute = AppShopGoodLibraryRouteImport.update({
  id: '/good-library',
  path: '/good-library',
  getParentRoute: () => AppShopRoute,
} as any)
const AppShopConversationsRoute = AppShopConversationsRouteImport.update({
  id: '/conversations',
  path: '/conversations',
  getParentRoute: () => AppShopRoute,
} as any)
const AppShopAgentConfigRoute = AppShopAgentConfigRouteImport.update({
  id: '/agent-config',
  path: '/agent-config',
  getParentRoute: () => AppShopRoute,
} as any)
const AppShopActivityLibraryRoute = AppShopActivityLibraryRouteImport.update({
  id: '/activity-library',
  path: '/activity-library',
  getParentRoute: () => AppShopRoute,
} as any)
const AppPromptManagementVersionRoute =
  AppPromptManagementVersionRouteImport.update({
    id: '/version',
    path: '/version',
    getParentRoute: () => AppPromptManagementRoute,
  } as any)
const AppPromptManagementVariableRoute =
  AppPromptManagementVariableRouteImport.update({
    id: '/variable',
    path: '/variable',
    getParentRoute: () => AppPromptManagementRoute,
  } as any)
const AppPromptManagementPromptRoute =
  AppPromptManagementPromptRouteImport.update({
    id: '/prompt',
    path: '/prompt',
    getParentRoute: () => AppPromptManagementRoute,
  } as any)
const AppManagementRoleRoute = AppManagementRoleRouteImport.update({
  id: '/role',
  path: '/role',
  getParentRoute: () => AppManagementRoute,
} as any)
const AppManagementPermissionRoute = AppManagementPermissionRouteImport.update({
  id: '/permission',
  path: '/permission',
  getParentRoute: () => AppManagementRoute,
} as any)
const AppManagementBrandRoute = AppManagementBrandRouteImport.update({
  id: '/brand',
  path: '/brand',
  getParentRoute: () => AppManagementRoute,
} as any)
const AppManagementAccountRoute = AppManagementAccountRouteImport.update({
  id: '/account',
  path: '/account',
  getParentRoute: () => AppManagementRoute,
} as any)
const AppEffectEvaluationTuningRoute =
  AppEffectEvaluationTuningRouteImport.update({
    id: '/tuning',
    path: '/tuning',
    getParentRoute: () => AppEffectEvaluationRoute,
  } as any)
const AppEffectEvaluationSamplesRoute =
  AppEffectEvaluationSamplesRouteImport.update({
    id: '/samples',
    path: '/samples',
    getParentRoute: () => AppEffectEvaluationRoute,
  } as any)
const AppEffectEvaluationManagementRoute =
  AppEffectEvaluationManagementRouteImport.update({
    id: '/management',
    path: '/management',
    getParentRoute: () => AppEffectEvaluationRoute,
  } as any)
const AppShopQualityInspectionIndexRoute =
  AppShopQualityInspectionIndexRouteImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AppShopQualityInspectionRoute,
  } as any)
const AppEffectEvaluationManagementIndexRoute =
  AppEffectEvaluationManagementIndexRouteImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AppEffectEvaluationManagementRoute,
  } as any)
const AppShopQualityInspectionOverviewRoute =
  AppShopQualityInspectionOverviewRouteImport.update({
    id: '/overview',
    path: '/overview',
    getParentRoute: () => AppShopQualityInspectionRoute,
  } as any)
const AppShopQualityInspectionDetailRoute =
  AppShopQualityInspectionDetailRouteImport.update({
    id: '/detail',
    path: '/detail',
    getParentRoute: () => AppShopQualityInspectionRoute,
  } as any)
const AppPromptManagementPromptSharedVariableRoute =
  AppPromptManagementPromptSharedVariableRouteImport.update({
    id: '/shared-variable',
    path: '/shared-variable',
    getParentRoute: () => AppPromptManagementPromptRoute,
  } as any)
const AppPromptManagementPromptModuleRoute =
  AppPromptManagementPromptModuleRouteImport.update({
    id: '/module',
    path: '/module',
    getParentRoute: () => AppPromptManagementPromptRoute,
  } as any)
const AppPromptManagementPromptAbilityRoute =
  AppPromptManagementPromptAbilityRouteImport.update({
    id: '/ability',
    path: '/ability',
    getParentRoute: () => AppPromptManagementPromptRoute,
  } as any)
const AppEffectEvaluationTuningIdRoute =
  AppEffectEvaluationTuningIdRouteImport.update({
    id: '/$id',
    path: '/$id',
    getParentRoute: () => AppEffectEvaluationTuningRoute,
  } as any)
const AppEffectEvaluationManagementDetailRoute =
  AppEffectEvaluationManagementDetailRouteImport.update({
    id: '/detail',
    path: '/detail',
    getParentRoute: () => AppEffectEvaluationManagementRoute,
  } as any)
const AppEffectEvaluationManagementContrastResultRoute =
  AppEffectEvaluationManagementContrastResultRouteImport.update({
    id: '/contrast-result',
    path: '/contrast-result',
    getParentRoute: () => AppEffectEvaluationManagementRoute,
  } as any)

export interface FileRoutesByFullPath {
  '': typeof AppRouteWithChildren
  '/404': typeof App404Route
  '/chat': typeof AppChatRoute
  '/effect-evaluation': typeof AppEffectEvaluationRouteWithChildren
  '/management': typeof AppManagementRouteWithChildren
  '/prompt-management': typeof AppPromptManagementRouteWithChildren
  '/shop': typeof AppShopRouteWithChildren
  '/system-setting': typeof AppSystemSettingRouteWithChildren
  '/workbench': typeof AppWorkbenchRouteWithChildren
  '/login': typeof AuthLoginRoute
  '/': typeof AppIndexRoute
  '/effect-evaluation/management': typeof AppEffectEvaluationManagementRouteWithChildren
  '/effect-evaluation/samples': typeof AppEffectEvaluationSamplesRoute
  '/effect-evaluation/tuning': typeof AppEffectEvaluationTuningRouteWithChildren
  '/management/account': typeof AppManagementAccountRoute
  '/management/brand': typeof AppManagementBrandRoute
  '/management/permission': typeof AppManagementPermissionRoute
  '/management/role': typeof AppManagementRoleRoute
  '/prompt-management/prompt': typeof AppPromptManagementPromptRouteWithChildren
  '/prompt-management/variable': typeof AppPromptManagementVariableRoute
  '/prompt-management/version': typeof AppPromptManagementVersionRoute
  '/shop/activity-library': typeof AppShopActivityLibraryRoute
  '/shop/agent-config': typeof AppShopAgentConfigRoute
  '/shop/conversations': typeof AppShopConversationsRoute
  '/shop/good-library': typeof AppShopGoodLibraryRoute
  '/shop/knowledge-library': typeof AppShopKnowledgeLibraryRoute
  '/shop/quality-inspection': typeof AppShopQualityInspectionRouteWithChildren
  '/shop/shop-config': typeof AppShopShopConfigRoute
  '/shop/simulation-conversation': typeof AppShopSimulationConversationRoute
  '/shop/version-management': typeof AppShopVersionManagementRoute
  '/system-setting/annotate-configure': typeof AppSystemSettingAnnotateConfigureRoute
  '/system-setting/field-configure': typeof AppSystemSettingFieldConfigureRoute
  '/workbench/transfer-task': typeof AppWorkbenchTransferTaskRoute
  '/effect-evaluation/management/contrast-result': typeof AppEffectEvaluationManagementContrastResultRoute
  '/effect-evaluation/management/detail': typeof AppEffectEvaluationManagementDetailRoute
  '/effect-evaluation/tuning/$id': typeof AppEffectEvaluationTuningIdRoute
  '/prompt-management/prompt/ability': typeof AppPromptManagementPromptAbilityRoute
  '/prompt-management/prompt/module': typeof AppPromptManagementPromptModuleRoute
  '/prompt-management/prompt/shared-variable': typeof AppPromptManagementPromptSharedVariableRoute
  '/shop/quality-inspection/detail': typeof AppShopQualityInspectionDetailRoute
  '/shop/quality-inspection/overview': typeof AppShopQualityInspectionOverviewRoute
  '/effect-evaluation/management/': typeof AppEffectEvaluationManagementIndexRoute
  '/shop/quality-inspection/': typeof AppShopQualityInspectionIndexRoute
}
export interface FileRoutesByTo {
  '/404': typeof App404Route
  '/chat': typeof AppChatRoute
  '/effect-evaluation': typeof AppEffectEvaluationRouteWithChildren
  '/management': typeof AppManagementRouteWithChildren
  '/prompt-management': typeof AppPromptManagementRouteWithChildren
  '/shop': typeof AppShopRouteWithChildren
  '/system-setting': typeof AppSystemSettingRouteWithChildren
  '/workbench': typeof AppWorkbenchRouteWithChildren
  '/login': typeof AuthLoginRoute
  '/': typeof AppIndexRoute
  '/effect-evaluation/samples': typeof AppEffectEvaluationSamplesRoute
  '/effect-evaluation/tuning': typeof AppEffectEvaluationTuningRouteWithChildren
  '/management/account': typeof AppManagementAccountRoute
  '/management/brand': typeof AppManagementBrandRoute
  '/management/permission': typeof AppManagementPermissionRoute
  '/management/role': typeof AppManagementRoleRoute
  '/prompt-management/prompt': typeof AppPromptManagementPromptRouteWithChildren
  '/prompt-management/variable': typeof AppPromptManagementVariableRoute
  '/prompt-management/version': typeof AppPromptManagementVersionRoute
  '/shop/activity-library': typeof AppShopActivityLibraryRoute
  '/shop/agent-config': typeof AppShopAgentConfigRoute
  '/shop/conversations': typeof AppShopConversationsRoute
  '/shop/good-library': typeof AppShopGoodLibraryRoute
  '/shop/knowledge-library': typeof AppShopKnowledgeLibraryRoute
  '/shop/shop-config': typeof AppShopShopConfigRoute
  '/shop/simulation-conversation': typeof AppShopSimulationConversationRoute
  '/shop/version-management': typeof AppShopVersionManagementRoute
  '/system-setting/annotate-configure': typeof AppSystemSettingAnnotateConfigureRoute
  '/system-setting/field-configure': typeof AppSystemSettingFieldConfigureRoute
  '/workbench/transfer-task': typeof AppWorkbenchTransferTaskRoute
  '/effect-evaluation/management/contrast-result': typeof AppEffectEvaluationManagementContrastResultRoute
  '/effect-evaluation/management/detail': typeof AppEffectEvaluationManagementDetailRoute
  '/effect-evaluation/tuning/$id': typeof AppEffectEvaluationTuningIdRoute
  '/prompt-management/prompt/ability': typeof AppPromptManagementPromptAbilityRoute
  '/prompt-management/prompt/module': typeof AppPromptManagementPromptModuleRoute
  '/prompt-management/prompt/shared-variable': typeof AppPromptManagementPromptSharedVariableRoute
  '/shop/quality-inspection/detail': typeof AppShopQualityInspectionDetailRoute
  '/shop/quality-inspection/overview': typeof AppShopQualityInspectionOverviewRoute
  '/effect-evaluation/management': typeof AppEffectEvaluationManagementIndexRoute
  '/shop/quality-inspection': typeof AppShopQualityInspectionIndexRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/_app': typeof AppRouteWithChildren
  '/_app/404': typeof App404Route
  '/_app/chat': typeof AppChatRoute
  '/_app/effect-evaluation': typeof AppEffectEvaluationRouteWithChildren
  '/_app/management': typeof AppManagementRouteWithChildren
  '/_app/prompt-management': typeof AppPromptManagementRouteWithChildren
  '/_app/shop': typeof AppShopRouteWithChildren
  '/_app/system-setting': typeof AppSystemSettingRouteWithChildren
  '/_app/workbench': typeof AppWorkbenchRouteWithChildren
  '/_auth/login': typeof AuthLoginRoute
  '/_app/': typeof AppIndexRoute
  '/_app/effect-evaluation/management': typeof AppEffectEvaluationManagementRouteWithChildren
  '/_app/effect-evaluation/samples': typeof AppEffectEvaluationSamplesRoute
  '/_app/effect-evaluation/tuning': typeof AppEffectEvaluationTuningRouteWithChildren
  '/_app/management/account': typeof AppManagementAccountRoute
  '/_app/management/brand': typeof AppManagementBrandRoute
  '/_app/management/permission': typeof AppManagementPermissionRoute
  '/_app/management/role': typeof AppManagementRoleRoute
  '/_app/prompt-management/prompt': typeof AppPromptManagementPromptRouteWithChildren
  '/_app/prompt-management/variable': typeof AppPromptManagementVariableRoute
  '/_app/prompt-management/version': typeof AppPromptManagementVersionRoute
  '/_app/shop/activity-library': typeof AppShopActivityLibraryRoute
  '/_app/shop/agent-config': typeof AppShopAgentConfigRoute
  '/_app/shop/conversations': typeof AppShopConversationsRoute
  '/_app/shop/good-library': typeof AppShopGoodLibraryRoute
  '/_app/shop/knowledge-library': typeof AppShopKnowledgeLibraryRoute
  '/_app/shop/quality-inspection': typeof AppShopQualityInspectionRouteWithChildren
  '/_app/shop/shop-config': typeof AppShopShopConfigRoute
  '/_app/shop/simulation-conversation': typeof AppShopSimulationConversationRoute
  '/_app/shop/version-management': typeof AppShopVersionManagementRoute
  '/_app/system-setting/annotate-configure': typeof AppSystemSettingAnnotateConfigureRoute
  '/_app/system-setting/field-configure': typeof AppSystemSettingFieldConfigureRoute
  '/_app/workbench/transfer-task': typeof AppWorkbenchTransferTaskRoute
  '/_app/effect-evaluation/management/contrast-result': typeof AppEffectEvaluationManagementContrastResultRoute
  '/_app/effect-evaluation/management/detail': typeof AppEffectEvaluationManagementDetailRoute
  '/_app/effect-evaluation/tuning/$id': typeof AppEffectEvaluationTuningIdRoute
  '/_app/prompt-management/prompt/ability': typeof AppPromptManagementPromptAbilityRoute
  '/_app/prompt-management/prompt/module': typeof AppPromptManagementPromptModuleRoute
  '/_app/prompt-management/prompt/shared-variable': typeof AppPromptManagementPromptSharedVariableRoute
  '/_app/shop/quality-inspection/detail': typeof AppShopQualityInspectionDetailRoute
  '/_app/shop/quality-inspection/overview': typeof AppShopQualityInspectionOverviewRoute
  '/_app/effect-evaluation/management/': typeof AppEffectEvaluationManagementIndexRoute
  '/_app/shop/quality-inspection/': typeof AppShopQualityInspectionIndexRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | ''
    | '/404'
    | '/chat'
    | '/effect-evaluation'
    | '/management'
    | '/prompt-management'
    | '/shop'
    | '/system-setting'
    | '/workbench'
    | '/login'
    | '/'
    | '/effect-evaluation/management'
    | '/effect-evaluation/samples'
    | '/effect-evaluation/tuning'
    | '/management/account'
    | '/management/brand'
    | '/management/permission'
    | '/management/role'
    | '/prompt-management/prompt'
    | '/prompt-management/variable'
    | '/prompt-management/version'
    | '/shop/activity-library'
    | '/shop/agent-config'
    | '/shop/conversations'
    | '/shop/good-library'
    | '/shop/knowledge-library'
    | '/shop/quality-inspection'
    | '/shop/shop-config'
    | '/shop/simulation-conversation'
    | '/shop/version-management'
    | '/system-setting/annotate-configure'
    | '/system-setting/field-configure'
    | '/workbench/transfer-task'
    | '/effect-evaluation/management/contrast-result'
    | '/effect-evaluation/management/detail'
    | '/effect-evaluation/tuning/$id'
    | '/prompt-management/prompt/ability'
    | '/prompt-management/prompt/module'
    | '/prompt-management/prompt/shared-variable'
    | '/shop/quality-inspection/detail'
    | '/shop/quality-inspection/overview'
    | '/effect-evaluation/management/'
    | '/shop/quality-inspection/'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/404'
    | '/chat'
    | '/effect-evaluation'
    | '/management'
    | '/prompt-management'
    | '/shop'
    | '/system-setting'
    | '/workbench'
    | '/login'
    | '/'
    | '/effect-evaluation/samples'
    | '/effect-evaluation/tuning'
    | '/management/account'
    | '/management/brand'
    | '/management/permission'
    | '/management/role'
    | '/prompt-management/prompt'
    | '/prompt-management/variable'
    | '/prompt-management/version'
    | '/shop/activity-library'
    | '/shop/agent-config'
    | '/shop/conversations'
    | '/shop/good-library'
    | '/shop/knowledge-library'
    | '/shop/shop-config'
    | '/shop/simulation-conversation'
    | '/shop/version-management'
    | '/system-setting/annotate-configure'
    | '/system-setting/field-configure'
    | '/workbench/transfer-task'
    | '/effect-evaluation/management/contrast-result'
    | '/effect-evaluation/management/detail'
    | '/effect-evaluation/tuning/$id'
    | '/prompt-management/prompt/ability'
    | '/prompt-management/prompt/module'
    | '/prompt-management/prompt/shared-variable'
    | '/shop/quality-inspection/detail'
    | '/shop/quality-inspection/overview'
    | '/effect-evaluation/management'
    | '/shop/quality-inspection'
  id:
    | '__root__'
    | '/_app'
    | '/_app/404'
    | '/_app/chat'
    | '/_app/effect-evaluation'
    | '/_app/management'
    | '/_app/prompt-management'
    | '/_app/shop'
    | '/_app/system-setting'
    | '/_app/workbench'
    | '/_auth/login'
    | '/_app/'
    | '/_app/effect-evaluation/management'
    | '/_app/effect-evaluation/samples'
    | '/_app/effect-evaluation/tuning'
    | '/_app/management/account'
    | '/_app/management/brand'
    | '/_app/management/permission'
    | '/_app/management/role'
    | '/_app/prompt-management/prompt'
    | '/_app/prompt-management/variable'
    | '/_app/prompt-management/version'
    | '/_app/shop/activity-library'
    | '/_app/shop/agent-config'
    | '/_app/shop/conversations'
    | '/_app/shop/good-library'
    | '/_app/shop/knowledge-library'
    | '/_app/shop/quality-inspection'
    | '/_app/shop/shop-config'
    | '/_app/shop/simulation-conversation'
    | '/_app/shop/version-management'
    | '/_app/system-setting/annotate-configure'
    | '/_app/system-setting/field-configure'
    | '/_app/workbench/transfer-task'
    | '/_app/effect-evaluation/management/contrast-result'
    | '/_app/effect-evaluation/management/detail'
    | '/_app/effect-evaluation/tuning/$id'
    | '/_app/prompt-management/prompt/ability'
    | '/_app/prompt-management/prompt/module'
    | '/_app/prompt-management/prompt/shared-variable'
    | '/_app/shop/quality-inspection/detail'
    | '/_app/shop/quality-inspection/overview'
    | '/_app/effect-evaluation/management/'
    | '/_app/shop/quality-inspection/'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  AppRoute: typeof AppRouteWithChildren
  AuthLoginRoute: typeof AuthLoginRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/_app': {
      id: '/_app'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AppRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_app/404': {
      id: '/_app/404'
      path: '/404'
      fullPath: '/404'
      preLoaderRoute: typeof App404RouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/chat': {
      id: '/_app/chat'
      path: '/chat'
      fullPath: '/chat'
      preLoaderRoute: typeof AppChatRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/effect-evaluation': {
      id: '/_app/effect-evaluation'
      path: '/effect-evaluation'
      fullPath: '/effect-evaluation'
      preLoaderRoute: typeof AppEffectEvaluationRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/management': {
      id: '/_app/management'
      path: '/management'
      fullPath: '/management'
      preLoaderRoute: typeof AppManagementRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/prompt-management': {
      id: '/_app/prompt-management'
      path: '/prompt-management'
      fullPath: '/prompt-management'
      preLoaderRoute: typeof AppPromptManagementRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/shop': {
      id: '/_app/shop'
      path: '/shop'
      fullPath: '/shop'
      preLoaderRoute: typeof AppShopRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/system-setting': {
      id: '/_app/system-setting'
      path: '/system-setting'
      fullPath: '/system-setting'
      preLoaderRoute: typeof AppSystemSettingRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/workbench': {
      id: '/_app/workbench'
      path: '/workbench'
      fullPath: '/workbench'
      preLoaderRoute: typeof AppWorkbenchRouteImport
      parentRoute: typeof AppRoute
    }
    '/_auth/login': {
      id: '/_auth/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof AuthLoginRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_app/': {
      id: '/_app/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof AppIndexRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/effect-evaluation/management': {
      id: '/_app/effect-evaluation/management'
      path: '/management'
      fullPath: '/effect-evaluation/management'
      preLoaderRoute: typeof AppEffectEvaluationManagementRouteImport
      parentRoute: typeof AppEffectEvaluationRoute
    }
    '/_app/effect-evaluation/samples': {
      id: '/_app/effect-evaluation/samples'
      path: '/samples'
      fullPath: '/effect-evaluation/samples'
      preLoaderRoute: typeof AppEffectEvaluationSamplesRouteImport
      parentRoute: typeof AppEffectEvaluationRoute
    }
    '/_app/effect-evaluation/tuning': {
      id: '/_app/effect-evaluation/tuning'
      path: '/tuning'
      fullPath: '/effect-evaluation/tuning'
      preLoaderRoute: typeof AppEffectEvaluationTuningRouteImport
      parentRoute: typeof AppEffectEvaluationRoute
    }
    '/_app/management/account': {
      id: '/_app/management/account'
      path: '/account'
      fullPath: '/management/account'
      preLoaderRoute: typeof AppManagementAccountRouteImport
      parentRoute: typeof AppManagementRoute
    }
    '/_app/management/brand': {
      id: '/_app/management/brand'
      path: '/brand'
      fullPath: '/management/brand'
      preLoaderRoute: typeof AppManagementBrandRouteImport
      parentRoute: typeof AppManagementRoute
    }
    '/_app/management/permission': {
      id: '/_app/management/permission'
      path: '/permission'
      fullPath: '/management/permission'
      preLoaderRoute: typeof AppManagementPermissionRouteImport
      parentRoute: typeof AppManagementRoute
    }
    '/_app/management/role': {
      id: '/_app/management/role'
      path: '/role'
      fullPath: '/management/role'
      preLoaderRoute: typeof AppManagementRoleRouteImport
      parentRoute: typeof AppManagementRoute
    }
    '/_app/prompt-management/prompt': {
      id: '/_app/prompt-management/prompt'
      path: '/prompt'
      fullPath: '/prompt-management/prompt'
      preLoaderRoute: typeof AppPromptManagementPromptRouteImport
      parentRoute: typeof AppPromptManagementRoute
    }
    '/_app/prompt-management/variable': {
      id: '/_app/prompt-management/variable'
      path: '/variable'
      fullPath: '/prompt-management/variable'
      preLoaderRoute: typeof AppPromptManagementVariableRouteImport
      parentRoute: typeof AppPromptManagementRoute
    }
    '/_app/prompt-management/version': {
      id: '/_app/prompt-management/version'
      path: '/version'
      fullPath: '/prompt-management/version'
      preLoaderRoute: typeof AppPromptManagementVersionRouteImport
      parentRoute: typeof AppPromptManagementRoute
    }
    '/_app/shop/activity-library': {
      id: '/_app/shop/activity-library'
      path: '/activity-library'
      fullPath: '/shop/activity-library'
      preLoaderRoute: typeof AppShopActivityLibraryRouteImport
      parentRoute: typeof AppShopRoute
    }
    '/_app/shop/agent-config': {
      id: '/_app/shop/agent-config'
      path: '/agent-config'
      fullPath: '/shop/agent-config'
      preLoaderRoute: typeof AppShopAgentConfigRouteImport
      parentRoute: typeof AppShopRoute
    }
    '/_app/shop/conversations': {
      id: '/_app/shop/conversations'
      path: '/conversations'
      fullPath: '/shop/conversations'
      preLoaderRoute: typeof AppShopConversationsRouteImport
      parentRoute: typeof AppShopRoute
    }
    '/_app/shop/good-library': {
      id: '/_app/shop/good-library'
      path: '/good-library'
      fullPath: '/shop/good-library'
      preLoaderRoute: typeof AppShopGoodLibraryRouteImport
      parentRoute: typeof AppShopRoute
    }
    '/_app/shop/knowledge-library': {
      id: '/_app/shop/knowledge-library'
      path: '/knowledge-library'
      fullPath: '/shop/knowledge-library'
      preLoaderRoute: typeof AppShopKnowledgeLibraryRouteImport
      parentRoute: typeof AppShopRoute
    }
    '/_app/shop/quality-inspection': {
      id: '/_app/shop/quality-inspection'
      path: '/quality-inspection'
      fullPath: '/shop/quality-inspection'
      preLoaderRoute: typeof AppShopQualityInspectionRouteImport
      parentRoute: typeof AppShopRoute
    }
    '/_app/shop/shop-config': {
      id: '/_app/shop/shop-config'
      path: '/shop-config'
      fullPath: '/shop/shop-config'
      preLoaderRoute: typeof AppShopShopConfigRouteImport
      parentRoute: typeof AppShopRoute
    }
    '/_app/shop/simulation-conversation': {
      id: '/_app/shop/simulation-conversation'
      path: '/simulation-conversation'
      fullPath: '/shop/simulation-conversation'
      preLoaderRoute: typeof AppShopSimulationConversationRouteImport
      parentRoute: typeof AppShopRoute
    }
    '/_app/shop/version-management': {
      id: '/_app/shop/version-management'
      path: '/version-management'
      fullPath: '/shop/version-management'
      preLoaderRoute: typeof AppShopVersionManagementRouteImport
      parentRoute: typeof AppShopRoute
    }
    '/_app/system-setting/annotate-configure': {
      id: '/_app/system-setting/annotate-configure'
      path: '/annotate-configure'
      fullPath: '/system-setting/annotate-configure'
      preLoaderRoute: typeof AppSystemSettingAnnotateConfigureRouteImport
      parentRoute: typeof AppSystemSettingRoute
    }
    '/_app/system-setting/field-configure': {
      id: '/_app/system-setting/field-configure'
      path: '/field-configure'
      fullPath: '/system-setting/field-configure'
      preLoaderRoute: typeof AppSystemSettingFieldConfigureRouteImport
      parentRoute: typeof AppSystemSettingRoute
    }
    '/_app/workbench/transfer-task': {
      id: '/_app/workbench/transfer-task'
      path: '/transfer-task'
      fullPath: '/workbench/transfer-task'
      preLoaderRoute: typeof AppWorkbenchTransferTaskRouteImport
      parentRoute: typeof AppWorkbenchRoute
    }
    '/_app/effect-evaluation/management/contrast-result': {
      id: '/_app/effect-evaluation/management/contrast-result'
      path: '/contrast-result'
      fullPath: '/effect-evaluation/management/contrast-result'
      preLoaderRoute: typeof AppEffectEvaluationManagementContrastResultRouteImport
      parentRoute: typeof AppEffectEvaluationManagementRoute
    }
    '/_app/effect-evaluation/management/detail': {
      id: '/_app/effect-evaluation/management/detail'
      path: '/detail'
      fullPath: '/effect-evaluation/management/detail'
      preLoaderRoute: typeof AppEffectEvaluationManagementDetailRouteImport
      parentRoute: typeof AppEffectEvaluationManagementRoute
    }
    '/_app/effect-evaluation/tuning/$id': {
      id: '/_app/effect-evaluation/tuning/$id'
      path: '/$id'
      fullPath: '/effect-evaluation/tuning/$id'
      preLoaderRoute: typeof AppEffectEvaluationTuningIdRouteImport
      parentRoute: typeof AppEffectEvaluationTuningRoute
    }
    '/_app/prompt-management/prompt/ability': {
      id: '/_app/prompt-management/prompt/ability'
      path: '/ability'
      fullPath: '/prompt-management/prompt/ability'
      preLoaderRoute: typeof AppPromptManagementPromptAbilityRouteImport
      parentRoute: typeof AppPromptManagementPromptRoute
    }
    '/_app/prompt-management/prompt/module': {
      id: '/_app/prompt-management/prompt/module'
      path: '/module'
      fullPath: '/prompt-management/prompt/module'
      preLoaderRoute: typeof AppPromptManagementPromptModuleRouteImport
      parentRoute: typeof AppPromptManagementPromptRoute
    }
    '/_app/prompt-management/prompt/shared-variable': {
      id: '/_app/prompt-management/prompt/shared-variable'
      path: '/shared-variable'
      fullPath: '/prompt-management/prompt/shared-variable'
      preLoaderRoute: typeof AppPromptManagementPromptSharedVariableRouteImport
      parentRoute: typeof AppPromptManagementPromptRoute
    }
    '/_app/shop/quality-inspection/detail': {
      id: '/_app/shop/quality-inspection/detail'
      path: '/detail'
      fullPath: '/shop/quality-inspection/detail'
      preLoaderRoute: typeof AppShopQualityInspectionDetailRouteImport
      parentRoute: typeof AppShopQualityInspectionRoute
    }
    '/_app/shop/quality-inspection/overview': {
      id: '/_app/shop/quality-inspection/overview'
      path: '/overview'
      fullPath: '/shop/quality-inspection/overview'
      preLoaderRoute: typeof AppShopQualityInspectionOverviewRouteImport
      parentRoute: typeof AppShopQualityInspectionRoute
    }
    '/_app/effect-evaluation/management/': {
      id: '/_app/effect-evaluation/management/'
      path: '/'
      fullPath: '/effect-evaluation/management/'
      preLoaderRoute: typeof AppEffectEvaluationManagementIndexRouteImport
      parentRoute: typeof AppEffectEvaluationManagementRoute
    }
    '/_app/shop/quality-inspection/': {
      id: '/_app/shop/quality-inspection/'
      path: '/'
      fullPath: '/shop/quality-inspection/'
      preLoaderRoute: typeof AppShopQualityInspectionIndexRouteImport
      parentRoute: typeof AppShopQualityInspectionRoute
    }
  }
}

declare module './routes/_app' {
  const createFileRoute: CreateFileRoute<
    '/_app',
    FileRoutesByPath['/_app']['parentRoute'],
    FileRoutesByPath['/_app']['id'],
    FileRoutesByPath['/_app']['path'],
    FileRoutesByPath['/_app']['fullPath']
  >
}
declare module './routes/_app/404' {
  const createFileRoute: CreateFileRoute<
    '/_app/404',
    FileRoutesByPath['/_app/404']['parentRoute'],
    FileRoutesByPath['/_app/404']['id'],
    FileRoutesByPath['/_app/404']['path'],
    FileRoutesByPath['/_app/404']['fullPath']
  >
}
declare module './routes/_app/chat' {
  const createFileRoute: CreateFileRoute<
    '/_app/chat',
    FileRoutesByPath['/_app/chat']['parentRoute'],
    FileRoutesByPath['/_app/chat']['id'],
    FileRoutesByPath['/_app/chat']['path'],
    FileRoutesByPath['/_app/chat']['fullPath']
  >
}
declare module './routes/_app/effect-evaluation' {
  const createFileRoute: CreateFileRoute<
    '/_app/effect-evaluation',
    FileRoutesByPath['/_app/effect-evaluation']['parentRoute'],
    FileRoutesByPath['/_app/effect-evaluation']['id'],
    FileRoutesByPath['/_app/effect-evaluation']['path'],
    FileRoutesByPath['/_app/effect-evaluation']['fullPath']
  >
}
declare module './routes/_app/management' {
  const createFileRoute: CreateFileRoute<
    '/_app/management',
    FileRoutesByPath['/_app/management']['parentRoute'],
    FileRoutesByPath['/_app/management']['id'],
    FileRoutesByPath['/_app/management']['path'],
    FileRoutesByPath['/_app/management']['fullPath']
  >
}
declare module './routes/_app/prompt-management' {
  const createFileRoute: CreateFileRoute<
    '/_app/prompt-management',
    FileRoutesByPath['/_app/prompt-management']['parentRoute'],
    FileRoutesByPath['/_app/prompt-management']['id'],
    FileRoutesByPath['/_app/prompt-management']['path'],
    FileRoutesByPath['/_app/prompt-management']['fullPath']
  >
}
declare module './routes/_app/shop' {
  const createFileRoute: CreateFileRoute<
    '/_app/shop',
    FileRoutesByPath['/_app/shop']['parentRoute'],
    FileRoutesByPath['/_app/shop']['id'],
    FileRoutesByPath['/_app/shop']['path'],
    FileRoutesByPath['/_app/shop']['fullPath']
  >
}
declare module './routes/_app/system-setting' {
  const createFileRoute: CreateFileRoute<
    '/_app/system-setting',
    FileRoutesByPath['/_app/system-setting']['parentRoute'],
    FileRoutesByPath['/_app/system-setting']['id'],
    FileRoutesByPath['/_app/system-setting']['path'],
    FileRoutesByPath['/_app/system-setting']['fullPath']
  >
}
declare module './routes/_app/workbench' {
  const createFileRoute: CreateFileRoute<
    '/_app/workbench',
    FileRoutesByPath['/_app/workbench']['parentRoute'],
    FileRoutesByPath['/_app/workbench']['id'],
    FileRoutesByPath['/_app/workbench']['path'],
    FileRoutesByPath['/_app/workbench']['fullPath']
  >
}
declare module './routes/_auth/login' {
  const createFileRoute: CreateFileRoute<
    '/_auth/login',
    FileRoutesByPath['/_auth/login']['parentRoute'],
    FileRoutesByPath['/_auth/login']['id'],
    FileRoutesByPath['/_auth/login']['path'],
    FileRoutesByPath['/_auth/login']['fullPath']
  >
}
declare module './routes/_app/index' {
  const createFileRoute: CreateFileRoute<
    '/_app/',
    FileRoutesByPath['/_app/']['parentRoute'],
    FileRoutesByPath['/_app/']['id'],
    FileRoutesByPath['/_app/']['path'],
    FileRoutesByPath['/_app/']['fullPath']
  >
}
declare module './routes/_app/effect-evaluation/management' {
  const createFileRoute: CreateFileRoute<
    '/_app/effect-evaluation/management',
    FileRoutesByPath['/_app/effect-evaluation/management']['parentRoute'],
    FileRoutesByPath['/_app/effect-evaluation/management']['id'],
    FileRoutesByPath['/_app/effect-evaluation/management']['path'],
    FileRoutesByPath['/_app/effect-evaluation/management']['fullPath']
  >
}
declare module './routes/_app/effect-evaluation/samples' {
  const createFileRoute: CreateFileRoute<
    '/_app/effect-evaluation/samples',
    FileRoutesByPath['/_app/effect-evaluation/samples']['parentRoute'],
    FileRoutesByPath['/_app/effect-evaluation/samples']['id'],
    FileRoutesByPath['/_app/effect-evaluation/samples']['path'],
    FileRoutesByPath['/_app/effect-evaluation/samples']['fullPath']
  >
}
declare module './routes/_app/effect-evaluation/tuning' {
  const createFileRoute: CreateFileRoute<
    '/_app/effect-evaluation/tuning',
    FileRoutesByPath['/_app/effect-evaluation/tuning']['parentRoute'],
    FileRoutesByPath['/_app/effect-evaluation/tuning']['id'],
    FileRoutesByPath['/_app/effect-evaluation/tuning']['path'],
    FileRoutesByPath['/_app/effect-evaluation/tuning']['fullPath']
  >
}
declare module './routes/_app/management/account' {
  const createFileRoute: CreateFileRoute<
    '/_app/management/account',
    FileRoutesByPath['/_app/management/account']['parentRoute'],
    FileRoutesByPath['/_app/management/account']['id'],
    FileRoutesByPath['/_app/management/account']['path'],
    FileRoutesByPath['/_app/management/account']['fullPath']
  >
}
declare module './routes/_app/management/brand' {
  const createFileRoute: CreateFileRoute<
    '/_app/management/brand',
    FileRoutesByPath['/_app/management/brand']['parentRoute'],
    FileRoutesByPath['/_app/management/brand']['id'],
    FileRoutesByPath['/_app/management/brand']['path'],
    FileRoutesByPath['/_app/management/brand']['fullPath']
  >
}
declare module './routes/_app/management/permission' {
  const createFileRoute: CreateFileRoute<
    '/_app/management/permission',
    FileRoutesByPath['/_app/management/permission']['parentRoute'],
    FileRoutesByPath['/_app/management/permission']['id'],
    FileRoutesByPath['/_app/management/permission']['path'],
    FileRoutesByPath['/_app/management/permission']['fullPath']
  >
}
declare module './routes/_app/management/role' {
  const createFileRoute: CreateFileRoute<
    '/_app/management/role',
    FileRoutesByPath['/_app/management/role']['parentRoute'],
    FileRoutesByPath['/_app/management/role']['id'],
    FileRoutesByPath['/_app/management/role']['path'],
    FileRoutesByPath['/_app/management/role']['fullPath']
  >
}
declare module './routes/_app/prompt-management/prompt' {
  const createFileRoute: CreateFileRoute<
    '/_app/prompt-management/prompt',
    FileRoutesByPath['/_app/prompt-management/prompt']['parentRoute'],
    FileRoutesByPath['/_app/prompt-management/prompt']['id'],
    FileRoutesByPath['/_app/prompt-management/prompt']['path'],
    FileRoutesByPath['/_app/prompt-management/prompt']['fullPath']
  >
}
declare module './routes/_app/prompt-management/variable' {
  const createFileRoute: CreateFileRoute<
    '/_app/prompt-management/variable',
    FileRoutesByPath['/_app/prompt-management/variable']['parentRoute'],
    FileRoutesByPath['/_app/prompt-management/variable']['id'],
    FileRoutesByPath['/_app/prompt-management/variable']['path'],
    FileRoutesByPath['/_app/prompt-management/variable']['fullPath']
  >
}
declare module './routes/_app/prompt-management/version' {
  const createFileRoute: CreateFileRoute<
    '/_app/prompt-management/version',
    FileRoutesByPath['/_app/prompt-management/version']['parentRoute'],
    FileRoutesByPath['/_app/prompt-management/version']['id'],
    FileRoutesByPath['/_app/prompt-management/version']['path'],
    FileRoutesByPath['/_app/prompt-management/version']['fullPath']
  >
}
declare module './routes/_app/shop/activity-library' {
  const createFileRoute: CreateFileRoute<
    '/_app/shop/activity-library',
    FileRoutesByPath['/_app/shop/activity-library']['parentRoute'],
    FileRoutesByPath['/_app/shop/activity-library']['id'],
    FileRoutesByPath['/_app/shop/activity-library']['path'],
    FileRoutesByPath['/_app/shop/activity-library']['fullPath']
  >
}
declare module './routes/_app/shop/agent-config' {
  const createFileRoute: CreateFileRoute<
    '/_app/shop/agent-config',
    FileRoutesByPath['/_app/shop/agent-config']['parentRoute'],
    FileRoutesByPath['/_app/shop/agent-config']['id'],
    FileRoutesByPath['/_app/shop/agent-config']['path'],
    FileRoutesByPath['/_app/shop/agent-config']['fullPath']
  >
}
declare module './routes/_app/shop/conversations' {
  const createFileRoute: CreateFileRoute<
    '/_app/shop/conversations',
    FileRoutesByPath['/_app/shop/conversations']['parentRoute'],
    FileRoutesByPath['/_app/shop/conversations']['id'],
    FileRoutesByPath['/_app/shop/conversations']['path'],
    FileRoutesByPath['/_app/shop/conversations']['fullPath']
  >
}
declare module './routes/_app/shop/good-library' {
  const createFileRoute: CreateFileRoute<
    '/_app/shop/good-library',
    FileRoutesByPath['/_app/shop/good-library']['parentRoute'],
    FileRoutesByPath['/_app/shop/good-library']['id'],
    FileRoutesByPath['/_app/shop/good-library']['path'],
    FileRoutesByPath['/_app/shop/good-library']['fullPath']
  >
}
declare module './routes/_app/shop/knowledge-library' {
  const createFileRoute: CreateFileRoute<
    '/_app/shop/knowledge-library',
    FileRoutesByPath['/_app/shop/knowledge-library']['parentRoute'],
    FileRoutesByPath['/_app/shop/knowledge-library']['id'],
    FileRoutesByPath['/_app/shop/knowledge-library']['path'],
    FileRoutesByPath['/_app/shop/knowledge-library']['fullPath']
  >
}
declare module './routes/_app/shop/quality-inspection' {
  const createFileRoute: CreateFileRoute<
    '/_app/shop/quality-inspection',
    FileRoutesByPath['/_app/shop/quality-inspection']['parentRoute'],
    FileRoutesByPath['/_app/shop/quality-inspection']['id'],
    FileRoutesByPath['/_app/shop/quality-inspection']['path'],
    FileRoutesByPath['/_app/shop/quality-inspection']['fullPath']
  >
}
declare module './routes/_app/shop/shop-config' {
  const createFileRoute: CreateFileRoute<
    '/_app/shop/shop-config',
    FileRoutesByPath['/_app/shop/shop-config']['parentRoute'],
    FileRoutesByPath['/_app/shop/shop-config']['id'],
    FileRoutesByPath['/_app/shop/shop-config']['path'],
    FileRoutesByPath['/_app/shop/shop-config']['fullPath']
  >
}
declare module './routes/_app/shop/simulation-conversation' {
  const createFileRoute: CreateFileRoute<
    '/_app/shop/simulation-conversation',
    FileRoutesByPath['/_app/shop/simulation-conversation']['parentRoute'],
    FileRoutesByPath['/_app/shop/simulation-conversation']['id'],
    FileRoutesByPath['/_app/shop/simulation-conversation']['path'],
    FileRoutesByPath['/_app/shop/simulation-conversation']['fullPath']
  >
}
declare module './routes/_app/shop/version-management' {
  const createFileRoute: CreateFileRoute<
    '/_app/shop/version-management',
    FileRoutesByPath['/_app/shop/version-management']['parentRoute'],
    FileRoutesByPath['/_app/shop/version-management']['id'],
    FileRoutesByPath['/_app/shop/version-management']['path'],
    FileRoutesByPath['/_app/shop/version-management']['fullPath']
  >
}
declare module './routes/_app/system-setting/annotate-configure' {
  const createFileRoute: CreateFileRoute<
    '/_app/system-setting/annotate-configure',
    FileRoutesByPath['/_app/system-setting/annotate-configure']['parentRoute'],
    FileRoutesByPath['/_app/system-setting/annotate-configure']['id'],
    FileRoutesByPath['/_app/system-setting/annotate-configure']['path'],
    FileRoutesByPath['/_app/system-setting/annotate-configure']['fullPath']
  >
}
declare module './routes/_app/system-setting/field-configure' {
  const createFileRoute: CreateFileRoute<
    '/_app/system-setting/field-configure',
    FileRoutesByPath['/_app/system-setting/field-configure']['parentRoute'],
    FileRoutesByPath['/_app/system-setting/field-configure']['id'],
    FileRoutesByPath['/_app/system-setting/field-configure']['path'],
    FileRoutesByPath['/_app/system-setting/field-configure']['fullPath']
  >
}
declare module './routes/_app/workbench/transfer-task' {
  const createFileRoute: CreateFileRoute<
    '/_app/workbench/transfer-task',
    FileRoutesByPath['/_app/workbench/transfer-task']['parentRoute'],
    FileRoutesByPath['/_app/workbench/transfer-task']['id'],
    FileRoutesByPath['/_app/workbench/transfer-task']['path'],
    FileRoutesByPath['/_app/workbench/transfer-task']['fullPath']
  >
}
declare module './routes/_app/effect-evaluation/management/contrast-result' {
  const createFileRoute: CreateFileRoute<
    '/_app/effect-evaluation/management/contrast-result',
    FileRoutesByPath['/_app/effect-evaluation/management/contrast-result']['parentRoute'],
    FileRoutesByPath['/_app/effect-evaluation/management/contrast-result']['id'],
    FileRoutesByPath['/_app/effect-evaluation/management/contrast-result']['path'],
    FileRoutesByPath['/_app/effect-evaluation/management/contrast-result']['fullPath']
  >
}
declare module './routes/_app/effect-evaluation/management/detail' {
  const createFileRoute: CreateFileRoute<
    '/_app/effect-evaluation/management/detail',
    FileRoutesByPath['/_app/effect-evaluation/management/detail']['parentRoute'],
    FileRoutesByPath['/_app/effect-evaluation/management/detail']['id'],
    FileRoutesByPath['/_app/effect-evaluation/management/detail']['path'],
    FileRoutesByPath['/_app/effect-evaluation/management/detail']['fullPath']
  >
}
declare module './routes/_app/effect-evaluation/tuning/$id' {
  const createFileRoute: CreateFileRoute<
    '/_app/effect-evaluation/tuning/$id',
    FileRoutesByPath['/_app/effect-evaluation/tuning/$id']['parentRoute'],
    FileRoutesByPath['/_app/effect-evaluation/tuning/$id']['id'],
    FileRoutesByPath['/_app/effect-evaluation/tuning/$id']['path'],
    FileRoutesByPath['/_app/effect-evaluation/tuning/$id']['fullPath']
  >
}
declare module './routes/_app/prompt-management/prompt/ability' {
  const createFileRoute: CreateFileRoute<
    '/_app/prompt-management/prompt/ability',
    FileRoutesByPath['/_app/prompt-management/prompt/ability']['parentRoute'],
    FileRoutesByPath['/_app/prompt-management/prompt/ability']['id'],
    FileRoutesByPath['/_app/prompt-management/prompt/ability']['path'],
    FileRoutesByPath['/_app/prompt-management/prompt/ability']['fullPath']
  >
}
declare module './routes/_app/prompt-management/prompt/module' {
  const createFileRoute: CreateFileRoute<
    '/_app/prompt-management/prompt/module',
    FileRoutesByPath['/_app/prompt-management/prompt/module']['parentRoute'],
    FileRoutesByPath['/_app/prompt-management/prompt/module']['id'],
    FileRoutesByPath['/_app/prompt-management/prompt/module']['path'],
    FileRoutesByPath['/_app/prompt-management/prompt/module']['fullPath']
  >
}
declare module './routes/_app/prompt-management/prompt/shared-variable' {
  const createFileRoute: CreateFileRoute<
    '/_app/prompt-management/prompt/shared-variable',
    FileRoutesByPath['/_app/prompt-management/prompt/shared-variable']['parentRoute'],
    FileRoutesByPath['/_app/prompt-management/prompt/shared-variable']['id'],
    FileRoutesByPath['/_app/prompt-management/prompt/shared-variable']['path'],
    FileRoutesByPath['/_app/prompt-management/prompt/shared-variable']['fullPath']
  >
}
declare module './routes/_app/shop/quality-inspection/detail' {
  const createFileRoute: CreateFileRoute<
    '/_app/shop/quality-inspection/detail',
    FileRoutesByPath['/_app/shop/quality-inspection/detail']['parentRoute'],
    FileRoutesByPath['/_app/shop/quality-inspection/detail']['id'],
    FileRoutesByPath['/_app/shop/quality-inspection/detail']['path'],
    FileRoutesByPath['/_app/shop/quality-inspection/detail']['fullPath']
  >
}
declare module './routes/_app/shop/quality-inspection/overview' {
  const createFileRoute: CreateFileRoute<
    '/_app/shop/quality-inspection/overview',
    FileRoutesByPath['/_app/shop/quality-inspection/overview']['parentRoute'],
    FileRoutesByPath['/_app/shop/quality-inspection/overview']['id'],
    FileRoutesByPath['/_app/shop/quality-inspection/overview']['path'],
    FileRoutesByPath['/_app/shop/quality-inspection/overview']['fullPath']
  >
}
declare module './routes/_app/effect-evaluation/management/index' {
  const createFileRoute: CreateFileRoute<
    '/_app/effect-evaluation/management/',
    FileRoutesByPath['/_app/effect-evaluation/management/']['parentRoute'],
    FileRoutesByPath['/_app/effect-evaluation/management/']['id'],
    FileRoutesByPath['/_app/effect-evaluation/management/']['path'],
    FileRoutesByPath['/_app/effect-evaluation/management/']['fullPath']
  >
}
declare module './routes/_app/shop/quality-inspection/index' {
  const createFileRoute: CreateFileRoute<
    '/_app/shop/quality-inspection/',
    FileRoutesByPath['/_app/shop/quality-inspection/']['parentRoute'],
    FileRoutesByPath['/_app/shop/quality-inspection/']['id'],
    FileRoutesByPath['/_app/shop/quality-inspection/']['path'],
    FileRoutesByPath['/_app/shop/quality-inspection/']['fullPath']
  >
}

interface AppEffectEvaluationManagementRouteChildren {
  AppEffectEvaluationManagementContrastResultRoute: typeof AppEffectEvaluationManagementContrastResultRoute
  AppEffectEvaluationManagementDetailRoute: typeof AppEffectEvaluationManagementDetailRoute
  AppEffectEvaluationManagementIndexRoute: typeof AppEffectEvaluationManagementIndexRoute
}

const AppEffectEvaluationManagementRouteChildren: AppEffectEvaluationManagementRouteChildren =
  {
    AppEffectEvaluationManagementContrastResultRoute:
      AppEffectEvaluationManagementContrastResultRoute,
    AppEffectEvaluationManagementDetailRoute:
      AppEffectEvaluationManagementDetailRoute,
    AppEffectEvaluationManagementIndexRoute:
      AppEffectEvaluationManagementIndexRoute,
  }

const AppEffectEvaluationManagementRouteWithChildren =
  AppEffectEvaluationManagementRoute._addFileChildren(
    AppEffectEvaluationManagementRouteChildren,
  )

interface AppEffectEvaluationTuningRouteChildren {
  AppEffectEvaluationTuningIdRoute: typeof AppEffectEvaluationTuningIdRoute
}

const AppEffectEvaluationTuningRouteChildren: AppEffectEvaluationTuningRouteChildren =
  {
    AppEffectEvaluationTuningIdRoute: AppEffectEvaluationTuningIdRoute,
  }

const AppEffectEvaluationTuningRouteWithChildren =
  AppEffectEvaluationTuningRoute._addFileChildren(
    AppEffectEvaluationTuningRouteChildren,
  )

interface AppEffectEvaluationRouteChildren {
  AppEffectEvaluationManagementRoute: typeof AppEffectEvaluationManagementRouteWithChildren
  AppEffectEvaluationSamplesRoute: typeof AppEffectEvaluationSamplesRoute
  AppEffectEvaluationTuningRoute: typeof AppEffectEvaluationTuningRouteWithChildren
}

const AppEffectEvaluationRouteChildren: AppEffectEvaluationRouteChildren = {
  AppEffectEvaluationManagementRoute:
    AppEffectEvaluationManagementRouteWithChildren,
  AppEffectEvaluationSamplesRoute: AppEffectEvaluationSamplesRoute,
  AppEffectEvaluationTuningRoute: AppEffectEvaluationTuningRouteWithChildren,
}

const AppEffectEvaluationRouteWithChildren =
  AppEffectEvaluationRoute._addFileChildren(AppEffectEvaluationRouteChildren)

interface AppManagementRouteChildren {
  AppManagementAccountRoute: typeof AppManagementAccountRoute
  AppManagementBrandRoute: typeof AppManagementBrandRoute
  AppManagementPermissionRoute: typeof AppManagementPermissionRoute
  AppManagementRoleRoute: typeof AppManagementRoleRoute
}

const AppManagementRouteChildren: AppManagementRouteChildren = {
  AppManagementAccountRoute: AppManagementAccountRoute,
  AppManagementBrandRoute: AppManagementBrandRoute,
  AppManagementPermissionRoute: AppManagementPermissionRoute,
  AppManagementRoleRoute: AppManagementRoleRoute,
}

const AppManagementRouteWithChildren = AppManagementRoute._addFileChildren(
  AppManagementRouteChildren,
)

interface AppPromptManagementPromptRouteChildren {
  AppPromptManagementPromptAbilityRoute: typeof AppPromptManagementPromptAbilityRoute
  AppPromptManagementPromptModuleRoute: typeof AppPromptManagementPromptModuleRoute
  AppPromptManagementPromptSharedVariableRoute: typeof AppPromptManagementPromptSharedVariableRoute
}

const AppPromptManagementPromptRouteChildren: AppPromptManagementPromptRouteChildren =
  {
    AppPromptManagementPromptAbilityRoute:
      AppPromptManagementPromptAbilityRoute,
    AppPromptManagementPromptModuleRoute: AppPromptManagementPromptModuleRoute,
    AppPromptManagementPromptSharedVariableRoute:
      AppPromptManagementPromptSharedVariableRoute,
  }

const AppPromptManagementPromptRouteWithChildren =
  AppPromptManagementPromptRoute._addFileChildren(
    AppPromptManagementPromptRouteChildren,
  )

interface AppPromptManagementRouteChildren {
  AppPromptManagementPromptRoute: typeof AppPromptManagementPromptRouteWithChildren
  AppPromptManagementVariableRoute: typeof AppPromptManagementVariableRoute
  AppPromptManagementVersionRoute: typeof AppPromptManagementVersionRoute
}

const AppPromptManagementRouteChildren: AppPromptManagementRouteChildren = {
  AppPromptManagementPromptRoute: AppPromptManagementPromptRouteWithChildren,
  AppPromptManagementVariableRoute: AppPromptManagementVariableRoute,
  AppPromptManagementVersionRoute: AppPromptManagementVersionRoute,
}

const AppPromptManagementRouteWithChildren =
  AppPromptManagementRoute._addFileChildren(AppPromptManagementRouteChildren)

interface AppShopQualityInspectionRouteChildren {
  AppShopQualityInspectionDetailRoute: typeof AppShopQualityInspectionDetailRoute
  AppShopQualityInspectionOverviewRoute: typeof AppShopQualityInspectionOverviewRoute
  AppShopQualityInspectionIndexRoute: typeof AppShopQualityInspectionIndexRoute
}

const AppShopQualityInspectionRouteChildren: AppShopQualityInspectionRouteChildren =
  {
    AppShopQualityInspectionDetailRoute: AppShopQualityInspectionDetailRoute,
    AppShopQualityInspectionOverviewRoute:
      AppShopQualityInspectionOverviewRoute,
    AppShopQualityInspectionIndexRoute: AppShopQualityInspectionIndexRoute,
  }

const AppShopQualityInspectionRouteWithChildren =
  AppShopQualityInspectionRoute._addFileChildren(
    AppShopQualityInspectionRouteChildren,
  )

interface AppShopRouteChildren {
  AppShopActivityLibraryRoute: typeof AppShopActivityLibraryRoute
  AppShopAgentConfigRoute: typeof AppShopAgentConfigRoute
  AppShopConversationsRoute: typeof AppShopConversationsRoute
  AppShopGoodLibraryRoute: typeof AppShopGoodLibraryRoute
  AppShopKnowledgeLibraryRoute: typeof AppShopKnowledgeLibraryRoute
  AppShopQualityInspectionRoute: typeof AppShopQualityInspectionRouteWithChildren
  AppShopShopConfigRoute: typeof AppShopShopConfigRoute
  AppShopSimulationConversationRoute: typeof AppShopSimulationConversationRoute
  AppShopVersionManagementRoute: typeof AppShopVersionManagementRoute
}

const AppShopRouteChildren: AppShopRouteChildren = {
  AppShopActivityLibraryRoute: AppShopActivityLibraryRoute,
  AppShopAgentConfigRoute: AppShopAgentConfigRoute,
  AppShopConversationsRoute: AppShopConversationsRoute,
  AppShopGoodLibraryRoute: AppShopGoodLibraryRoute,
  AppShopKnowledgeLibraryRoute: AppShopKnowledgeLibraryRoute,
  AppShopQualityInspectionRoute: AppShopQualityInspectionRouteWithChildren,
  AppShopShopConfigRoute: AppShopShopConfigRoute,
  AppShopSimulationConversationRoute: AppShopSimulationConversationRoute,
  AppShopVersionManagementRoute: AppShopVersionManagementRoute,
}

const AppShopRouteWithChildren =
  AppShopRoute._addFileChildren(AppShopRouteChildren)

interface AppSystemSettingRouteChildren {
  AppSystemSettingAnnotateConfigureRoute: typeof AppSystemSettingAnnotateConfigureRoute
  AppSystemSettingFieldConfigureRoute: typeof AppSystemSettingFieldConfigureRoute
}

const AppSystemSettingRouteChildren: AppSystemSettingRouteChildren = {
  AppSystemSettingAnnotateConfigureRoute:
    AppSystemSettingAnnotateConfigureRoute,
  AppSystemSettingFieldConfigureRoute: AppSystemSettingFieldConfigureRoute,
}

const AppSystemSettingRouteWithChildren =
  AppSystemSettingRoute._addFileChildren(AppSystemSettingRouteChildren)

interface AppWorkbenchRouteChildren {
  AppWorkbenchTransferTaskRoute: typeof AppWorkbenchTransferTaskRoute
}

const AppWorkbenchRouteChildren: AppWorkbenchRouteChildren = {
  AppWorkbenchTransferTaskRoute: AppWorkbenchTransferTaskRoute,
}

const AppWorkbenchRouteWithChildren = AppWorkbenchRoute._addFileChildren(
  AppWorkbenchRouteChildren,
)

interface AppRouteChildren {
  App404Route: typeof App404Route
  AppChatRoute: typeof AppChatRoute
  AppEffectEvaluationRoute: typeof AppEffectEvaluationRouteWithChildren
  AppManagementRoute: typeof AppManagementRouteWithChildren
  AppPromptManagementRoute: typeof AppPromptManagementRouteWithChildren
  AppShopRoute: typeof AppShopRouteWithChildren
  AppSystemSettingRoute: typeof AppSystemSettingRouteWithChildren
  AppWorkbenchRoute: typeof AppWorkbenchRouteWithChildren
  AppIndexRoute: typeof AppIndexRoute
}

const AppRouteChildren: AppRouteChildren = {
  App404Route: App404Route,
  AppChatRoute: AppChatRoute,
  AppEffectEvaluationRoute: AppEffectEvaluationRouteWithChildren,
  AppManagementRoute: AppManagementRouteWithChildren,
  AppPromptManagementRoute: AppPromptManagementRouteWithChildren,
  AppShopRoute: AppShopRouteWithChildren,
  AppSystemSettingRoute: AppSystemSettingRouteWithChildren,
  AppWorkbenchRoute: AppWorkbenchRouteWithChildren,
  AppIndexRoute: AppIndexRoute,
}

const AppRouteWithChildren = AppRoute._addFileChildren(AppRouteChildren)

const rootRouteChildren: RootRouteChildren = {
  AppRoute: AppRouteWithChildren,
  AuthLoginRoute: AuthLoginRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
