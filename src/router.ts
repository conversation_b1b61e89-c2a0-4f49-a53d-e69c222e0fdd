import { createRouter } from "@tanstack/react-router";
import { t } from "i18next";
import { NotFoundPage } from "./components/common/not-found";
import { PageSkeleton } from "./components/ui/page-skeleton";
import { FileRouteTypes, routeTree } from "./routeTree.gen";

export interface RootContext {
  crumbNameMap: Partial<Record<FileRouteTypes["to"], string>>;
  hasPermission: (permissionCode: string) => boolean;
}

export const routeContext: RootContext = {
  crumbNameMap: {
    "/management": t("management"),
    "/management/account": t("management.account"),
    "/management/brand": t("management.brand"),
    "/management/role": t("management.role"),
    "/workbench": t("menu.workspace"),
    "/workbench/transfer-task": t("menu.workspace.transfer.task"),
    "/effect-evaluation": t("effect.evaluation"),
    "/effect-evaluation/samples": t("effect.evaluation.samples"),
    "/effect-evaluation/tuning": t("effect.evaluation.tuning"),
    "/effect-evaluation/management": t("effect.evaluation.management"),
    "/effect-evaluation/management/contrast-result": t(
      "effect.evaluation.management.contrast.result",
    ),
    "/effect-evaluation/management/detail": t(
      "effect.evaluation.management.detail",
    ),
    "/shop": t("shop.management"),
    "/shop/shop-config": t("shop.config"),
    "/shop/good-library": t("good.library"),
    "/shop/knowledge-library": t("knowledge.base"),
    "/shop/agent-config": t("shop.agent.config"),
    "/shop/activity-library": t("activity.library"),
    "/shop/conversations": t("menu.conversations"),
    "/shop/simulation-conversation": t("simulation.conversation"),
    "/shop/quality-inspection": t("menu.quality.inspection"),
    "/shop/quality-inspection/overview": t("quality.inspection.overview"),
    "/shop/quality-inspection/detail": t("quality.inspection.detail"),
    "/system-setting": t("system.setting"),
    "/system-setting/annotate-configure": t(
      "system.setting.annotate.configure",
    ),
    "/system-setting/field-configure": t("system.setting.field.configure"),
    "/management/permission": t("system.setting.permission.management"),
    "/prompt-management": t("prompt.management"),
    "/prompt-management/prompt": t("prompt.management.prompt"),
    "/prompt-management/prompt/ability": t("prompt.ability"),
    "/prompt-management/prompt/module": t("prompt.module"),
    "/prompt-management/prompt/shared-variable": t("prompt.shared.variable"),
    "/prompt-management/variable": t("prompt.management.variable"),
    "/prompt-management/version": t("prompt.management.version"),
    "/shop/version-management": t("version.management"),
    "/chat": t("chat"),
  },
  hasPermission: () => false,
};

export const router = createRouter({
  routeTree,
  context: routeContext,
  defaultHashScrollIntoView: true,
  defaultNotFoundComponent: NotFoundPage,
  defaultPendingComponent: PageSkeleton,
});
