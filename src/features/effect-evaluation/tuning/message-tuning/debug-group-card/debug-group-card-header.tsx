import { Tooltip } from "@/components/common/tooltip";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import { useMolecule } from "bunshi/react";
import { useAtom, useSetAtom } from "jotai";
import {
  Check,
  Copy,
  FileDiff,
  MoreHorizontal,
  Settings,
  SquarePen,
  Trash2,
  X,
} from "lucide-react";
import { Suspense, useState } from "react";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";
import { useMessageTuning } from "../message-tuning-context";
import { evaluationTuningMolecule } from "../store";
import { DebugGroup } from "../types";
import { VersionConfigHoverCard } from "./version-config-hover-card";

// 卡片操作按钮组件
const CardActions = ({
  group,
  compareGroupIds,
  onToggleCompare,
}: {
  group: DebugGroup;
  compareGroupIds: string[];
  onToggleCompare: (groupId: string) => void;
}) => {
  const { t } = useTranslation();
  const {
    setEditingDebugGroupIdAtom,
    deleteDebugGroupAtom,
    duplicateDebugGroupAtom,
  } = useMolecule(evaluationTuningMolecule);
  const { promptDrawer } = useMessageTuning();
  const setEditingDebugGroupId = useSetAtom(setEditingDebugGroupIdAtom);
  const deleteDebugGroup = useSetAtom(deleteDebugGroupAtom);
  const duplicateDebugGroup = useSetAtom(duplicateDebugGroupAtom);

  const isCompared = compareGroupIds.includes(group.id);

  return (
    <div className="flex items-center gap-1">
      <Tooltip content={<p>对比</p>}>
        <Button
          variant="icon"
          onClick={() => onToggleCompare(group.id)}
          icon={<FileDiff />}
          className={cn({
            "text-primary bg-primary/10 hover:bg-primary/10": isCompared,
          })}
        />
      </Tooltip>

      <Suspense fallback={<Button variant="icon" loading={true} />}>
        <VersionConfigHoverCard group={group} />
      </Suspense>

      <Tooltip content={<p>编辑提示词</p>}>
        <Button
          variant="icon"
          icon={<SquarePen />}
          onClick={() => {
            promptDrawer.showDrawer(group);
          }}
        />
      </Tooltip>

      <Tooltip content={<p>配置调试组</p>}>
        <Button
          variant="icon"
          onClick={() => {
            if (!group.promptVersionId || !group.configVersionId) {
              toast.info(t("debug.version.config.tooltip"));

              return;
            }

            return setEditingDebugGroupId(group.id);
          }}
          icon={<Settings />}
        />
      </Tooltip>

      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Tooltip content={<p>更多操作</p>}>
            <Button variant="icon" icon={<MoreHorizontal />} />
          </Tooltip>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          align="end"
          className="w-fit rounded-md border-gray-200"
        >
          <DropdownMenuItem
            className="flex items-center gap-2 rounded-sm cursor-pointer"
            onClick={() => duplicateDebugGroup(group.id)}
          >
            <Copy className="size-4" />
            <span>复制组</span>
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem
            className="flex items-center gap-2 !text-destructive rounded-sm cursor-pointer"
            onClick={() => deleteDebugGroup(group.id)}
          >
            <Trash2 className="size-4" />
            <span>删除组</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};

interface DebugGroupCardHeaderProps {
  group: DebugGroup;
  compareGroupIds: string[];
  onToggleCompare: (groupId: string) => void;
}

export const DebugGroupCardHeader = ({
  group,
  compareGroupIds,
  onToggleCompare,
}: DebugGroupCardHeaderProps) => {
  const { updateDebugGroupNameAtom } = useMolecule(evaluationTuningMolecule);
  const [, updateDebugGroupName] = useAtom(updateDebugGroupNameAtom);

  const [isEditing, setIsEditing] = useState(false);
  const [editingName, setEditingName] = useState(group.name);

  const handleEditClick = () => {
    setEditingName(group.name);
    setIsEditing(true);
  };

  const handleSave = () => {
    if (editingName.trim()) {
      updateDebugGroupName({
        id: group.id,
        name: editingName.trim(),
      });
    }
    setIsEditing(false);
  };

  const handleCancel = () => {
    setIsEditing(false);
    setEditingName(group.name);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSave();
    } else if (e.key === "Escape") {
      handleCancel();
    }
  };

  return (
    <div className="px-4 py-3 flex items-center justify-between border-b border-gray-100">
      <div className="flex items-center gap-2">
        {isEditing ? (
          <div className="flex items-center gap-1">
            <Input
              value={editingName}
              onChange={(e) => setEditingName(e.target.value)}
              onKeyDown={handleKeyDown}
              autoFocus
              className="h-7 py-0 px-2 text-sm w-40 mr-1"
            />
            <Button
              variant="icon"
              icon={<Check className="text-success" />}
              onClick={handleSave}
            />
            <Button
              variant="icon"
              icon={<X className="text-destructive" />}
              onClick={handleCancel}
            />
          </div>
        ) : (
          <h3 className="flex flex-wrap gap-2 text-sm font-semibold text-gray-800 truncate">
            <div className="flex items-center gap-1">
              <span>{group.name || "未知调试组"}</span>
              <Button
                variant="icon"
                size="icon"
                className="opacity-70 hover:opacity-100"
                icon={<SquarePen />}
                onClick={handleEditClick}
              />
            </div>
          </h3>
        )}
      </div>

      <CardActions
        group={group}
        compareGroupIds={compareGroupIds}
        onToggleCompare={onToggleCompare}
      />
    </div>
  );
};
