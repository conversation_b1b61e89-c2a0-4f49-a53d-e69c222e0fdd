import { Tooltip } from "@/components/common/tooltip";
import {
  ConfigVersionSelect,
  PromptVersionSelect,
} from "@/components/common/version-select";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";
import { Label } from "@/components/ui/label";
import { isDraftVersion } from "@/features/prompt/management/utils";
import { cn } from "@/lib/utils";
import { PromptVersion } from "@/types/api/prompt-version";
import { useBoolean, useClickAway } from "ahooks";
import { useMolecule } from "bunshi/react";
import { useAtomValue, useSet<PERSON>tom } from "jotai";
import { Settings2 } from "lucide-react";
import { useCallback, useMemo, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { evaluationTuningMolecule } from "../store";
import { DebugGroup } from "../types";

interface VersionConfigHoverCardProps {
  group: DebugGroup;
}

export const VersionConfigHoverCard = ({
  group,
}: VersionConfigHoverCardProps) => {
  const { t } = useTranslation();
  const {
    updateDebugGroupPromptVersionAtom,
    updateDebugGroupConfigVersionAtom,
    configVersionsAtom,
    promptVersionsAtom,
  } = useMolecule(evaluationTuningMolecule);
  const updateDebugGroupConfigVersion = useSetAtom(
    updateDebugGroupConfigVersionAtom,
  );
  const updateDebugGroupPromptVersion = useSetAtom(
    updateDebugGroupPromptVersionAtom,
  );
  const { data: configVersions } = useAtomValue(configVersionsAtom);
  const promptVersions = useAtomValue(promptVersionsAtom);

  const [
    showVersionConfig,
    { setFalse: closeVersionConfig, toggle: toggleVersionConfig },
  ] = useBoolean(false);
  const [isAlertOpen, setIsAlertOpen] = useState(false);
  const [newVersionValue, setNewVersionValue] = useState<string>();
  const hoverCardRef = useRef<HTMLDivElement>(null);

  const currentVersionConfigDescription = useMemo(() => {
    const configVersion = configVersions?.find(
      (version) => version.configId === group.configVersionId,
    );
    const promptVersion = promptVersions?.find((version) =>
      isDraftVersion(version)
        ? group.promptVersionId === version.id
        : group.promptVersionId === version.promptVersionId,
    );

    return (
      <>
        <p className="mb-1">版本配置</p>
        <p>配置版本： {configVersion?.versionName || "未配置"}</p>
        <p>提示词版本： {promptVersion?.versionName || "未配置"}</p>
      </>
    );
  }, [
    configVersions,
    promptVersions,
    group.configVersionId,
    group.promptVersionId,
  ]);

  const handleConfigVersionChange = useCallback(
    (value: string) => {
      if (group.configVersionId && group.configVersionId !== value) {
        setNewVersionValue(value);
        setIsAlertOpen(true);
      } else {
        updateDebugGroupConfigVersion(group.id, value);
      }
    },
    [group.configVersionId, group.id, updateDebugGroupConfigVersion],
  );

  const handlePromptVersionChange = useCallback(
    (value: string) => {
      updateDebugGroupPromptVersion(group.id, value);
    },
    [updateDebugGroupPromptVersion, group.id],
  );

  const handleConfirmVersionChange = useCallback(() => {
    if (newVersionValue) {
      // 更新版本配置
      updateDebugGroupConfigVersion(group.id, newVersionValue);
    }

    setIsAlertOpen(false);
    setNewVersionValue(undefined);
  }, [group.id, newVersionValue, updateDebugGroupConfigVersion]);

  const handleCancelVersionChange = useCallback(() => {
    setIsAlertOpen(false);
    setNewVersionValue(undefined);
  }, []);

  useClickAway(closeVersionConfig, hoverCardRef);

  const hasVersions = group.promptVersionId || group.configVersionId;

  return (
    <>
      <HoverCard open={showVersionConfig}>
        <HoverCardTrigger asChild>
          <Tooltip content={currentVersionConfigDescription}>
            <Button
              variant="icon"
              size="sm"
              icon={<Settings2 />}
              className={cn({
                "text-primary bg-primary/10 hover:bg-primary/10": hasVersions,
              })}
              onClick={(event) => {
                event.stopPropagation();
                toggleVersionConfig();
              }}
            />
          </Tooltip>
        </HoverCardTrigger>
        <HoverCardContent
          className="w-80 p-4"
          side="bottom"
          align="start"
          ref={hoverCardRef}
          sideOffset={5}
          onClick={(e) => e.stopPropagation()}
        >
          <div className="space-y-4" onClick={(e) => e.stopPropagation()}>
            <div className="space-y-2">
              <h4 className="text-sm font-medium">版本配置</h4>
              <p className="text-xs text-muted-foreground">
                为此调试组选择特定的提示词版本和配置版本
              </p>
            </div>

            <div className="space-y-3">
              <div className="space-y-2">
                <Label className="text-xs">{t("prompt.version")}</Label>
                <PromptVersionSelect
                  className="w-full"
                  value={group.promptVersionId}
                  versions={(promptVersions || []).filter(
                    (version): version is PromptVersion =>
                      !isDraftVersion(version),
                  )}
                  draftVersions={(promptVersions || []).filter(isDraftVersion)}
                  side="right"
                  sideOffset={10}
                  modal={false}
                  onChange={handlePromptVersionChange}
                />
              </div>

              <div className="space-y-2">
                <Label className="text-xs">{t("select.version")}</Label>
                <ConfigVersionSelect
                  value={group.configVersionId}
                  versions={configVersions || []}
                  onChange={handleConfigVersionChange}
                  side="right"
                  sideOffset={10}
                  modal={false}
                />
              </div>
            </div>
          </div>
        </HoverCardContent>
      </HoverCard>

      <AlertDialog open={isAlertOpen} onOpenChange={setIsAlertOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {t("debug.confirm.version.change")}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {t("debug.confirm.version.change.description")}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={handleCancelVersionChange}>
              {t("common.cancel")}
            </AlertDialogCancel>
            <AlertDialogAction
              className="text-destructive bg-destructive/10 hover:bg-destructive/20"
              onClick={handleConfirmVersionChange}
            >
              {t("common.confirm")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};
