import { shop<PERSON>tom } from "@/atoms/shop";
import {
  draftVersionsAtom,
  versionsAtom,
} from "@/features/prompt/management/atoms";
import { getDraftDataAtom } from "@/features/prompt/management/services";
import {
  CompleteVersionData,
  DraftVersion,
} from "@/features/prompt/management/types";
import {
  getCompleteVersionData,
  isDraftId,
  isDraftVersion,
} from "@/features/prompt/management/utils";
import { createLRUStorage } from "@/lib/create-lru-storage";
import { ConfigureService } from "@/services/configure-service";
import { DebugService } from "@/services/debug-service";
import { EvaluationConfigService } from "@/services/evaluation-config-service";
import { MessageService } from "@/services/message-service";
import { getQueryClient } from "@/services/query-client";
import { LogicValue } from "@/types/api/config";
import {
  EvaluationDebugParams,
  EvaluationDebugResult,
  EvaluationDebugStatus,
} from "@/types/api/evaluation-tuning";
import { PromptVersion, PromptVersionStatus } from "@/types/api/prompt-version";
import {
  ConfigVersionStatus,
  Version,
  VersionCreateParams,
  VersionDetail,
} from "@/types/api/version";
import { resolveLogicValue } from "@/utils/logic-resolver";
import { safeParseJson } from "@/utils/safe-parse-json";
import { createScope, molecule } from "bunshi";
import { t } from "i18next";
import { Draft } from "immer";
import { atom } from "jotai";
import { observe } from "jotai-effect";
import { withImmer } from "jotai-immer";
import {
  atomWithMutation,
  atomWithQuery,
  atomWithSuspenseQuery,
} from "jotai-tanstack-query";
import { atomWithStorage, loadable } from "jotai/utils";
import { isEmpty, omit } from "lodash";
import { toast } from "sonner";
import { v4 as uuidv4 } from "uuid";
import { latestMessageIdAtom } from "../configure-atoms";
import { ConfigUtils } from "./json-patch-utils";
import { DebugGroup } from "./types";
import { getShopConfig } from "./utils";

const DEBUG_CONFIGURE_KEY = "tuning-debug-configure-v1" as const;

const NEW_DEBUG_GROUP_NAME = "新调试组" as const;

/**
 * 生成新的调试组名称
 *
 * @param name
 * @param list
 * @returns
 */
const generateNewGroupName = (name: string, list: DebugGroup[]) => {
  // 如果列表为空，直接返回原名称
  if (list.length === 0) {
    return name;
  }

  // 获取所有已存在的以该名称为前缀的组名
  const existingNames = list
    .map((group) => group.name)
    .filter(
      (groupName) => groupName === name || groupName.startsWith(`${name} `),
    );

  if (!existingNames.includes(name)) {
    return name;
  }

  // 找出最大的后缀数字
  let maxNum = 0;

  existingNames.forEach((existName) => {
    // 匹配名称后的数字
    const match = existName.match(new RegExp(`^${name} (\\d+)$`));

    if (match) {
      const num = parseInt(match[1], 10);

      if (num > maxNum) {
        maxNum = num;
      }
    }
  });

  // 返回新名称，后缀数字加1
  return `${name} ${maxNum + 1}`;
};

/**
 * 获取默认版本ID
 */
const initializeDebugGroupDefaultVersions = (
  configVersions: Version[] | null,
  promptVersions: Array<PromptVersion | DraftVersion> | null,
) => {
  let configVersionId: string | undefined;
  let promptVersionId: string | undefined;

  // 设置默认配置版本（使用当前线上版本）
  if (configVersions) {
    const onlineConfigVersion = configVersions.find(
      (version) => version.onlineStatus === ConfigVersionStatus.ONLINE,
    );
    configVersionId = onlineConfigVersion?.configId;
  }

  // 设置默认提示词版本（使用当前线上版本）
  if (promptVersions) {
    const onlinePromptVersion = promptVersions.find(
      (version): version is PromptVersion => {
        if (isDraftVersion(version)) {
          return false;
        }

        return version.status === PromptVersionStatus.ONLINE;
      },
    );

    if (onlinePromptVersion) {
      promptVersionId = onlinePromptVersion?.promptVersionId;
    } else {
      const latestPromptVersion = promptVersions[0];

      if (latestPromptVersion) {
        promptVersionId = isDraftVersion(latestPromptVersion)
          ? latestPromptVersion.id
          : latestPromptVersion.promptVersionId;
      }
    }
  }

  return { configVersionId, promptVersionId };
};

export const EvaluationTuningScope = createScope<{
  id: string;
}>({
  id: "",
});

export const evaluationTuningMolecule = molecule((_, getScope) => {
  const { id } = getScope(EvaluationTuningScope);
  const debugConfigureKey = `${DEBUG_CONFIGURE_KEY}-${id}`;

  // 调试配置
  const debugConfigureAtom = withImmer(
    atomWithStorage<{
      // 当前调试环节
      currentStage?: string;
      // 调试组列表
      debugGroups: DebugGroup[];
    }>(
      debugConfigureKey,
      {
        currentStage: undefined,
        debugGroups: [],
      },
      createLRUStorage(DEBUG_CONFIGURE_KEY),
      {
        getOnInit: true,
      },
    ),
  );

  // 当前消息 id
  const currentIdAtom = atom(id);

  // 当前消息内容
  const currentMessageAtom = atomWithSuspenseQuery((get) => {
    const currentId = get(currentIdAtom);

    return {
      queryKey: ["getMessage", currentId],
      retry: false,
      queryFn: () => {
        if (isEmpty(currentId)) {
          return null;
        }

        return MessageService.getMessage({ messageId: currentId }).catch(
          () => null,
        );
      },
    };
  }, getQueryClient);
  const loadableCurrentMessageAtom = loadable(currentMessageAtom);

  // 配置版本列表
  const configVersionsAtom = atomWithSuspenseQuery((get) => {
    const shop = get(shopAtom);

    return {
      queryKey: ["evaluationConfigVersions"],
      queryFn: () => {
        if (shop?.id == null) {
          return null;
        }

        return EvaluationConfigService.getVersionList(
          {
            version: "",
          },
          {
            shopId: shop.id,
          },
        );
      },
    };
  }, getQueryClient);
  const loadableConfigVersionAtom = loadable(configVersionsAtom);

  // 调试环节列表
  const debugStagesAtom = atomWithSuspenseQuery(
    () => ({
      queryKey: ["getDebugStageConfig"],
      queryFn: () =>
        ConfigureService.getDebugStageConfig().then(
          (res) => res?.fields || null,
        ),
    }),
    getQueryClient,
  );
  const loadableDebugStagesAtom = loadable(debugStagesAtom);

  // 提示词版本列表
  const promptVersionsAtom = atom(async (get) => {
    const versions = get(versionsAtom);
    const draftVersions = await get(draftVersionsAtom);

    return [...draftVersions, ...(versions.data || [])];
  });

  // 模型配置列表
  const modelFieldsAtom = atomWithSuspenseQuery(
    () => ({
      queryKey: ["getModelFieldConfig"],
      queryFn: () =>
        ConfigureService.getModelFieldConfig().then(
          (res) => res?.fields || null,
        ),
    }),
    getQueryClient,
  );

  // 当前环节
  const currentStageAtom = atom(
    (get) => get(debugConfigureAtom).currentStage,
    (_, set, stage: string | undefined) => {
      set(debugConfigureAtom, (draft) => {
        draft.currentStage = stage;
      });
    },
  );

  // 只读调试组列表
  const debugGroupsAtom = atom((get) => get(debugConfigureAtom).debugGroups);

  // 设置调试组列表
  const setDebugGroupsAtom = atom(
    null,
    (_, set, updater: (draft: Draft<DebugGroup[]>) => void) => {
      set(debugConfigureAtom, (draft) => {
        updater(draft.debugGroups);
      });
    },
  );

  // 清理调试组列表
  const clearDebugGroupsAtom = atom(null, (_, set) => {
    set(setDebugGroupsAtom, (draft) => {
      draft.length = 0;
    });
  });

  // 清理当前版本信息
  const clearCurrentConfigVersionAtom = atom(null, (_, set) => {
    // 清理调试组
    set(clearDebugGroupsAtom);
    // 清理调试组结果
    set(debugIdAtom, null);
  });

  // 调试组 ID
  const debugIdAtom = atom<string | null>(null);

  // 调试组结果
  const debugGroupResultAtom = atomWithQuery<EvaluationDebugResult[] | null>(
    (get) => ({
      queryKey: ["getDebugGroupResult", get(debugIdAtom)],
      queryFn: () => {
        const debugId = get(debugIdAtom);
        const debugStages = get(loadableDebugStagesAtom);
        const currentStage = get(currentStageAtom);

        if (debugId == null || debugStages.state !== "hasData") {
          return null;
        }

        return DebugService.getEvaluationDebugResult(debugId).then((results) =>
          results.map((result) => ({
            ...result,
            result: resolveLogicValue(
              debugStages.data.data?.find((stage) => stage.id === currentStage)
                ?.value,
              safeParseJson(result.metadata, {}),
            ),
          })),
        );
      },
      refetchInterval: (query) =>
        query.state.data?.some(
          (item) => item.status === EvaluationDebugStatus.IN_PROGRESS,
        )
          ? 600
          : false,
    }),
    getQueryClient,
  );

  // 执行调试
  const evaluationDebugAtom = atomWithMutation(
    () => ({
      mutationFn: (params: EvaluationDebugParams) => {
        return DebugService.evaluationDebug(params);
      },
    }),
    getQueryClient,
  );

  // 创建发布版本
  const createPublishVersionAtom = atomWithMutation(
    (get) => ({
      mutationFn: (params: VersionCreateParams) =>
        EvaluationConfigService.createVersion(params),
      onSuccess: () => {
        const configVersions = get(loadableConfigVersionAtom);

        // 刷新版本列表
        if (configVersions.state === "hasData") {
          configVersions.data.refetch();
        }
      },
    }),
    getQueryClient,
  );

  // 解析店铺配置字段
  const resolveShopConfigFieldAtom = atom(
    null,
    (
      _get,
      _set,
      configVersion: VersionDetail,
      path: LogicValue | undefined,
    ) => {
      return resolveLogicValue(path, getShopConfig(configVersion));
    },
  );

  // 当前编辑的调试组 ID
  const editingDebugGroupIdAtom = atom<string | undefined>(undefined);
  // 当前编辑的调试组
  const editingDebugGroupAtom = atom((get) => {
    const editingDebugGroupId = get(editingDebugGroupIdAtom);

    return get(debugGroupsAtom).find(
      (group) => group.id === editingDebugGroupId,
    );
  });

  // 设置当前编辑的调试组 ID
  const setEditingDebugGroupIdAtom = atom(null, (_, set, groupId: string) => {
    set(editingDebugGroupIdAtom, groupId);
  });

  // 取消编辑调试组 ID
  const cancelEditingDebugGroupIdAtom = atom(null, (_, set) => {
    set(editingDebugGroupIdAtom, undefined);
  });

  // 执行调试组
  const runDebugGroupAtom = atom(null, async (get, set) => {
    const groups = get(debugGroupsAtom);
    const currentId = get(currentIdAtom);
    const { mutateAsync: evaluationDebug } = get(evaluationDebugAtom);
    const shop = get(shopAtom);

    // 检查所有调试组是否都已配置版本
    const hasUnConfiguredGroups = groups.some(
      (group) => !group.promptVersionId || !group.configVersionId,
    );

    if (hasUnConfiguredGroups) {
      toast.error(t("debug.version.config.tooltip.all"));
      return;
    }

    // 为每个调试组获取对应的版本配置
    const configList = await Promise.all(
      groups.map(async (group) => {
        // 获取该组的配置版本详情
        let shopConfig: Record<string, unknown> = {};
        // 获取该组的提示词版本详情
        let promptConfig: CompleteVersionData;

        if (
          group.configVersionId == null ||
          shop?.id == null ||
          group.promptVersionId == null
        ) {
          throw new Error("版本数据不完整");
        }

        const versionDetail = await EvaluationConfigService.getVersionDetail({
          configId: group.configVersionId,
          shopId: shop.id,
        });

        if (versionDetail) {
          shopConfig = getShopConfig(versionDetail);
        }

        if (isDraftId(group.promptVersionId)) {
          const draftData = await get(getDraftDataAtom)(group.promptVersionId);

          if (!draftData) {
            throw new Error("草稿数据不存在");
          }

          promptConfig = draftData;
        } else {
          promptConfig = await getCompleteVersionData(group.promptVersionId);
        }

        // 应用调试组的自定义配置
        const { modifiedShopConfig } = ConfigUtils.generateConfigForVersion(
          group,
          shopConfig,
        );

        // 目前 promptConfig 为空字符串，后续需要根据 group.promptVersionId 获取提示词配置
        return {
          shopConfig: JSON.stringify(modifiedShopConfig),
          promptConfig: JSON.stringify({
            ...promptConfig,
            version: omit(promptConfig.version, [
              "versionChange",
              "evaluationTasks",
            ]),
          }),
        };
      }),
    );

    const params: EvaluationDebugParams = {
      msgId: currentId,
      async: true,
      configList,
    };

    // 执行调试
    try {
      const result = await evaluationDebug(params);

      set(debugIdAtom, result.debugId);
    } catch (error) {
      console.error("调试执行失败:", error);
    }
  });

  // 添加调试组
  const addDebugGroupAtom = atom(null, async (get, set) => {
    // 获取版本数据
    const configVersions = await get(configVersionsAtom);
    const promptVersions = await get(promptVersionsAtom);

    // 初始化默认版本
    const { configVersionId, promptVersionId } =
      initializeDebugGroupDefaultVersions(configVersions?.data, promptVersions);

    set(setDebugGroupsAtom, (draft) => {
      draft.push({
        id: uuidv4(),
        name: generateNewGroupName(NEW_DEBUG_GROUP_NAME, draft),
        customConfigs: [],
        shopVariableConfigs: [],
        configVersionId,
        promptVersionId,
      });
    });
  });

  // 删除调试组
  const deleteDebugGroupAtom = atom(null, (get, set, groupId: string) => {
    // 如果删除的调试组是有结果的，则清理调试组结果
    const groupIndex = get(debugGroupsAtom).findIndex(
      (group) => group.id === groupId,
    );

    set(setDebugGroupsAtom, (draft) => {
      if (groupIndex !== -1) {
        draft.splice(groupIndex, 1);
      }
    });

    // 如果删除的调试组是有结果的，则清理调试组结果
    if (groupIndex !== -1 && get(debugGroupResultAtom).data?.[groupIndex]) {
      set(debugIdAtom, null);
    }
  });

  // 复制调试组
  const duplicateDebugGroupAtom = atom(null, (get, set, groupId: string) => {
    const group = get(debugGroupsAtom).find((group) => group.id === groupId);

    if (group) {
      set(setDebugGroupsAtom, (draft) => {
        draft.push({
          ...group,
          id: uuidv4(),
          name: generateNewGroupName(NEW_DEBUG_GROUP_NAME, draft),
        });
      });
    }
  });

  // 更新调试组
  const updateDebugGroupAtom = atom(
    null,
    (_, set, updatedGroup: DebugGroup) => {
      set(setDebugGroupsAtom, (draft) => {
        const index = draft.findIndex((group) => group.id === updatedGroup.id);

        if (index !== -1) {
          draft[index] = updatedGroup;
        }
      });
    },
  );

  // 更新调试组名称
  const updateDebugGroupNameAtom = atom(
    null,
    (_, set, payload: { id: string; name: string }) => {
      set(setDebugGroupsAtom, (draft) => {
        const index = draft.findIndex((group) => group.id === payload.id);

        if (index !== -1) {
          draft[index].name = payload.name;
        }
      });
    },
  );

  // 更新调试组提示词版本
  const updateDebugGroupPromptVersionAtom = atom(
    null,
    (_, set, groupId: string, promptVersionId: string) => {
      set(setDebugGroupsAtom, (draft) => {
        const index = draft.findIndex((group) => group.id === groupId);

        if (index !== -1) {
          draft[index].promptVersionId = promptVersionId;
        }
      });
    },
  );

  // 更新调试组版本
  const updateDebugGroupConfigVersionAtom = atom(
    null,
    (_, set, groupId: string, configVersionId: string) => {
      set(setDebugGroupsAtom, (draft) => {
        const index = draft.findIndex((group) => group.id === groupId);
        let isChanged = false;

        if (index !== -1) {
          draft[index].configVersionId = configVersionId;
          isChanged = true;

          // 如果版本发生变化，则清理自定义配置
          if (isChanged) {
            draft[index].customConfigs = [];
            draft[index].shopVariableConfigs = [];
          }
        }
      });
    },
  );

  // 应用表单数据到调试组
  const applyDebugGroupFormAtom = atom(
    null,
    (
      get,
      set,
      payload: { groupId: string; debugGroupValues: Partial<DebugGroup> },
    ) => {
      const { groupId, debugGroupValues } = payload;
      const group = get(debugGroupsAtom).find((group) => group.id === groupId);

      if (group) {
        const updatedGroup = {
          ...group,
          ...debugGroupValues,
        };

        set(updateDebugGroupAtom, updatedGroup);
      }
    },
  );

  // 监听环节列表变更，设置默认值
  observe((get, set) => {
    const debugStages = get(loadableDebugStagesAtom);

    if (debugStages.state === "hasData") {
      if (get(currentStageAtom) == null) {
        set(currentStageAtom, debugStages.data.data?.[0]?.id);
      }
    }
  });

  // 监听当前消息变更
  observe((get, set) => {
    const currentMessage = get(loadableCurrentMessageAtom);

    // 如果当前消息获取失败，清理当前消息
    if (
      currentMessage.state === "hasError" ||
      (currentMessage.state === "hasData" && currentMessage.data.data == null)
    ) {
      set(latestMessageIdAtom, null);
    }
  });

  return {
    currentIdAtom,
    currentMessageAtom,
    configVersionsAtom,
    promptVersionsAtom,
    debugStagesAtom,
    modelFieldsAtom,
    clearCurrentConfigVersionAtom,
    currentStageAtom,
    debugGroupsAtom,
    editingDebugGroupAtom,
    setEditingDebugGroupIdAtom,
    cancelEditingDebugGroupIdAtom,
    addDebugGroupAtom,
    deleteDebugGroupAtom,
    updateDebugGroupAtom,
    updateDebugGroupNameAtom,
    updateDebugGroupPromptVersionAtom,
    updateDebugGroupConfigVersionAtom,
    applyDebugGroupFormAtom,
    duplicateDebugGroupAtom,
    resolveShopConfigFieldAtom,
    runDebugGroupAtom,
    debugGroupResultAtom,
    evaluationDebugAtom,
    createPublishVersionAtom,
  };
});
