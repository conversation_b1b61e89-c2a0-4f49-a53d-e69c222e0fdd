import { Skeleton } from "@/components/ui/skeleton";

// 标签页骨架屏组件
export const TabSkeleton = () => (
  <div className="flex items-center gap-2 px-3 h-[45px]">
    <Skeleton className="h-4 w-4" />
    <Skeleton className="h-4 w-16" />
  </div>
);

// 标签页列表骨架屏
export const TabsListSkeleton = () => (
  <div className="bg-transparent flex items-center justify-between">
    <div className="flex">
      {[...Array(3)].map((_, index) => (
        <TabSkeleton key={index} />
      ))}
    </div>
    <div className="flex items-center gap-2 mr-4">
      <Skeleton className="h-8 w-20" />
      <Skeleton className="h-8 w-20" />
      <Skeleton className="h-8 w-20" />
    </div>
  </div>
);

// 管理内容骨架屏组件
export const ManagementContentSkeleton = () => (
  <div className="flex-1 p-4 space-y-4">
    <div className="space-y-3">
      <Skeleton className="h-6 w-40" />
      <div className="space-y-2">
        {[...Array(4)].map((_, index) => (
          <div key={index} className="border border-border rounded-lg p-3">
            <div className="flex justify-between items-center mb-2">
              <Skeleton className="h-5 w-32" />
              <Skeleton className="h-4 w-4" />
            </div>
            <Skeleton className="h-4 w-full mb-1" />
            <Skeleton className="h-4 w-3/4" />
          </div>
        ))}
      </div>
    </div>
  </div>
);

// 提示词管理抽屉骨架屏
export const PromptManagementDrawerSkeleton = () => (
  <div className="flex flex-col h-full">
    <TabsListSkeleton />
    <div className="flex flex-1 min-h-0">
      <ManagementContentSkeleton />
    </div>
  </div>
);
