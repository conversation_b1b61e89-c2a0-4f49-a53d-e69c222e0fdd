import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  AbilityManagement,
  ModuleManagement,
  SharedVariableManagement,
} from "@/features/prompt/management";
import {
  createDraftVersionAtom,
  deleteDraftVersionCompleteAtom,
  draftVersionsAtom,
  selectedVersionIdAtom,
  versionsAtom,
} from "@/features/prompt/management/atoms";
import {
  DraftCreateDialog,
  DraftDeleteDialog,
  VersionCreateDialog,
  VersionSyncDialog,
} from "@/features/prompt/management/components";
import { isDraftId } from "@/features/prompt/management/utils";
import { useAtom, useAtomValue, useSetAtom } from "jotai";
import {
  Box,
  Brain,
  Copy,
  LucideIcon,
  Plus,
  Save,
  SendToBack,
} from "lucide-react";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

interface PromptManagementDrawerProps {
  open: boolean;
  initialVersionId?: string;
  onVersionChange: (versionId: string) => void;
}

export const PromptManagementDrawer = ({
  open,
  initialVersionId,
  onVersionChange,
}: PromptManagementDrawerProps) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<string>("ability");
  const [isDraftDialogOpen, setIsDraftDialogOpen] = useState(false);

  // 同步对话框状态
  const [syncDialogOpen, setSyncDialogOpen] = useState(false);
  const [deleteDraftDialogOpen, setDeleteDraftDialogOpen] = useState(false);
  // 对话框状态
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  // 获取当前选中的版本信息
  const createDraftVersion = useSetAtom(createDraftVersionAtom);
  const [selectedVersionId, setSelectedVersionId] = useAtom(
    selectedVersionIdAtom,
  );
  const draftVersions = useAtomValue(draftVersionsAtom);
  const { data: versions, refetch: refetchVersions } =
    useAtomValue(versionsAtom);
  const deleteDraftVersionComplete = useSetAtom(deleteDraftVersionCompleteAtom);

  // 当前选中的是草稿版本
  const hasVersions = (versions?.length ?? 0) + draftVersions.length > 0;

  // 打开创建版本对话框
  const handleOpenCreateVersionDialog = useCallback(() => {
    setIsDialogOpen(true);
  }, []);

  // 获取当前版本详情
  const currentVersion = selectedVersionId
    ? Array.from(draftVersions).find((v) => v.id === selectedVersionId)
    : null;

  // 判断是否为草稿版本
  const isDraftVersion = selectedVersionId
    ? isDraftId(selectedVersionId)
    : false;

  // 获取版本名称
  const currentVersionName = currentVersion?.versionName || "未知版本";

  // 处理保存版本按钮点击
  const handleSaveVersion = () => {
    if (!isDraftVersion || !selectedVersionId) {
      toast.error("只有草稿版本才能同步");

      return;
    }
    setSyncDialogOpen(true);
  };

  // 处理删除草稿按钮点击
  const handleDeleteDraft = () => {
    if (!isDraftVersion || !selectedVersionId) {
      toast.error("只有草稿版本才能删除");

      return;
    }

    setDeleteDraftDialogOpen(true);
  };

  // 处理同步完成
  const handleSyncComplete = useCallback(
    async (success: boolean, syncedVersionId?: string) => {
      if (success) {
        toast.success(`版本 "${currentVersionName}" 同步成功！`);

        if (syncedVersionId) {
          await refetchVersions();
          setSelectedVersionId(syncedVersionId);
        }
      } else {
        toast.error(`版本 "${currentVersionName}" 同步失败，请查看详细信息`);
      }
    },
    [currentVersionName, refetchVersions, setSelectedVersionId],
  );

  // 打开创建草稿对话框
  const handleOpenDraftDialog = useCallback(() => {
    setIsDraftDialogOpen(true);
  }, []);

  // 确认创建版本
  const handleConfirmCreateVersion = useCallback(
    (versionName: string) => {
      createDraftVersion(versionName);
    },
    [createDraftVersion],
  );

  const handleDeleteDraftConfirm = useCallback(() => {
    if (!selectedVersionId) {
      toast.error("请先选择一个版本");

      return;
    }

    setDeleteDraftDialogOpen(false);
    deleteDraftVersionComplete(selectedVersionId);
  }, [deleteDraftVersionComplete, selectedVersionId]);

  const tabOptions: Array<{
    value: string;
    label: string;
    icon: LucideIcon;
  }> = useMemo(
    () => [
      {
        value: "ability",
        label: t("prompt.ability"),
        icon: Brain,
      },
      {
        value: "module",
        label: t("prompt.module"),
        icon: Box,
      },
      {
        value: "shared-variable",
        label: t("prompt.shared.variable"),
        icon: SendToBack,
      },
    ],
    [t],
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case "ability":
        return <AbilityManagement />;
      case "module":
        return <ModuleManagement />;
      case "shared-variable":
        return <SharedVariableManagement />;
      default:
        return <AbilityManagement />;
    }
  };

  // 初始化选中版本
  useEffect(() => {
    if (open && initialVersionId) {
      setSelectedVersionId(initialVersionId);
    }
  }, [open, initialVersionId, setSelectedVersionId]);

  // 当选中版本变化时
  useEffect(() => {
    if (selectedVersionId) {
      onVersionChange(selectedVersionId);
    }
  }, [selectedVersionId, onVersionChange]);

  return (
    <>
      <div className="flex flex-col h-full">
        <Tabs
          value={activeTab}
          onValueChange={setActiveTab}
          className="flex flex-col h-full"
        >
          <TabsList
            className="bg-transparent"
            extra={
              <div className="flex items-center gap-2 mr-4">
                {selectedVersionId && isDraftId(selectedVersionId) && (
                  <Button
                    size="sm"
                    variant="destructive"
                    onClick={handleDeleteDraft}
                  >
                    删除草稿版本
                  </Button>
                )}

                {hasVersions && (
                  <Button
                    variant="outline"
                    size="sm"
                    icon={<Copy />}
                    onClick={handleOpenDraftDialog}
                  >
                    创建草稿
                  </Button>
                )}

                {!hasVersions && (
                  <Button
                    variant="outline"
                    size="sm"
                    icon={<Plus />}
                    onClick={handleOpenCreateVersionDialog}
                  >
                    创建版本
                  </Button>
                )}

                <Button
                  size="sm"
                  icon={<Save />}
                  onClick={handleSaveVersion}
                  disabled={!isDraftVersion || !selectedVersionId}
                >
                  保存版本
                </Button>
              </div>
            }
          >
            {tabOptions.map((option) => (
              <TabsTrigger
                key={option.value}
                value={option.value}
                className="h-[45px]"
              >
                <div className="flex items-center gap-2 px-3">
                  <option.icon className="size-4" />
                  <span>{option.label}</span>
                </div>
              </TabsTrigger>
            ))}
          </TabsList>

          <div className="flex flex-1 min-h-0">{renderTabContent()}</div>
        </Tabs>
      </div>

      {/* 同步对话框 */}
      <VersionSyncDialog
        open={syncDialogOpen}
        versionId={selectedVersionId || ""}
        onOpenChange={setSyncDialogOpen}
        onSyncComplete={handleSyncComplete}
      />

      {/* 删除草稿对话框 */}
      <DraftDeleteDialog
        open={deleteDraftDialogOpen}
        draftVersion={currentVersion}
        onOpenChange={setDeleteDraftDialogOpen}
        onConfirm={handleDeleteDraftConfirm}
      />

      <DraftCreateDialog
        open={isDraftDialogOpen}
        onOpenChange={setIsDraftDialogOpen}
      />

      <VersionCreateDialog
        open={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        onConfirm={handleConfirmCreateVersion}
      />
    </>
  );
};
