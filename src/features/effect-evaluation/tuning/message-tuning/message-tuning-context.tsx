import { SideDrawer } from "@/components/common/side-drawer";
import { MessageToolkitPanel } from "@/features/inspect-annotate-toolkit";
import { MessageLogData } from "@/types/api/configure-item";
import { useMolecule } from "bunshi/react";
import { useSetAtom } from "jotai";
import {
  createContext,
  ReactNode,
  Suspense,
  useCallback,
  useContext,
  useMemo,
  useState,
} from "react";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";
import {
  PromptManagementDrawer,
  PromptManagementDrawerSkeleton,
} from "./components";
import { evaluationTuningMolecule } from "./store";
import { DebugGroupDiffDialog, VersionCreateDialog } from "./tuning-output";
import { DebugGroup } from "./types";

interface MessageTuningContextType {
  // 调试组变更弹窗
  debugGroupChangeDialog: {
    isOpen: boolean;
    showDialog: (group: DebugGroup) => void;
    hideDialog: () => void;
  };

  // 版本创建对话框相关状态和方法
  versionDialog: {
    isOpen: boolean;
    selectedGroup?: DebugGroup;
    showDialog: (group: DebugGroup) => void;
    hideDialog: () => void;
  };

  // 日志面板相关状态和方法
  logPanel: {
    isOpen: boolean;
    showDialog: (logData: MessageLogData) => void;
    hideDialog: () => void;
  };

  // 提示词编辑抽屉
  promptDrawer: {
    isOpen: boolean;
    showDrawer: (group: DebugGroup) => void;
    hideDrawer: () => void;
  };
}

const MessageTuningContext = createContext<
  MessageTuningContextType | undefined
>(undefined);

export const MessageTuningProvider = ({
  children,
}: {
  children: ReactNode;
}) => {
  const { t } = useTranslation();
  const { updateDebugGroupPromptVersionAtom } = useMolecule(
    evaluationTuningMolecule,
  );
  const updateDebugGroupPromptVersion = useSetAtom(
    updateDebugGroupPromptVersionAtom,
  );

  // 版本创建对话框状态
  const [isVersionDialogOpen, setIsVersionDialogOpen] = useState(false);
  const [selectedGroup, setSelectedGroup] = useState<DebugGroup | undefined>(
    undefined,
  );

  // 日志面板状态
  const [isLogPanelOpen, setIsLogPanelOpen] = useState(false);
  const [logPanelData, setLogPanelData] = useState<
    MessageLogData | undefined
  >();

  // 调试组变更弹窗状态
  const [isDebugGroupChangeDialogOpen, setIsDebugGroupChangeDialogOpen] =
    useState(false);
  const [debugGroupChangeDialogData, setDebugGroupChangeDialogData] = useState<
    DebugGroup | undefined
  >();

  // 提示词编辑抽屉状态
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [drawerGroup, setDrawerGroup] = useState<DebugGroup | undefined>(
    undefined,
  );

  // 版本创建对话框方法
  const showVersionDialog = useCallback((group: DebugGroup) => {
    setSelectedGroup(group);
    setIsVersionDialogOpen(true);
  }, []);

  const hideVersionDialog = useCallback(() => {
    setIsVersionDialogOpen(false);
    setSelectedGroup(undefined);
  }, []);

  // 日志面板方法
  const showLogPanel = useCallback((logData: MessageLogData) => {
    setLogPanelData(logData);
    setIsLogPanelOpen(true);
  }, []);

  const hideLogPanel = useCallback(() => {
    setIsLogPanelOpen(false);
    setLogPanelData(undefined);
  }, []);

  // 调试组变更弹窗方法
  const showDebugGroupChangeDialog = useCallback(
    (group: DebugGroup) => {
      if (!group.promptVersionId || !group.configVersionId) {
        toast.info(t("debug.version.config.tooltip"));

        return;
      }

      setDebugGroupChangeDialogData(group);
      setIsDebugGroupChangeDialogOpen(true);
    },
    [t],
  );

  const hideDebugGroupChangeDialog = useCallback(() => {
    setIsDebugGroupChangeDialogOpen(false);
    setDebugGroupChangeDialogData(undefined);
  }, []);

  // 提示词编辑抽屉方法
  const showPromptDrawer = useCallback(
    (group: DebugGroup) => {
      if (group.promptVersionId == null) {
        toast.info(t("debug.version.prompt.tooltip"));

        return;
      }

      setDrawerGroup(group);
      setIsDrawerOpen(true);
    },
    [t],
  );

  const hidePromptDrawer = useCallback(() => {
    setIsDrawerOpen(false);
    setDrawerGroup(undefined);
  }, []);

  // 构建上下文值
  const contextValue: MessageTuningContextType = useMemo(
    () => ({
      versionDialog: {
        isOpen: isVersionDialogOpen,
        selectedGroup,
        showDialog: showVersionDialog,
        hideDialog: hideVersionDialog,
      },
      logPanel: {
        isOpen: isLogPanelOpen,
        showDialog: showLogPanel,
        hideDialog: hideLogPanel,
      },
      debugGroupChangeDialog: {
        isOpen: isDebugGroupChangeDialogOpen,
        showDialog: showDebugGroupChangeDialog,
        hideDialog: hideDebugGroupChangeDialog,
      },
      promptDrawer: {
        isOpen: isDrawerOpen,
        showDrawer: showPromptDrawer,
        hideDrawer: hidePromptDrawer,
      },
    }),
    [
      isVersionDialogOpen,
      selectedGroup,
      showVersionDialog,
      hideVersionDialog,
      isLogPanelOpen,
      showLogPanel,
      hideLogPanel,
      isDebugGroupChangeDialogOpen,
      showDebugGroupChangeDialog,
      hideDebugGroupChangeDialog,
      isDrawerOpen,
      showPromptDrawer,
      hidePromptDrawer,
    ],
  );

  return (
    <MessageTuningContext.Provider value={contextValue}>
      {children}

      <VersionCreateDialog />

      <DebugGroupDiffDialog group={debugGroupChangeDialogData} />

      <MessageToolkitPanel
        title="日志"
        open={isLogPanelOpen}
        logData={logPanelData}
        onClose={hideLogPanel}
        floatPanelProps={{
          position: "left",
          defaultOffset: undefined,
          defaultSize: { width: 600 },
        }}
      />

      <SideDrawer
        open={isDrawerOpen}
        title="提示词管理"
        minWidth="80vw"
        maxWidth="1400px"
        modal={false}
        onClose={hidePromptDrawer}
      >
        <Suspense fallback={<PromptManagementDrawerSkeleton />}>
          <PromptManagementDrawer
            open={isDrawerOpen}
            initialVersionId={drawerGroup?.promptVersionId}
            onVersionChange={(promptVersionId) => {
              if (drawerGroup == null) {
                return;
              }

              updateDebugGroupPromptVersion(drawerGroup.id, promptVersionId);
            }}
          />
        </Suspense>
      </SideDrawer>
    </MessageTuningContext.Provider>
  );
};

// 使用消息调优上下文的Hook
export const useMessageTuning = () => {
  const context = useContext(MessageTuningContext);

  if (context === undefined) {
    throw new Error(
      "useMessageTuning must be used within a MessageTuningProvider",
    );
  }

  return context;
};
