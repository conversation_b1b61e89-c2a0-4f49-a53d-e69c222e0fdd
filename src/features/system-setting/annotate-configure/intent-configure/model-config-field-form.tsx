import {
  CommonFieldRenderers,
  FieldFormBase,
} from "@/components/common/field-configure";
import { ConfigureFormProps } from "@/components/common/field-configure-editor";
import { BaseFieldConfigure } from "@/types/api/config";

export const IntentConfigureForm = <T extends BaseFieldConfigure>({
  ...props
}: ConfigureFormProps<T>) => {
  return (
    <FieldFormBase<T> {...props}>
      <CommonFieldRenderers.StringField label="意图名称" name="label" />
      <CommonFieldRenderers.StringField
        label="意图编码"
        name="code"
        labelPath="label"
        enableAI={true}
      />
    </FieldFormBase>
  );
};
