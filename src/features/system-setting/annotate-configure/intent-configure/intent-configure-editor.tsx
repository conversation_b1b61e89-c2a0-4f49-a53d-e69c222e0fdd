import {
  BaseFieldSchema,
  createFieldListSchema,
  FieldConfigureBase,
} from "@/components/common/field-configure";
import { ConfigureService } from "@/services";
import { useMemo } from "react";
import { z } from "zod";
import { IntentConfigureForm } from "./model-config-field-form";

// 模型配置字段 schema
const IntentConfigureSchema = BaseFieldSchema.extend({
  code: z.string({
    message: "意图编码不能为空",
  }),
});

const IntentConfigureListSchema = createFieldListSchema(
  IntentConfigureSchema,
).check((ctx) => {
  const labelSet = new Set<string>();
  const codeSet = new Set<string>();

  ctx.value.fields.forEach((field) => {
    if (labelSet.has(field.code)) {
      ctx.issues.push({
        code: "custom",
        message: `意图编码 "${field.code}" 重复`,
        input: field,
      });
    } else {
      labelSet.add(field.code);
    }
  });

  ctx.value.fields.forEach((field) => {
    if (labelSet.has(field.label)) {
      ctx.issues.push({
        code: "custom",
        message: `意图名称 "${field.label}" 重复`,
        input: field,
      });
    } else {
      codeSet.add(field.label);
    }
  });
});

export const IntentConfigureEditor = ({ id }: { id: string }) => {
  const defaultField = useMemo(
    () => ({
      label: "未命名意图",
    }),
    [],
  );

  return (
    <FieldConfigureBase
      id={id}
      title="意图列表"
      defaultField={defaultField}
      schema={IntentConfigureListSchema}
      formSchema={IntentConfigureSchema}
      queryKey="SystemSetting.getIntentConfig"
      mutationKey="SystemSetting.saveIntentConfig"
      fetchFn={ConfigureService.getIntentConfig}
      saveFn={ConfigureService.saveIntentConfig}
      fieldName="意图"
      ConfigureForm={IntentConfigureForm}
    />
  );
};
