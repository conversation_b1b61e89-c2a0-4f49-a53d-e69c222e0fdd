import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { useNavigate, useSearch } from "@tanstack/react-router";
import { useCallback, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { AscribeConfigure } from "./ascribe-configure";
import { IntentConfigure } from "./intent-configure";
import { SceneConfigure } from "./scene-configure";
import { PERMISSION_CODES } from "@/constants/permission";
import { usePermission } from "@/hooks/use-permission";

export enum AnnotateConfigureType {
  ASC_CONFIG = "ASC_CONFIG",
  SCENE_CONFIG = "SCENE_CONFIG",
  INTENT_CONFIG = "INTENT_CONFIG",
}

export const AnnotateConfigure = () => {
  const { t } = useTranslation();
  const navigate = useNavigate({
    from: "/system-setting/annotate-configure",
  });
  const { tab } = useSearch({
    from: "/_app/system-setting/annotate-configure",
  });
  const { hasPermission } = usePermission();

  const settingTabs = useMemo(
    () => [
      {
        label: t("ascribe.configure"),
        value: AnnotateConfigureType.ASC_CONFIG,
        content: <AscribeConfigure />,
        permissionCode:
          PERMISSION_CODES.SYSTEM_SETTING_ANNOTATE_CONFIGURE_ASCRIBE,
      },
      {
        label: t("scene.configure"),
        value: AnnotateConfigureType.SCENE_CONFIG,
        content: <SceneConfigure />,
        permissionCode:
          PERMISSION_CODES.SYSTEM_SETTING_ANNOTATE_CONFIGURE_SCENE,
      },
      {
        label: t("intent.list.configure"),
        value: AnnotateConfigureType.INTENT_CONFIG,
        content: <IntentConfigure />,
        permissionCode:
          PERMISSION_CODES.SYSTEM_SETTING_ANNOTATE_CONFIGURE_INTENT,
      },
    ],
    [t],
  );

  const accessibleTabs = useMemo(
    () =>
      settingTabs.filter((settingTab) =>
        hasPermission(settingTab.permissionCode),
      ),
    [hasPermission, settingTabs],
  );

  const activeTab = useMemo(() => {
    const isTabAccessible = accessibleTabs.some(
      (accessibleTab) => accessibleTab.value === tab,
    );
    if (tab && isTabAccessible) {
      return tab;
    }
    return accessibleTabs.length > 0
      ? accessibleTabs[0].value
      : AnnotateConfigureType.ASC_CONFIG;
  }, [accessibleTabs, tab]);

  const handleTabChange = useCallback(
    (value: string) => {
      navigate({
        search: {
          tab: value as AnnotateConfigureType,
        },
      });
    },
    [navigate],
  );

  return (
    <Tabs
      value={activeTab}
      className="flex flex-col h-full gap-4 p-6"
      onValueChange={handleTabChange}
    >
      {accessibleTabs.length > 0 ? (
        <>
          <TabsList variant="solid">
            {accessibleTabs.map((settingTab) => (
              <TabsTrigger
                className="text-base"
                key={settingTab.value}
                value={settingTab.value}
              >
                {settingTab.label}
              </TabsTrigger>
            ))}
          </TabsList>

          {accessibleTabs.map((settingTab) => (
            <TabsContent
              className="flex flex-col gap-4 flex-1 min-h-0"
              key={settingTab.value}
              value={settingTab.value}
            >
              {settingTab.content}
            </TabsContent>
          ))}
        </>
      ) : (
        <div className="flex flex-col gap-4 flex-1 min-h-0">
          <div className="text-center text-sm text-gray-500">
            {t("no.permission")}
          </div>
        </div>
      )}
    </Tabs>
  );
};
