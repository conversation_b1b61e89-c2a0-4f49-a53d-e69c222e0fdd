import { shop<PERSON>tom } from "@/atoms/shop";
import { EnumConfigureBase } from "@/components/common/enum-configure";
import { sceneQueryKeys } from "@/constants/query-keys";
import { AnnotateService } from "@/services/annotate-service";
import { SaveSceneConfigParams } from "@/types/api/config";
import { useAtomValue } from "jotai";
import { isEmpty } from "lodash";
import { useCallback, useMemo } from "react";
import { z } from "zod";
import { SceneConfigureForm } from "./scene-configure-form";
import { SceneItemWithId } from "./types";

const sceneConfigureSchema = z
  .array(
    z.object({
      id: z.string(),
      sceneLabelId: z.string().optional(),
      sceneName: z
        .string({ error: "场景名称必须是字符串" })
        .min(1, { error: "场景名称不能为空" }),
      description: z.string().optional(),
    }),
  )
  .check((ctx) => {
    const labelSet = new Set<string>();

    ctx.value.forEach((field) => {
      if (labelSet.has(field.sceneName)) {
        ctx.issues.push({
          code: "custom",
          message: `场景名称 "${field.sceneName}" 重复`,
          input: field,
        });
      } else {
        labelSet.add(field.sceneName);
      }
    });
  });

export const SceneEnumConfigure = ({ id }: { id: string }) => {
  const shop = useAtomValue(shopAtom);
  const queryKey = useMemo(() => sceneQueryKeys.list(shop?.id ?? ""), [shop]);
  const mutationKey = useMemo(
    () => sceneQueryKeys.update(shop?.id ?? ""),
    [shop],
  );

  const getDuplicateDefaultValues = useCallback(
    (value: Partial<SceneItemWithId>) => ({
      sceneName: `${value.sceneName}（复制）`,
    }),
    [],
  );

  const isUnSyncedItem = useCallback(
    (item: SceneItemWithId) => isEmpty(item.sceneLabelId),
    [],
  );

  const fetchFn = useCallback(() => {
    if (!shop?.id) {
      return Promise.resolve(null);
    }

    return AnnotateService.getSceneList(shop.id);
  }, [shop]);

  const saveFn = useCallback(
    (params: SaveSceneConfigParams) => {
      if (shop == null) {
        throw new Error("Shop not found");
      }

      return AnnotateService.saveSceneConfig(
        params.map((param) => ({
          ...param,
          shopId: shop.id,
        })),
      );
    },
    [shop],
  );

  const deleteSingleFn = useCallback(
    (data: Omit<SceneItemWithId, "id">) => {
      if (shop == null) {
        throw new Error("Shop not found");
      }

      return AnnotateService.deleteSceneConfig({
        ...data,
        shopId: shop.id,
      });
    },
    [shop],
  );

  return (
    <EnumConfigureBase<SceneItemWithId>
      id={id}
      title="场景配置"
      defaultField={{
        sceneName: "未命名场景标签",
      }}
      idKey="sceneLabelId"
      labelKey="sceneName"
      schema={sceneConfigureSchema}
      formSchema={sceneConfigureSchema.element}
      isUnSyncedItem={isUnSyncedItem}
      queryKey={queryKey}
      mutationKey={mutationKey}
      fetchFn={fetchFn}
      saveFn={saveFn}
      deleteSingleFn={deleteSingleFn}
      ConfigureForm={SceneConfigureForm}
      getDuplicateDefaultValues={getDuplicateDefaultValues}
    />
  );
};

SceneEnumConfigure.displayName = "SceneConfigure";
