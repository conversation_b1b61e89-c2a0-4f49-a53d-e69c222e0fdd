import { EnumFormBase } from "@/components/common/enum-configure";
import { CommonFieldRenderers } from "@/components/common/field-configure";
import { ConfigureFormProps } from "@/components/common/field-configure-editor";
import { SceneItemWithId } from "./types";

export const SceneConfigureForm = ({
  ...props
}: ConfigureFormProps<SceneItemWithId>) => {
  return (
    <EnumFormBase<SceneItemWithId> {...props}>
      <CommonFieldRenderers.StringField label="场景名称" name="sceneName" />
      <CommonFieldRenderers.StringField
        label="场景描述"
        name="description"
        textarea={true}
      />
    </EnumFormBase>
  );
};

SceneConfigureForm.displayName = "SceneConfigureForm";
