import { EnumFormBase } from "@/components/common/enum-configure";
import { CommonFieldRenderers } from "@/components/common/field-configure";
import { ConfigureFormProps } from "@/components/common/field-configure-editor";
import { isOtherReason } from "@/features/inspect-annotate-toolkit/message-annotate";
import { AscribeItemWithId } from "./types";

export const AscribeConfigureForm = ({
  ...props
}: ConfigureFormProps<AscribeItemWithId>) => {
  return (
    <EnumFormBase<AscribeItemWithId> {...props}>
      <CommonFieldRenderers.StringField label="归因名称" name="itemName" />
      <CommonFieldRenderers.StringField
        label="归因编码"
        name="itemCode"
        isDisabled={(field) => isOtherReason(field.value)}
        labelPath="itemName"
        enableAI={true}
      />
    </EnumFormBase>
  );
};
