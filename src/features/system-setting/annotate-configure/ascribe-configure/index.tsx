import { FieldConfigureEditorSkeleton } from "@/components/common/field-configure-editor";
import { Suspense } from "react";
import { AscribeEnumConfigure } from "./ascribe-enum-configure";

export const AscribeConfigure = () => {
  const id = "ascribe-configure";

  return (
    <Suspense fallback={<FieldConfigureEditorSkeleton id={id} />}>
      <AscribeEnumConfigure id={id} />
    </Suspense>
  );
};
