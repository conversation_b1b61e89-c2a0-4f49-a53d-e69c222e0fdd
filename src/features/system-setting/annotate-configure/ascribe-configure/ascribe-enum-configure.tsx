import { shopAtom } from "@/atoms/shop";
import { EnumConfigureBase } from "@/components/common/enum-configure";
import { ascribeQueryKeys } from "@/constants/query-keys";
import { AnnotateService } from "@/services/annotate-service";
import { SaveAscribeListParams } from "@/types/api/config";
import { useAtomValue } from "jotai";
import { isEmpty } from "lodash";
import { useCallback, useMemo } from "react";
import { useTranslation } from "react-i18next";
import z from "zod";
import { AscribeConfigureForm } from "./ascribe-configure-form";
import { AscribeItemWithId } from "./types";

const ascribeConfigureSchema = z
  .array(
    z.object({
      id: z.string(),
      ascribeItemId: z.string().optional(),
      itemCode: z
        .string({ error: "归因编码必须是字符串" })
        .min(1, { error: "归因编码不能为空" }),
      itemName: z
        .string({ error: "归因名称必须是字符串" })
        .min(1, { error: "归因名称不能为空" }),
    }),
  )
  .check((ctx) => {
    const labelSet = new Set<string>();
    const codeSet = new Set<string>();

    ctx.value.forEach((field) => {
      if (labelSet.has(field.itemName)) {
        ctx.issues.push({
          code: "custom",
          message: `归因名称 "${field.itemName}" 重复`,
          input: field,
        });
      }
    });

    ctx.value.forEach((field) => {
      if (codeSet.has(field.itemCode)) {
        ctx.issues.push({
          code: "custom",
          message: `归因编码 "${field.itemCode}" 重复`,
          input: field,
        });
      } else {
        codeSet.add(field.itemCode);
      }
    });

    if (!codeSet.has("other")) {
      ctx.issues.push({
        code: "custom",
        message: "请至少保留一个归因编码为 other 的配置",
        input: null,
      });
    }
  });

export const AscribeEnumConfigure = ({ id }: { id: string }) => {
  const { t } = useTranslation();
  const shop = useAtomValue(shopAtom);

  const queryKey = useMemo(() => ascribeQueryKeys.list(shop?.id ?? ""), [shop]);
  const mutationKey = useMemo(
    () => ascribeQueryKeys.update(shop?.id ?? ""),
    [shop],
  );
  const fetchFn = useCallback(() => {
    if (!shop?.id) {
      return Promise.resolve(null);
    }

    return AnnotateService.getAscribeList(shop.id);
  }, [shop]);

  const saveFn = useCallback(
    (params: SaveAscribeListParams) => {
      if (shop == null) {
        throw new Error("Shop not found");
      }

      return AnnotateService.saveAscribeList(
        params.map((param) => ({
          ...param,
          shopId: shop.id,
        })),
      );
    },
    [shop],
  );

  const deleteSingleFn = useCallback(
    (data: Omit<AscribeItemWithId, "id">) => {
      if (shop == null) {
        throw new Error("Shop not found");
      }

      return AnnotateService.deleteAscribe({
        ...data,
        shopId: shop.id,
      });
    },
    [shop],
  );

  const getDuplicateDefaultValues = useCallback(
    (value: Partial<AscribeItemWithId>) => ({
      itemName: `${value.itemName}（复制）`,
    }),
    [],
  );

  const isUnSyncedItem = useCallback(
    (item: AscribeItemWithId) => isEmpty(item.ascribeItemId),
    [],
  );

  return (
    <EnumConfigureBase<AscribeItemWithId>
      id={id}
      idKey="ascribeItemId"
      schema={ascribeConfigureSchema}
      title={t("ascribe.configure")}
      defaultField={{
        itemName: "未命名归因",
      }}
      labelKey="itemName"
      formSchema={ascribeConfigureSchema.element}
      isUnSyncedItem={isUnSyncedItem}
      queryKey={queryKey}
      mutationKey={mutationKey}
      getDuplicateDefaultValues={getDuplicateDefaultValues}
      fetchFn={fetchFn}
      saveFn={saveFn}
      deleteSingleFn={deleteSingleFn}
      ConfigureForm={AscribeConfigureForm}
    />
  );
};
