import {
  CommonFieldRenderers,
  FieldFormBase,
} from "@/components/common/field-configure";
import { ConfigureFormProps } from "@/components/common/field-configure-editor";
import { BaseFieldConfigure } from "@/types/api/config";
import { ConfigureItemType } from "@/types/api/configure-item";

export const MessageLogFieldForm = <T extends BaseFieldConfigure>({
  ...props
}: ConfigureFormProps<T>) => {
  return (
    <FieldFormBase<T> {...props}>
      <CommonFieldRenderers.StringField label="配置项名称" name="label" />
      <CommonFieldRenderers.IconField label="图标" name="display.icon" />
      <CommonFieldRenderers.JSONLogicField
        label="配置项值路径"
        name="value"
        fieldType={ConfigureItemType.STRING}
      />
    </FieldFormBase>
  );
};
