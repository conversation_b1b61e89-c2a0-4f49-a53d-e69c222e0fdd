import { FieldConfigureEditorSkeleton } from "@/components/common/field-configure-editor";
import { Suspense } from "react";
import { MessageLogFieldConfigure } from "./message-log-field-configure";

export const MessageLogField = () => {
  const id = "message-log-field-configure";

  return (
    <Suspense fallback={<FieldConfigureEditorSkeleton id={id} />}>
      <MessageLogFieldConfigure id={id} />
    </Suspense>
  );
};
