import {
  BaseFieldSchema,
  createFieldListSchema,
  createLogicJSONFieldSchema,
  FieldConfigureBase,
} from "@/components/common/field-configure";
import { ConfigureService } from "@/services";
import { useMemo } from "react";
import { z } from "zod";
import { MessageLogFieldForm } from "./message-log-field-form";

// 模型配置字段 schema
const MessageLogFieldSchema = BaseFieldSchema.extend({
  value: createLogicJSONFieldSchema("消息日志字段"),
});

// 消息日志字段表单验证 schema
const MessageLogFieldFormSchema = MessageLogFieldSchema.extend({
  value: z
    .string({
      error: "消息日志字段必须为字符串",
    })
    .min(1, { error: "消息日志字段不能为空" }),
});

const MessageLogFieldListSchema = createFieldListSchema(MessageLogFieldSchema);

export const MessageLogFieldConfigure = ({ id }: { id: string }) => {
  const defaultField = useMemo(
    () => ({
      label: "未命名字段",
    }),
    [],
  );

  return (
    <FieldConfigureBase
      id={id}
      title="消息日志字段列表"
      defaultField={defaultField}
      schema={MessageLogFieldListSchema}
      formSchema={MessageLogFieldFormSchema}
      queryKey="SystemSetting.getMessageLogFieldConfig"
      mutationKey="SystemSetting.saveMessageLogFieldConfig"
      fetchFn={ConfigureService.getMessageLogFilterFieldConfig}
      saveFn={ConfigureService.saveMessageLogFilterFieldConfig}
      jsonFieldPaths={["value"]}
      fieldName="消息日志字段"
      ConfigureForm={MessageLogFieldForm}
    />
  );
};
