import { shop<PERSON>tom } from "@/atoms/shop";
import {
  BaseFieldSchema,
  createFieldListSchema,
  createLogicJSONFieldSchema,
  FieldConfigureBase,
} from "@/components/common/field-configure";
import { FIELD_TYPES_MAP } from "@/constants/configure";
import { ConfigureService } from "@/services";
import { EvaluationConfigService } from "@/services/evaluation-config-service";
import {
  FieldConfigureValue,
  ModelConfigField,
  ModelConfigFieldType,
} from "@/types/api/config";
import { ConfigureItemType } from "@/types/api/configure-item";
import { ConfigVersionStatus } from "@/types/api/version";
import { safeParseJson } from "@/utils";
import { useQuery } from "@tanstack/react-query";
import { useAtomValue } from "jotai";
import { useCallback, useMemo } from "react";
import { z } from "zod";
import { ModelConfigFieldForm } from "./model-config-field-form";

const FIELD_TYPES = [
  ConfigureItemType.MODEL,
  ConfigureItemType.PROMPT,
  ConfigureItemType.STRING,
  ConfigureItemType.NUMBER,
  ConfigureItemType.JSON,
  ConfigureItemType.BOOLEAN,
] satisfies ModelConfigFieldType[];

// 模型配置字段 schema
const ModelConfigFieldSchema = BaseFieldSchema.extend({
  value: createLogicJSONFieldSchema("模型配置").refine(
    (value) => {
      const keys = Object.keys(value);

      return (
        keys.length === 1 &&
        keys.includes("var") &&
        typeof value["var"] === "string"
      );
    },
    {
      message: "模型配置仅支持 var 操作符",
    },
  ),
  type: z.enum(FIELD_TYPES, {
    message: "配置项类型不能为空",
  }),
});

// 模型配置字段表单验证 schema
const ModelConfigFieldFormSchema = ModelConfigFieldSchema.extend({
  // 因为在 json 编辑器中，value 类型为 string
  value: z
    .string({
      error: "模型配置必须为字符串",
    })
    .min(1, { error: "模型配置不能为空" }),
});

const ModelConfigFieldListSchema = createFieldListSchema(
  ModelConfigFieldSchema,
);

export const ModelConfigFieldConfigure = ({ id }: { id: string }) => {
  const defaultField = useMemo(
    () => ({
      label: "未命名配置项",
    }),
    [],
  );
  const shop = useAtomValue(shopAtom);

  /**
   * 配置版本列表
   */
  const { data: configVersions } = useQuery({
    queryKey: ["evaluationConfigVersions", shop?.id],
    queryFn: () =>
      EvaluationConfigService.getVersionList(
        {
          version: "",
        },
        {
          shopId: shop?.id,
        },
      ),
  });

  /**
   * 当前上线版本
   */
  const { data: onlineVersion } = useQuery({
    queryKey: ["evaluationConfigOnlineVersion", configVersions, shop],
    queryFn: async () => {
      const currentVersion = configVersions?.find(
        (version) => version.onlineStatus === ConfigVersionStatus.ONLINE,
      );

      if (!currentVersion || !shop) {
        return null;
      }

      return EvaluationConfigService.getVersionDetail({
        configId: currentVersion.configId,
        shopId: shop.id,
      });
    },
  });

  const shopConfig = safeParseJson(onlineVersion?.shopConfig, {});

  const itemTagRender = useCallback((configureItem: FieldConfigureValue) => {
    const { label, icon: Icon } =
      FIELD_TYPES_MAP[(configureItem as ModelConfigField).type] || {};

    if (!label || !Icon) {
      return null;
    }

    return (
      <span className="flex items-center gap-1">
        <Icon className="size-3" />
        {label}
      </span>
    );
  }, []);

  return (
    <FieldConfigureBase
      id={id}
      title="模型配置项列表"
      titleTooltip={<p>预览数据使用当前线上版本</p>}
      defaultField={defaultField}
      schema={ModelConfigFieldListSchema}
      formSchema={ModelConfigFieldFormSchema}
      runtimeData={shopConfig}
      itemTagRender={itemTagRender}
      queryKey="SystemSetting.getModelFieldConfig"
      mutationKey="SystemSetting.saveModelFieldConfig"
      fetchFn={ConfigureService.getModelFieldConfig}
      saveFn={ConfigureService.saveModelFieldConfig}
      jsonFieldPaths={["value"]}
      fieldName="配置项"
      ConfigureForm={ModelConfigFieldForm}
    />
  );
};
