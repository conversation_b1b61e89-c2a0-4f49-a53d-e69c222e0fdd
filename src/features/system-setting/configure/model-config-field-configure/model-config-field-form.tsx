import {
  CommonFieldRenderers,
  FieldFormBase,
} from "@/components/common/field-configure";
import { ConfigureFormProps } from "@/components/common/field-configure-editor";
import { BaseFieldConfigure, ModelConfigFieldType } from "@/types/api/config";
import { ConfigureItemType } from "@/types/api/configure-item";
import {
  Braces,
  Hash,
  LucideIcon,
  MessagesSquare,
  Package,
  ToggleLeft,
  Type,
} from "lucide-react";
import { ReactNode, useMemo } from "react";

export const ModelConfigFieldForm = <T extends BaseFieldConfigure>({
  ...props
}: ConfigureFormProps<T>) => {
  const modelConfigTypeOptions = useMemo<
    Array<{
      value: ModelConfigFieldType;
      label: ReactNode;
      icon: LucideIcon;
      disabled?: boolean;
    }>
  >(
    () => [
      {
        value: ConfigureItemType.MODEL,
        label: "模型",
        icon: Package,
      },
      {
        value: ConfigureItemType.PROMPT,
        label: <span className="text-muted-foreground">提示词（已弃用）</span>,
        icon: MessagesSquare,
        disabled: true,
      },
      {
        value: ConfigureItemType.STRING,
        label: "字符串",
        icon: Type,
      },
      {
        value: ConfigureItemType.NUMBER,
        label: "数字",
        icon: Hash,
      },
      {
        value: ConfigureItemType.JSON,
        label: "JSON",
        icon: Braces,
      },
      {
        value: ConfigureItemType.BOOLEAN,
        label: "布尔值",
        icon: ToggleLeft,
      },
    ],
    [],
  );

  return (
    <FieldFormBase<T> {...props}>
      <CommonFieldRenderers.StringField label="配置项名称" name="label" />
      <CommonFieldRenderers.TypeSelectField
        label="配置项类型"
        name="type"
        options={modelConfigTypeOptions}
      />
      <CommonFieldRenderers.IconField label="图标" name="display.icon" />
      <CommonFieldRenderers.JSONLogicField
        label="配置项值路径"
        name="value"
        tooltip="配置项值路径仅支持 var 操作符"
        fieldType={ConfigureItemType.STRING}
      />
    </FieldFormBase>
  );
};
