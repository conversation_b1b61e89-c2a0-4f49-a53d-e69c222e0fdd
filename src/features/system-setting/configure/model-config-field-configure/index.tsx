import { Suspense } from "react";
import { ModelConfigFieldConfigure } from "./model-config-field-configure";
import { FieldConfigureEditorSkeleton } from "@/components/common/field-configure-editor";

export const ModelConfigField = () => {
  const id = "model-config-field-configure";

  return (
    <Suspense fallback={<FieldConfigureEditorSkeleton id={id} />}>
      <ModelConfigFieldConfigure id={id} />
    </Suspense>
  );
};
