import { Suspense } from "react";
import { DebugPhaseFieldConfigure } from "./debug-phase-field-configure";
import { FieldConfigureEditorSkeleton } from "@/components/common/field-configure-editor";

export const DebugPhaseField = () => {
  const id = "debug-phase-field-configure";

  return (
    <Suspense fallback={<FieldConfigureEditorSkeleton id={id} />}>
      <DebugPhaseFieldConfigure id={id} />
    </Suspense>
  );
};
