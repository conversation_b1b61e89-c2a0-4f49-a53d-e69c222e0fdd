import {
  CommonFieldRenderers,
  FieldFormBase,
} from "@/components/common/field-configure";
import { ConfigureFormProps } from "@/components/common/field-configure-editor";
import { BaseFieldConfigure } from "@/types/api/config";
import { ConfigureItemType } from "@/types/api/configure-item";

export const DebugPhaseFieldForm = <T extends BaseFieldConfigure>({
  ...props
}: ConfigureFormProps<T>) => {
  return (
    <FieldFormBase<T> {...props}>
      <CommonFieldRenderers.StringField label="环节名称" name="label" />
      <CommonFieldRenderers.IconField label="图标" name="display.icon" />
      <CommonFieldRenderers.JSONLogicField
        label="环节结果展示"
        name="value"
        fieldType={ConfigureItemType.TEXT}
      />
    </FieldFormBase>
  );
};
