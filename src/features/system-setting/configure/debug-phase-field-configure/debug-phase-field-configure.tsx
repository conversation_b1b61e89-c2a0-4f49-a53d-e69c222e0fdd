import {
  BaseFieldSchema,
  createFieldListSchema,
  createLogicJSONFieldSchema,
  FieldConfigureBase,
} from "@/components/common/field-configure";
import { ConfigureService } from "@/services";
import { useMemo } from "react";
import z from "zod";
import { DebugPhaseFieldForm } from "./debug-phase-field-form";

// 调试环节字段 schema
const DebugPhaseFieldSchema = BaseFieldSchema.extend({
  value: createLogicJSONFieldSchema("环节结果展示"),
});

// 调试环节字段表单验证 schema
const DebugPhaseFieldFormSchema = DebugPhaseFieldSchema.extend({
  value: z
    .string({
      error: "环节结果展示必须为字符串",
    })
    .min(1, { error: "环节结果展示不能为空" }),
});

const DebugPhaseFieldListSchema = createFieldListSchema(DebugPhaseFieldSchema);

export const DebugPhaseFieldConfigure = ({ id }: { id: string }) => {
  const defaultField = useMemo(
    () => ({
      label: "未命名环节",
    }),
    [],
  );

  return (
    <FieldConfigureBase
      id={id}
      title="调试环节列表"
      titleTooltip={
        <p>
          配置这些环节仅决定输出结果的收集位置和展示方式，不会影响任务的实际执行流程。
        </p>
      }
      defaultField={defaultField}
      schema={DebugPhaseFieldListSchema}
      formSchema={DebugPhaseFieldFormSchema}
      queryKey="SystemSetting.getDebugStageConfig"
      mutationKey="SystemSetting.saveDebugStageConfig"
      fetchFn={ConfigureService.getDebugStageConfig}
      saveFn={ConfigureService.saveDebugStageConfig}
      jsonFieldPaths={["value"]}
      fieldName="环节"
      ConfigureForm={DebugPhaseFieldForm}
    />
  );
};
