import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { useNavigate, useSearch } from "@tanstack/react-router";
import { useCallback, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { DebugPhaseField } from "./debug-phase-field-configure";
import { MessageLogField } from "./message-log-field-configure";
import { ModelConfigField } from "./model-config-field-configure";
import { PERMISSION_CODES } from "@/constants/permission";
import { usePermission } from "@/hooks/use-permission";

export enum FieldConfigureType {
  DEBUG_PHASE = "DEBUG_PHASE",
  MODEL_FIELD_CONFIG = "MODEL_CONFIG",
  MESSAGE_LOG_FILTER_FIELD = "MESSAGE_LOG_FILTER_FIELD",
}

export const FieldConfigure = () => {
  const { t } = useTranslation();
  const { tab } = useSearch({
    from: "/_app/system-setting/field-configure",
  });
  const navigate = useNavigate({
    from: "/system-setting/field-configure",
  });
  const { hasPermission } = usePermission();
  const settingTabs = useMemo(
    () => [
      {
        label: t("debug.phase"),
        value: FieldConfigureType.DEBUG_PHASE,
        content: <DebugPhaseField />,
        permissionCode:
          PERMISSION_CODES.SYSTEM_SETTING_FIELD_CONFIGURE_DEBUG_PHASE,
      },
      {
        label: t("model.field.config"),
        value: FieldConfigureType.MODEL_FIELD_CONFIG,
        content: <ModelConfigField />,
        permissionCode: PERMISSION_CODES.SYSTEM_SETTING_FIELD_CONFIGURE_MODEL,
      },
      {
        label: t("message.log.filter.field"),
        value: FieldConfigureType.MESSAGE_LOG_FILTER_FIELD,
        content: <MessageLogField />,
        permissionCode:
          PERMISSION_CODES.SYSTEM_SETTING_FIELD_CONFIGURE_MESSAGE_FIELD,
      },
    ],
    [t],
  );

  const accessibleTabs = useMemo(
    () =>
      settingTabs.filter((settingTab) =>
        hasPermission(settingTab.permissionCode),
      ),
    [hasPermission, settingTabs],
  );

  const activeTab = useMemo(() => {
    const isTabAccessible = accessibleTabs.some(
      (accessibleTab) => accessibleTab.value === tab,
    );
    if (tab && isTabAccessible) {
      return tab;
    }
    return accessibleTabs.length > 0
      ? accessibleTabs[0].value
      : FieldConfigureType.DEBUG_PHASE;
  }, [accessibleTabs, tab]);

  const handleTabChange = useCallback(
    (value: string) => {
      navigate({
        search: {
          tab: value as FieldConfigureType,
        },
      });
    },
    [navigate],
  );

  return (
    <Tabs
      value={activeTab}
      className="flex flex-col h-full gap-4 p-6"
      onValueChange={handleTabChange}
    >
      {accessibleTabs.length > 0 ? (
        <>
          <TabsList variant="solid">
            {accessibleTabs.map((settingTab) => (
              <TabsTrigger
                className="text-base"
                key={settingTab.value}
                value={settingTab.value}
              >
                {settingTab.label}
              </TabsTrigger>
            ))}
          </TabsList>

          {accessibleTabs.map((settingTab) => (
            <TabsContent
              className="flex flex-col gap-4 flex-1 min-h-0"
              key={settingTab.value}
              value={settingTab.value}
            >
              {settingTab.content}
            </TabsContent>
          ))}
        </>
      ) : (
        <div className="flex flex-col gap-4 flex-1 min-h-0">
          <div className="text-center text-sm text-gray-500">
            {t("no.permission")}
          </div>
        </div>
      )}
    </Tabs>
  );
};
