import { userAtom } from "@/atoms/auth";
import { shopAtom } from "@/atoms/shop";
import {
  SelectSearch,
  SelectSearchOption,
  ValueType,
} from "@/components/common/select-search";
import { useAtom, useAtomValue } from "jotai";
import { groupBy } from "lodash";
import { useCallback, useMemo } from "react";
import { useTranslation } from "react-i18next";

export const ShopSelect = () => {
  const { t } = useTranslation();
  const { data: user } = useAtomValue(userAtom);
  const [shop, setShop] = useAtom(shopAtom);
  const brandMap = useMemo(
    () =>
      (user?.brandList || []).reduce<Record<number, string>>(
        (acc, brand) => ({
          ...acc,
          [brand.id]: brand.name,
        }),
        {},
      ),
    [user],
  );
  const shops = useMemo(
    () =>
      (user?.shopList || []).map((shop) => ({
        label: shop.name,
        value: shop.id,
        brandName: brandMap[shop.brandId],
      })),
    [brandMap, user?.shopList],
  );
  const handleChange = useCallback(
    (value: ValueType) => {
      setShop(user?.shopList.find((shop) => shop.id === value));
    },
    [setShop, user?.shopList],
  );
  const groupByOptions = useCallback(
    (options: SelectSearchOption[]) => groupBy(options, "brandName"),
    [],
  );

  return (
    <SelectSearch
      placeholder={t("select.shop.loading")}
      options={shops}
      triggerClassName="min-w-48 h-8 w-fit"
      value={shop?.id || ""}
      align="end"
      groupBy={groupByOptions}
      onChange={handleChange}
    />
  );
};
