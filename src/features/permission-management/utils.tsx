import { CopyableText } from "@/components/common/copyable-text";
import { TreeDataItem } from "@/components/ui/tree-view";
import {
  Permission,
  PermissionMenu,
  PermissionType,
} from "@/types/api/permission";
import { KeyRound, Server, SquareGanttChart } from "lucide-react";
import { ComponentProps, ComponentType } from "react";
import { PermissionActions } from "./components/permission-actions";
import { PermissionMenuActions } from "./components/permission-menu-actions";

export const IconMap: Record<
  PermissionType,
  ComponentType<ComponentProps<"svg">>
> = {
  [PermissionType.BUTTON]: KeyRound,
  [PermissionType.INTERFACE]: Server,
  [PermissionType.MENU]: SquareGanttChart,
};

export const transformPermissionMenuToTreeItem = (
  permissionMenus: PermissionMenu[],
): TreeDataItem[] =>
  permissionMenus.map((permissionMenu) => {
    const children = (() => {
      if (permissionMenu.subMenus) {
        return transformPermissionMenuToTreeItem(permissionMenu.subMenus);
      }

      if (permissionMenu.permissions) {
        return transformPermissionToTreeItem(
          permissionMenu.permissions,
          permissionMenu.menuId,
        );
      }

      return [];
    })();

    return {
      id: permissionMenu.menuId.toString(),
      name: permissionMenu.menuName,
      title: (
        <div
          className="flex items-center gap-2"
          onClick={(ev) => {
            ev.stopPropagation();
          }}
        >
          <span className="text-sm truncate">{permissionMenu.menuName}</span>
          <CopyableText
            className="text-sm bg-blue-50 p-1 rounded-md"
            text={permissionMenu.menuCode}
          />
        </div>
      ),
      icon: IconMap[PermissionType.MENU],
      children,
      actions: <PermissionMenuActions permissionMenu={permissionMenu} />,
    };
  });

export const transformPermissionToTreeItem = (
  permissions: Permission[],
  parentId: string,
): TreeDataItem[] =>
  permissions.map((permission) => ({
    id: permission.permissionId.toString(),
    name: permission.permissionName,
    title: (
      <div
        className="flex items-center gap-2"
        onClick={(ev) => {
          ev.stopPropagation();
        }}
      >
        <span className="text-sm truncate">{permission.permissionName}</span>
        <CopyableText
          className="text-sm bg-blue-50 p-1 rounded-md"
          text={permission.permissionCode}
        />
      </div>
    ),
    icon: IconMap[permission.permissionType],
    actions: <PermissionActions permission={permission} parentId={parentId} />,
  }));

export const isMenu = (
  permission: Permission | PermissionMenu,
): permission is PermissionMenu =>
  (permission as PermissionMenu).type === PermissionType.MENU;

/**
 * 搜索过滤权限菜单
 */
export const filterPermissionMenus = (
  permissionMenus: PermissionMenu[],
  searchTerm: string,
): PermissionMenu[] => {
  if (!searchTerm.trim()) {
    return permissionMenus;
  }

  const term = searchTerm.toLowerCase();

  const filterMenu = (menu: PermissionMenu): PermissionMenu | null => {
    // 检查当前菜单是否匹配
    const menuMatches =
      menu.menuName.toLowerCase().includes(term) ||
      menu.menuCode.toLowerCase().includes(term);

    // 递归过滤子菜单
    const filteredSubMenus = menu.subMenus
      ? menu.subMenus
          .map(filterMenu)
          .filter((item): item is PermissionMenu => item !== null)
      : [];

    // 过滤权限
    const filteredPermissions = menu.permissions
      ? menu.permissions.filter(
          (permission) =>
            permission.permissionName.toLowerCase().includes(term) ||
            permission.permissionCode.toLowerCase().includes(term),
        )
      : [];

    // 如果当前菜单匹配或有匹配的子项，则保留
    if (
      menuMatches ||
      filteredSubMenus.length > 0 ||
      filteredPermissions.length > 0
    ) {
      return {
        ...menu,
        subMenus: filteredSubMenus.length > 0 ? filteredSubMenus : undefined,
        permissions:
          filteredPermissions.length > 0 ? filteredPermissions : undefined,
      };
    }

    return null;
  };

  return permissionMenus
    .map(filterMenu)
    .filter((item): item is PermissionMenu => item !== null);
};

/**
 * 获取所有权限名称和编码的集合，用于唯一性校验
 */
export const getAllPermissionNamesAndCodes = (
  permissionMenus: PermissionMenu[],
  excludeId?: string,
): { names: Set<string>; codes: Set<string> } => {
  const names = new Set<string>();
  const codes = new Set<string>();

  const collectFromMenu = (menu: PermissionMenu) => {
    // 如果不是要排除的项，则添加到集合中
    if (menu.menuId !== excludeId) {
      names.add(menu.menuName);
      codes.add(menu.menuCode);
    }

    // 递归处理子菜单
    if (menu.subMenus) {
      menu.subMenus.forEach(collectFromMenu);
    }

    // 处理权限
    if (menu.permissions) {
      menu.permissions.forEach((permission) => {
        if (permission.permissionId !== excludeId) {
          names.add(permission.permissionName);
          codes.add(permission.permissionCode);
        }
      });
    }
  };

  permissionMenus.forEach(collectFromMenu);

  return { names, codes };
};
