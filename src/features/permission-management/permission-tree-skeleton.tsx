import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";

interface PermissionTreeSkeletonProps {
  className?: string;
}

export const PermissionTreeSkeleton = ({
  className,
}: PermissionTreeSkeletonProps) => {
  return (
    <div className={cn("flex flex-col h-full", className)}>
      {/* 顶部搜索和按钮区域 */}
      <div className="flex w-full items-center p-6 pb-4 gap-4 justify-between">
        <Skeleton className="flex-1 h-10" />
        <Skeleton className="h-10 w-20" />
      </div>

      <Separator />

      {/* 树形结构区域 */}
      <div className="flex-1 min-h-0 p-4 overflow-auto">
        <div className="max-w-[800px] space-y-2">
          {/* 一级节点 */}
          <div className="space-y-1">
            <div className="flex items-center gap-2 p-2">
              <Skeleton className="h-7 w-4" />
              <Skeleton className="h-7 w-40" />
            </div>

            {/* 二级节点 */}
            <div className="ml-6 space-y-1">
              <div className="flex items-center gap-2 p-2">
                <Skeleton className="h-7 w-4" />
                <Skeleton className="h-7 w-34" />
              </div>

              {/* 三级节点 */}
              <div className="ml-6 space-y-1">
                <div className="flex items-center gap-2 p-2">
                  <Skeleton className="h-7 w-30" />
                </div>
                <div className="flex items-center gap-2 p-2">
                  <Skeleton className="h-7 w-26" />
                </div>
              </div>
            </div>
          </div>

          {/* 另一个一级节点 */}
          <div className="space-y-1">
            <div className="flex items-center gap-2 p-2">
              <Skeleton className="h-7 w-4" />
              <Skeleton className="h-7 w-40" />
            </div>

            {/* 二级节点 */}
            <div className="ml-6 space-y-1">
              <div className="flex items-center gap-2 p-2">
                <Skeleton className="h-7 w-4" />
                <Skeleton className="h-7 w-30" />
              </div>
              <div className="flex items-center gap-2 p-2">
                <Skeleton className="h-7 w-4" />
                <Skeleton className="h-7 w-30" />
              </div>
            </div>
          </div>

          {/* 第三个一级节点 */}
          <div className="space-y-1">
            <div className="flex items-center gap-2 p-2">
              <Skeleton className="h-7 w-4" />
              <Skeleton className="h-7 w-28" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
