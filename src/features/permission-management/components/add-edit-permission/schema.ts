import { PermissionMenu, PermissionType } from "@/types/api/permission";
import { z } from "zod";
import { IconMap, getAllPermissionNamesAndCodes } from "../../utils";

// 基础schema（用于类型推断）
export const PermissionFormSchema = z.object({
  permissionId: z.string().optional(),
  permissionName: z
    .string()
    .min(1, { message: "名称不能为空" })
    .max(50, { message: "名称最多50个字符" }),
  permissionCode: z
    .string()
    .min(1, { message: "编码不能为空" })
    .max(100, { message: "编码最多100个字符" })
    .regex(/^[a-zA-Z0-9:._-]+$/, {
      message: "编码只能包含字母、数字、冒号",
    }),
  permissionType: z.string({
    message: "请选择类型",
  }),
  permissionUrl: z.string().optional(),
  parentId: z.string().optional(),
});

// 创建动态schema的函数
export const createPermissionFormSchema = (
  permissionList?: PermissionMenu[],
  excludeId?: string,
) => {
  const { names, codes } = permissionList
    ? getAllPermissionNamesAndCodes(permissionList, excludeId)
    : { names: new Set<string>(), codes: new Set<string>() };

  return PermissionFormSchema.extend({
    permissionName: z
      .string()
      .min(1, { message: "名称不能为空" })
      .max(50, { message: "名称最多50个字符" })
      .refine((value) => !names.has(value), {
        message: "权限名称已存在，请使用其他名称",
      }),
    permissionCode: z
      .string()
      .min(1, { message: "编码不能为空" })
      .max(100, { message: "编码最多100个字符" })
      .regex(/^[a-zA-Z0-9:]+$/, {
        message: "编码只能包含字母、数字、冒号",
      })
      .refine((value) => !codes.has(value), {
        message: "权限编码已存在，请使用其他编码",
      }),
  });
};

export type PermissionFormValues = z.infer<typeof PermissionFormSchema>;

export const PERMISSION_TYPE_OPTIONS = [
  {
    value: PermissionType.MENU,
    label: "菜单",
    icon: IconMap[PermissionType.MENU],
  },
  {
    value: PermissionType.BUTTON,
    label: "权限点",
    icon: IconMap[PermissionType.BUTTON],
  },
  {
    value: PermissionType.INTERFACE,
    label: "接口",
    icon: IconMap[PermissionType.INTERFACE],
  },
] as const;
