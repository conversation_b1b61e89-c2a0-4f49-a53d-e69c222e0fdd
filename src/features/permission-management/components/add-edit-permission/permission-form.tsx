import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { UseFormReturn } from "react-hook-form";
import { PERMISSION_TYPE_OPTIONS, PermissionFormValues } from "./schema";

interface PermissionFormProps {
  form: UseFormReturn<PermissionFormValues>;
  disabledTypeSelect?: boolean;
}

export const PermissionForm = ({
  form,
  disabledTypeSelect,
}: PermissionFormProps) => {
  return (
    <div className="space-y-4 py-2">
      <Form {...form}>
        <FormField
          control={form.control}
          name="permissionName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>名称</FormLabel>
              <FormControl>
                <Input placeholder="请输入名称" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="permissionCode"
          render={({ field }) => (
            <FormItem>
              <FormLabel>编码</FormLabel>
              <FormControl>
                <Input placeholder="例如：system:user:add" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="permissionType"
          render={({ field }) => (
            <FormItem>
              <FormLabel>类型</FormLabel>
              <Select {...field} disabled={disabledTypeSelect}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="选择类型" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {PERMISSION_TYPE_OPTIONS.map((option) => (
                    <SelectItem
                      key={option.value}
                      value={option.value.toString()}
                    >
                      <div className="flex items-center gap-2">
                        <option.icon className="size-4" />
                        {option.label}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="permissionUrl"
          render={({ field }) => (
            <FormItem>
              <FormLabel>权限URL（可选）</FormLabel>
              <FormControl>
                <Input placeholder="例如：/users" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </Form>
    </div>
  );
};
