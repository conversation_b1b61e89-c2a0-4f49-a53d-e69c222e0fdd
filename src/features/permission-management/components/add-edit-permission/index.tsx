import { Button } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { zodResolver } from "@hookform/resolvers/zod";
import { useAtom, useAtomValue } from "jotai";
import { useCallback, useEffect } from "react";
import { useForm } from "react-hook-form";
import {
  permissionActionSateAtom,
  permissionListAtom,
  savePermissionMutationAtom,
} from "../../atoms";
import { isMenu } from "../../utils";
import { PermissionForm } from "./permission-form";
import { createPermissionFormSchema, PermissionFormValues } from "./schema";

export const AddEditPermission = () => {
  const [actionState, setActionState] = useAtom(permissionActionSateAtom);
  const { data: permissionList } = useAtomValue(permissionListAtom);
  const { mutateAsync: savePermission, isPending: isAddingPermission } =
    useAtomValue(savePermissionMutationAtom);

  // 获取当前编辑项的ID，用于排除自身
  const excludeId =
    actionState?.action === "edit"
      ? isMenu(actionState.data!)
        ? actionState.data!.menuId
        : actionState.data!.permissionId
      : undefined;

  // 使用动态schema
  const dynamicSchema = createPermissionFormSchema(
    permissionList || undefined,
    excludeId,
  );

  const form = useForm<PermissionFormValues>({
    resolver: zodResolver(dynamicSchema),
    defaultValues: {},
  });

  const handleClose = useCallback(() => {
    form.reset();
    setActionState(null);
  }, [form, setActionState]);

  const handleSubmit = async () => {
    try {
      // 使用最新的schema进行验证
      const currentSchema = createPermissionFormSchema(
        permissionList || undefined,
        excludeId,
      );
      const formData = form.getValues();

      // 手动验证数据
      const validationResult = await currentSchema.safeParseAsync(formData);

      if (!validationResult.success) {
        // 将验证错误设置到表单中
        validationResult.error.issues.forEach((error) => {
          const fieldName = error.path[0] as keyof PermissionFormValues;
          form.setError(fieldName, { message: error.message });
        });
        return;
      }

      await savePermission({
        ...formData,
        permissionType: Number(formData.permissionType),
      });

      handleClose();
    } catch (error) {
      console.error("Form submit error:", error);
    }
  };

  useEffect(() => {
    const { action, data, parentId } = actionState || {};

    if (action === "edit" && data) {
      if (isMenu(data)) {
        form.reset({
          permissionId: data.menuId,
          permissionName: data.menuName,
          permissionCode: data.menuCode,
          permissionType: data.type.toString(),
          permissionUrl: data.menuUrl || "",
        });
      } else {
        form.reset({
          permissionId: data.permissionId,
          permissionName: data.permissionName,
          permissionCode: data.permissionCode,
          permissionType: data.permissionType.toString(),
          permissionUrl: "",
          parentId,
        });
      }
    } else if (actionState?.action === "add") {
      form.reset({
        permissionName: "",
        permissionCode: "",
        permissionType: actionState.type?.toString(),
        permissionUrl: "",
        parentId: actionState.parentId,
      });
    }
  }, [actionState, form]);

  const title = actionState?.action === "edit" ? "编辑权限" : "添加权限";

  return (
    <Dialog open={actionState != null} onOpenChange={handleClose}>
      <DialogContent className="max-w-md">
        <DialogDescription className="sr-only">{title}</DialogDescription>

        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
        </DialogHeader>

        <PermissionForm
          form={form}
          disabledTypeSelect={
            actionState?.action === "edit" || actionState?.type != null
          }
        />

        <DialogFooter>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={handleClose}
              disabled={isAddingPermission}
              className="flex-1"
            >
              取消
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={isAddingPermission}
              loading={isAddingPermission}
              className="flex-1"
            >
              {actionState?.action === "edit" ? "更新" : "添加"}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
