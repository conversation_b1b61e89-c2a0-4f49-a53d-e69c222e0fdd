import { Button } from "@/components/ui/button";
import { Permission } from "@/types/api/permission";
import { useSetAtom } from "jotai";
import { Edit } from "lucide-react";
import { useCallback } from "react";
import { permissionActionSateAtom } from "../atoms";

interface PermissionActionsProps {
  permission: Permission;
  parentId: string;
}

export const PermissionActions = ({
  permission,
  parentId,
}: PermissionActionsProps) => {
  const setActionState = useSetAtom(permissionActionSateAtom);

  const handleEdit = useCallback(
    (event: React.MouseEvent<HTMLButtonElement>) => {
      event.stopPropagation();

      setActionState({
        action: "edit",
        data: permission,
        parentId,
      });
    },
    [parentId, permission, setActionState],
  );

  return (
    <div className="flex gap-2">
      <Button size="sm" icon={<Edit />} onClick={handleEdit}>
        编辑
      </Button>
    </div>
  );
};
