import { Button } from "@/components/ui/button";
import { PermissionMenu, PermissionType } from "@/types/api/permission";
import { useSetAtom } from "jotai";
import { Edit, Plus } from "lucide-react";
import { useCallback } from "react";
import { permissionActionSateAtom } from "../atoms";
import { Can } from "@/components/common/can";
import { PERMISSION_CODES } from "@/constants/permission";

interface PermissionMenuActionsProps {
  permissionMenu: PermissionMenu;
}

export const PermissionMenuActions = ({
  permissionMenu,
}: PermissionMenuActionsProps) => {
  const setActionState = useSetAtom(permissionActionSateAtom);
  const isRoot = permissionMenu.permissions == null;

  const handleEdit = useCallback(
    (event: React.MouseEvent<HTMLButtonElement>) => {
      event.stopPropagation();

      setActionState({
        action: "edit",
        data: permissionMenu,
      });
    },
    [permissionMenu, setActionState],
  );

  const handleAddChild = useCallback(
    (type: PermissionType.BUTTON | PermissionType.MENU) =>
      (event: React.MouseEvent<HTMLButtonElement>) => {
        event.stopPropagation();

        setActionState({
          action: "add",
          data: null,
          type,
          parentId: permissionMenu.menuId,
        });
      },
    [permissionMenu, setActionState],
  );

  return (
    <div className="flex gap-2">
      <Can permissionCode={PERMISSION_CODES.SYSTEM_MANAGEMENT_PERMISSION_CRUD}>
        <Button variant="bordered" icon={<Edit />} onClick={handleEdit}>
          编辑
        </Button>
        {isRoot ? (
          <Button
            variant="bordered"
            icon={<Plus />}
            onClick={handleAddChild(PermissionType.MENU)}
          >
            添加子菜单
          </Button>
        ) : (
          <Button
            variant="bordered"
            icon={<Plus />}
            onClick={handleAddChild(PermissionType.BUTTON)}
          >
            添加权限点
          </Button>
        )}
      </Can>
    </div>
  );
};
