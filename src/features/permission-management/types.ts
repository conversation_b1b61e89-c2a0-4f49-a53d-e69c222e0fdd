import { Permission, PermissionMenu } from "@/types/api/permission";
import { UseQueryResult } from "@tanstack/react-query";

export interface TreePermission extends Permission {
  children?: TreePermission[];
  level: number;
  hasChildren: boolean;
  parentId?: number;
}

export interface PermissionTreeState {
  searchTerm: string;
  expandedNodes: Set<number>;
  selectedNodes: Set<number>;
}

export interface PermissionOperation {
  type: "add" | "edit" | "delete";
  target?: Permission;
  parentId?: number;
}

export interface PermissionTreeProps {
  className?: string;
  query: UseQueryResult<PermissionMenu[] | null, Error>;
}

export interface PermissionTreeNodeProps {
  permission: TreePermission;
  level: number;
  onEdit: (permission: Permission) => void;
  onAddChild: (parentId: number) => void;
}

export interface PermissionTreeItemProps {
  permission: TreePermission;
  level: number;
  hasChildren: boolean;
  isExpanded: boolean;
  onToggle: () => void;
  onEdit: () => void;
  onAddChild: () => void;
}
