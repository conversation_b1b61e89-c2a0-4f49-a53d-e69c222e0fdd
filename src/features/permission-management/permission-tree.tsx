import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { TreeView } from "@/components/ui/tree-view";
import { cn } from "@/lib/utils";
import { PermissionType } from "@/types/api/permission";
import { useSetAtom } from "jotai";
import { Plus, Search, Shield } from "lucide-react";
import { use, useCallback, useDeferredValue, useMemo, useState } from "react";
import { permissionActionSateAtom } from "./atoms";
import { AddEditPermission } from "./components/add-edit-permission";
import { PermissionTreeProps } from "./types";
import {
  filterPermissionMenus,
  transformPermissionMenuToTreeItem,
} from "./utils";
import { usePermission } from "@/hooks/use-permission";
import { PERMISSION_CODES } from "@/constants/permission";

export const PermissionTree = ({ className, query }: PermissionTreeProps) => {
  const [searchTerm, setSearchTerm] = useState("");
  const deferredSearchTerm = useDeferredValue(searchTerm);
  const setPermissionActionState = useSetAtom(permissionActionSateAtom);
  const permissionList = use(query.promise);
  const { hasPermission } = usePermission();

  const treeData = useMemo(() => {
    if (permissionList) {
      const filteredPermissions = filterPermissionMenus(
        permissionList,
        deferredSearchTerm,
      );

      return transformPermissionMenuToTreeItem(filteredPermissions);
    }

    return [];
  }, [permissionList, deferredSearchTerm]);

  // 处理添加权限
  const handleAddPermission = useCallback(() => {
    setPermissionActionState({
      action: "add",
      type: PermissionType.MENU,
      data: null,
      parentId: undefined,
    });
  }, [setPermissionActionState]);

  return (
    <>
      <div className={cn("flex flex-col h-full", className)}>
        <div className="flex w-full items-center p-6 pb-4 gap-4 justify-between">
          <Input
            placeholder="搜索权限..."
            boxClassName="flex-1 min-w-0"
            value={searchTerm}
            icon={<Search className="size-4" />}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          {hasPermission(
            PERMISSION_CODES.SYSTEM_MANAGEMENT_PERMISSION_CRUD,
          ) && (
            <Button onClick={handleAddPermission} size="sm" icon={<Plus />}>
              添加菜单
            </Button>
          )}
        </div>

        <Separator />

        <div className="flex-1 min-h-0 p-4 overflow-auto">
          {treeData.length === 0 ? (
            <div className="text-center py-12">
              <Shield className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="font-semibold mb-2">
                {searchTerm ? "未找到匹配的权限" : "暂无权限数据"}
              </h3>
              <p className="text-sm text-muted-foreground mb-4">
                {searchTerm
                  ? "请尝试其他搜索词"
                  : "开始添加权限来构建您的权限体系"}
              </p>
              {!searchTerm &&
                hasPermission(
                  PERMISSION_CODES.SYSTEM_MANAGEMENT_PERMISSION_CRUD,
                ) && (
                  <Button onClick={handleAddPermission} variant="outline">
                    <Plus className="h-4 w-4 mr-2" />
                    添加第一个权限
                  </Button>
                )}
            </div>
          ) : (
            <div className="max-w-[800px]">
              <TreeView
                data={treeData}
                expandAll={true}
                emptyStateComponent={
                  <div className="text-center py-8">
                    <Shield className="h-8 w-8 text-muted-foreground mx-auto mb-3" />
                    <h4 className="font-medium text-sm mb-2">暂无子权限</h4>
                    <p className="text-xs text-muted-foreground">
                      此节点下没有任何权限点和菜单
                    </p>
                  </div>
                }
              />
            </div>
          )}
        </div>
      </div>

      <AddEditPermission />
    </>
  );
};
