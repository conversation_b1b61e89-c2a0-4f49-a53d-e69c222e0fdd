import { Suspense } from "react";
import { PermissionTree } from "./permission-tree";
import { PermissionTreeSkeleton } from "./permission-tree-skeleton";
import { useAtomValue } from "jotai";
import { permissionListAtom } from "./atoms";

export const PermissionManagement = () => {
  const permissionListQuery = useAtomValue(permissionListAtom);

  return (
    <Suspense fallback={<PermissionTreeSkeleton />}>
      <PermissionTree query={permissionListQuery} />
    </Suspense>
  );
};
