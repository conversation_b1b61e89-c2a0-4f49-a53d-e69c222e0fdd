import { useKnowledgeContext } from "./context";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { KnowledgeItem } from "@/types/api/knowledge-library";
import { KnowledgeService } from "@/services/knowledge";
import { useCallback, useRef, useState } from "react";
import { toast } from "sonner";
import { ModalJsonEdit } from "@/components/common/modal-json-edit";
import { ShopConfigService } from "@/services/config-shop";
import { useFileUpload } from "@/hooks/use-file-upload";
import { PERMISSION_CODES } from "@/constants/permission";
import { usePermission } from "@/hooks/use-permission";

export const KnowledgeBar = () => {
  const { setKeyword, t, setAction, id, refetch } = useKnowledgeContext();
  const [jsonModalOpen, setJsonModalOpen] = useState<boolean>(false);
  const [editJson, setEditJson] = useState<string>("");
  const [configId, setConfigId] = useState<string>("");
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { hasPermission } = usePermission();
  const { uploadFile, isUploading } = useFileUpload({
    onSuccess: async (url, file) => {
      toast.success(`文件 ${file.name} 上传成功`);
      const res = await KnowledgeService.knowledgeImport({
        shopId: id,
        url,
      });
      if (res.success) {
        refetch();
        toast.success("导入成功");
      } else {
        toast.error("导入失败");
      }
    },
    onError: (error, file) => {
      toast.error(`文件 ${file.name} 上传失败: ${error.message}`);
    },
  });

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;
    const file = files[0];
    try {
      await uploadFile(file);
    } catch (error) {
      console.error("上传文件失败:", error);
    }
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleDownload = useCallback(async () => {
    const res = await KnowledgeService.knowledgeDownload({
      shopId: id,
    });
    if (res.success) {
      const url = res.data.url;
      window.open(url, "_self");
    } else {
      toast.error("下载失败");
    }
  }, [id]);

  const handleOpenJsonModal = async () => {
    const config = await ShopConfigService.getIntentConfig({
      shopId: id,
    });
    if (config) {
      setJsonModalOpen(true);
      setEditJson(config.intentConfig);
      setConfigId(config.configId);
    }
  };
  // 处理JSON编辑提交
  const handleJsonEditSubmit = async (jsonData: string) => {
    const res = await ShopConfigService.saveIntentConfig({
      shopId: id,
      configId,
      intentConfig: JSON.stringify(jsonData),
    });
    if (res.success) {
      setJsonModalOpen(false);
      toast.success("保存成功");
    }
  };
  return (
    <div className="flex items-center space-x-4 justify-end sticky top-0  pb-4 pt-1 bg-white">
      <Input
        boxClassName="w-[224px]"
        placeholder="搜索知识库内容"
        onChange={(e) => {
          setKeyword(e.target.value);
        }}
      />
      {hasPermission(PERMISSION_CODES.SHOP_KNOWLEDGE_EDIT) && (
        <>
          <Button
            onClick={() => {
              setAction({
                type: "knowledge-edit",
                operation: "add",
                data: {} as KnowledgeItem,
              });
            }}
          >
            {t("knowledge.add")}
          </Button>

          <Button onClick={handleUploadClick} disabled={isUploading}>
            {isUploading ? "上传中..." : t("common.upload")}
          </Button>
          <input
            type="file"
            ref={fileInputRef}
            style={{ display: "none" }}
            onChange={handleFileChange}
            accept=".json"
          />
          <Button onClick={handleDownload}>{t("common.down")}</Button>
        </>
      )}
      {hasPermission(PERMISSION_CODES.SHOP_KNOWLEDGE_INTENT) && (
        <Button onClick={() => handleOpenJsonModal()}>
          {t("intention.edit")}
        </Button>
      )}
      <ModalJsonEdit
        type="edit"
        open={jsonModalOpen}
        onCancel={() => setJsonModalOpen(false)}
        onSubmit={handleJsonEditSubmit}
        data={JSON.stringify(editJson)}
        title={t("intention.edit")}
        type="edit"
      />
    </div>
  );
};
