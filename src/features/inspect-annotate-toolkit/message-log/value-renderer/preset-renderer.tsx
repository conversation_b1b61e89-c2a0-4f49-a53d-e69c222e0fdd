import { t } from "i18next";

const PresetRenderer = ({ value }: { value: Record<string, any> }) => {
  const zhMap = new Map()
    .set("assistant_id", t("common.assistant.id"))
    .set("assistant_name", t("common.assistant.name"))
    .set("source", t("common.source"))
    .set("user", t("single.conversation"))
    .set("batch", t("batch.conversation"))
    .set("agent", t("agent.conversation"))
    .set("config_id", t("version.config.id"))
    .set("promptVersionId", t("prompt.version.id"));

  return (
    <div>
      {Object.keys(value ?? {})?.map((key) => {
        if (typeof value[key] === "object") {
          return <></>;
        }
        return (
          <div key={key} className="flex flex-wrap w-[330px]">
            <span className="mr-2">{zhMap.get(key) ?? key}: </span>
            <span className="text-muted-foreground">
              {zhMap.get(value[key] ?? "") ?? value[key]}
            </span>
          </div>
        );
      })}
      {Object.keys(value ?? {})?.length === 0 && "-"}
    </div>
  );
};

export default PresetRenderer;
