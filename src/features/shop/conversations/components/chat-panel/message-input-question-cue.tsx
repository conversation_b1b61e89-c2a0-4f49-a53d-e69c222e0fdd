import { cn } from "@/lib/utils";
import { SvgIcon } from "@/components/common/svg-icon";
import { useQuery } from "@tanstack/react-query";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { KnowledgeService } from "@/services/knowledge";
import { shopAtom } from "@/atoms/shop";
import { useAtomValue } from "jotai";

export type QuestionCueProps = {
  className: string;
  onChange: (value: string) => void;
  disabled?: boolean;
};

const QuestionItem = ({
  content,
  onClick,
  disabled,
}: {
  content: string;
  onClick: () => void;
  disabled?: boolean;
}) => {
  return (
    <div
      className={cn(
        "p-2.5 mb-2 rounded-lg bg-gray-50 whitespace-nowrap overflow-hidden text-ellipsis",
        disabled
          ? "cursor-not-allowed opacity-60"
          : "cursor-pointer hover:bg-gray-100",
      )}
      onClick={disabled ? undefined : onClick}
    >
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <span className="text-sm text-gray-700 w-full">{content}</span>
          </TooltipTrigger>
          <TooltipContent>
            <p>{content}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>
  );
};

export const QuestionCue = ({
  className,
  onChange,
  disabled = false,
}: QuestionCueProps) => {
  const shop = useAtomValue(shopAtom);
  const { data: questionsData, refetch: loadProductQuery } = useQuery({
    queryKey: ["defaultQuestions", shop?.id],
    queryFn: async () => {
      if (!shop?.id) {
        return [];
      }
      const response = await KnowledgeService.getConversationQuestions({
        shopId: shop.id,
      });
      console.log("response", response);

      if (response?.data) {
        if (Array.isArray(response.data)) {
          return response.data;
        }
      }
      return [];
    },
    enabled: !!shop?.id,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  });

  const defaultQuestions = questionsData || [];

  return (
    <div className={cn("h-[214px] flex flex-col-reverse", className)}>
      {defaultQuestions.slice(0, 3).map((item, index) => (
        <QuestionItem
          key={index}
          content={item}
          onClick={() => onChange(item)}
          disabled={disabled}
        />
      ))}

      <div
        onClick={disabled ? undefined : () => loadProductQuery()}
        className={cn(
          "flex items-center w-auto justify-start mb-2.5",
          disabled ? "opacity-60 cursor-not-allowed" : "cursor-pointer",
        )}
      >
        <SvgIcon name="icon_refresh_list" color="#7C7C7C" />
        <span className="text-sm text-slate-500 hover:text-slate-700 ml-1">
          换一换
        </span>
      </div>
    </div>
  );
};
