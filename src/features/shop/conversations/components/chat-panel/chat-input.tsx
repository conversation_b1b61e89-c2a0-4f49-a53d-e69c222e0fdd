import { forwardRef, useImper<PERSON><PERSON><PERSON><PERSON>, useState } from "react";
import {
  ChatInput as UIChatInput,
  ChatInputTextArea,
  ChatInputSubmit,
} from "@/components/ui/chat-input";
import { QuestionCue } from "./message-input-question-cue";
import { useAtom, useAtomValue } from "jotai";
import { isRunningAtom } from "@/atoms/conversation";
import { TextareaImageUploader } from "@/components/common/textarea-image-uploader";
import { ImageIcon, ShoppingBagIcon } from "lucide-react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import ProductSelector from "./product-selector";
import { toast } from "sonner";
import { createPictureMessage, createTextMessage, MessageItem } from "@/utils";
import { loadableUserAtom } from "@/atoms/auth";
import { persistedPresetInfoFormAtom, ProductInfo } from "@/atoms/conversation";

type ChatInputProps = {
  sourceType?: any;
  onSend?: (message: any, options?: { from: "questionCue" }) => void;
  threadId?: string | null;
};

type ChatInputRef = {
  setInitMetaData: (data: any) => void;
  setInitMsgMetaData: (data: any) => void;
  sendSuccess: () => void;
};

const ChatInputComponent = forwardRef<ChatInputRef, ChatInputProps>(
  ({ onSend, threadId }, ref) => {
    const [message, setMessage] = useState("");
    const [initMetaData, setInitMetaData] = useState<any>({});
    const [initMsgMetaData, setInitMsgMetaData] = useState<any>({});
    const [isRunning, setIsRunning] = useAtom(isRunningAtom);
    const [popoverOpen, setPopoverOpen] = useState(false);
    const [isSending, setIsSending] = useState(false);
    const presetFormData = useAtomValue(persistedPresetInfoFormAtom);
    const userInfo = useAtomValue(loadableUserAtom);

    // Expose methods via ref
    useImperativeHandle(ref, () => ({
      setInitMetaData: (data: any) => {
        setInitMetaData(data || {});
      },
      setInitMsgMetaData: (data: any) => {
        setInitMsgMetaData(data || {});
      },
      sendSuccess: () => {
        setMessage("");
      },
    }));

    const resetState = () => {
      setMessage("");
      setIsSending(false);
    };

    const sendMessage = (
      messageToSend: string,
      uploadedImages: string[] = [],
      options?: { from: "questionCue" },
    ) => {
      if (!messageToSend.trim() && uploadedImages.length === 0) return;

      try {
        setIsSending(true);
        setIsRunning(true);

        // 构造消息内容
        const messageContent: MessageItem[] = [];

        if (messageToSend.trim()) {
          messageContent.push(createTextMessage(messageToSend.trim()));
        }

        if (uploadedImages.length > 0) {
          messageContent.push(
            ...uploadedImages.map((image) => createPictureMessage(image)),
          );
        }

        const msgData = {
          content: JSON.stringify(messageContent),
          conversationId: threadId,
          metadata: {
            ...initMetaData,
            ...initMsgMetaData,
            config_id: presetFormData?.configId,
            promptVersionId: presetFormData?.promptVersion,
            originMessages: [
              {
                msg: messageToSend,
                time: +new Date(),
                type: uploadedImages.length > 0 ? "mixed" : "txt",
              },
            ],
          },
          role: "user",
          source: "user",
          uid:
            userInfo?.state === "hasData" ? userInfo?.data?.data?.username : "",
          messageRealAt: +new Date(),
          workBench: true,
        };

        onSend?.(msgData, options);

        // 设置发送超时保护
        const timeout = setTimeout(() => {
          setIsSending(false);
          setIsRunning(false);
        }, 30000);

        // 发送完成后重置状态
        resetState();

        return () => clearTimeout(timeout);
      } catch (error) {
        console.error("发送消息失败:", error);
        toast.error("发送消息失败", {
          description: error instanceof Error ? error.message : "未知错误",
        });
        setIsSending(false);
        setIsRunning(false);
      }
    };

    const handleSend = (uploadedImages: string[] = []) => {
      sendMessage(message, uploadedImages);
    };

    const handleSetMessage = (value: string) => {
      sendMessage(value, [], { from: "questionCue" });
    };

    // 选择商品
    const handleSelectProduct = (product: ProductInfo) => {
      setMessage(`https://detail.tmall.com/item.htm?id=${product.item_id}`);
      setPopoverOpen(false);
    };

    return (
      <div className="flex p-4">
        <div className="flex-1">
          {!threadId && (
            <QuestionCue
              className="ml-[15px]"
              onChange={handleSetMessage}
              disabled={isSending}
            />
          )}
          <TextareaImageUploader>
            {({
              dragProps,
              isUploading,
              uploadedImages,
              clearImages,
              onImageUpload,
            }) => {
              const disabled =
                (!message.trim() && uploadedImages.length === 0) || isUploading;
              return (
                <UIChatInput
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  onSubmit={() => {
                    handleSend(uploadedImages);
                    clearImages();
                  }}
                  loading={isRunning || isSending}
                  rows={3}
                >
                  <ChatInputTextArea
                    placeholder="请输入您的问题..."
                    className="min-h-[80px]"
                    disabled={isSending}
                    submitDisabled={disabled}
                    {...dragProps}
                  />
                  <div className="flex justify-between w-full items-center mt-2">
                    <div className="flex gap-2">
                      <button
                        className="p-1.5 rounded-md hover:bg-slate-100 text-slate-500 transition-colors cursor-pointer"
                        onClick={onImageUpload}
                        title="上传图片"
                        disabled={isUploading || isSending}
                      >
                        <ImageIcon className="h-5 w-5" />
                      </button>
                      <Popover open={popoverOpen} onOpenChange={setPopoverOpen}>
                        <PopoverTrigger asChild>
                          <button
                            className="p-1.5 rounded-md hover:bg-slate-100 text-slate-500 transition-colors cursor-pointer"
                            title="选择商品"
                            disabled={isSending}
                          >
                            <ShoppingBagIcon className="h-5 w-5" />
                          </button>
                        </PopoverTrigger>
                        <PopoverContent
                          forceMount
                          className="w-[450px] p-0 shadow-lg border-gray-200"
                        >
                          <ProductSelector
                            onSelect={handleSelectProduct}
                            onClose={() => setPopoverOpen(false)}
                          />
                        </PopoverContent>
                      </Popover>
                    </div>
                    <ChatInputSubmit disabled={disabled} />
                  </div>
                </UIChatInput>
              );
            }}
          </TextareaImageUploader>
        </div>
      </div>
    );
  },
);

ChatInputComponent.displayName = "ChatInputComponent";

export default ChatInputComponent;
