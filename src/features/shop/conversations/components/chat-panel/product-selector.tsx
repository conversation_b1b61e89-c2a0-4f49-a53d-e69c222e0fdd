import { useState, useEffect, useCallback } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { SearchIcon, ShoppingBagIcon } from "lucide-react";
import { api } from "@/api";
import { shopAtom } from "@/atoms/shop";
import { useAtomValue } from "jotai";
import { useDebounceFn } from "ahooks";
import { ProductInfo } from "@/atoms/conversation";

interface ProductSelectorProps {
  shopId?: string;
  onSelect: (product: ProductInfo) => void;
  onClose: () => void;
}

interface ProductItem {
  master_product: {
    code: string;
    title: string;
    children?: ProductInfo[];
  };
  sub_products?: Array<ProductInfo>;
}

interface ApiResponse {
  data: {
    data: ProductItem[];
  };
}

const ProductSelector = ({ onSelect, onClose }: ProductSelectorProps) => {
  const [productList, setProductList] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const shop = useAtomValue(shopAtom);
  const getProductList = useCallback(
    (searchValue?: string) => {
      setIsLoading(true);
      setProductList([]);

      return api
        .getProductMasterPage(searchValue, 1, 100, shop?.id)
        .then((res: unknown) => {
          const response = res as ApiResponse;
          const proData = response?.data?.data
            .filter((item) => item?.master_product?.code)
            .map((item: ProductItem) => {
              if (item.sub_products && item.sub_products.length > 0) {
                return {
                  productType: "主商品",
                  ...item.master_product,
                  children: item.sub_products.map((subItem) => {
                    return {
                      ...subItem,
                      children: null,
                    };
                  }),
                };
              } else {
                item.master_product.children = undefined;

                return {
                  productType: "主商品",
                  ...item.master_product,
                };
              }
            });
          setProductList(proData);
        })
        .finally(() => {
          setIsLoading(false);
        });
    },
    [shop],
  );

  useEffect(() => {
    getProductList();
  }, [getProductList]);

  // 防抖处理搜索
  const { run: debouncedSearch } = useDebounceFn(
    (value: string) => {
      getProductList(value);
    },
    {
      wait: 500,
    },
  );

  const handleSearch = (value: string) => {
    debouncedSearch(value);
  };

  // 选择商品
  const handleSelectProduct = (product: any) => {
    onSelect(product);
  };

  // 骨架屏加载项
  const SkeletonItem = () => (
    <div className="mb-2 bg-white rounded-md overflow-hidden animate-pulse">
      <div className="p-2.5 flex items-center justify-between">
        <div className="h-4 bg-gray-200 rounded w-3/4"></div>
        <div className="h-4 w-4 bg-gray-200 rounded"></div>
      </div>
    </div>
  );

  return (
    <div className="flex flex-col">
      <div className="p-4 border-b border-gray-100">
        <h4 className="font-medium text-gray-800 mb-3">快速选择商品</h4>
        <div className="flex items-center bg-gray-50 rounded-md p-1.5">
          <SearchIcon className="h-4 w-4 ml-1.5 text-gray-400" />
          <Input
            placeholder="搜索商品名称..."
            onChange={(e) => handleSearch(e.target.value)}
            className="flex-1 border-none bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 h-8 text-sm"
          />
        </div>
      </div>

      <ScrollArea className="h-[300px]">
        {isLoading ? (
          <div className="p-2">
            {[...Array(7)].map((_, index) => (
              <SkeletonItem key={index} />
            ))}
          </div>
        ) : productList.length > 0 ? (
          <div className="p-2">
            {productList.map((product: any) => (
              <div
                key={product.code}
                className="mb-2 bg-white rounded-md hover:bg-blue-50/30 transition-all duration-200 overflow-hidden"
              >
                <div
                  className="p-2.5 cursor-pointer flex items-center justify-between"
                  onClick={() => handleSelectProduct(product)}
                >
                  <div className="font-medium text-gray-700 truncate max-w-[350px] text-sm">
                    {product.title}
                  </div>
                  <ShoppingBagIcon className="h-3.5 w-3.5 text-gray-400" />
                </div>

                {product.children && product.children.length > 0 && (
                  <div className="bg-gray-50">
                    {product.children.map((subProduct: any) => (
                      <div
                        key={subProduct.code}
                        className="px-4 py-1.5 text-xs text-gray-600 hover:bg-blue-50 cursor-pointer flex items-center border-b border-gray-50 last:border-b-0"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleSelectProduct(subProduct);
                        }}
                      >
                        <div className="w-1 h-1 rounded-full bg-gray-300 mr-2"></div>
                        <div className="truncate max-w-[370px]">
                          {subProduct.title}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center h-full p-8 text-gray-500">
            <ShoppingBagIcon className="h-12 w-12 text-gray-300 mb-2" />
            <span>未找到商品</span>
            <span className="text-xs text-gray-400 mt-1">请尝试其他关键词</span>
          </div>
        )}
      </ScrollArea>

      <div className="flex justify-end p-3 border-t border-gray-100 bg-gray-50">
        <Button
          variant="outline"
          size="sm"
          onClick={onClose}
          className="text-sm"
        >
          取消
        </Button>
      </div>
    </div>
  );
};

export default ProductSelector;
