import { builtInServices } from "@/features/inspect-annotate-toolkit";
import {
  MessageToolkitPanelContent,
  MessageToolkitPanelTab,
} from "@/features/inspect-annotate-toolkit/message-toolkit-panel/message-toolkit-panel-content";
import { MessageToolkitSkeleton } from "@/features/inspect-annotate-toolkit/message-toolkit-panel/message-toolkit-skeleton";
import { Suspense } from "react";
import { tableActionAtom } from "@/atoms/conversation";
import { useAtom } from "jotai";
import { Button } from "@/components/ui/button";
import { ChevronRight } from "lucide-react";

const MessageLogPanel = ({
  activeTab,
}: {
  activeTab: MessageToolkitPanelTab;
}) => {
  const [tableAction, setTableAction] = useAtom(tableActionAtom);

  const handleClose = () => {
    setTableAction(null);
  };

  return (
    <div
      className={`relative w-[400px] border-l ${
        tableAction?.type === "view-log" ? "block" : "none"
      }`}
    >
      <Button
        variant="ghost"
        size="icon"
        className="absolute top-1/2 -translate-y-1/2 -left-4 z-10 h-8 w-8 rounded-full border bg-white shadow-md"
        onClick={handleClose}
      >
        <ChevronRight className="h-4 w-4" />
      </Button>
      <div className="h-full overflow-y-auto p-3">
        <Suspense fallback={<MessageToolkitSkeleton />}>
          <MessageToolkitPanelContent
            role={tableAction?.data?.role}
            logData={{
              messageId: tableAction?.data?.messageId,
            }}
            messageId={tableAction?.data?.messageId}
            {...builtInServices.messageGenerator(tableAction?.data?.messageId)}
            messageLogQueryFnKey={[
              "getMessageLog",
              tableAction?.data?.messageId,
            ]}
            initialTab={activeTab}
          />
        </Suspense>
      </div>
    </div>
  );
};

export default MessageLogPanel;
