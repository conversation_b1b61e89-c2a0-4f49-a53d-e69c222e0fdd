import { use<PERSON>tom, useAtomValue, useSet<PERSON>tom } from "jotai";
import {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
  useCallback,
  useMemo,
} from "react";
import {
  currentThreadIdAtom,
  messagesAtom,
  highlightMessageIdAtom,
  isRunningConversationIdAtom,
  isRunning<PERSON>tom,
  tableActionAtom,
} from "@/atoms/conversation";
import { MessageList } from "@/components/common/message/message-list";
import ChatInput from "./chat-input";
import { shopAtom } from "@/atoms/shop";
import useWebSocket, {
  ConnectionType,
  WebSocketEvents,
} from "@/hooks/use-websocket";
import {
  BaseMessage,
  ConversationMessage,
  MessageSource,
} from "@/types/api/message";
import { MessageToolkitPanelTab } from "@/features/inspect-annotate-toolkit/message-toolkit-panel/message-toolkit-panel-content";
import Header from "./chat-panel-header";
import { useDebounceFn } from "ahooks";
import { getAccessToken, user<PERSON>tom } from "@/atoms/auth";
import { useQueryClient } from "@tanstack/react-query";
import { MessageService } from "@/services/message-service";
import MessageLogPanel from "./message-log-panel";

const ENDPOINT = import.meta.env.VITE_CHAT_SOCKET_APP_API_ENDPOINT;

type ChatPanelProps = {
  sourceType?: any;
  getList?: () => void;
  className?: string;
};

export type ChatPanelRef = {
  resetInitMetaData: () => void;
  resetInitMsgMetaData: () => void;
  showMessageToolkit: (messageId: string) => void;
};

const ChatPanel = forwardRef<ChatPanelRef, ChatPanelProps>(
  ({ sourceType, getList, className }, ref) => {
    const shop = useAtomValue(shopAtom);
    const userResult = useAtomValue(userAtom);
    const messagesResult = useAtomValue(messagesAtom);
    const highlightMessageId = useAtomValue(highlightMessageIdAtom);
    const [threadId, setThreadId] = useAtom(currentThreadIdAtom);
    const setIsRunning = useSetAtom(isRunningAtom);
    const [isRunningConversationId, setIsRunningConversationId] = useAtom(
      isRunningConversationIdAtom,
    );
    const setTableAction = useSetAtom(tableActionAtom);
    const chatInputRef = useRef<any>(null);
    const [optimisticMessages, setOptimisticMessages] = useState<
      ConversationMessage[]
    >([]);
    const queryClient = useQueryClient();
    const [activeTab, setActiveTab] = useState<MessageToolkitPanelTab>(
      MessageToolkitPanelTab.INFO,
    );
    const messageListRef = useRef<any>(null);
    const [tableAction] = useAtom(tableActionAtom);
    const runningMessage = useMemo(
      () => ({
        isRunning: true,
        messageId: new Date().getTime().toString(),
        conversationId: threadId || "",
        userId: userResult.data?.userId || "",
        uid: userResult.data?.userId || "",
        role: MessageSource.assistant,
        content: "",
        thumbs: 0,
        source: MessageSource.assistant,
        messageRealAt: new Date().getTime().toString(),
        createdAt: new Date().getTime().toString(),
      }),
      [threadId, userResult.data?.userId],
    );

    useEffect(() => {
      if (!threadId) return;
      if (isRunningConversationId === threadId) {
        setOptimisticMessages([runningMessage]);
      } else {
        setOptimisticMessages([]);
      }
    }, [threadId, isRunningConversationId, runningMessage]);

    const showMessageToolkit = useCallback(
      (messageId: string) => {
        setActiveTab(MessageToolkitPanelTab.PROCESS);
        setTableAction({
          type: "view-log",
          data: {
            messageId,
            role: MessageSource.assistant,
          },
        });
      },
      [setTableAction],
    );

    // 使用 useWebSocket Hook
    const {
      connect,
      sendMessage: sendWebSocketMessage,
      addEventListener,
      disconnect,
    } = useWebSocket<WebSocketEvents>(ConnectionType.USER);

    // 处理消息数据，确保适配不同的返回格式
    const getConversationMessages = (): ConversationMessage[] => {
      if (messagesResult.status !== "success") {
        return [];
      }

      const data = messagesResult.data;

      // 判断返回的数据结构，适配不同类型
      if ("messageDTOS" in data && Array.isArray(data.messageDTOS)) {
        return data.messageDTOS;
      }

      // 如果没有正确的数据格式，返回空数组
      return [];
    };

    const messages = [...getConversationMessages(), ...optimisticMessages];

    // Reset meta data functions
    function resetInitMetaData() {
      if (chatInputRef.current) {
        chatInputRef.current.setInitMetaData({});
      }
    }

    function resetInitMsgMetaData() {
      if (chatInputRef.current) {
        chatInputRef.current.setInitMsgMetaData({});
      }
    }

    // Expose functions via ref
    useImperativeHandle(ref, () => ({
      resetInitMetaData,
      resetInitMsgMetaData,
      showMessageToolkit,
    }));

    // Message sending function
    const sendMessage = (msg: any) => {
      if (!shop || userResult.status !== "success" || !userResult.data) {
        return;
      }

      // sendWebSocketMessage的回调有问题，无法收到回调信息，通过注册事件的形式
      addEventListener("send-message", (id) => {
        if (id) {
          queryClient.invalidateQueries({ queryKey: ["messages", id] });
          getList?.();
          setIsRunningConversationId(id);
          setTimeout(() => {
            messageListRef.current?.scrollToBottom();
          }, 0);

          // 发送消息后打开日志组件并显示过程标签页
          // setTimeout(() => {
          //   showMessageToolkit(id);
          // }, 1000);
        }

        // setTimeout(() => {
        //   setMessageId("");
        // }, 1000);
      });
      sendWebSocketMessage("send-message", msg);
      setTimeout(() => {
        messageListRef.current?.scrollToBottom();
      }, 0);
    };

    // WebSocket connection function
    const connectWebSocketHandler = useCallback(() => {
      if (!sourceType || !shop?.id) return;
      // 配置并连接 WebSocket
      connect({
        authorization: getAccessToken(),
        socketUrl: ENDPOINT,
        connectionType: ConnectionType.USER,
        reconnectAttempts: 50,
        reconnectInterval: 3000,
        shopId: shop?.id,
      });

      // 注册回复消息事件处理
      const handleReplyMessage = (msg: BaseMessage) => {
        setIsRunning(false);
        queryClient.invalidateQueries({ queryKey: ["messages"] });
        setThreadId(msg.conversationId);
        setIsRunningConversationId(null);

        // 轮训getMessageLog，返回的数据中有metaData就显示日志
        const poll = setInterval(async () => {
          try {
            const res = await MessageService.getMessageLog({
              messageId: msg.messageId,
            });
            if (res && res.metaData) {
              clearInterval(poll);
              showMessageToolkit(msg.messageId);
            }
          } catch (error) {
            console.error("Polling message log failed:", error);
            clearInterval(poll);
          }
        }, 500);

        // 设置一个超时，以防万一轮询无法停止
        setTimeout(() => {
          clearInterval(poll);
        }, 30000); // 30秒后超时
      };

      // 注册事件监听器
      addEventListener("reply-message", handleReplyMessage);
    }, [
      sourceType,
      shop?.id,
      connect,
      addEventListener,
      setIsRunning,
      queryClient,
      setThreadId,
      setIsRunningConversationId,
      showMessageToolkit,
    ]);

    const { run: debouncedConnect } = useDebounceFn(connectWebSocketHandler, {
      wait: 500,
    });

    // WebSocket connection
    useEffect(() => {
      if (!shop?.id) return;
      // 调用防抖处理的WebSocket连接函数
      debouncedConnect();

      return () => {
        // 断开连接
        disconnect();
        setIsRunning(false);
        setIsRunningConversationId("");
      };
    }, [
      debouncedConnect,
      disconnect,
      setIsRunning,
      setIsRunningConversationId,
      shop?.id,
    ]);

    useEffect(() => {
      if (highlightMessageId) {
        messageListRef.current?.scrollToAnchor(highlightMessageId);
      }
    }, [highlightMessageId]);

    useEffect(() => {
      setTableAction(null);
      setIsRunning(false);
      setIsRunningConversationId(null);
    }, [shop?.id, setTableAction, setIsRunning, setIsRunningConversationId]);

    return (
      <div
        className={`flex h-full bg-white rounded-lg overflow-hidden ${className}`}
      >
        <div className={`flex flex-col flex-1 h-full bg-white rounded-lg `}>
          <Header sourceType={sourceType} />
          <div className="flex flex-col flex-1 overflow-hidden">
            <MessageList
              ref={messageListRef}
              messages={messages}
              className="overflow-visible"
              autoScrollToBottom={true}
              smoothScroll={false}
              // autoScrollToAnchor={highlightMessageId || undefined }
            />
            {sourceType && (
              <ChatInput
                ref={chatInputRef}
                sourceType={sourceType}
                onSend={sendMessage}
                threadId={threadId}
              />
            )}
          </div>
        </div>
        {tableAction?.type === "view-log" && (
          <MessageLogPanel activeTab={activeTab} />
        )}
      </div>
    );
  },
);

ChatPanel.displayName = "ChatPanel";

export default ChatPanel;
