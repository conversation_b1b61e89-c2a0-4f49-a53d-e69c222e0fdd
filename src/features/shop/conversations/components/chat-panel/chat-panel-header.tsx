import {
  currentThreadIdAtom,
  replyMessageRoleAtom,
} from "@/atoms/conversation";
import { useAtom, useSet<PERSON>tom } from "jotai";
import { useTranslation } from "react-i18next";
import { Button } from "antd";
import { Select } from "antd";
import { CopyableText } from "@/components/common/copyable-text";
import { PresetInfo } from "@/features/shop/conversations/components/chat-panel/preset-info";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { PERMISSION_CODES } from "@/constants/permission";
import { usePermission } from "@/hooks/use-permission";

const Header = ({ sourceType }: { sourceType?: any }) => {
  const [threadId] = useAtom(currentThreadIdAtom);
  const [replyMessageRole, setReplyMessageRole] = useAtom(replyMessageRoleAtom);
  const setCurrentThreadId = useSetAtom(currentThreadIdAtom);
  const { hasPermission } = usePermission();
  // const setMessageData = useSetAtom(messageDataAtom);
  // const [rightContent, setLogContentType] = useState("log");
  // const [rightCollapsed, setRightCollapsed] = useState(false);
  const { t } = useTranslation();

  return (
    <div className="flex justify-between items-center p-4 border-b border-gray-200">
      <div className="flex items-center gap-2">
        <span className="font-medium text-base">
          {sourceType ? t("simulation.conversation") : t("conversation")}{" "}
        </span>
        {threadId && (
          <div className="flex items-center gap-1 whitespace-nowrap flex-1 min-w-0 truncate">
            <span className="text-[#ababab]">{t("conversation.id")}</span>
            <CopyableText
              wrapperClassName="flex-1 min-w-0 truncate"
              text={threadId}
              className="text-gray-500 text-sm"
            />
          </div>
        )}
      </div>
      {sourceType ? (
        <>
          {(hasPermission(PERMISSION_CODES.SHOP_SIMULATION_PRESET_VERSION) ||
            hasPermission(PERMISSION_CODES.SHOP_SIMULATION_PRESET_INFO)) && (
            <Popover>
              <PopoverTrigger asChild>
                <Button type="primary">模拟预设</Button>
              </PopoverTrigger>
              <PopoverContent side="left">
                <PresetInfo />
              </PopoverContent>
            </Popover>
          )}
        </>
      ) : (
        <Select
          style={{ width: 190 }}
          value={replyMessageRole}
          onSelect={(e) => {
            setReplyMessageRole(e);
            if (threadId) {
              // This will trigger the messagesAtom query to reload
              setCurrentThreadId(threadId);
            }
          }}
        >
          <Select.Option value="all">展示全部回复</Select.Option>
          <Select.Option value="vitoCustomer">
            只展示转人工任务回复
          </Select.Option>
          <Select.Option value="customer">只展示实际发送回复</Select.Option>
          <Select.Option value="assistant">只展示AI生成回复</Select.Option>
        </Select>
      )}
    </div>
  );
};

export default Header;
