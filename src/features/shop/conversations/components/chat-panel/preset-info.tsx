import { useState, useEffect, useCallback } from "react";
import { useTranslation } from "react-i18next";
import { useAtomValue, useAtom } from "jotai";
import { useForm } from "react-hook-form";
import { shopAtom } from "@/atoms/shop";
import { persistedPresetInfoFormAtom, ProductInfo } from "@/atoms/conversation";
import { versionsAtom } from "@/features/prompt/management/atoms/queries/version-atoms";
import {
  ConfigVersionSelect,
  PromptVersionSelect,
} from "@/components/common/version-select";
import { useQuery } from "@tanstack/react-query";
import { EvaluationConfigService } from "@/services/evaluation-config-service";
import { ConfigVersionStatus } from "@/types/api/version";
import ProductSelector from "./product-selector";
import { PresetFormData } from "@/atoms/conversation";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { PERMISSION_CODES } from "@/constants/permission";
import { Can } from "@/components/common/can";
import { PromptVersionStatus } from "@/types/api/prompt-version";
import { usePermission } from "@/hooks/use-permission";

export const PresetInfo = () => {
  const { t } = useTranslation();
  const shop = useAtomValue(shopAtom);
  const { data: versionList = [] } = useAtomValue(versionsAtom);
  const [presetFormData, setPresetFormData] = useAtom(
    persistedPresetInfoFormAtom,
  );
  const { hasPermission } = usePermission();

  // 使用react-hook-form替代Form
  const form = useForm<PresetFormData>({
    defaultValues: {
      ...presetFormData,
      entryProduct: presetFormData.entryProduct,
      orderProduct: presetFormData.orderProduct,
      orderType: presetFormData.orderType,
    },
  });

  // 获取版本列表
  const { data: versionData } = useQuery({
    queryKey: ["getVersionList", shop?.id],
    enabled: hasPermission(PERMISSION_CODES.SHOP_SIMULATION_PRESET_VERSION),
    queryFn: () =>
      EvaluationConfigService.getVersionList(
        { version: "" },
        { shopId: shop?.id },
      ),
  });

  const [system] = useState({ rightPresets: true });

  // 商品选择器状态
  const [selectedProductName, setSelectedProductName] = useState<string>("");
  const [productSelectOpen, setProductSelectOpen] = useState(false);
  const [selectedOrderProductName, setSelectedOrderProductName] =
    useState<string>("");
  const [orderProductSelectOpen, setOrderProductSelectOpen] = useState(false);

  // 处理表单数据更改
  const handleFormChange = useCallback(
    (values: Partial<PresetFormData>) => {
      const updatedValues = { ...values };

      setPresetFormData({
        ...presetFormData,
        ...updatedValues,
      });
    },
    [presetFormData, setPresetFormData],
  );

  // 处理商品选择
  const handleProductSelect = useCallback(
    (product: ProductInfo) => {
      // 更新表单值为商品代码（字符串）
      form.setValue("entryProduct", product);

      handleFormChange({
        entryProduct: product, // 保存完整的商品对象
      });

      setProductSelectOpen(false); // 关闭Select下拉菜单
      setSelectedProductName(product.title);
    },
    [form, handleFormChange],
  );

  // 处理订单商品选择
  const handleOrderProductSelect = useCallback(
    (product: ProductInfo) => {
      form.setValue("orderProduct", product);

      handleFormChange({
        orderProduct: product,
      });

      setOrderProductSelectOpen(false);
      setSelectedOrderProductName(product.title);
    },
    [form, handleFormChange],
  );

  // 初始化选中商品名称 - 使用保存的商品信息或者API回填
  useEffect(() => {
    if (
      presetFormData.entryProduct &&
      typeof presetFormData.entryProduct === "object"
    ) {
      setSelectedProductName(presetFormData.entryProduct.title);
    }

    if (
      presetFormData.orderProduct &&
      typeof presetFormData.orderProduct === "object"
    ) {
      setSelectedOrderProductName(presetFormData.orderProduct.title);
    }

    // 处理orderType，确保类型一致
    if (
      presetFormData.orderType !== undefined &&
      presetFormData.orderType !== ""
    ) {
      const orderTypeValue = String(presetFormData.orderType);
      console.log("初始化orderType值:", orderTypeValue);
      form.setValue("orderType", orderTypeValue);
    }

    presetFormData.promptVersion &&
      form.setValue("promptVersion", presetFormData.promptVersion);
    presetFormData.configId &&
      form.setValue("configId", presetFormData.configId);
    // console.log(presetFormData);
  }, [presetFormData, form]);

  useEffect(() => {
    if (!presetFormData.promptVersion) {
      const onlineVersion = versionList.find(
        (v) => v.status === PromptVersionStatus.ONLINE,
      );

      if (onlineVersion) {
        form.setValue("promptVersion", onlineVersion.promptVersionId);
        handleFormChange({ promptVersion: onlineVersion.promptVersionId });
      }
    }

    if (!presetFormData.configId) {
      const onlineVersion = versionData?.find(
        (item) => item.onlineStatus === ConfigVersionStatus.ONLINE,
      );
      if (onlineVersion) {
        form.setValue("configId", onlineVersion.configId);
        handleFormChange({ configId: onlineVersion.configId });
      }
    }
  }, [
    versionList,
    versionData,
    presetFormData.promptVersion,
    presetFormData.configId,
    form,
    handleFormChange,
  ]);

  return (
    <>
      {system.rightPresets && (
        <div className=" border-gray-200 dark:border-gray-800 pt-4">
          <Form {...form}>
            <form className="space-y-4">
              <Can
                permissionCode={PERMISSION_CODES.SHOP_SIMULATION_PRESET_VERSION}
              >
                <FormField
                  control={form.control}
                  name="promptVersion"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("assistant.info.promptVersion")}</FormLabel>
                      <FormControl>
                        <PromptVersionSelect
                          className="w-full"
                          versions={versionList || []}
                          side="left"
                          sideOffset={20}
                          onChange={(value) => {
                            field.onChange(value);
                            handleFormChange({ promptVersion: value });
                          }}
                          value={field.value}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="configId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("assistant.info.version")}</FormLabel>
                      <FormControl>
                        <ConfigVersionSelect
                          versions={versionData || []}
                          side="left"
                          sideOffset={20}
                          onChange={(value) => {
                            field.onChange(value);
                            handleFormChange({ configId: value });
                          }}
                          value={field.value}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </Can>
              <Can
                permissionCode={PERMISSION_CODES.SHOP_SIMULATION_PRESET_INFO}
              >
                <FormField
                  control={form.control}
                  name="entryProduct"
                  render={() => (
                    <FormItem>
                      <FormLabel>{t("product.entry")}</FormLabel>
                      <FormControl>
                        <Select
                          open={productSelectOpen}
                          onOpenChange={setProductSelectOpen}
                        >
                          <SelectTrigger className="w-full">
                            <SelectValue
                              placeholder={
                                selectedProductName ||
                                t("common.placeholder.select")
                              }
                            />
                          </SelectTrigger>
                          <SelectContent>
                            <div className="w-[400px]">
                              <ProductSelector
                                onSelect={handleProductSelect}
                                onClose={() => setProductSelectOpen(false)}
                              />
                            </div>
                          </SelectContent>
                        </Select>
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="orderProduct"
                  render={() => (
                    <FormItem>
                      <FormLabel>{t("product.order")}</FormLabel>
                      <FormControl>
                        <Select
                          open={orderProductSelectOpen}
                          onOpenChange={setOrderProductSelectOpen}
                        >
                          <SelectTrigger className="w-full">
                            <SelectValue
                              placeholder={
                                selectedOrderProductName ||
                                t("common.placeholder.select")
                              }
                            />
                          </SelectTrigger>
                          <SelectContent>
                            <div className="w-[400px]">
                              <ProductSelector
                                onSelect={handleOrderProductSelect}
                                onClose={() => setOrderProductSelectOpen(false)}
                              />
                            </div>
                          </SelectContent>
                        </Select>
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="orderType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("order.status.title")}</FormLabel>
                      <Select
                        value={field.value}
                        onValueChange={(value) => {
                          if (!value) {
                            return;
                          }
                          field.onChange(value);
                          handleFormChange({ orderType: value });
                        }}
                      >
                        <SelectTrigger className="w-full">
                          <SelectValue
                            placeholder={t("common.placeholder.select")}
                          />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="0">
                            {t("order.status.before")}
                          </SelectItem>
                          <SelectItem value="1">
                            {t("order.status.after")}
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </FormItem>
                  )}
                />
              </Can>
            </form>
          </Form>
        </div>
      )}
    </>
  );
};
