import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { useState, useEffect } from "react";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";

export interface FilterConfig {
  type: "checkbox" | "radio";
  name: string;
  label: string;
  options?: { label: string; value: string; disabled?: boolean }[];
}

// 动态推导filterValue类型
type DynamicFilterValue<T extends FilterConfig[]> = {
  [K in T[number]["name"]]: string[];
};

interface FilterFieldsProps<T extends FilterConfig[]> {
  filterConfigs: T;
  initialValues?: Record<string, any>;
  onChange?: (name: string, values: string[]) => void;
  disabled?: boolean;
}

export const FilterFields = <T extends FilterConfig[]>({
  filterConfigs,
  initialValues = {},
  onChange,
  disabled,
}: FilterFieldsProps<T>) => {
  // 根据filterConfigs初始化filterValue
  const initialFilterValue = {} as DynamicFilterValue<T>;
  filterConfigs.forEach((config) => {
    initialFilterValue[config.name as keyof typeof initialFilterValue] =
      initialValues[config.name] || [];
  });

  const [filterValue, setFilterValue] =
    useState<DynamicFilterValue<T>>(initialFilterValue);

  useEffect(() => {
    // 当外部initialValues变化时更新内部状态
    const updatedValues = { ...filterValue };
    let hasChanged = false;

    filterConfigs.forEach((config) => {
      const name = config.name as keyof typeof filterValue;
      if (
        initialValues[name] &&
        JSON.stringify(initialValues[name]) !==
          JSON.stringify(filterValue[name])
      ) {
        updatedValues[name] = initialValues[name];
        hasChanged = true;
      }
    });

    if (hasChanged) {
      setFilterValue(updatedValues);
    }
  }, [initialValues, filterConfigs, filterValue]);

  // 渲染单选组
  const renderRadioGroup = (item: FilterConfig) => {
    const currentValue =
      filterValue[item.name as keyof typeof filterValue]?.[0] || "";

    return (
      <RadioGroup
        className="flex flex-wrap gap-1"
        value={currentValue}
        onValueChange={(value: string) => {
          const newFilterValue = {
            ...filterValue,
            [item.name]: [value],
          };
          setFilterValue(newFilterValue);

          if (onChange) {
            onChange(item.name, [value]);
          }
        }}
      >
        {item.options?.map((option) => (
          <div key={option.value} className="flex items-center space-x-1 mr-2">
            <RadioGroupItem
              value={option.value}
              id={`radio-${item.name}-${option.value}`}
              disabled={disabled || option.disabled}
            />
            <Label
              htmlFor={`radio-${item.name}-${option.value}`}
              className={`text-sm font-medium leading-none cursor-pointer ${disabled || option.disabled ? "opacity-50 cursor-not-allowed" : ""}`}
            >
              {option.label}
            </Label>
          </div>
        ))}
      </RadioGroup>
    );
  };

  // 渲染复选组的自定义实现
  const renderCheckboxGroup = (item: FilterConfig) => {
    const currentValue = (
      filterValue[item.name as keyof typeof filterValue] || []
    ).map((val) => val?.toString());

    // 如果有disabled属性，为每个选项添加disabled状态
    const itemsWithDisabled = item.options?.map((option) => ({
      ...option,
      disabled: disabled || option.disabled,
    }));

    return (
      <div className="flex flex-wrap gap-1">
        {itemsWithDisabled?.map((option) => (
          <div key={option.value} className="flex items-center space-x-1 mr-2">
            <Checkbox
              id={`checkbox-${item.name}-${option.value}`}
              checked={currentValue.includes(option.value)}
              disabled={option.disabled}
              onCheckedChange={(checked) => {
                let newValues = [...currentValue];
                if (checked) {
                  if (!newValues.includes(option.value)) {
                    newValues.push(option.value);
                  }
                } else {
                  newValues = newValues.filter((v) => v !== option.value);
                }

                const newFilterValue = {
                  ...filterValue,
                  [item.name]: newValues,
                };
                setFilterValue(newFilterValue);

                if (onChange) {
                  onChange(item.name, newValues);
                }
              }}
            />
            <Label
              htmlFor={`checkbox-${item.name}-${option.value}`}
              className={`text-sm font-medium leading-none cursor-pointer ${option.disabled ? "opacity-50 cursor-not-allowed" : ""}`}
            >
              {option.label}
            </Label>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className="flex flex-wrap -mx-2">
      {filterConfigs.map((item) => (
        <div className="px-2 py-1 flex items-center w-full" key={item.name}>
          <div className="text-gray-600 w-10 flex-shrink-0 text-sm">
            {item.label}
          </div>
          <div className="flex-1">
            {item.type === "radio"
              ? renderRadioGroup(item)
              : renderCheckboxGroup(item)}
          </div>
        </div>
      ))}
    </div>
  );
};
