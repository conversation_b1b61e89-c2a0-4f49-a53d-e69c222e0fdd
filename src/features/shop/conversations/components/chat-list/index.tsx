import React, {
  use<PERSON><PERSON>back,
  useEffect,
  useImperative<PERSON><PERSON><PERSON>,
  useState,
} from "react";
import { Empty, Pagination, Spin } from "antd";
import dayjs from "dayjs";
import { useTranslation } from "react-i18next";
import default_content from "@/assets/imgs/default_content.png";
import { SearchFilters } from "./search-filters";
import { ChatListItem } from "./chat-list-item";
// import { ExportModal } from "./export-modal";
import { GetConversationListParams } from "@/services/message-service";
import { MessageService } from "@/services/message-service";
import { useAtomValue, useSetAtom } from "jotai";
import { shopAtom } from "@/atoms/shop";
import { Button } from "@/components/ui/button";
import { Download, RefreshCw } from "lucide-react";
import {
  currentThreadIdAtom,
  // replyMessageRoleAtom,
  currentMessageIdAtom,
  messageData<PERSON>tom,
  highlightMessageId<PERSON>tom,
  messages<PERSON>tom,
  isRunning<PERSON><PERSON>,
} from "@/atoms/conversation";
import { useQuery } from "@tanstack/react-query";
import { ExportModal } from "./export-modal";

interface ChatListProps {
  ref?: React.Ref<any>;
  sourceType?: string;
  scrollToBottom?: () => void;
  onThreadSelect?: (threadId: string) => void;
}

export type ChatListRef = {
  search: () => void;
};

// 默认搜索参数
const DEFAULT_SEARCH_PARAMS: GetConversationListParams = {
  source: ["agent", "open"],
  pageNum: 1,
  pageSize: 10,
  startTime: dayjs(new Date()).format("YYYY-MM-DD 00:00:00"),
  endTime: dayjs(new Date()).format("YYYY-MM-DD 23:59:59"),
  thumbs: [0, 2, -2],
  // adoptedStatus: ["NOT_ADOPTED", "IGNORE"],
};

export const ChatList: React.FC<ChatListProps> = React.memo(
  ({ ref, sourceType, onThreadSelect }) => {
    const { t } = useTranslation();
    const shop = useAtomValue(shopAtom);
    const [searchParams, setSearchParams] = useState<GetConversationListParams>(
      {
        ...DEFAULT_SEARCH_PARAMS,
        source: sourceType ? [sourceType] : DEFAULT_SEARCH_PARAMS.source,
      },
    );

    // Jotai atoms for conversation state
    const setThreadId = useSetAtom(currentThreadIdAtom);
    const setMessageId = useSetAtom(currentMessageIdAtom);
    const setMessageData = useSetAtom(messageDataAtom);
    const messagesList = useAtomValue(messagesAtom);
    const setHighlightMessageId = useSetAtom(highlightMessageIdAtom);
    const [exportModalOpen, setExportModalOpen] = useState(false);
    const setIsRunning = useSetAtom(isRunningAtom);

    const [selectedConversationId, setSelectedConversationId] =
      useState<string>("");

    // 获取消息函数
    const getMessage = useCallback(
      (conversationId: string, scrollId?: string) => {
        if (!conversationId) return;

        setSelectedConversationId(conversationId);
        setThreadId(conversationId);

        // 设置消息滚动ID
        if (scrollId) {
          setHighlightMessageId(scrollId);
        }

        // Call the onThreadSelect callback if provided
        if (onThreadSelect) {
          onThreadSelect(conversationId);
        }

        return conversationId;
      },
      [setHighlightMessageId, setThreadId, onThreadSelect],
    );

    const { data, isLoading, refetch, isRefetching } = useQuery({
      queryKey: ["conversationList", shop?.id, searchParams],
      queryFn: async () => {
        if (!shop?.id) return;

        return await MessageService.getConversationList({
          ...searchParams,
          shopId: shop?.id,
        });
      },
      refetchOnWindowFocus: false,
    });

    // 处理数据加载后的逻辑
    useEffect(() => {
      if (data && data.results && data.results.length > 0) {
        const firstConversation = data.results[0];

        // Handle specific search cases
        if (searchParams.messageId) {
          getMessage(firstConversation.conversationId, searchParams.messageId);
        } else if (searchParams.dialogueQualityId) {
          getMessage(firstConversation.conversationId);
        } else {
          getMessage(firstConversation.conversationId);
        }
      } else if (data) {
        // 没有会话数据时清空当前选择
        setThreadId(null);
      }
    }, [
      data,
      searchParams.messageId,
      searchParams.dialogueQualityId,
      getMessage,
      setHighlightMessageId,
      setThreadId,
    ]);

    useEffect(() => {
      // 找到开始时间之后最近的消息
      let highlightMessageId = "";
      const startTime = dayjs(searchParams.startTime).valueOf();
      const message = messagesList.data?.messageDTOS.find((message) => {
        const messageTime = +message.messageRealAt;
        return messageTime >= startTime;
      });

      if (message) {
        highlightMessageId = message.messageId;
      }

      if (searchParams.messageId) {
        highlightMessageId = searchParams.messageId;
      }

      setHighlightMessageId(highlightMessageId);
    }, [messagesList, searchParams, setHighlightMessageId]);

    const pageNum = searchParams.pageNum || 1;
    const pageSize = searchParams.pageSize || 10;

    // 搜索函数 - 重置状态并重新获取数据
    const search = useCallback(() => {
      // Reset UI state
      setMessageData({});
      setMessageId(null);
      setHighlightMessageId(null);

      // 使用 React Query 的 refetch 触发重新查询
      refetch();
    }, [refetch, setMessageData, setMessageId, setHighlightMessageId]);

    // 下载对话数据
    const downloadConversations = async (params: any) => {
      if (!shop?.id) return;

      const exportParams = {
        ...params,
        shopId: shop?.id,
      };

      MessageService.exportConversations(exportParams)
        .then((result) => {
          if (result?.data) {
            window.open(result.data, "_self");
          } else {
            console.error("导出失败，未获取到下载链接");
          }
        })
        .catch((error) => {
          console.error("导出失败", error);
        });
    };

    // 暴露search方法给父组件
    useImperativeHandle(ref, () => ({ search }));

    // 处理搜索参数变更
    const handleSearchParamsChange = (
      params: Partial<GetConversationListParams>,
    ) => {
      setSearchParams(() => ({
        ...params,
        pageNum: 1, // 重置到第一页
      }));
    };

    // 处理分页变更
    const handlePaginationChange = (num: number, size: number) => {
      setSearchParams((prev) => ({
        ...prev,
        pageNum: num,
        pageSize: size,
      }));
    };

    const handleChatListItemClick = (conversationId: string) => {
      getMessage(conversationId);
    };

    return (
      <div className="flex flex-col h-full overflow-hidden">
        {/* 搜索过滤器 */}
        <div className="px-4 pt-4  border-b border-gray-100">
          <SearchFilters
            sourceType={sourceType}
            defaultParams={searchParams}
            onSearch={(params) => {
              handleSearchParamsChange(params);
            }}
          />
        </div>

        {/* 总计信息和操作 */}
        <div className="flex justify-between items-center px-4 py-1 border-b border-gray-100">
          <span className="text-gray-600 text-sm">
            {t("common.pager.total", { count: data?.totalSize })}
          </span>
          <div className="flex items-center">
            {!sourceType && (
              <Button
                variant="icon"
                size="icon"
                className="mr-4"
                onClick={() => {
                  refetch();
                }}
                title="刷新"
              >
                <RefreshCw className="w-4 h-4" />
              </Button>
            )}
            {sourceType !== "user" && (
              <Button
                variant="icon"
                size="icon"
                onClick={() => setExportModalOpen(true)}
                title="批量导出"
              >
                <Download className="w-4 h-4" />
              </Button>
            )}
            {sourceType && (
              <div
                className="text-blue-500 cursor-pointer ml-2.5"
                onClick={() => {
                  setIsRunning(false);
                  setThreadId(null);
                  setSelectedConversationId("");
                  setMessageData({});
                  setMessageId(null);
                  setHighlightMessageId(null);
                }}
              >
                {t("add.simulation.conversation")}
              </div>
            )}
          </div>
        </div>

        {/* 会话列表 */}
        <div className="flex-1 min-h-0 overflow-auto">
          <Spin spinning={isLoading || isRefetching} className="flex-1 min-h-0">
            {data && data.results && data.results.length > 0 ? (
              data.results.map((item: any, index) => (
                <ChatListItem
                  key={index}
                  item={item}
                  index={index}
                  pageNum={pageNum}
                  pageSize={pageSize}
                  sourceType={sourceType}
                  selected={selectedConversationId === item.conversationId}
                  onClick={() => handleChatListItemClick(item.conversationId)}
                />
              ))
            ) : (
              <Empty className="mt-[calc(50%-100px)]" image={default_content} />
            )}
          </Spin>
        </div>

        <div className="flex justify-center py-2 border-t border-gray-100">
          <Pagination
            simple
            current={pageNum}
            showSizeChanger={true}
            onChange={handlePaginationChange}
            total={data?.totalSize}
            pageSize={pageSize}
          />
        </div>
        <ExportModal
          open={exportModalOpen}
          setOpen={setExportModalOpen}
          onExport={downloadConversations}
          exportContent={searchParams}
        />
      </div>
    );
  },
);
