import React, { useMemo } from "react";
import { useTranslation } from "react-i18next";
import dayjs from "dayjs";
// import { useAtom } from "jotai";
import { FilterConfig, FilterFields } from "./filter-fields";
import { GetConversationListParams } from "@/services/message-service";
import { DatePicker, ConfigProvider } from "antd";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { PERMISSION_CODES } from "@/constants/permission";
import { usePermission } from "@/hooks/use-permission";

// 临时定义缺失的类型
// TODO: 需要导入或创建这些类型
type ChatListSourceType = string;
// type AdoptedStatusType = string;
type ChatListSearchType = number;

const { RangePicker } = DatePicker;

const options = [
  { value: 6, label: "顾客聊天记录" },
  { value: 7, label: "客服聊天记录" },
  { value: 2, label: "消息ID" },
  { value: 3, label: "会话ID" },
  { value: 4, label: "买家ID" },
  { value: 5, label: "对话ID" },
];

// 默认搜索值
const DEFAULT_SEARCH_VALUES = {
  source: ["agent", "open", "batch"],
  time: [
    dayjs(new Date()).format("YYYY-MM-DD 00:00:00"),
    dayjs(new Date()).format("YYYY-MM-DD 23:59:59"),
  ],
  thumb: [0, 2, -2],
  searchType: 6,
  isQuality: -1,
  isReQuality: -1,
  userTime: [
    dayjs(new Date()).format("YYYY-MM-DD 00:00:00"),
    dayjs(new Date()).format("YYYY-MM-DD 23:59:59"),
  ],
  userThumb: [0, 2, -2],
  userSearchType: 6,
  // adoptedStatus: ["NOT_ADOPTED", "IGNORE"],
};

interface SearchFiltersProps {
  sourceType?: string;
  defaultParams?: Partial<GetConversationListParams>;
  onSearch: (params: Partial<GetConversationListParams>) => void;
}

export const SearchFilters: React.FC<SearchFiltersProps> = ({
  sourceType,
  defaultParams,
  onSearch,
}) => {
  const { t } = useTranslation();

  const [searchValues, setSearchValues] = React.useState({
    ...DEFAULT_SEARCH_VALUES,
    source: sourceType ? [sourceType] : DEFAULT_SEARCH_VALUES.source,
    time:
      defaultParams?.startTime && defaultParams?.endTime
        ? [defaultParams.startTime, defaultParams.endTime]
        : DEFAULT_SEARCH_VALUES.time,
  });
  const [inputValue, setInputValue] = React.useState("");
  const { hasPermission } = usePermission();

  const {
    source,
    time,
    thumb,
    searchType,
    isQuality,
    isReQuality,
    userTime,
    userThumb,
    userSearchType,
    // adoptedStatus,
  } = searchValues;

  // 创建搜索参数的公共函数
  const createSearchParams = (
    overrides: Partial<GetConversationListParams> = {},
  ): Partial<GetConversationListParams> => {
    const searchContent: Partial<GetConversationListParams> = {};
    const type = !sourceType ? searchType : userSearchType;

    // 设置时间范围
    const nowTime = sourceType ? [...userTime] : [...time];
    searchContent.startTime = nowTime[0];
    searchContent.endTime = nowTime[1];

    // 设置来源
    searchContent.source = sourceType ? [sourceType] : source;

    // 设置点赞状态
    searchContent.thumbs = sourceType ? userThumb : thumb;

    // 设置质检状态
    if (isQuality !== -1) {
      searchContent.isQuality = isQuality;
    } else {
      // 明确设置为undefined以清除条件
      searchContent.isQuality = undefined;
    }

    // 设置复检状态
    if (isReQuality !== -1) {
      searchContent.isReQuality = isReQuality;
    } else {
      // 明确设置为undefined以清除条件
      searchContent.isReQuality = undefined;
    }

    // // 设置采用状态
    // if (!sourceType && source?.includes("assist")) {
    //   searchContent.adoptedStatus = adoptedStatus;
    // }

    // 根据搜索类型设置搜索内容
    if (type === 6) {
      searchContent.userContent = inputValue;
    } else if (type === 7) {
      searchContent.customerContent = inputValue;
    } else if (type === 2) {
      searchContent.messageId = inputValue;
    } else if (type === 3) {
      searchContent.conversationId = inputValue;
    } else if (type === 4) {
      searchContent.uid = inputValue;
    } else if (type === 5) {
      searchContent.dialogueQualityId = inputValue;
    }

    // 合并传入的覆盖参数
    return { ...searchContent, ...overrides };
  };

  const disabledDate = (current: dayjs.Dayjs) => {
    return current.isAfter(dayjs());
  };

  // 定义筛选项配置
  const sourceFilterConfig: FilterConfig = {
    type: "checkbox",
    name: "source",
    label: t("common.source"),
    options: [
      { label: t("silent.run.conversation"), value: "agent" },
      { label: t("open.run.conversation"), value: "open" },
      { label: t("assist.run.conversation"), value: "assist" },
      // { label: t("assist.run.transferTask"), value: "transferTask" },
      // { label: t("rerun.conversation"), value: "batch" },
    ],
  };
  if (hasPermission(PERMISSION_CODES.SHOP_CONVERSATION_TRANSFER_TASK_FILTER)) {
    sourceFilterConfig.options?.push({
      label: t("assist.run.transferTask"),
      value: "transferTask",
    });
  }

  const adoptedStatusFilterConfig: FilterConfig = {
    type: "checkbox",
    name: "adoptedStatus",
    label: "采用",
    options: [
      { label: "已采用", value: "ADOPTED" },
      { label: "未采用", value: "NOT_ADOPTED" },
      { label: "无需判断", value: "IGNORE" },
    ],
  };

  const thumbFilterConfig: FilterConfig = {
    type: "checkbox",
    name: "thumb",
    label: t("common.thumb"),
    options: [
      { label: t("common.thumb.thumbs.up"), value: "2" },
      { label: t("common.thumb.thumbs.down"), value: "-2" },
      { label: t("common.thumb.other"), value: "0" },
    ],
  };

  const qualityFilterConfig: FilterConfig = {
    type: "radio",
    name: "isQuality",
    label: t("quality.inspection.quality"),
    options: [
      { label: t("common.all"), value: "-1" },
      { label: t("quality.inspection.is.quality"), value: "1" },
      { label: t("quality.inspection.not.quality"), value: "0" },
    ],
  };

  const reQualityFilterConfig: FilterConfig = {
    type: "radio",
    name: "isReQuality",
    label: t("quality.inspection.re.quality"),
    options: [
      { label: t("common.all"), value: "-1" },
      { label: t("quality.inspection.re.quality"), value: "1" },
      { label: t("quality.inspection.not.requalified"), value: "0" },
    ],
  };

  // 根据条件动态生成筛选配置
  const filterConfigs = useMemo(() => {
    const configs: FilterConfig[] = [];

    // 源筛选配置
    if (!sourceType) {
      configs.push(sourceFilterConfig);
    }

    // 采用状态筛选配置
    if (!sourceType && source?.includes("assist")) {
      configs.push(adoptedStatusFilterConfig);
    }

    // 点赞筛选配置
    configs.push(thumbFilterConfig);

    // 质检和复检筛选配置
    if (sourceType !== "user") {
      configs.push(qualityFilterConfig);
      configs.push(reQualityFilterConfig);
    }

    return configs;
  }, [sourceType, source, t]);

  // 处理筛选值变化
  const handleFilterChange = (name: string, values: string[]) => {
    if (values.length === 0) return;

    const newSearchValues = { ...searchValues };

    switch (name) {
      case "source":
        newSearchValues.source = values as ChatListSourceType[];
        break;
      // case "adoptedStatus":
      //   newSearchValues.adoptedStatus = values as AdoptedStatusType[];
      //   break;
      case "thumb":
        if (!sourceType) {
          newSearchValues.thumb = values.map((v) => Number(v));
        } else {
          newSearchValues.userThumb = values.map((v) => Number(v));
        }
        break;
      case "isQuality":
        newSearchValues.isQuality = Number(values[0]);
        break;
      case "isReQuality":
        newSearchValues.isReQuality = Number(values[0]);
        break;
    }

    setSearchValues(newSearchValues);

    // 根据筛选器类型创建覆盖参数
    const overrides: Partial<GetConversationListParams> = {};

    // 提前计算数值，避免在case块中使用lexical声明
    const qualityValue = name === "isQuality" ? Number(values[0]) : -1;
    const reQualityValue = name === "isReQuality" ? Number(values[0]) : -1;

    switch (name) {
      case "source":
        overrides.source = values as ChatListSourceType[];
        break;
      // case "adoptedStatus":
      //   overrides.adoptedStatus = values as AdoptedStatusType[];
      //   break;
      case "thumb":
        overrides.thumbs = values.map((v) => Number(v));
        break;
      case "isQuality":
        // 无论是否为-1，都需要设置该参数，以确保清除之前的筛选条件
        if (qualityValue !== -1) {
          overrides.isQuality = qualityValue;
        } else {
          // 设置为undefined以明确清除该条件
          overrides.isQuality = undefined;
        }
        break;
      case "isReQuality":
        // 无论是否为-1，都需要设置该参数，以确保清除之前的筛选条件
        if (reQualityValue !== -1) {
          overrides.isReQuality = reQualityValue;
        } else {
          // 设置为undefined以明确清除该条件
          overrides.isReQuality = undefined;
        }
        break;
    }

    // 使用公共函数创建搜索参数，并应用覆盖
    onSearch(createSearchParams(overrides));
  };

  // 准备当前筛选值
  const currentFilterValues = useMemo(() => {
    return {
      source: source || [],
      // adoptedStatus: adoptedStatus || [],
      thumb: (!sourceType ? thumb : userThumb)?.map((v) => v?.toString()) || [],
      isQuality: isQuality !== undefined ? [isQuality.toString()] : ["-1"],
      isReQuality:
        isReQuality !== undefined ? [isReQuality.toString()] : ["-1"],
    };
  }, [
    source,
    // adoptedStatus,
    thumb,
    userThumb,
    isQuality,
    isReQuality,
    sourceType,
  ]);

  // 初始化搜索参数
  React.useEffect(() => {
    // 初始化时直接调用搜索
    onSearch(createSearchParams());
  }, []);

  return (
    <div>
      <div className="mb-4 flex items-center gap-2">
        <div className="flex-1">
          <ConfigProvider
            theme={{
              token: {
                borderRadius: 6,
              },
              components: {
                DatePicker: {
                  controlHeight: 36,
                  controlItemBgActive: "rgba(0, 0, 0, 0.04)",
                },
              },
            }}
          >
            <RangePicker
              disabledDate={disabledDate}
              className="w-full"
              style={{ height: "36px" }}
              value={
                !sourceType
                  ? time?.length > 0
                    ? [dayjs(time[0]), dayjs(time[1])]
                    : undefined
                  : userTime?.length > 0
                    ? [dayjs(userTime[0]), dayjs(userTime[1])]
                    : undefined
              }
              allowClear={false}
              presets={[
                { label: "今天", value: [dayjs(), dayjs()] },
                {
                  label: "昨天",
                  value: [
                    dayjs().subtract(1, "day"),
                    dayjs().subtract(1, "day"),
                  ],
                },
                {
                  label: "7天内",
                  value: [dayjs().subtract(6, "day"), dayjs()],
                },
                {
                  label: "30天内",
                  value: [dayjs().subtract(29, "day"), dayjs()],
                },
              ]}
              onChange={(dates) => {
                const newSearchValues = { ...searchValues };
                if (dates && dates[0] && dates[1]) {
                  // 格式化新的时间范围
                  const newStartTime = dates[0].format("YYYY-MM-DD 00:00:00");
                  const newEndTime = dates[1].format("YYYY-MM-DD 23:59:59");

                  if (!sourceType) {
                    newSearchValues.time = [newStartTime, newEndTime];
                  } else {
                    newSearchValues.userTime = [newStartTime, newEndTime];
                  }
                  setSearchValues(newSearchValues);

                  // 使用时间范围覆盖参数
                  onSearch(
                    createSearchParams({
                      startTime: newStartTime,
                      endTime: newEndTime,
                    }),
                  );
                }
              }}
              format="YYYY-MM-DD"
            />
          </ConfigProvider>
        </div>
      </div>

      <div className="flex w-full items-center mb-3 gap-2">
        <Select
          value={
            !sourceType ? searchType.toString() : userSearchType.toString()
          }
          onValueChange={(value) => {
            const numValue = Number(value) as ChatListSearchType;
            if (!sourceType) {
              setSearchValues({
                ...searchValues,
                searchType: numValue,
              });
            } else {
              setSearchValues({
                ...searchValues,
                userSearchType: numValue,
              });
            }
          }}
          data-testid="search-type-select"
        >
          <SelectTrigger className="w-[130px]">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {options
              .filter((item) =>
                sourceType === "user" ? item.value !== 5 : true,
              )
              .map((option) => (
                <SelectItem key={option.value} value={option.value.toString()}>
                  {option.label}
                </SelectItem>
              ))}
          </SelectContent>
        </Select>

        <div className="flex-1">
          <Input
            className="w-full"
            placeholder="请输入搜索内容,回车键搜索"
            data-testid="search-input"
            value={inputValue}
            onKeyDown={(e) => {
              if (e.key === "Enter") {
                // 使用公共函数创建搜索参数
                onSearch(createSearchParams());
              }
            }}
            onChange={(e) => {
              setInputValue(e.target.value);
            }}
          />
        </div>
      </div>

      <div className="mb-3 p-2 border border-gray-200 rounded-md bg-gray-50">
        <FilterFields
          filterConfigs={filterConfigs}
          initialValues={currentFilterValues}
          onChange={handleFilterChange}
        />
      </div>
    </div>
  );
};
