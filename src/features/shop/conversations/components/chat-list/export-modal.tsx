import React, { useEffect } from "react";
import { DatePicker } from "antd";
import dayjs from "dayjs";
import { useTranslation } from "react-i18next";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogFooter,
  DialogHeader,
} from "@/components/ui/dialog";
import {
  CheckboxGroup,
  CheckboxGroupItem,
} from "@/components/ui/checkbox-group";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
// import { useAtom } from "jotai";
// import { exportModalContentAtom } from "@/atoms/chat-list";

const { RangePicker } = DatePicker;

interface ExportModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  onExport: (params: any) => Promise<any>;
  exportContent?: {
    thumbs?: number[];
    source?: string[];
    startTime?: string;
    endTime?: string;
  };
}

// interface ExportContent {
//   thumb?: number[];
//   source?: string[];
//   time?: string[];
// }

export const ExportModal: React.FC<ExportModalProps> = ({
  open,
  setOpen,
  onExport,
  exportContent,
}) => {
  const { t } = useTranslation();
  // const [exportContent] = useAtom(exportModalContentAtom);

  // 使用本地状态替代传入的props
  const [thumbs, setThumb] = React.useState([0, 2, -2]);
  const [source, setSource] = React.useState(["agent", "open", "assist"]);
  const [loading, setLoading] = React.useState(false);
  const [time, setTime] = React.useState([
    dayjs(new Date()).format("YYYY-MM-DD 00:00:00"),
    dayjs(new Date()).format("YYYY-MM-DD 23:59:59"),
  ]);

  const download = () => {
    toast.info("下载中");
    setLoading(true);

    const searchContent = {
      source,
      thumbs,
      startTime: time[0] ? dayjs(time[0]).format("YYYY-MM-DD 00:00:00") : "",
      endTime: time[1] ? dayjs(time[1]).format("YYYY-MM-DD 23:59:59") : "",
    };

    onExport(searchContent)
      .then(() => {
        setOpen(false);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  useEffect(() => {
    if (open) {
      // 使用exportContent作为默认值，如果存在
      if (exportContent) {
        if (exportContent.thumbs) setThumb(exportContent.thumbs);
        if (exportContent.source)
          setSource(exportContent.source.filter((item) => item !== "batch"));
        if (exportContent.startTime && exportContent.endTime) {
          setTime([exportContent.startTime, exportContent.endTime]);
        }
      } else {
        // 否则使用默认值
        setSource(["agent", "open", "assist"]);
        setThumb([0, 2, -2]);
        setTime([
          dayjs(new Date()).format("YYYY-MM-DD 00:00:00"),
          dayjs(new Date()).format("YYYY-MM-DD 23:59:59"),
        ]);
      }
    }
  }, [open, exportContent]);

  // useEffect(() => {
  //   const content = exportContent as ExportContent;
  //   if (content && Object.keys(content).length > 0) {
  //     if (content.thumb) setThumb(content.thumb);
  //     if (content.source)
  //       setSource(content.source.filter((item) => item !== "batch"));
  //     if (content.time) setTime(content.time);
  //   }
  // }, [exportContent]);

  const disabledDate = (current) => {
    return current && current > dayjs().endOf("day");
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="w-[520px] max-w-[90vw] p-6">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">批量导出</DialogTitle>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* 来源选择 */}
          <div className="grid grid-cols-[50px_1fr] gap-4 items-start">
            <div className="font-medium text-sm text-gray-700">来源</div>
            <CheckboxGroup
              className="space-y-2"
              value={source.map(String)}
              onChange={(e) => {
                if (e?.length === 0) return;
                setSource(e);
              }}
            >
              <div className="grid grid-cols-3 gap-4">
                <CheckboxGroupItem
                  value="agent"
                  label="静默运行消息"
                  disabled={loading}
                />
                <CheckboxGroupItem
                  value="open"
                  label="开线运行消息"
                  disabled={loading}
                />
                <CheckboxGroupItem
                  value="assist"
                  label="AI辅助消息"
                  disabled={loading}
                />
              </div>
              <div className="mt-2">
                <CheckboxGroupItem
                  value="transferTask"
                  label={t("assist.run.transferTask")}
                  disabled={loading}
                />
              </div>
            </CheckboxGroup>
          </div>

          {/* 标注选择 */}
          <div className="grid grid-cols-[50px_1fr] gap-4 items-start">
            <div className="font-medium text-sm text-gray-700">标注</div>
            <CheckboxGroup
              className="space-y-2"
              value={thumbs.map(String)}
              onChange={(e) => {
                setThumb(e.map(Number));
              }}
            >
              <div className="grid grid-cols-3 gap-4">
                <CheckboxGroupItem value="2" label="赞" disabled={loading} />
                <CheckboxGroupItem value="-2" label="踩" disabled={loading} />
                <CheckboxGroupItem value="0" label="其他" disabled={loading} />
              </div>
            </CheckboxGroup>
          </div>

          {/* 时间选择 */}
          <div className="grid grid-cols-[50px_1fr] gap-4 items-start">
            <div className="font-medium text-sm text-gray-700">时间</div>
            <RangePicker
              disabledDate={disabledDate}
              className="w-full !rounded-md !border-input"
              value={
                time?.length > 0
                  ? [dayjs(time[0]), dayjs(time[1])]
                  : [null, null]
              }
              presets={[
                { label: "今天", value: [dayjs(), dayjs()] },
                {
                  label: "昨天",
                  value: [dayjs().add(-1, "d"), dayjs().add(-1, "d")],
                },
                { label: "7天内", value: [dayjs().add(-6, "d"), dayjs()] },
                { label: "30天内", value: [dayjs().add(-29, "d"), dayjs()] },
              ]}
              onChange={(e: any) => {
                if (e && e.length === 2) {
                  setTime([
                    dayjs(e[0]).format("YYYY-MM-DD"),
                    dayjs(e[1]).format("YYYY-MM-DD"),
                  ]);
                }
              }}
              format="YYYY-MM-DD"
            />
          </div>
        </div>

        <DialogFooter className="pt-4 gap-3">
          <Button
            variant="outline"
            size="default"
            onClick={() => setOpen(false)}
          >
            取消
          </Button>
          <Button size="default" onClick={download} loading={loading}>
            确认
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
