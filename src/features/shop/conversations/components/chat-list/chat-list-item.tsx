import React from "react";
import { Tooltip } from "antd";
import Avatar from "@/assets/imgs/avatar.png";

interface ChatListItemProps {
  item: any;
  index: number;
  pageNum: number;
  pageSize: number;
  sourceType?: string;
  onClick: () => void;
  selectedId?: string;
  selected?: boolean;
}

export const ChatListItem: React.FC<ChatListItemProps> = ({
  item,
  index,
  pageNum,
  pageSize,
  sourceType,
  onClick,
  selectedId = "",
  selected = false,
}) => {
  // 使用ref和事件委托模式来处理tooltip显示逻辑
  const textRef = React.useRef<HTMLSpanElement>(null);

  const checkOverflow = () => {
    if (textRef.current) {
      const element = textRef.current;
      return element.offsetWidth < element.scrollWidth;
    }
    return false;
  };

  // 使用 selected 或 selectedId 来确定是否选中
  const isSelected = selected || selectedId === item.conversationId;

  return (
    <div
      className={`flex justify-between items-center p-3 border-b border-gray-100 cursor-pointer ${
        isSelected ? "bg-blue-50" : "hover:bg-gray-50"
      }`}
      onClick={onClick}
    >
      <div className="flex items-center gap-2 flex-1 min-w-0">
        <img src={Avatar} alt="Avatar" className="w-6 h-6 rounded-full" />
        <Tooltip
          title={
            checkOverflow()
              ? sourceType
                ? (JSON.parse(item.userContent)[0]?.text[0]?.value ?? "")
                : item.uid
              : null
          }
          mouseEnterDelay={0.5}
        >
          <span ref={textRef} className="truncate text-sm">
            {sourceType
              ? (JSON.parse(item.userContent)[0]?.text[0]?.value ?? "")
              : item.uid}
          </span>
        </Tooltip>
      </div>
      <span className="text-xs text-gray-500">
        {(pageNum - 1) * pageSize + index < 9
          ? "0" + (index + 1)
          : (pageNum - 1) * pageSize + index + 1}
      </span>
    </div>
  );
};
