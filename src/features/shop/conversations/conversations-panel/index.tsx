import { useEffect, useRef } from "react";
import { ChatList, ChatListRef } from "../components/chat-list/index";
import ChatPanel, { ChatPanelRef } from "../components/chat-panel/index";
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from "@/components/ui/resizable";
import { usePanelSize } from "@/hooks/use-panel-size";
import { clearConversationAtom } from "@/atoms/conversation";
import { useSetAtom } from "jotai";

const ConversationPanel = ({ sourceType = "" }: { sourceType?: string }) => {
  const chatPanelRef = useRef<ChatPanelRef | null>(null);
  const chatListRef = useRef<ChatListRef | null>(null);
  const clearConversation = useSetAtom(clearConversationAtom);

  const chatListPanelProps = usePanelSize({
    panelId: `conversations-chatlist-panel`,
    defaultSize: 30,
    minSize: 20,
  });

  // 刷新会话列表
  const refreshConversations = () => {
    if (chatListRef.current) {
      chatListRef.current.search();
    }
  };

  useEffect(() => {
    return () => {
      clearConversation();
    };
  }, [clearConversation]);

  return (
    <ResizablePanelGroup
      className="flex-1 min-h-0 h-full overflow-hidden"
      direction="horizontal"
      id="conversations-panel-group"
    >
      <ResizablePanel
        maxSize={40}
        className="flex flex-col"
        {...chatListPanelProps}
      >
        <ChatList
          ref={chatListRef}
          sourceType={sourceType}
          // scrollToDialogueSeparator={scrollToDialogueSeparator}
          // onThreadSelect={handleThreadSelect}
        />
      </ResizablePanel>

      <ResizableHandle />

      <ResizablePanel className="flex flex-col">
        <ChatPanel
          ref={chatPanelRef}
          sourceType={sourceType}
          getList={refreshConversations}
          className="flex-1"
        />
      </ResizablePanel>
    </ResizablePanelGroup>
  );
};

export default ConversationPanel;
