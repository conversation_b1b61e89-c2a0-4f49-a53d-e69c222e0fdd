import { goOnline<PERSON>tom } from "@/atoms/transfer-task";
import { Button } from "@/components/ui/button";
import { useSet<PERSON>tom } from "jotai";
import { WifiOffIcon } from "lucide-react";

export const OfflineState = () => {
  const goOnline = useSetAtom(goOnlineAtom);

  return (
    <div className="flex flex-col items-center justify-center h-full py-8 px-4 text-center">
      <div className="bg-destructive/10 p-5 rounded-full mb-5">
        <WifiOffIcon className="h-6 w-6 text-destructive" />
      </div>
      <h3 className="text-base font-medium mb-2">您目前处于离线状态</h3>
      <p className="text-sm text-muted-foreground mb-6 max-w-[280px]">
        需要切换到在线状态才能接收客户转接任务
      </p>
      <Button variant="default" onClick={() => goOnline()} className="px-6">
        上线
      </Button>
    </div>
  );
};
