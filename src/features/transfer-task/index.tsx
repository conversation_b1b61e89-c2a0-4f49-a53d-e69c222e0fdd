import { webSocketStatusAtom } from "@/atoms/transfer-task";
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from "@/components/ui/resizable";
import { usePanelSize } from "@/hooks/use-panel-size";
import { useAtomValue } from "jotai";
import { useMemo } from "react";
import { ChatMessagePanel } from "./chat-message-panel";
import { TransferTaskInfoPanel } from "./transfer-task-info-panel";
import { TransferTasksPanel } from "./transfer-tasks-panel";

export const TransferTask = () => {
  const { isOnline } = useAtomValue(webSocketStatusAtom);
  const leftPanelProps = usePanelSize({
    panelId: "transfer-task-left-panel",
    defaultSize: 16,
    minSize: 12,
  });
  const rightPanelProps = usePanelSize({
    panelId: "transfer-task-right-panel",
    defaultSize: 20,
    minSize: 16,
  });
  const centerPanelSize = useMemo(() => {
    let width = 100;

    width -= leftPanelProps.defaultSize;

    // 如果在线状态下才减去右侧面板宽度
    if (isOnline) {
      width -= rightPanelProps.defaultSize;
    }

    return width;
  }, [leftPanelProps.defaultSize, rightPanelProps.defaultSize, isOnline]);

  return (
    <ResizablePanelGroup
      direction="horizontal"
      className="h-full rounded-md overflow-hidden"
    >
      {/* 左侧客户列表面板 - 始终显示 */}
      <ResizablePanel
        id="transfer-task-left-panel"
        order={1}
        {...leftPanelProps}
      >
        <TransferTasksPanel />
      </ResizablePanel>

      <ResizableHandle id="handle-left-center" />

      {/* 中间聊天区域 - 始终显示 */}
      <ResizablePanel
        id="transfer-task-center-panel"
        order={2}
        defaultSize={centerPanelSize}
        className="flex flex-col h-full"
      >
        <ChatMessagePanel />
      </ResizablePanel>

      {/* 右侧客户信息面板 - 仅在在线状态下显示 */}
      {isOnline ? (
        <>
          <ResizableHandle id="handle-center-right" />
          <ResizablePanel
            id="transfer-task-right-panel"
            order={3}
            {...rightPanelProps}
          >
            <TransferTaskInfoPanel />
          </ResizablePanel>
        </>
      ) : (
        <>
          <ResizableHandle className="hidden" id="handle-center-right" />
          <ResizablePanel
            id="transfer-task-right-panel-placeholder"
            className="hidden"
            order={3}
            defaultSize={0}
            minSize={0}
            maxSize={0}
          />
        </>
      )}
    </ResizablePanelGroup>
  );
};
