import { ChatMessageArea } from "@/components/ui/chat-message-area";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";

export const ChatMessageListSkeleton = () => {
  const messageCount = Math.floor(Math.random() * 3) + 3;

  return (
    <ChatMessageArea
      className="p-5 space-y-6 min-w-125"
      autoScrollToBottom={false}
    >
      {Array.from({ length: messageCount }).map((_, index) => {
        const isUser = index % 2 === 0;
        const position = isUser ? "right" : "left";

        return (
          <div key={index} className="flex flex-col gap-2">
            {/* 消息时间和发送者信息 */}
            <div
              className={cn("flex items-center gap-2 text-xs", {
                "justify-end": position === "right",
                "justify-start": position === "left",
              })}
            >
              <Skeleton className="h-3 w-24" />
            </div>

            {/* 消息气泡 */}
            <div
              className={cn("flex gap-3", {
                "flex-row-reverse": position === "right",
                "flex-row": position === "left",
              })}
            >
              {/* 头像 */}
              <Skeleton className="h-8 w-8 rounded-full flex-shrink-0" />

              {/* 消息内容 */}
              <div className="flex flex-col gap-2 max-w-[85%]">
                <div
                  className={cn({
                    "ml-auto": position === "right",
                  })}
                >
                  <Skeleton
                    className={cn("h-20 w-64 rounded-lg", {
                      "ml-auto": position === "right",
                      "mr-auto": position === "left",
                    })}
                  />
                </div>
              </div>
            </div>
          </div>
        );
      })}
    </ChatMessageArea>
  );
};
