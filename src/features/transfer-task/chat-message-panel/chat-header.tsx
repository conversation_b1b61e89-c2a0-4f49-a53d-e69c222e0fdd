import { CopyableText } from "@/components/common/copyable-text";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { UserAvatar } from "@/components/ui/user-avatar";
import { TransferTaskService } from "@/services";
import {
  ProcState,
  TransferTask,
  TransferType,
} from "@/types/api/transfer-task";
import { useMutation } from "@tanstack/react-query";
import { HeadphonesIcon, ShoppingCartIcon } from "lucide-react";
import { useCallback } from "react";
import { useTranslation } from "react-i18next";

export const ChatHeader = ({
  currentTransferTask,
}: {
  currentTransferTask: TransferTask;
}) => {
  const { t } = useTranslation();
  const { mutate: finishTransferTaskMutation } = useMutation({
    mutationKey: ["finishTransferTask"],
    mutationFn: TransferTaskService.finishTransferTask,
  });

  // 根据转接类型显示不同的文本
  const getTransferTypeText = useCallback(
    (type: TransferType) => {
      switch (type) {
        case TransferType.MYQA_TRANSFER:
          return t("transfer.task.type.myqa.transfer");
        case TransferType.STOP_WORDS:
          return t("transfer.task.type.stop.words");
        case TransferType.DUPLICATED:
          return t("transfer.task.type.duplicated");
        case TransferType.DUPLICATED_QUESTION:
          return t("transfer.task.type.duplicated.question");
        default:
          return type;
      }
    },
    [t],
  );

  return (
    <div className="flex items-center justify-between border-b px-4 py-3 bg-background h-12 gap-4">
      <div className="flex items-center gap-2 flex-1 min-w-0">
        <UserAvatar name={currentTransferTask.uid} size="md" />
        <div className="flex items-center gap-2 flex-1 min-w-0">
          <span className="inline-block text-sm truncate">
            {currentTransferTask.uid}
          </span>
          <div className="flex items-center gap-1 flex-1 min-w-0  text-description text-xs">
            <span className="whitespace-nowrap">{t("conversation.id")}</span>
            <CopyableText
              wrapperClassName="flex-1 min-w-0"
              text={currentTransferTask.conversationId}
            />
          </div>
        </div>
      </div>

      <div className="flex items-center h-full shrink-0 gap-2">
        <Label variant="info">
          {t("transfer.task.type.label")}
          {getTransferTypeText(currentTransferTask.transferType)}
        </Label>
        <Button
          size="sm"
          variant="ghost"
          icon={<ShoppingCartIcon className="mr-0.5" />}
          onClick={() => {
            finishTransferTaskMutation({
              content: null,
              taskId: currentTransferTask.taskId,
              procState: ProcState.PRE_SALE,
            });
          }}
        >
          {t("transfer.task.transfer.pre.sale")}
        </Button>
        <Separator orientation="vertical" />
        <Button
          size="sm"
          variant="ghost"
          icon={<HeadphonesIcon className="mr-0.5" />}
          onClick={() => {
            finishTransferTaskMutation({
              content: null,
              taskId: currentTransferTask.taskId,
              procState: ProcState.AFTER_SALE,
            });
          }}
        >
          {t("transfer.task.transfer.after.sale")}
        </Button>
      </div>
    </div>
  );
};
