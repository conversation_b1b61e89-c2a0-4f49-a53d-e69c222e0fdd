import { transferTaskMessagesAtom } from "@/atoms/transfer-task";
import { Message } from "@/components/common/message";
import { ChatMessageArea } from "@/components/ui/chat-message-area";
import { useAtomValue } from "jotai";

export const ChatMessageList = () => {
  const { data: messages } = useAtomValue(transferTaskMessagesAtom);

  if (messages == null) {
    return null;
  }

  return (
    <ChatMessageArea
      className="p-5 space-y-6 min-w-125"
      autoScrollToBottom={true}
    >
      {messages.map((message) => (
        <Message key={message.messageId} message={message} />
      ))}
    </ChatMessageArea>
  );
};
