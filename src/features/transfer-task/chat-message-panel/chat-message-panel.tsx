import { currentTransferTaskAtom } from "@/atoms/transfer-task";
import { TransferTaskService } from "@/services";
import {
  createPictureMessage,
  createTextMessage,
  MessageItem,
  stringifyMessageContent,
} from "@/utils";
import { useMutation } from "@tanstack/react-query";
import { useAtomValue } from "jotai";
import { Suspense, useCallback } from "react";
import { ChatHeader } from "./chat-header";
import { ChatInputBox } from "./chat-input-box";
import { ChatMessageList } from "./chat-message-list";
import { ChatMessageListSkeleton } from "./chat-message-list-skeleton";
import { EmptyChatState } from "./empty-chat-state";
import { usePermission } from "@/hooks/use-permission";

export const ChatMessagePanel = () => {
  const currentTransferTask = useAtomValue(currentTransferTaskAtom);
  const { hasPermission } = usePermission();
  const { mutate: finishTransferTaskMutation } = useMutation({
    mutationKey: ["finishTransferTask"],
    mutationFn: TransferTaskService.finishTransferTask,
  });
  const onSubmit = useCallback(
    (inputMessage: string, images: string[]) => {
      if (currentTransferTask) {
        const content: MessageItem[] = [];

        if (inputMessage.length) {
          content.push(createTextMessage(inputMessage));
        }

        if (images.length) {
          content.push(...images.map((image) => createPictureMessage(image)));
        }

        finishTransferTaskMutation({
          content: stringifyMessageContent(content),
          taskId: currentTransferTask.taskId,
          procState: null,
        });
      }
    },
    [currentTransferTask, finishTransferTaskMutation],
  );

  return (
    <>
      {currentTransferTask ? (
        <>
          <ChatHeader currentTransferTask={currentTransferTask} />

          <div className="flex-1 flex flex-col min-h-0">
            <Suspense fallback={<ChatMessageListSkeleton />}>
              <ChatMessageList />
            </Suspense>

            {hasPermission("workbench:transfer:task:enable") && (
              <ChatInputBox
                onSubmit={onSubmit}
                stopWords={currentTransferTask.stopWords}
              />
            )}
          </div>
        </>
      ) : (
        <EmptyChatState />
      )}
    </>
  );
};
