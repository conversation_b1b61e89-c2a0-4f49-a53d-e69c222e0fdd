import { ArrowLeftIcon, MessagesSquareIcon, UserIcon } from "lucide-react";

export const EmptyChatState = () => {
  return (
    <div className="flex h-full w-full items-center justify-center flex-col gap-5 bg-background">
      <div className="flex flex-col items-center max-w-[380px] text-center">
        <div className="flex justify-center mb-6 relative">
          <div className="relative z-10 bg-primary/10 p-6 rounded-full">
            <MessagesSquareIcon className="h-12 w-12 text-primary" />
          </div>
          <div className="absolute -right-5 -top-2 bg-blue-100 p-3 rounded-full dark:bg-blue-900/30">
            <UserIcon className="h-6 w-6 text-blue-500" />
          </div>
          <div className="absolute -left-5 bottom-0 bg-green-100 p-3 rounded-full dark:bg-green-900/30">
            <ArrowLeftIcon className="h-6 w-6 text-green-500" />
          </div>
        </div>

        <h3 className="text-xl font-medium mb-3">等待开始对话</h3>
        <p className="text-muted-foreground text-sm mb-6 max-w-[280px]">
          从左侧列表中选择一个客户开始回复
        </p>
      </div>
    </div>
  );
};
