import { transferTaskInputMessageAtom } from "@/atoms/transfer-task";
import { TextareaImageUploader } from "@/components/common/textarea-image-uploader";
import {
  ChatInput,
  ChatInputSubmit,
  ChatInputTextArea,
} from "@/components/ui/chat-input";
import { useAtom } from "jotai";
import { ImageIcon } from "lucide-react";
import { useCallback, useState } from "react";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

interface ChatInputBoxProps {
  onSubmit: (text: string, images: string[]) => void;
  stopWords?: string;
}

export const ChatInputBox = ({ onSubmit, stopWords }: ChatInputBoxProps) => {
  const [inputMessage, setInputMessage] = useAtom(transferTaskInputMessageAtom);
  const [submitting, setSubmitting] = useState(false);
  const { t } = useTranslation();

  // 检查输入内容是否包含违禁词
  const hasStopWords = useCallback(() => {
    if (!stopWords || !inputMessage) {
      return false;
    }

    return inputMessage.includes(stopWords);
  }, [inputMessage, stopWords]);

  // 发送消息并包含图片
  const handleSubmit = useCallback(
    ({
      isUploading,
      uploadedImages,
      clearImages,
    }: {
      isUploading: boolean;
      uploadedImages: string[];
      clearImages: () => void;
    }) => {
      if (isUploading || submitting) {
        toast.warning(
          isUploading
            ? t("common.file.upload.in.progress")
            : t("common.message.sending.in.progress"),
        );
        return;
      }

      // 检查是否包含违禁词
      if (hasStopWords()) {
        toast.error(t("common.message.contains.stop.words"), {
          description: t("common.message.contains.stop.words.description", {
            stopWords,
          }),
        });
        return;
      }

      setSubmitting(true);

      try {
        onSubmit(inputMessage.trim(), uploadedImages);

        // 清空已发送的图片
        clearImages();
        setInputMessage("");
      } finally {
        // 防止快速重复提交，添加小延迟
        setTimeout(() => {
          setSubmitting(false);
        }, 300);
      }
    },
    [
      submitting,
      hasStopWords,
      t,
      stopWords,
      onSubmit,
      inputMessage,
      setInputMessage,
    ],
  );

  return (
    <div className="bg-background border-t">
      {/* 违禁词警告提示 */}
      {stopWords && (
        <div className="px-4 py-2 bg-red-50 border-b border-border">
          <p className="text-sm text-red-600 font-medium">
            当前会话包含违禁词: {stopWords}
          </p>
        </div>
      )}

      <TextareaImageUploader>
        {({
          dragProps,
          isUploading,
          uploadedImages,
          clearImages,
          onImageUpload,
          onKeyDown,
        }) => (
          <ChatInput
            value={inputMessage}
            onChange={(e) => {
              setInputMessage(e.target.value);
            }}
            className="border border-border"
          >
            <ChatInputTextArea
              placeholder="请输入回复内容或粘贴图片..."
              className="min-h-[60px] focus-visible:ring-0"
              onKeyDown={onKeyDown}
              {...dragProps}
            />
            <div className="flex justify-between items-center w-full pt-2">
              <div className="flex gap-2">
                <button
                  className="p-1.5 rounded-md hover:bg-slate-100 text-slate-500 transition-colors cursor-pointer"
                  onClick={onImageUpload}
                  title="上传图片"
                  disabled={isUploading}
                >
                  <ImageIcon className="h-5 w-5" />
                </button>
              </div>
              <ChatInputSubmit
                className="px-4 py-1.5"
                onClick={() => {
                  handleSubmit({ isUploading, uploadedImages, clearImages });
                }}
                disabled={
                  isUploading ||
                  submitting ||
                  (!inputMessage && uploadedImages.length == 0)
                }
              >
                <span className="font-medium text-base">
                  {submitting ? "发送中..." : "发送"}
                </span>
              </ChatInputSubmit>
            </div>
          </ChatInput>
        )}
      </TextareaImageUploader>
    </div>
  );
};
