import {
  currentTransferTask<PERSON>tom,
  queuedTransferTasksAtom,
  webSocketStatusAtom,
} from "@/atoms/transfer-task";
import { OnlineStatusSelect } from "@/components/common/online-status-select";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { isTaskTimeout } from "@/lib/transfer-task";
import { TransferTaskService } from "@/services";
import { useMutation } from "@tanstack/react-query";
import { useAtomValue } from "jotai";
import { MessagesSquareIcon, UserCircle2Icon } from "lucide-react";
import { memo, useCallback } from "react";
import { OfflineState } from "./offline-state";
import { TransferTaskCard } from "./transfer-task-card";
import { TransferTaskEmptyState } from "./transfer-task-empty-state";
import { toast } from "sonner";
import { useTranslation } from "react-i18next";
import { usePermission } from "@/hooks/use-permission";

export const TransferTasksPanelContent = memo(() => {
  const { t } = useTranslation();
  const queuedTransferTasks = useAtomValue(queuedTransferTasksAtom);
  const currentTransferTask = useAtomValue(currentTransferTaskAtom);
  const { isOnline } = useAtomValue(webSocketStatusAtom);
  const { hasPermission } = usePermission();
  const { mutate: grabTransferTask } = useMutation({
    mutationKey: ["grabTransferTask"],
    mutationFn: TransferTaskService.grabTransferTask,
  });
  const handleTaskGrab = useCallback(
    (taskId: string, createdAt: string) => () => {
      if (hasPermission("workbench:transfer:task:enable")) {
        toast.warning(t("common.tip.permission.no"));

        return;
      }
      if (currentTransferTask) {
        toast.warning(t("grab.transfer.task.grab.error"));

        return;
      }

      if (isTaskTimeout(createdAt)) {
        toast.error(t("grab.transfer.task.timeout"));

        return;
      }

      grabTransferTask({
        taskId,
      });
    },
    [currentTransferTask, grabTransferTask, t],
  );

  if (!isOnline) {
    return (
      <div className="flex flex-col h-full text-sm">
        {/* 在线状态切换区域 */}
        <div className="flex items-center px-4 py-3 border-b border-border h-12 gap-2">
          <div className="inline-block flex-1 min-w-0 whitespace-nowrap truncate">
            客户转接
          </div>

          <OnlineStatusSelect />
        </div>

        <OfflineState />
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full text-sm">
      {/* 在线状态切换区域 */}
      <div className="flex items-center px-4 py-3 border-b border-border h-12 gap-2">
        <div className="inline-block flex-1 min-w-0 whitespace-nowrap truncate">
          正在回复
        </div>

        <OnlineStatusSelect />
      </div>

      <ScrollArea className="w-full h-36">
        <div className="p-2 space-y-2 w-(--scroll-area-width)">
          {currentTransferTask ? (
            <TransferTaskCard
              key={currentTransferTask.taskId}
              isSelected={true}
              name={currentTransferTask.uid}
              createdAt={currentTransferTask.operatorStartDate}
            />
          ) : (
            <TransferTaskEmptyState
              icon={UserCircle2Icon}
              title="暂无正在回复的客户"
              description="您可以从排队列表中接收一个任务"
            />
          )}
        </div>
      </ScrollArea>

      <Separator className="my-1" />

      {/* 正在排队区域 */}
      <div className="flex flex-col flex-1 min-h-0">
        <div className="flex items-center border-b border-border px-4 py-3 h-12 gap-2 justify-between">
          <span>正在排队</span>
          <span className="bg-primary/10 text-primary text-xs rounded-full size-6 inline-flex items-center justify-center font-medium">
            {queuedTransferTasks.length}
          </span>
        </div>
        <div className="flex flex-1 min-h-0">
          <ScrollArea className="w-full">
            <div className="p-2 space-y-2 w-(--scroll-area-width)">
              {queuedTransferTasks.length > 0 ? (
                queuedTransferTasks.map((task) => (
                  <TransferTaskCard
                    key={task.taskId}
                    name={task.uid}
                    isSelected={false}
                    createdAt={task.createdAt}
                    onClick={handleTaskGrab(task.taskId, task.createdAt)}
                  />
                ))
              ) : (
                <TransferTaskEmptyState
                  icon={MessagesSquareIcon}
                  title="暂无排队中的客户"
                  description="当有客户需要人工服务时会在此显示"
                />
              )}
            </div>
          </ScrollArea>
        </div>
      </div>
    </div>
  );
});
