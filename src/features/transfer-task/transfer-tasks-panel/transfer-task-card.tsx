import { UserAvatar } from "@/components/ui/user-avatar";
import { cn } from "@/lib/utils";
import { ComponentProps } from "react";
import { WaitTimeCountDown } from "./wait-time-count-down";

interface TransferTaskCardProps extends ComponentProps<"div"> {
  name: string;
  isSelected: boolean;
  createdAt?: string | null;
}

export const TransferTaskCard = ({
  isSelected,
  className,
  name,
  createdAt,
  ...props
}: TransferTaskCardProps) => {
  return (
    <div
      className={cn(
        "py-2.5 px-3 cursor-pointer rounded-lg",
        {
          "bg-primary-foreground text-primary": isSelected,
          "hover:bg-slate-100 border-r-transparent": !isSelected,
        },
        className,
      )}
      {...props}
    >
      <div className="flex items-center gap-2">
        <UserAvatar name={name} />
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between gap-1">
            <div className="truncate text-sm">{name}</div>

            {createdAt && <WaitTimeCountDown createdAt={createdAt} />}
          </div>
        </div>
      </div>
    </div>
  );
};
