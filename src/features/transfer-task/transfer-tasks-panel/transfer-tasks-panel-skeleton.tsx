import { Skeleton } from "@/components/ui/skeleton";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";

export const TransferTasksPanelSkeleton = () => {
  return (
    <div className="flex flex-col h-full text-sm">
      {/* 在线状态切换区域 */}
      <div className="flex items-center px-4 py-3 border-b border-border h-12 gap-2">
        <div className="flex-1 min-w-0 flex items-center gap-2 whitespace-nowrap">
          <Skeleton className="h-4 w-16" />
        </div>

        <Skeleton className="h-7 w-24" />
      </div>

      <ScrollArea className="h-[120px]">
        <div className="p-2 space-y-2">
          <TransferTaskCardSkeleton />
        </div>
      </ScrollArea>

      <Separator className="my-1" />

      {/* 正在排队区域 */}
      <div className="flex flex-col flex-1 min-h-0">
        <div className="flex items-center border-b border-border px-4 py-3 h-12 gap-2">
          <Skeleton className="h-4 w-16" />
          <Skeleton className="h-5 w-5 rounded-full" />
        </div>
        <div className="flex flex-1 min-h-0">
          <ScrollArea className="w-full">
            <div className="p-2 space-y-2 w-(--scroll-area-width)">
              {Array.from({ length: 3 }).map((_, index) => (
                <TransferTaskCardSkeleton key={index} />
              ))}
            </div>
          </ScrollArea>
        </div>
      </div>
    </div>
  );
};

const TransferTaskCardSkeleton = () => {
  return (
    <div className="py-2.5 px-3 rounded-lg">
      <div className="flex items-center gap-2">
        <Skeleton className="h-8 w-8 rounded-full" />
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between">
            <Skeleton className="h-4 w-24" />
          </div>
        </div>
      </div>
    </div>
  );
};
