import { LucideIcon } from "lucide-react";

interface TransferTaskEmptyStateProps {
  icon: LucideIcon;
  title: string;
  description?: string;
}

export const TransferTaskEmptyState = ({
  icon: Icon,
  title,
  description,
}: TransferTaskEmptyStateProps) => {
  return (
    <div className="flex flex-col items-center justify-center w-full h-full p-4 text-center">
      <div className="bg-primary/10 p-3 rounded-full mb-3">
        <Icon className="h-5 w-5 text-primary" />
      </div>
      <p className="text-sm font-medium text-muted-foreground">{title}</p>
      {description && (
        <p className="text-xs text-muted-foreground/80 mt-1 max-w-[200px]">
          {description}
        </p>
      )}
    </div>
  );
};
