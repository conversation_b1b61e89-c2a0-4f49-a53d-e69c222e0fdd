import { Label } from "@/components/ui/label";
import {
  calculateRemainingTime,
  formatRemainingTime,
  isTaskTimeout,
} from "@/lib/transfer-task";
import dayjs from "dayjs";
import duration from "dayjs/plugin/duration";
import { useEffect, useState } from "react";

// 扩展 dayjs 以支持 duration
dayjs.extend(duration);

interface WaitTimeCountDownProps {
  createdAt: string;
  timeout?: number; // 超时时间，默认为2分钟（120秒）
}

export const WaitTimeCountDown = ({
  createdAt,
  timeout = 120,
}: WaitTimeCountDownProps) => {
  const [remainingSeconds, setRemainingSeconds] = useState<number>(0);
  const [isTimeout, setIsTimeout] = useState<boolean>(false);

  useEffect(() => {
    // 初始化计算
    const updateRemainingTime = () => {
      const remaining = calculateRemainingTime(createdAt, timeout);
      setRemainingSeconds(remaining);
      setIsTimeout(isTaskTimeout(createdAt, timeout));
    };

    // 初始化计算一次
    updateRemainingTime();

    // 设置定时器，每秒更新一次
    const timerId = setInterval(updateRemainingTime, 1000);

    return () => clearInterval(timerId);
  }, [createdAt, timeout]);

  // 根据剩余时间确定显示颜色
  const getVariant = () => {
    if (isTimeout) return "error";
    if (remainingSeconds < 30) return "warning";
    return "info";
  };

  return (
    <Label variant={getVariant()}>
      {isTimeout ? "已超时" : formatRemainingTime(remainingSeconds)}
    </Label>
  );
};
