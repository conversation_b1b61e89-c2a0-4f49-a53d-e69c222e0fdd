// 订单状态枚举
export enum OrderStatusEnum {
  UNPAID = "UNPAID", // 未付款
  WAITING_SHIPMENT = "WAITING_SHIPMENT", // 待发货
  SHIPPED = "SHIPPED", // 已发货
  WAITING_REVIEW = "WAITING_REVIEW", // 未评价
  REVIEWED = "REVIEWED", // 已评价
  CLOSED = "CLOSED", // 已关闭
  COMPLETED = "COMPLETED", // 已完成
  UNKNOWN = "UNKNOWN", // 未知状态
}

export const OrderStatusMap: Record<OrderStatusEnum, string> = {
  [OrderStatusEnum.UNPAID]: "未付款",
  [OrderStatusEnum.WAITING_SHIPMENT]: "待发货",
  [OrderStatusEnum.SHIPPED]: "已发货",
  [OrderStatusEnum.WAITING_REVIEW]: "未评价",
  [OrderStatusEnum.REVIEWED]: "已评价",
  [OrderStatusEnum.CLOSED]: "已关闭",
  [OrderStatusEnum.COMPLETED]: "已完成",
  [OrderStatusEnum.UNKNOWN]: "未知状态",
};

// 根据订单状态码获取订单状态
export const getOrderStatus = (
  orderStatus: number,
  payStatus: number,
  shippingStatus: number,
): OrderStatusEnum => {
  if (orderStatus === 2) return OrderStatusEnum.CLOSED;

  if (orderStatus === 1) {
    if (payStatus === 0) return OrderStatusEnum.UNPAID;

    if (payStatus === 1) {
      if (shippingStatus === 0) return OrderStatusEnum.WAITING_SHIPMENT;
      if (shippingStatus === 1) return OrderStatusEnum.SHIPPED;
      if (shippingStatus === 2) return OrderStatusEnum.COMPLETED;
    }
  }

  return OrderStatusEnum.UNKNOWN;
};
