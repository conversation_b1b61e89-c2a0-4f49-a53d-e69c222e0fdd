import { transferTaskMessages<PERSON>tom } from "@/atoms/transfer-task";
import { ScrollArea } from "@/components/ui/scroll-area";
import { MessageService } from "@/services/message-service";
import { MessageLogData, UserOrder } from "@/types/api/configure-item";
import { MessageSource } from "@/types/api/message";
import { safeParseJson } from "@/utils";
import { useSuspenseQuery } from "@tanstack/react-query";
import { useAtomValue } from "jotai";
import { get, last } from "lodash";
import { ClipboardList, FileQuestion, Package } from "lucide-react";
import { useEffect, useMemo, useState } from "react";
import { InfoCard, OrderItem } from "./components";

// 空状态组件
const EmptyInfoState = () => {
  return (
    <div className="flex h-full w-full items-center justify-center flex-col py-12">
      <div className="flex flex-col items-center max-w-[320px] text-center">
        <div className="flex justify-center mb-5 relative">
          <div className="relative z-10 bg-primary/10 p-4 rounded-full">
            <FileQuestion className="h-8 w-8 text-primary" />
          </div>
        </div>

        <h3 className="text-base font-medium mb-2">暂无信息</h3>
        <p className="text-muted-foreground text-sm max-w-[260px]">
          当前没有可显示的商品或订单信息
        </p>
      </div>
    </div>
  );
};

export const TransferTaskInfoPanelContent = () => {
  const { data: messages } = useAtomValue(transferTaskMessagesAtom);
  const lastUserMessage = last(
    messages?.filter((message) => message.role === MessageSource.user),
  );
  const { data } = useSuspenseQuery({
    queryKey: ["getMessageLog", lastUserMessage?.conversationId],
    queryFn: async () => {
      if (lastUserMessage == null) {
        return null;
      }

      return MessageService.getMessageLog({
        messageId: lastUserMessage.messageId,
      }).then((data) =>
        safeParseJson<MessageLogData["metadata"]>(data.metaData, {}),
      );
    },
  });
  const goodTitle = useMemo(() => get(data, "goods.title"), [data]);
  const rawUserOrders = useMemo(
    () => get(data, "userOrders", []) as UserOrder[],
    [data],
  );

  // 过滤掉"已关闭"状态的订单并按时间排序（从新到旧）
  const userOrders = useMemo(
    () =>
      rawUserOrders
        .filter((order) => order.orderStatus !== 2)
        .sort((a, b) => b.orderTime - a.orderTime),
    [rawUserOrders],
  );

  // 控制每个订单的折叠状态
  const [openOrders, setOpenOrders] = useState<Record<string, boolean>>({});

  // 初始化时，默认展开第一条订单
  useEffect(() => {
    if (userOrders.length > 0) {
      setOpenOrders({ [userOrders[0].orderSn]: true });
    }
  }, [userOrders]);

  // 切换订单的折叠状态
  const toggleOrder = (orderId: string) => {
    setOpenOrders((prev) => ({
      ...prev,
      [orderId]: !prev[orderId],
    }));
  };

  // 检查是否需要显示空状态
  const showEmptyState = !goodTitle && userOrders.length === 0;

  return (
    <ScrollArea className="h-full w-full">
      {showEmptyState ? (
        <EmptyInfoState />
      ) : (
        <div className="space-y-4 p-4 text-sm">
          {goodTitle != null && (
            <InfoCard
              icon={<Package className="h-5 w-5 mr-2 text-primary" />}
              title="进店商品"
            >
              {goodTitle}
            </InfoCard>
          )}

          {userOrders.length > 0 && (
            <InfoCard
              icon={<ClipboardList className="h-5 w-5 mr-2 text-primary" />}
              title="订单信息"
            >
              <div className="space-y-2">
                {userOrders.map((order) => (
                  <OrderItem
                    key={order.orderSn}
                    order={order}
                    isOpen={openOrders[order.orderSn] || false}
                    onToggle={() => toggleOrder(order.orderSn)}
                  />
                ))}
              </div>
            </InfoCard>
          )}
        </div>
      )}
    </ScrollArea>
  );
};
