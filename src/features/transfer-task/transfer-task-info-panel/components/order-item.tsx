import { CopyableText } from "@/components/common/copyable-text";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { cn } from "@/lib/utils";
import { OrderGood, UserOrder } from "@/types/api/configure-item";
import dayjs from "dayjs";
import { ChevronDown } from "lucide-react";
import { memo } from "react";
import { getOrderStatus } from "../constants/order-status";
import { GoodDetail } from "./good-detail";
import { OrderStatusBadge } from "./order-status-badge";

interface OrderItemProps {
  order: UserOrder;
  isOpen: boolean;
  onToggle: () => void;
}

export const OrderItem = memo(({ order, isOpen, onToggle }: OrderItemProps) => {
  const orderStatus = getOrderStatus(
    order.orderStatus,
    order.payStatus,
    order.shippingStatus,
  );

  return (
    <Collapsible
      open={isOpen}
      onOpenChange={onToggle}
      className="border border-border rounded-lg overflow-hidden bg-accent/5 hover:bg-accent/10 transition-colors"
    >
      <CollapsibleTrigger
        asChild={true}
        className="flex justify-between items-center w-full py-2.5 px-3 cursor-pointer hover:bg-accent/20 transition-colors"
      >
        <div className="flex items-center justify-between w-full">
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-xs">
              <span>订单编号:</span>
              <CopyableText text={order.orderSn} />
            </div>
            <div className="flex items-center gap-3 text-muted-foreground text-xs">
              <span className="inline-flex items-center gap-1.5">
                <span>订单时间:</span>
                <span>
                  {dayjs(order.orderTime * 1000).format("YYYY-MM-DD HH:mm:ss")}
                </span>
              </span>
              <div className="h-3.5 w-px bg-border" />
              <div className="flex items-center gap-1.5">
                <span>订单状态:</span>
                <OrderStatusBadge status={orderStatus} />
              </div>
            </div>
          </div>
          <ChevronDown
            className={cn(
              "h-5 w-5 text-muted-foreground transition-transform duration-200",
              isOpen ? "rotate-180 text-primary" : "",
            )}
          />
        </div>
      </CollapsibleTrigger>
      <CollapsibleContent>
        <OrderItemDetails goods={order.orderGoodsList || []} />
      </CollapsibleContent>
    </Collapsible>
  );
});

interface OrderItemDetailsProps {
  goods: OrderGood[];
}

export const OrderItemDetails = memo(({ goods }: OrderItemDetailsProps) => (
  <div className="border-t border-border p-3 bg-accent/10">
    <div className="text-sm mb-3 flex items-center">
      <span className="mr-1">商品清单</span>
      <span className="text-xs text-muted-foreground font-normal">
        ({goods.length}个商品)
      </span>
    </div>
    <div className="space-y-3">
      {goods.map((good, index) => (
        <GoodDetail key={index} good={good} />
      ))}
    </div>
  </div>
));
