import { CopyableText } from "@/components/common/copyable-text";
import { OrderGood } from "@/types/api/configure-item";
import { memo } from "react";

// 商品详情组件
export const GoodDetail = memo(({ good }: { good: OrderGood }) => (
  <div className="border-t border-border pt-2 first:border-0 first:pt-0">
    <div className="text-xs">{good.goodsName || "-"}</div>
    <table className="w-full text-left">
      <tbody className="text-xs">
        <tr>
          <td className="text-muted-foreground pr-2 w-20">商品ID:</td>
          <td className="truncate">
            {good.goodsId ? <CopyableText text={good.goodsId} /> : "-"}
          </td>
        </tr>
        <tr>
          <td className="text-muted-foreground pr-2 w-20">SKU:</td>
          <td className="break-words">{good.spec || "-"}</td>
        </tr>
        <tr>
          <td className="text-muted-foreground pr-2 w-20">编号:</td>
          <td className="truncate">{good.skuId || "-"}</td>
        </tr>
      </tbody>
    </table>
  </div>
));
