import { Skeleton } from "@/components/ui/skeleton";

// 商品详情骨架屏组件
export const GoodDetailSkeleton = () => (
  <div className="border-t border-border pt-2 first:border-0 first:pt-0">
    <Skeleton className="h-5 w-4/5 mb-2" />
    <table className="w-full">
      <tbody>
        {[...Array(3)].map((_, index) => (
          <tr key={index}>
            <td className="pr-2 py-1 w-20">
              <Skeleton className="h-3.5 w-16" />
            </td>
            <td className="py-1">
              <Skeleton className="h-3.5 w-full max-w-60" />
            </td>
          </tr>
        ))}
      </tbody>
    </table>
  </div>
);

// 订单项骨架屏组件
export const OrderItemSkeleton = () => (
  <div className="border border-border rounded-lg overflow-hidden bg-accent/5 p-3">
    <div className="flex justify-between items-center">
      <div className="space-y-2 w-full">
        <div className="flex items-center gap-2">
          <Skeleton className="h-5 w-40" />
          <Skeleton className="h-3.5 w-3.5 rounded-full" />
        </div>
        <div className="flex items-center gap-3">
          <Skeleton className="h-4 w-36" />
          <div className="h-3.5 w-px bg-border" />
          <Skeleton className="h-4 w-24" />
        </div>
      </div>
      <Skeleton className="h-5 w-5 rounded-full" />
    </div>
  </div>
);

// 订单列表骨架屏
export const OrderListSkeleton = () => (
  <div className="space-y-2">
    {[...Array(3)].map((_, index) => (
      <OrderItemSkeleton key={index} />
    ))}
  </div>
);

// 信息卡片骨架屏
export const InfoCardSkeleton = ({
  children,
}: {
  children: React.ReactNode;
}) => (
  <div className="flex flex-col bg-background rounded-lg py-2.5 px-3 gap-3 w-full border border-border">
    <div className="flex items-center pb-3 border-b border-border">
      <Skeleton className="h-5 w-5 mr-2 rounded-full" />
      <Skeleton className="h-5 w-20" />
    </div>
    {children}
  </div>
);

// 整体面板骨架屏
export const TransferTaskInfoPanelSkeleton = () => (
  <div className="space-y-4 p-4">
    <InfoCardSkeleton>
      <Skeleton className="h-5 w-4/5" />
    </InfoCardSkeleton>

    <InfoCardSkeleton>
      <OrderListSkeleton />
    </InfoCardSkeleton>
  </div>
);
