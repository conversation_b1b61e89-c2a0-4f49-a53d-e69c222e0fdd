import { Badge } from "@/components/ui/badge";
import { OrderStatusEnum } from "../constants/order-status";
import { OrderStatusMap } from "../constants/order-status";

// 订单状态徽章
export const OrderStatusBadge = ({ status }: { status: OrderStatusEnum }) => {
  type BadgeVariant =
    | "error"
    | "success"
    | "warning"
    | "info"
    | "secondary"
    | "primary"
    | "waiting";

  const statusMap: Record<
    OrderStatusEnum,
    { variant: BadgeVariant; className?: string }
  > = {
    [OrderStatusEnum.UNPAID]: { variant: "warning", className: "font-medium" },
    [OrderStatusEnum.WAITING_SHIPMENT]: {
      variant: "info",
      className: "font-medium",
    },
    [OrderStatusEnum.SHIPPED]: { variant: "waiting", className: "font-medium" },
    [OrderStatusEnum.WAITING_REVIEW]: {
      variant: "secondary",
      className: "font-medium",
    },
    [OrderStatusEnum.REVIEWED]: {
      variant: "success",
      className: "font-medium",
    },
    [OrderStatusEnum.CLOSED]: { variant: "error", className: "font-medium" },
    [OrderStatusEnum.COMPLETED]: {
      variant: "success",
      className: "font-medium",
    },
    [OrderStatusEnum.UNKNOWN]: {
      variant: "secondary",
      className: "font-medium",
    },
  };

  const style = statusMap[status] || { variant: "secondary" };
  const displayText = OrderStatusMap[status];

  return (
    <Badge variant={style.variant} className={style.className}>
      {displayText}
    </Badge>
  );
};
