import React from "react";

// 信息卡片组件
interface InfoCardProps {
  icon: React.ReactNode;
  title: string;
  children: React.ReactNode;
}

export const InfoCard = ({ icon, title, children }: InfoCardProps) => (
  <div className="flex flex-col bg-background rounded-lg py-2.5 px-3 transition-all duration-300 gap-3 w-full border border-border">
    <div className="flex items-center pb-3 border-b border-border">
      {icon}
      <h2 className="font-medium">{title}</h2>
    </div>
    {children}
  </div>
);
