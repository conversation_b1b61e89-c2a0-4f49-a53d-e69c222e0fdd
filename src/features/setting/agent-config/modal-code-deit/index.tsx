import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
} from "@/components/ui/dialog";
import { safeParseJson } from "@/utils/safe-parse-json";
import { useTranslation } from "react-i18next";
import { useMemo, useState } from "react";
import { AgentConfigTableData } from "@/types/api/agent-config";
import { Editor } from "@/components/common/code-mirror";
import ReactJson from "react-json-view";
import { AiAgentService } from "@/services/ai-agent-service";
interface delProps {
  open: boolean;
  onCancel: () => void;
  refetch: () => void;
  data: AgentConfigTableData | null;
}
export const ModalCodeEdit = ({ open, onCancel, refetch, data }: delProps) => {
  const { t } = useTranslation();
  const [isEdit, setIsEdit] = useState(false);
  const [editJson, setEditJson] = useState<AgentConfigTableData>({});
  const closeChange = () => {
    onCancel();
    setIsEdit(false);
  };
  const dataJson = useMemo(() => {
    if (open && data) {
      setEditJson(JSON.parse(data?.configContent || "{}"));
      return JSON.parse(data?.configContent || "{}");
    }
    return {};
  }, [open, data]);
  const onSubmit = () => {
    AiAgentService.updateAiAgentConfigContent({
      aiAgentConfigId: data?.aiAgentConfigId || "",
      configContent: JSON.stringify(editJson),
    }).then(() => {
      refetch();
      closeChange();
    });
  };

  return (
    <Dialog open={open} onOpenChange={closeChange}>
      <DialogContent className="w-[600px] max-w-full">
        <DialogTitle className="flex items-center">
          {t("edit.agent.code")}
        </DialogTitle>
        <div className="min-h-[300px] max-h-[500px] overflow-auto">
          {isEdit ? (
            <Editor
              value={dataJson ? JSON.stringify(dataJson, null, 2) : "{}"}
              onChange={(e) => {
                try {
                  setEditJson(safeParseJson(e, {}));
                } catch (error) {
                  console.error("JSON解析错误", error);
                }
              }}
            />
          ) : (
            <ReactJson src={dataJson} collapsed={true} />
          )}
        </div>
        <DialogFooter>
          {isEdit ? (
            <>
              <Button type="button" onClick={onSubmit}>
                {t("common.confirm")}
              </Button>
              <Button
                type="button"
                onClick={() => {
                  setIsEdit(false);
                }}
                variant="default"
                color="gray"
              >
                {t("common.cancel")}
              </Button>
            </>
          ) : (
            <Button
              type="button"
              onClick={() => {
                setIsEdit(true);
              }}
            >
              {t("common.edit")}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
