import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogTitle,
  DialogFooter,
  DialogDescription,
} from "@/components/ui/dialog";
import { useTranslation } from "react-i18next";
import { AgentConfigTableData } from "@/types/api/agent-config";
import { CircleAlert } from "lucide-react";
import { AiAgentService } from "@/services/ai-agent-service";
interface delProps {
  open: boolean;
  onCancel: () => void;
  refetch: () => void;
  data: AgentConfigTableData | null;
}
export const ModalDelAgent = ({ open, onCancel, refetch, data }: delProps) => {
  const { t } = useTranslation();
  const onSubmit = () => {
    if (data?.aiAgentConfigId) {
      AiAgentService.delAiAgentConfig({
        aiAgentConfigId: data.aiAgentConfigId,
      }).then(() => {
        refetch();
        onCancel();
      });
    }
  };
  return (
    <Dialog open={open} onOpenChange={onCancel}>
      <DialogContent>
        <DialogTitle className="flex items-center">
          <CircleAlert className="h-8 w-8 mr-1" />
          {t("confirm.delete.agent")}
        </DialogTitle>
        <DialogDescription>
          {t("confirm.delete.agent.description")}
        </DialogDescription>
        <DialogFooter>
          <DialogClose asChild>
            <Button onClick={onSubmit}> {t("common.confirm")}</Button>
          </DialogClose>
          <DialogClose asChild>
            <Button
              onClick={() => {
                onCancel && onCancel();
              }}
              variant="default"
              color="gray"
            >
              {t("common.cancel")}
            </Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
