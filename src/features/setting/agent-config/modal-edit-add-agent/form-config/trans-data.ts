// 将数组[{value:[{valie:''}]}]=>[[""]]
export function flattenValueArrays<T = any>(data: T): T {
  // 处理数组类型
  if (Array.isArray(data)) {
    return data.map((item) => {
      // 如果是包含`value`属性的对象，提取value值并递归处理
      if (item && typeof item === "object" && "value" in item) {
        return flattenValueArrays(item.value);
      }
      // 否则递归处理数组元素
      return flattenValueArrays(item);
    }) as T;
  }

  // 处理普通对象类型
  if (data && typeof data === "object") {
    const result: Record<string, any> = { ...data };
    for (const key in result) {
      result[key] = flattenValueArrays(result[key]);
    }
    return result as T;
  }

  // 基础类型直接返回
  return data;
}
//  数组转数组对象
function transformArray<T>(arr: T[]): { value: T }[] {
  // 检查arr是否为数组，非数组时返回空数组
  if (!Array.isArray(arr)) {
    return [{ value: "" as T }];
  }
  return arr.map((v) => ({ value: v }));
}
//  数组转数组对象
function transformNestedArray<T>(arr: T[][]) {
  if (!Array.isArray(arr)) {
    return [
      {
        value: [{ value: "" }],
      },
    ];
  }
  return arr.map((innerArr) => ({
    value: transformArray(innerArr),
  }));
}
// 消息相关的默认数据格式 用于复用
const defaultMsg = [
  {
    value: [
      {
        value: "",
      },
    ],
  },
];
// 将后后端返回的数据转为form表单用于校验的格式，主要为数组[[""]]=>[{value:[{valie:''}]}]
export function transformAgentConfig(rawConfig) {
  // 深拷贝避免修改原始数据
  const newConfig = JSON.parse(JSON.stringify(rawConfig));
  // 转换 transferBatchCron
  newConfig.configContent.transferBatchCron = transformArray(
    newConfig.configContent.transferBatchCron,
  );

  // 转换 transferGroupWhiteList
  newConfig.configContent.transferGroupWhiteList = transformArray(
    newConfig.configContent.transferGroupWhiteList,
  );

  if (newConfig.configContent.transfer.divide) {
    newConfig.configContent.transfer.afterSale.off =
      newConfig.configContent.transfer.afterSale.off.map((offItem) => ({
        ...offItem,
        replyMsg: transformNestedArray(offItem.replyMsg),
      }));
    // 转换 transfer.transferMsg
    newConfig.configContent.transfer.afterSale.transferMsg =
      transformNestedArray(
        newConfig.configContent.transfer.afterSale.transferMsg,
      );

    // 转换 transfer.transferFailedMsg
    newConfig.configContent.transfer.afterSale.transferFailedMsg =
      transformNestedArray(
        newConfig.configContent.transfer.afterSale.transferFailedMsg,
      );

    newConfig.configContent.transfer.preSale.off =
      newConfig.configContent.transfer.preSale.off.map((offItem) => ({
        ...offItem,
        replyMsg: transformNestedArray(offItem.replyMsg),
      }));

    // 转换 transfer.transferMsg
    newConfig.configContent.transfer.preSale.transferMsg = transformNestedArray(
      newConfig.configContent.transfer.preSale.transferMsg,
    );

    // 转换 transfer.transferFailedMsg
    newConfig.configContent.transfer.preSale.transferFailedMsg =
      transformNestedArray(
        newConfig.configContent.transfer.preSale.transferFailedMsg,
      );
    newConfig.configContent.transfer.off = [
      {
        period: ["", ""],
        replyMsg: defaultMsg,
      },
    ];
    newConfig.configContent.transfer.strategy = "group";
    newConfig.configContent.transfer.transferMsg = defaultMsg;
    newConfig.configContent.transfer.transferFailedMsg = defaultMsg;
    newConfig.configContent.transfer.target = "";
  } else {
    newConfig.configContent.transfer.off =
      newConfig.configContent.transfer.off.map((offItem) => ({
        ...offItem,
        replyMsg: transformNestedArray(offItem.replyMsg),
      }));
    // 转换 transfer.transferMsg
    newConfig.configContent.transfer.transferMsg = transformNestedArray(
      newConfig.configContent.transfer.transferMsg,
    );
    // 转换 transfer.transferFailedMsg
    newConfig.configContent.transfer.transferFailedMsg = transformNestedArray(
      newConfig.configContent.transfer.transferFailedMsg,
    );
    newConfig.configContent.transfer.preSale = {
      target: "",
      strategy: "group",
      transferFailedMsg: defaultMsg,
      transferMsg: defaultMsg,
      off: [
        {
          period: ["", ""],
          replyMsg: defaultMsg,
        },
      ],
    };
    newConfig.configContent.transfer.afterSale = {
      target: "",
      strategy: "group",
      transferFailedMsg: defaultMsg,
      transferMsg: defaultMsg,
      off: [
        {
          period: ["", ""],
          replyMsg: defaultMsg,
        },
      ],
    };
  }

  return newConfig;
}

/**
 * 校验cron表达式是否符合秒分时日月周的6段格式
 * @param cron 待校验的cron表达式（如："0 5 3 * * *"）
 */
const validateCron = (cron: string): boolean => {
  const parts = cron.trim().split(/\s+/);
  return (
    parts.length === 6 &&
    parts.every((part, index) => {
      const [min, max] = [
        [0, 59], // 秒（0-59）
        [0, 59], // 分（0-59）
        [0, 23], // 时（0-23）
        [1, 31], // 日（1-31）
        [1, 12], // 月（1-12）
        [0, 6], // 周（0-6，0=周日）
      ][index];
      return part === "*" || (Number(part) >= min && Number(part) <= max);
    })
  );
};

/**
 * 校验时间字符串是否符合HH:MM格式
 * @param time 待校验的时间字符串（如："03:05"）
 */
const validateTime = (time: string): boolean => {
  const regex = /^([01]\d|2[0-3]):([0-5]\d)$/;
  return regex.test(time);
};

/**
 * cron表达式转时间字符串（HH:MM）
 * @param cron cron表达式（如："0 5 3 * * *"）
 * @returns 时间字符串（如："03:05"）或错误信息
 */
export const cronToTime = (cron: string): string => {
  if (!validateCron(cron))
    return "无效的cron表达式（需满足：秒 分 时 日 月 周）";
  const [, minute, hour] = cron.trim().split(/\s+/);
  return `${String(hour).padStart(2, "0")}:${String(minute).padStart(2, "0")}`;
};

/**
 * 时间字符串转cron表达式
 * @param time 时间字符串（如："03:05"）
 * @returns cron表达式（如："0 5 3 * * *"）或错误信息
 */
export const timeToCron = (time: string): string => {
  if (!validateTime(time)) return "无效的时间格式（需为HH:MM）";
  const [hour, minute] = time.split(":").map(Number);
  return `0 ${minute} ${hour} * * *`;
};
