import * as z from "zod";
// 添加错误消息常量
const ERROR_MESSAGES = {
  REQUIRED: "此字段为必填项",
  AGENT_NAME: "请输入智能体名称",
  AGENT_DESC: "请输入智能体描述",
  GREETING_MSG: "请输入进店欢迎语",
  FEISHU_TOKEN: "请输入飞书token",
  SHOP_TYPE: "请选择店铺类型",
  RECOMMEND_MSG: "请输入推荐商品欢迎语",
  HANGUP_THRESHOLD: "请输入最大接待客服数",
  TRANSFER_TARGET: "请输入转交客服",
  TIME_SELECT: "请选择时间",
  PERIOD_EMPTY: "时间段不能为空",
  MSG_EMPTY: "请输入消息",
  CUSTOMER_SELECT: "请选择接待客服",
};
const receptionModeSchema = z.enum(["SILENCE", "OFFICIAL", "ASSISTANT"]);
export type receptionModeType = z.infer<typeof receptionModeSchema>;
const msgSchema = z.array(
  z.object({
    value: z.array(
      z.object({
        value: z.string().optional(),
      }),
    ),
  }),
);
// 转人工相关配置
const transferSchema = {
  off: z.array(
    z.object({
      period: z.array(
        z.string().min(1, { message: ERROR_MESSAGES.PERIOD_EMPTY }),
      ),
      replyMsg: msgSchema,
    }),
  ),
  strategy: z.string(),
  transferMsg: msgSchema,
  transferFailedMsg: msgSchema,
};
const baseSchema = z.object({
  receptionMode: receptionModeSchema,
  userIds: z.array(z.string()).optional(),
  name: z.string().min(1, { message: ERROR_MESSAGES.AGENT_NAME }),
  description: z.string().min(1, { message: ERROR_MESSAGES.AGENT_DESC }),
});
const transferBaseSchema = z.object({
  backupTarget: z.string().min(1, { message: ERROR_MESSAGES.TRANSFER_TARGET }),
  backupStrategy: z.string(),
});
const transferSchemaShow = z.intersection(
  z.discriminatedUnion("divide", [
    z.object({
      divide: z.literal(true),
      preSale: z.object({
        target: z.string().min(1, { message: "请输入售前转交客服" }),
        ...transferSchema,
      }),
      afterSale: z.object({
        target: z.string().min(1, { message: "请输入售后转交客服" }),
        ...transferSchema,
      }),
    }),
    z.object({
      divide: z.literal(false),
      ...transferSchema,
    }),
  ]),
  transferBaseSchema,
);
// 提取RPA配置schema
const rpaConfigSchema = z.object({
  burrowVersion: z.string(),
  includeExe: z.boolean(),
  feishuToken: z.string().optional(),
});
const configBase = z.object({
  greeting: z.string().min(1, { message: ERROR_MESSAGES.GREETING_MSG }),
  rpa: rpaConfigSchema,
  HITL: z.boolean(),
  recommendation: z.string().optional(),
  picture_info_path: z.string(),
  hangUpThreshold: z.preprocess((val) => {
    if (val === "" || val === null || val === undefined) {
      return undefined;
    }
    const num = Number(val);
    return isNaN(num) || !isFinite(num) ? undefined : num;
  }, z.number().min(1).optional()),
  transfer: transferSchemaShow,
});
const configContentJudge = z.intersection(
  z.discriminatedUnion("transferBatchSwitch", [
    z.object({
      transferBatchSwitch: z.literal(true),
      transferBatchCron: z.array(
        z.object({
          value: z.string().min(1, { message: ERROR_MESSAGES.TIME_SELECT }),
        }),
      ),
      transferGroupWhiteList: z.array(
        z.object({
          value: z.string().min(1, { message: "请输入客服组" }),
        }),
      ),
    }),
    z.object({
      transferBatchSwitch: z.literal(false),
      transferBatchCron: z.array(
        z.object({
          value: z.string(),
        }),
      ),
      transferGroupWhiteList: z.array(
        z.object({
          value: z.string(),
        }),
      ),
    }),
  ]),
  configBase,
);
export const agentFormSchema = z.intersection(
  z.discriminatedUnion("receptionMode", [
    z.object({
      receptionMode: z.literal("OFFICIAL"),
      customerId: z
        .string()
        .min(1, { message: ERROR_MESSAGES.CUSTOMER_SELECT }),
      configContent: configContentJudge,
    }),
    z.object({
      receptionMode: z.literal("ASSISTANT"),
      customerId: z
        .string()
        .min(1, { message: ERROR_MESSAGES.CUSTOMER_SELECT }),
      configContent: z.object({
        greeting: z.string().min(1, { message: ERROR_MESSAGES.GREETING_MSG }),
        rpa: rpaConfigSchema,
      }),
    }),
    z.object({
      receptionMode: z.literal("SILENCE"),
      configContent: z.object({
        rpa: rpaConfigSchema,
      }),
    }),
  ]),
  baseSchema,
);
// 创建默认消息结构生成函数
const createDefaultMsg = () => [
  {
    value: [
      {
        value: "",
      },
    ],
  },
];

// 创建默认时间段配置生成函数
const createDefaultPeriodConfig = () => ({
  period: ["", ""],
  replyMsg: createDefaultMsg(),
});
export const defaultValues = {
  receptionMode: "SILENCE" as receptionModeType,
  name: "",
  description: "",
  customerId: "",
  userIds: [],
  configContent: {
    greeting: "",
    HITL: false,
    hangUpThreshold: null,
    recommendation: "",
    picture_info_path: "",
    rpa: {
      burrowVersion: 15,
      includeExe: true,
      feishuToken: "",
    },
    transfer: {
      divide: true,
      backupTarget: "",
      backupStrategy: "person",
      preSale: {
        target: "",
        strategy: "group",
        transferFailedMsg: createDefaultMsg(),
        transferMsg: createDefaultMsg(),
        off: [createDefaultPeriodConfig()],
      },
      afterSale: {
        target: "",
        strategy: "group",
        transferFailedMsg: createDefaultMsg(),
        transferMsg: createDefaultMsg(),
        off: [createDefaultPeriodConfig()],
      },
      target: "",
      strategy: "group",
      transferFailedMsg: createDefaultMsg(),
      transferMsg: createDefaultMsg(),
      off: [createDefaultPeriodConfig()],
    },
    transferBatchSwitch: false,
    transferBatchCron: [{ value: "" }],
    transferGroupWhiteList: [{ value: "" }],
  },
};
export type FormValues = z.infer<typeof agentFormSchema>;
