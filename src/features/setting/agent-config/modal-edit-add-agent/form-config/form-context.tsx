import { createContext, useContext, ReactNode } from "react";
import { UseFormReturn } from "react-hook-form";
import { FormValues } from "@/features/setting/agent-config/modal-edit-add-agent/form-config/form-schemas";

type FormContextType = {
  form: UseFormReturn<FormValues>;
  t: (key: string) => string;
};

const FormContext = createContext<FormContextType | undefined>(undefined);

export const FormProvider = ({
  children,
  form,
  t,
}: {
  children: ReactNode;
  form: UseFormReturn<FormValues>;
  t: (key: string) => string;
}) => {
  return (
    <FormContext.Provider value={{ form, t }}>{children}</FormContext.Provider>
  );
};

export const useFormContext = () => {
  const context = useContext(FormContext);

  if (context === undefined) {
    throw new Error("useFormContext must be used within a FormProvider");
  }

  return context;
};
