import { flattenValueArrays } from "./trans-data";
import { FormValues } from "./form-schemas";
interface TransferSection {
  off: Array<{
    period: string[];
    replyMsg: string[][];
  }>;
  target: string;
  strategy: string;
  transferMsg: string[][];
  transferFailedMsg: string[][];
}
const createTransferSection = (): TransferSection => ({
  off: [{ period: [], replyMsg: [[""]] }],
  target: "",
  strategy: "group",
  transferMsg: [[""]],
  transferFailedMsg: [[""]],
});

const defaultCommonFields = {
  checkCSStatusInterval: "1m",
  syncStatisticsCron: ["0 0 9 * * *"],
};
export function baseAddData<T = any>(data: T): T {
  const typedData = data as { transfer?: { divide: any } } | undefined;

  if (typedData?.transfer?.divide) {
    return {
      ...data,
      transfer: {
        ...(typedData?.transfer || {}),
        off: createTransferSection().off,
        target: "",
        strategy: "group",
        transferMsg: [[""]],
        transferFailedMsg: [[""]],
      },
      ...defaultCommonFields,
    };
  } else {
    return {
      ...data,
      transfer: {
        ...(typedData?.transfer || {}),
        preSale: createTransferSection(),
        afterSale: createTransferSection(),
      },
      ...defaultCommonFields,
    };
  }
}
//  仅用于添加默认值
export function baseAddDataSilence<T = any>(data: T): T {
  return {
    ...data,
    transfer: {
      divide: true,
      preSale: createTransferSection(),
      afterSale: createTransferSection(),
      off: createTransferSection().off,
      backupTarget: "",
      backupStrategy: "person",
      target: "",
      strategy: "group",
      transferMsg: [[""]],
      transferFailedMsg: [[""]],
    },
    recommendation: "",
    hangUpThreshold: undefined,
    picture_info_path: "",
    ...defaultCommonFields,
  };
}
export function transformSubmitData(formData: FormValues, shopName: string) {
  const configContent = flattenValueArrays(formData.configContent);
  const baseAddConfigContent =
    formData.receptionMode === "OFFICIAL"
      ? baseAddData(configContent)
      : baseAddDataSilence(configContent);

  // 设置店铺相关配置
  baseAddConfigContent["checkCSStatusShop"] = shopName;
  baseAddConfigContent["grabGoodsWhiteList"] = [shopName];
  baseAddConfigContent["hangUpShop"] = shopName;
  baseAddConfigContent["transferBatchShop"] = shopName;
  baseAddConfigContent["syncStatisticsShop"] = shopName;

  return baseAddConfigContent;
}
