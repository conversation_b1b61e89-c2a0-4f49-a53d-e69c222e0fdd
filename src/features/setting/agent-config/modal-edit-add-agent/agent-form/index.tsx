import { useFormContext } from "@/features/setting/agent-config/modal-edit-add-agent/form-config/form-context";
import { Divider } from "@/components/ui/divider";
import { BaseForm } from "@/features/setting/agent-config/modal-edit-add-agent/agent-form/base-form";
import { TransFrom } from "@/features/setting/agent-config/modal-edit-add-agent/agent-form/trans-from";
import { Input } from "@/components/ui/input";
import { BandListType, CustomerListType } from "@/types/api/agent-config";
import { defaultValues, receptionModeType } from "../form-config";
import { useState } from "react";
import { PreSale } from "./pre-sales";
import { BatchTransConfig } from "@/features/setting/agent-config/modal-edit-add-agent/agent-form/batch-trans-config";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";

export const AgentFrom = ({
  bandList,
  userList,
}: {
  bandList: BandListType[] | [];
  userList: CustomerListType[] | [];
}) => {
  const { form, t } = useFormContext();
  const [baseLocal, setBaseLocal] = useState<{
    SILENCE: string | null;
    OFFICIAL: string | null;
    ASSISTANT: string | null;
  }>({
    SILENCE: null,
    OFFICIAL: null,
    ASSISTANT: null,
  });
  //监听接待模式
  const receptionMode = form.watch("receptionMode");

  const formSessionStorage = (modalType: string) => {
    const baseValue = form.getValues();
    const baseLocalBk = JSON.parse(JSON.stringify(baseLocal));
    baseLocalBk[baseValue.receptionMode] = JSON.stringify(baseValue);
    setBaseLocal(baseLocalBk);
    const local = baseLocalBk[modalType];
    if (local) {
      const defaultLocal = JSON.parse(local);
      form.reset({
        ...defaultLocal,
        receptionMode: modalType,
      });
    } else {
      const bfDefaultValues = JSON.parse(JSON.stringify(defaultValues));
      bfDefaultValues.receptionMode = modalType;
      form.reset({
        ...bfDefaultValues,
      });
    }
  };
  return (
    <Form {...form}>
      <form className="pt-6">
        <FormField
          control={form.control}
          name="receptionMode"
          render={({ field }) => (
            <FormItem className="flex flex-row items-center py-1">
              <FormLabel className="w-40 flex-shrink-0 mb-0 inline-flex  justify-end mr-2  before:content-['*'] before:text-red-500">
                {t("agent.config.form.reception.mode")}：
              </FormLabel>
              <FormControl className="flex-1">
                <Select
                  onValueChange={(e) => {
                    formSessionStorage(e as receptionModeType);
                  }}
                  value={field.value}
                >
                  <div className="flex-1 mb-0">
                    <FormControl>
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder={t("reception.mode")} />
                      </SelectTrigger>
                    </FormControl>
                  </div>
                  <SelectContent>
                    <SelectItem value="SILENCE">静默</SelectItem>
                    <SelectItem value="ASSISTANT">ai辅助</SelectItem>
                    <SelectItem value="OFFICIAL">正式接待</SelectItem>
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        ></FormField>
        {/* 基础配置 */}
        <BaseForm bandList={bandList} userLis={userList} />
        {receptionMode === "OFFICIAL" ? (
          <>
            <Divider className="py-2 text-base font-medium" align="left">
              {t("reception.mode.greeting")}
            </Divider>
            <PreSale />
            <Divider className="py-2 text-base font-medium" align="left">
              {t("agent.config.form.transfer.title")}
            </Divider>
            <TransFrom />
          </>
        ) : (
          ""
        )}
        <Divider className="py-2 text-base font-medium" align="left">
          {t("agent.config.form.warning")}
        </Divider>
        <FormField
          control={form.control}
          name="configContent.rpa.feishuToken"
          render={({ field }) => (
            <FormItem className="flex flex-row items-center py-1">
              <FormLabel className="w-40 flex-shrink-0 mb-0 inline-flex  justify-end mr-2 ">
                {t("agent.config.form.warning.feishu.token")}：
              </FormLabel>
              <div className="w-full">
                <FormControl className="flex-1">
                  <div className="w-full">
                    <Input placeholder="请输入飞书报警token" {...field} />
                  </div>
                </FormControl>
                <FormMessage />
              </div>
            </FormItem>
          )}
        ></FormField>
        {receptionMode === "OFFICIAL" ? (
          <>
            <Divider className="py-2 text-base font-medium" align="left">
              {t("transfer.batch.config")}
            </Divider>
            <BatchTransConfig />
            <Divider className="py-2 text-base font-medium" align="left">
              {t("shop.config")}
            </Divider>
            <FormField
              control={form.control}
              name="configContent.hangUpThreshold"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center py-1">
                  <FormLabel className="w-40 flex-shrink-0 mb-0 inline-flex  justify-end mr-2 ">
                    {t("max.customer.count")}：
                  </FormLabel>
                  <div className="w-full">
                    <FormControl className="flex-1">
                      <div className="w-full">
                        <Input
                          type="number"
                          placeholder={t("max.customer.count.placeholder")}
                          {...field}
                          onChange={(e) =>
                            field.onChange(parseInt(e.target.value))
                          }
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </div>
                </FormItem>
              )}
            ></FormField>
          </>
        ) : (
          ""
        )}
      </form>
    </Form>
  );
};
