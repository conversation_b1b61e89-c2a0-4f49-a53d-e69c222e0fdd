import { useFormContext } from "../form-config";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Trash2, Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useFieldArray } from "react-hook-form";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { MsgGroup } from "./msg-group";
import { TimePicker } from "@/components/common/time-picker";
import { isArray } from "lodash";
export const TransPublic = ({ formName }: { formName: string }) => {
  const { form, t } = useFormContext();
  const {
    fields: offConfig,
    append: appendOffConfig,
    remove: removeOffConfig,
  } = useFieldArray({
    control: form.control,
    name: `${formName}.off` as any,
  });
  return (
    <>
      <div className="flex items-center py-2">
        <div className=" text-sm w-40 flex-shrink-0 mb-0 inline-flex  justify-end mr-2  before:content-['*'] before:text-red-500">
          {t("agent.config.form.transfer.agent")}：
        </div>
        <div className="flex-1 flex items-center">
          <FormField
            control={form.control}
            name={`${formName}.strategy` as any}
            render={({ field }) => (
              <FormItem className="w-4/12 mr-4">
                <div className="w-full">
                  <FormControl className="flex-1">
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <div className="flex-1 mb-0">
                        <FormControl>
                          <SelectTrigger className="w-full">
                            <SelectValue
                              placeholder={t("common.placeholder.select")}
                            />
                          </SelectTrigger>
                        </FormControl>
                      </div>
                      <SelectContent>
                        <SelectItem value="person">
                          {t("transfer.to.person")}
                        </SelectItem>
                        <SelectItem value="group">
                          {t("transfer.to.group")}
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </div>
              </FormItem>
            )}
          ></FormField>
          <FormField
            control={form.control}
            name={`${formName}.target` as any}
            render={({ field }) => (
              <FormItem className="w-8/12">
                <div className="w-full">
                  <FormControl>
                    <Input placeholder="请输入转交客服名称" {...field} />
                  </FormControl>
                  <FormMessage />
                </div>
              </FormItem>
            )}
          ></FormField>
        </div>
      </div>
      <MsgGroup
        formName={`${formName}.transferMsg`}
        title={t("agent.config.form.transfer.msg")}
      />
      <MsgGroup
        formName={`${formName}.transferFailedMsg`}
        title={t("agent.config.form.transfer.none.failed.msg")}
      />
      <div className="text-sm mb-2 flex-shrink-0  inline-flex items-center ">
        {t("transfer.to.none")}
      </div>
      {offConfig.map((offItem, offIndex) => {
        return (
          <Card key={offItem.id} className="mb-3 bg-slate-50 pt-2 gap-2 pb-2">
            <CardContent className="px-4">
              <FormField
                control={form.control}
                name={`${formName}.off.${offIndex}.period` as any}
                render={({ field }) => {
                  let startTime = field.value[0];
                  let endTime = field.value[1];
                  if (isArray(field.value) && field.value.length > 0) {
                    startTime = field.value[0] || "";
                    endTime = field.value[1] || "";
                  }
                  return (
                    <FormItem className="flex flex-row items-center py-1">
                      <FormLabel className=" flex-shrink-0 mb-0 inline-flex  justify-start mr-2">
                        {t("transfer.to.none.time")}：
                      </FormLabel>
                      <div className="w-full">
                        <FormControl className="flex-1">
                          <div className="w-full flex justify-between items-center">
                            <div className="flex items-center">
                              <TimePicker
                                mode="range"
                                onRangeChange={(e) => {
                                  field.onChange([e.start, e.end]);
                                }}
                                defaultStart={startTime}
                                defaultEnd={endTime}
                              />
                            </div>
                            {offConfig.length > 1 ? (
                              <Trash2
                                size={16}
                                onClick={() => removeOffConfig(offIndex)}
                                className="text-gray-400 hover:text-red-400 cursor-pointer"
                              />
                            ) : (
                              ""
                            )}
                          </div>
                        </FormControl>
                      </div>
                    </FormItem>
                  );
                }}
              ></FormField>
              <MsgGroup
                formName={`${formName}.off.${offIndex}.replyMsg`}
                title={t("agent.config.form.transfer.none.msg")}
              />
            </CardContent>
          </Card>
        );
      })}
      <Button
        size="sm"
        variant="outline"
        type="button"
        className="w-full text-xs text-gray-500"
        onClick={() =>
          appendOffConfig({
            period: "",
            replyMsg: [
              {
                value: [
                  {
                    value: "",
                  },
                ],
              },
            ],
          })
        }
      >
        <Plus size={16} />
        {t("agent.config.form.transfer.none.time")}
      </Button>
    </>
  );
};
