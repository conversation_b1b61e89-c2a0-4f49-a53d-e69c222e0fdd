import { useFormContext } from "../form-config";
import { <PERSON><PERSON><PERSON><PERSON>, Trash2, Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useFieldArray } from "react-hook-form";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import {
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";
// 子集
export const MsgItem = ({ formName, index }) => {
  const { form, t } = useFormContext();
  const {
    fields: subMessages,
    append: appendSubMsg,
    remove: removeSubMsg,
  } = useFieldArray({
    control: form.control,
    name: formName,
  });
  return (
    <div key={index}>
      {subMessages.map((subMsg, subIndex) => (
        <div key={subMsg.id} className="mb-2">
          <FormField
            control={form.control}
            name={`${formName}.${subIndex}.value` as any}
            render={({ field }) => (
              <FormItem>
                <div className="w-full flex flex-col">
                  <div className="flex flex-row items-center">
                    <FormControl>
                      <Textarea
                        placeholder={t("agent.config.form.add.msg.placeholder")}
                        {...field}
                      />
                    </FormControl>
                    {subMessages.length > 1 ? (
                      <Trash2
                        size={16}
                        onClick={() => removeSubMsg(subIndex)}
                        className="text-gray-400 hover:text-red-400 cursor-pointer ml-2"
                      />
                    ) : (
                      ""
                    )}
                  </div>
                  <FormMessage />
                </div>
              </FormItem>
            )}
          />
        </div>
      ))}
      <Button
        size="sm"
        type="button"
        variant="outline"
        className="w-full text-xs text-gray-500"
        onClick={() => appendSubMsg({ value: "" })}
      >
        <Plus size={16} />
        {t("agent.config.form.add.msg.title")}
      </Button>
    </div>
  );
};

// 分组
export const MsgGroup = ({
  formName,
  title,
}: {
  formName: string;
  title: string;
}) => {
  const { form, t } = useFormContext();
  const {
    fields: msgGroups,
    append: appendMsgGroups,
    remove: removeMsgGroups,
  } = useFieldArray({
    control: form.control,
    name: formName as any,
  });
  return (
    <div className="py-1">
      <div className="text-sm mb-2 flex-shrink-0  inline-flex items-center ">
        {title}
        <CircleAlert size={16} className="text-xs text-zinc-400 ml-2 mr-0.5" />
        <span className="text-xs text-zinc-400">
          {t("transfer.group.msg.to.one.description")}
        </span>
      </div>
      <div className="w-full flex-col">
        {msgGroups.map((groupItem, groupIndex) => {
          return (
            <div key={groupItem.id} className="w-full mb-3">
              <Card className="w-full bg-zinc-50 pt-0 gap-2 pb-2 ">
                <CardHeader className="bg-zinc-100  rounded-t-lg pt-2 pb-1 px-4">
                  <div className="flex justify-between items-center">
                    <div className="text-sm">
                      {t("agent.config.form.msg.group.title")}
                      {groupIndex + 1}
                    </div>
                    {msgGroups.length > 1 ? (
                      <Trash2
                        size={16}
                        onClick={() => removeMsgGroups(groupIndex)}
                        className="text-gray-400 hover:text-red-400 cursor-pointer"
                      />
                    ) : (
                      ""
                    )}
                  </div>
                </CardHeader>
                <CardContent className="px-4">
                  <MsgItem
                    formName={`${formName}.${groupIndex}.value`}
                    index={groupIndex}
                  ></MsgItem>
                </CardContent>
              </Card>
            </div>
          );
        })}
        <Button
          size="sm"
          variant="outline"
          type="button"
          className="w-full text-xs text-gray-500"
          onClick={() => appendMsgGroups({ value: [{ value: "" }] })}
        >
          <Plus size={16} />
          {t("agent.config.form.add.msg.group.title")}
        </Button>
      </div>
    </div>
  );
};
