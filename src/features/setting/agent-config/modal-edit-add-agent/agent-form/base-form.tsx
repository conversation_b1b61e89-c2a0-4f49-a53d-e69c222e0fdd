import { useFormContext } from "@/features/setting/agent-config/modal-edit-add-agent/form-config/form-context";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { BandListType, CustomerListType } from "@/types/api/agent-config";
import { Divider } from "@/components/ui/divider";
import { SelectSearch } from "@/components/common/select-search";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useMemo } from "react";
import { isArray } from "lodash";
const transToOptions = (
  arr: any[],
  labelKey: string,
  valueKey: string,
  noUseKey: string | null,
): Array<{ label: string; value: string; disabled: boolean }> => {
  if (arr.length === 0) return [];
  return arr.map((item) => ({
    label: item[labelKey], // 容错处理
    value: item[valueKey],
    disabled: noUseKey ? !item[noUseKey] : false,
  }));
};
export const BaseForm = ({
  bandList,
  userLis,
}: {
  bandList: BandListType[];
  userLis: CustomerListType[];
}) => {
  const { form, t } = useFormContext();
  //监听接待模式
  const receptionMode = form.watch("receptionMode");
  const isMultiple = useMemo(() => {
    return receptionMode === "SILENCE";
  }, [receptionMode]);
  return (
    <>
      <Divider className="py-2 text-base font-medium" align="left">
        {t("shop.agent.config")}
      </Divider>
      <FormField
        control={form.control}
        name="name"
        render={({ field }) => (
          <FormItem className="flex flex-row items-center py-1">
            <FormLabel className="w-40 flex-shrink-0 mb-0 inline-flex justify-end mr-2  before:content-['*'] before:text-red-500">
              {t("agent.config.name")}：
            </FormLabel>
            <div className="w-full">
              <FormControl className="flex-1">
                <div className="w-full">
                  <Input placeholder="请输入智能体名称" max={20} {...field} />
                </div>
              </FormControl>
              <FormMessage />
            </div>
          </FormItem>
        )}
      ></FormField>
      <FormField
        control={form.control}
        name="description"
        render={({ field }) => (
          <FormItem className="flex flex-row items-center py-1">
            <FormLabel className="w-40 flex-shrink-0 mb-0 inline-flex  justify-end mr-2  before:content-['*'] before:text-red-500">
              {t("agent.config.description")}：
            </FormLabel>
            <div className="w-full">
              <FormControl className="flex-1">
                <Textarea
                  placeholder="请输入智能体描述"
                  maxLength={50}
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </div>
          </FormItem>
        )}
      ></FormField>
      <Divider className="py-2 text-base font-medium" align="left">
        {t("agent.config.form.userNames.config")}
      </Divider>
      {!isMultiple && (
        <FormField
          control={form.control}
          name="customerId"
          render={({ field }) => (
            <FormItem className="flex flex-row items-center py-1">
              <FormLabel className="w-40 flex-shrink-0 mb-0 inline-flex  justify-end mr-2  before:content-['*'] before:text-red-500">
                {t("agent.config.form.relevance.service")}：
              </FormLabel>
              <div className="flex-1">
                <SelectSearch
                  placeholder={t("form.placeholder.select")}
                  options={transToOptions(
                    userLis,
                    "customerServiceName",
                    "id",
                    "noUse",
                  )}
                  popWidthClass="w-135"
                  multiple={false}
                  value={field.value}
                  onChange={(value) => {
                    if (value) field.onChange(value);
                  }}
                ></SelectSearch>
                <FormMessage />
              </div>
            </FormItem>
          )}
        ></FormField>
      )}
      <FormField
        control={form.control}
        name="userIds"
        render={({ field }) => {
          return (
            <FormItem className="flex flex-row items-center py-1">
              <FormLabel className="w-40 flex-shrink-0 mb-0 inline-flex  justify-end mr-2  ">
                {t("agent.config.form.relevance.account")}：
              </FormLabel>
              <div className="flex-1">
                <FormControl>
                  <SelectSearch
                    placeholder={t("form.placeholder.select")}
                    options={transToOptions(bandList, "username", "id", null)}
                    popWidthClass="w-135"
                    multiple={isMultiple}
                    value={field.value ? field.value : []}
                    onChange={(value) => {
                      if (value) {
                        if (isArray(value)) {
                          field.onChange(value);
                        } else {
                          field.onChange([value]);
                        }
                      }
                    }}
                  />
                </FormControl>
                <FormMessage />
              </div>
            </FormItem>
          );
        }}
      ></FormField>
      {receptionMode !== "SILENCE" ? (
        <>
          <Divider className="py-2 text-base font-medium" align="left">
            {t("agent.config.form.greeting.title")}
          </Divider>
          <FormField
            control={form.control}
            name="configContent.greeting"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center py-1">
                <FormLabel className="w-40 flex-shrink-0 mb-0 inline-flex  justify-end mr-2  before:content-['*'] before:text-red-500">
                  {t("agent.config.form.greeting")}：
                </FormLabel>
                <div className="w-full">
                  <FormControl className="flex-1">
                    <Textarea placeholder="请输入进店欢迎语" {...field} />
                  </FormControl>
                  <FormMessage />
                </div>
              </FormItem>
            )}
          ></FormField>
        </>
      ) : (
        ""
      )}
    </>
  );
};
