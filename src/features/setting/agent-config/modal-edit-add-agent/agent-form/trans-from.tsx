import { Switch } from "@/components/ui/switch";
import { useState } from "react";
import { useFormContext } from "@/features/setting/agent-config/modal-edit-add-agent/form-config/form-context";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Tabs<PERSON>rigger, <PERSON><PERSON><PERSON>ontent } from "@/components/ui/tabs";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { TransPublic } from "@/features/setting/agent-config/modal-edit-add-agent/agent-form/trans-public";
export const TransFrom = () => {
  const { form, t } = useFormContext();
  const [tabValue, setTabValue] = useState<string>("pre-sales");
  const divide = form.watch("configContent.transfer.divide");
  return (
    <>
      <div className="flex items-center py-1">
        <div className="text-sm w-40 flex-shrink-0 mb-0 inline-flex  justify-end mr-2  before:content-['*'] before:text-red-500">
          {t("agent.config.form.transfer.agent.backups")}：
        </div>
        <div className="flex-1 flex items-center">
          <FormField
            control={form.control}
            name="configContent.transfer.backupStrategy"
            render={({ field }) => (
              <FormItem className="w-4/12 mr-4">
                <div className="w-full">
                  <FormControl className="flex-1">
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <div className="flex-1 mb-0">
                        <FormControl>
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="请选择" />
                          </SelectTrigger>
                        </FormControl>
                      </div>
                      <SelectContent>
                        <SelectItem value="person">转接到人</SelectItem>
                        <SelectItem value="group">转接到组</SelectItem>
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </div>
              </FormItem>
            )}
          ></FormField>
          <FormField
            control={form.control}
            name="configContent.transfer.backupTarget"
            render={({ field }) => (
              <FormItem className="w-8/12">
                <div className="w-full">
                  <FormControl>
                    <Input placeholder="请输入转交客服名称" {...field} />
                  </FormControl>
                  <FormMessage />
                </div>
              </FormItem>
            )}
          ></FormField>
        </div>
      </div>
      <FormField
        control={form.control}
        name="configContent.transfer.divide"
        render={({ field }) => (
          <FormItem className="flex flex-row items-center py-1">
            <FormLabel className="flex-shrink-0 w-40 mb-0 inline-flex  justify-end mr-2  before:content-['*'] before:text-red-500">
              {t("agent.config.form.transfer.divide")}：
            </FormLabel>
            <div className="w-full">
              <FormControl className="flex-1">
                <Switch
                  checked={field.value as boolean}
                  onCheckedChange={(checked) => field.onChange(checked)}
                ></Switch>
              </FormControl>
              <FormMessage />
            </div>
          </FormItem>
        )}
      ></FormField>
      {tabValue === "pre-sales" ? (
        <FormField
          control={form.control}
          name="configContent.transfer.afterSale.target"
          render={() => <FormMessage />}
        ></FormField>
      ) : (
        ""
      )}

      {divide ? (
        <Tabs defaultValue="pre-sales" className="w-full">
          <TabsList>
            <TabsTrigger
              value="pre-sales"
              onClick={() => setTabValue("pre-sales")}
            >
              {t("agent.config.form.transfer.pre.tab")}
            </TabsTrigger>
            <TabsTrigger
              value="after-sale"
              onClick={() => setTabValue("after-sale")}
            >
              {t("agent.config.form.transfer.after.tab")}
            </TabsTrigger>
          </TabsList>
          <TabsContent value="pre-sales">
            <TransPublic formName="configContent.transfer.preSale" />
          </TabsContent>
          <TabsContent value="after-sale">
            <TransPublic formName="configContent.transfer.afterSale" />
          </TabsContent>
        </Tabs>
      ) : (
        <TransPublic formName="configContent.transfer" />
      )}
      <FormField
        control={form.control}
        name="configContent.HITL"
        render={({ field }) => (
          <FormItem className="flex flex-row items-center py-1">
            <FormLabel className="flex-shrink-0 w-60 mb-0 inline-flex  justify-end mr-2  before:content-['*'] before:text-red-500">
              {t("send.failed.report")}：
            </FormLabel>
            <div className="w-full">
              <FormControl className="flex-1">
                <Switch
                  checked={field.value as boolean}
                  onCheckedChange={(checked) => field.onChange(checked)}
                ></Switch>
              </FormControl>
              <FormMessage />
            </div>
          </FormItem>
        )}
      ></FormField>
    </>
  );
};
