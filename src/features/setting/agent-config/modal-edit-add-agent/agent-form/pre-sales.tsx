import { useFormContext } from "@/features/setting/agent-config/modal-edit-add-agent/form-config/form-context";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { ImageUploader } from "@/components/common/image-uploader";
import { Textarea } from "@/components/ui/textarea";
export const PreSale = () => {
  const { form, t } = useFormContext();
  return (
    <>
      <FormField
        control={form.control}
        name="configContent.recommendation"
        render={({ field }) => (
          <FormItem className="flex flex-row items-center py-1">
            <FormLabel className="w-40 flex-shrink-0 mb-0 inline-flex  justify-end mr-2">
              {t("recommend.goods.welcome")}：
            </FormLabel>
            <div className="w-full">
              <FormControl className="flex-1">
                <div className="w-full">
                  <Textarea
                    placeholder={t("recommend.goods.welcome.placeholder")}
                    {...field}
                  />
                </div>
              </FormControl>
              <FormMessage />
            </div>
          </FormItem>
        )}
      ></FormField>
      <FormField
        control={form.control}
        name="configContent.picture_info_path"
        render={({ field }) => (
          <FormItem className="flex flex-row items-center py-1">
            <FormLabel className="w-40 flex-shrink-0 mb-0 inline-flex  justify-end mr-2 ">
              {t("recommend.goods.pic")}：
            </FormLabel>
            <div className="w-48">
              <ImageUploader
                onUploadComplete={(url) => {
                  if (url) field.onChange(url);
                }}
                initialUrl={field.value}
                maxSize={2}
              />
            </div>
          </FormItem>
        )}
      ></FormField>
    </>
  );
};
