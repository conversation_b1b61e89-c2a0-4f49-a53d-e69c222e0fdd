import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Trash2, Plus } from "lucide-react";
import { TimePicker } from "@/components/common/time-picker";
import { useFieldArray } from "react-hook-form";
import { useFormContext, timeToCron, cronToTime } from "../form-config";
import { Switch } from "@/components/ui/switch";
export const BatchTransConfig = () => {
  const { form, t } = useFormContext();
  const {
    fields: transferBatchCronList,
    append: appendTransferBatchCron,
    remove: removeTransferBatchCron,
  } = useFieldArray({
    control: form.control,
    name: "configContent.transferBatchCron",
  });
  const { fields: transferBatchGroup } = useFieldArray({
    control: form.control,
    name: "configContent.transferGroupWhiteList",
  });
  return (
    <div>
      <FormField
        control={form.control}
        name="configContent.transferBatchSwitch"
        render={({ field }) => (
          <FormItem className="flex flex-row items-center py-1">
            <FormLabel className="flex-shrink-0 w-40 mb-0 inline-flex  justify-end mr-2  before:content-['*'] before:text-red-500">
              {t("agent.config.form.transfer.batch")}：
            </FormLabel>
            <div className="w-full">
              <FormControl className="flex-1">
                <Switch
                  checked={field.value as boolean}
                  onCheckedChange={(checked) => field.onChange(checked)}
                ></Switch>
              </FormControl>
              <FormMessage />
            </div>
          </FormItem>
        )}
      ></FormField>
      <div className="flex items-center py-2">
        <div className=" text-sm w-40 flex-shrink-0 mb-0 inline-flex  justify-end mr-2  ">
          批量转人工时间：
        </div>
        <Card className="mb-3 bg-slate-50 pt-2 gap-2 pb-2 flex-1">
          <CardContent className="px-4 ">
            {transferBatchCronList.map((timeIbj, index) => {
              return (
                <FormField
                  key={timeIbj.id}
                  control={form.control}
                  name={`configContent.transferBatchCron.${index}.value` as any}
                  render={({ field }) => {
                    let cron = field.value;
                    if (cron) {
                      cron = cronToTime(cron);
                    }
                    return (
                      <FormItem className="flex flex-row items-center py-1 mr-4">
                        <div className="w-full">
                          <FormControl className="flex-1">
                            <div className="flex items-center">
                              <div className="flex items-center mr-1">
                                <TimePicker
                                  mode="single"
                                  onChange={(e) => {
                                    console.log(timeToCron(e), "e");
                                    field.onChange(timeToCron(e));
                                  }}
                                  defaultValue={cron}
                                />
                              </div>
                              <Trash2
                                size={16}
                                onClick={() => removeTransferBatchCron(index)}
                                className="text-gray-400 hover:text-red-400 cursor-pointer"
                              />
                            </div>
                          </FormControl>
                          <FormMessage />
                        </div>
                      </FormItem>
                    );
                  }}
                ></FormField>
              );
            })}
            <Button
              size="sm"
              variant="outline"
              type="button"
              className="w-30 text-xs text-gray-500"
              onClick={() => appendTransferBatchCron({ value: "" })}
            >
              <Plus size={16} />
              添加时段
            </Button>
          </CardContent>
        </Card>
      </div>
      {transferBatchGroup.map((groupObj, index) => {
        return (
          <FormField
            key={groupObj.id}
            control={form.control}
            name={`configContent.transferGroupWhiteList.${index}.value`}
            render={({ field }) => (
              <FormItem className="flex flex-row items-center py-1">
                <FormLabel className="w-40 flex-shrink-0 mb-0 inline-flex  justify-end mr-2  ">
                  {t("批量转交客服组")}：
                </FormLabel>
                <div className="w-full">
                  <FormControl className="flex-1">
                    <Input placeholder="请输入批量转交客服组名称" {...field} />
                  </FormControl>
                  <FormMessage />
                </div>
              </FormItem>
            )}
          ></FormField>
        );
      })}
    </div>
  );
};
