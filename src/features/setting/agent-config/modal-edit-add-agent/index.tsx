import {
  <PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/drawer";
import { useQuery } from "@tanstack/react-query";
import { ScrollArea } from "@/components/ui/scroll-area";
import { X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useTranslation } from "react-i18next";
import { AgentFrom } from "@/features/setting/agent-config/modal-edit-add-agent/agent-form";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useState, useEffect, useCallback } from "react";
import {
  AgentConfigTableData,
  CustomerListType,
} from "@/types/api/agent-config";
import { shopAtom } from "@/atoms/shop";
import { useAtomValue } from "jotai";
import { AiAgentService } from "@/services/ai-agent-service";

import {
  FormValues,
  agentFormSchema,
  FormProvider,
  defaultValues,
  transformAgentConfig,
  receptionModeType,
  transformSubmitData,
} from "./form-config";
interface Props {
  open: boolean;
  editType?: "add" | "edit" | null; // 使用联合类
  data?: AgentConfigTableData | null;
  onClose: () => void;
  refetch: () => void;
}

export const ModalEditAddAgent = ({
  open,
  editType,
  data,
  onClose,
  refetch,
}: Props) => {
  const shop = useAtomValue(shopAtom);
  const [userList, setUserList] = useState<CustomerListType[]>([]);

  const { t } = useTranslation();
  const correctedDefaultValues = {
    ...defaultValues,
    receptionMode: "SILENCE" as receptionModeType,
  };
  const form = useForm<FormValues>({
    resolver: zodResolver(agentFormSchema),
    defaultValues: {
      ...correctedDefaultValues,
      receptionMode: "SILENCE" as const, // 明确指定为字面量类型
    },
  });
  // 关闭弹框
  const handleClose = () => {
    onClose();
    form.reset({
      ...correctedDefaultValues,
      receptionMode: "SILENCE",
    });
  };
  // 获取品牌列表
  const { data: bandList = [] } = useQuery({
    queryKey: ["bandListAgent", shop?.id],
    enabled: !!shop?.id,
    queryFn: () => AiAgentService.getSelectUserList({ shopId: shop?.id }),
  });

  const getUserList = useCallback(() => {
    if (!shop?.id) return;
    const customerId = data?.customerId ?? "";
    AiAgentService.getSelectCustomerList({ shopId: shop?.id }).then((res) => {
      const list = customerId
        ? res.map((item) => {
            return {
              ...item,
              noUse: item.id === customerId ? true : item.noUse,
            };
          })
        : res;
      setUserList(list);
    });
  }, [data, shop?.id]);
  // 提交
  const handleSubmit = async () => {
    await form.handleSubmit(
      async (formData) => {
        const baseAddConfigContent = transformSubmitData(
          formData,
          shop?.name || "",
        );
        if (shop?.id) {
          await AiAgentService.upDataAgent({
            aiAgentConfigId: data?.aiAgentConfigId || "",
            shopId: shop?.id,
            ...formData,
            configContent: JSON.stringify(baseAddConfigContent),
          }).then(() => {
            handleClose();
            refetch();
          });
        }
      },
      (errors) => {
        console.error("Failed to parse configContent:", errors);
      },
    )();
  };
  const initializeForm = useCallback(
    (data) => {
      if (!data) return;
      try {
        // 简化JSON解析逻辑
        const parsedConfig = JSON.parse(data.configContent || "{}");
        const transformedData = transformAgentConfig({
          ...data,
          configContent: parsedConfig,
        });

        form.reset({
          receptionMode: transformedData.receptionMode ?? "SILENCE",
          name: transformedData.name,
          description: transformedData.description,
          userIds: transformedData.userIds,
          customerId: transformedData.customerId,
          configContent: transformedData.configContent,
        });
      } catch (error) {
        // 添加错误日志便于调试
        console.error("Failed to parse configContent:", error);
        form.reset({
          ...correctedDefaultValues,
          receptionMode: "SILENCE",
        });
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [form],
  );
  useEffect(() => {
    if (!open) return; // 早期退出，减少嵌套
    if (data) {
      initializeForm(data);
    }
  }, [open, data, initializeForm]);
  useEffect(() => {
    if (!open) return;
    getUserList();
  }, [open, getUserList]);
  return (
    <Drawer
      open={open}
      dismissible={false}
      direction="right"
      onOpenChange={handleClose}
    >
      <DrawerContent
        aria-describedby=""
        className="!max-w-[800px] flex flex-col h-[100vh]"
      >
        <DrawerHeader className="border-b border-border shrink-0">
          <DrawerTitle className="flex items-center justify-between">
            <div className="flex items-center">
              <X
                className="w-6 h-6 cursor-pointer mr-2"
                onClick={handleClose}
              />
              <span>{editType && t(`agent.${editType}`)}</span>
            </div>
          </DrawerTitle>
        </DrawerHeader>
        <div className="w-full px-6 flex flex-col">
          <ScrollArea
            horizontal={true}
            style={{ height: "calc(100vh - 123px)" }}
          >
            <FormProvider form={form} t={t}>
              <AgentFrom bandList={bandList} userList={userList} />
            </FormProvider>
          </ScrollArea>
        </div>
        <DrawerFooter className="border-y border-border flex-row justify-end gap-2 flex-shrink-0">
          <Button size="sm" className="w-[64px]" onClick={handleClose}>
            {t("common.cancel")}
          </Button>
          <Button size="sm" className="w-[64px]" onClick={handleSubmit}>
            {t("common.save")}
          </Button>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  );
};
