import { AgentConfigTable } from "@/features/setting/agent-config/agent-config-table";
import { useQuery } from "@tanstack/react-query";
import { useTableState } from "@/components/common/data-table";
import { getAgentConfigParams } from "@/types/api/agent-config";
import { AiAgentService } from "@/services/ai-agent-service";
import { shopAtom } from "@/atoms/shop";
import { useAtomValue } from "jotai";
export const AgentConfig = () => {
  const shop = useAtomValue(shopAtom);
  const { params, isParamsInitialized, ...tableState } =
    useTableState<getAgentConfigParams>({
      initialParams: {
        pageNum: 1,
        pageSize: 10,
        shopId: shop?.id,
      },
    });
  const agentConfigQuery = useQuery({
    queryKey: ["agentConfigTable", params],
    enabled: isParamsInitialized && !!params.shopId, // 新增对shopId的检查
    queryFn: () => AiAgentService.getAgentList(params),
  });
  return (
    <>
      {shop?.id ? (
        <AgentConfigTable {...tableState} query={agentConfigQuery} />
      ) : (
        <div className="w-full h-full flex items-center justify-center">
          请选择店铺
        </div>
      )}
    </>
  );
};
