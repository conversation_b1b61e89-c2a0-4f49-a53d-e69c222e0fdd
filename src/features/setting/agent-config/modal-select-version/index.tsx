import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
} from "@/components/ui/dialog";
import { PromptVersionService } from "@/services/prompt-version-service";
import { PromptVersionStatusView } from "@/components/common/version-select/prompt-version/prompt-version-status-view";
import { ConfigVersionStatusView } from "@/components/common/version-select";
import { AiAgentService } from "@/services/ai-agent-service";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useAtomValue } from "jotai";
import { shopAtom } from "@/atoms/shop";
import { useForm } from "react-hook-form";
import { SelectSearch } from "@/components/common/select-search";
import { Button } from "@/components/ui/button";
import { useTranslation } from "react-i18next";
import { PromptVersion } from "@/types/api/prompt-version";
import { useQuery } from "@tanstack/react-query";
import { EvaluationConfigService } from "@/services/evaluation-config-service";
import * as z from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Version } from "@/types/api/version";
import { AgentConfigTableData } from "@/types/api/agent-config";
import { useEffect } from "react";
interface Props {
  open: boolean;
  refetch: () => void;
  data: AgentConfigTableData | null;
  onClose: () => void;
}
const formSchema = z.object({
  configId: z.string(),
  promptVersionId: z.string(),
});
type FormValues = z.infer<typeof formSchema>;

const transformShopToOptions = (stageData: Version[] | null) => {
  return (
    stageData?.map((field) => ({
      label: (
        <div className="w-full flex justify-between items-center pr-4">
          <span>{field.versionName}</span>
          <ConfigVersionStatusView version={field} />
        </div>
      ),
      value: field.configId,
    })) || []
  );
};
const transformPromptToOptions = (stageData: PromptVersion[] | null) => {
  return (
    stageData?.map((field) => ({
      label: (
        <div className="w-full flex justify-between items-center pr-4">
          <span>{field.versionName}</span>
          <PromptVersionStatusView version={field} />
        </div>
      ),
      value: field.promptVersionId.toString(),
    })) || []
  );
};
export const ModalSelectVersion = ({ open, data, refetch, onClose }: Props) => {
  const shop = useAtomValue(shopAtom);
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
  });
  const { t } = useTranslation();
  const handleSubmit = async () => {
    await form.handleSubmit((formData) => {
      AiAgentService.putChooseVersion({
        ...formData,
        aiAgentConfigId: data?.aiAgentConfigId || "",
      }).then(() => {
        resetForm();
        refetch();
        onClose();
      });
    })();
  };
  const resetForm = () => {
    form.reset({
      configId: "",
      promptVersionId: "",
    });
  };
  const { data: versionList } = useQuery({
    queryKey: ["getVersionList", shop?.id],
    queryFn: () => {
      return EvaluationConfigService.getVersionList(
        {
          version: "",
        },
        { shopId: shop?.id },
      );
    },
  });
  const { data: promptList } = useQuery({
    queryKey: ["getPromptList"],
    queryFn: () => {
      return PromptVersionService.selectPromptVersionList({});
    },
  });

  useEffect(() => {
    if (open && data) {
      form.setValue("configId", data.configId || "");
      form.setValue("promptVersionId", data.promptVersionId || "");
    }
  }, [data, form, open]);
  return (
    <Dialog open={open} onOpenChange={onClose} modal={true}>
      <DialogContent>
        <DialogTitle className="flex items-center">
          {t("select.version")}
        </DialogTitle>
        <Form {...form}>
          <form className="space-y-6  py-6">
            <FormField
              control={form.control}
              name="configId"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center">
                  <FormLabel className="w-26 justify-end items-center flex-shrink-0 mr-4">
                    {t("shop.config.version")}:
                  </FormLabel>
                  <div className="flex-1">
                    <FormControl>
                      <SelectSearch
                        placeholder={t("form.placeholder.select")}
                        options={
                          versionList ? transformShopToOptions(versionList) : []
                        }
                        popWidthClass="w-[18rem]"
                        modal={false}
                        multiple={false}
                        value={field.value}
                        onChange={(value) => {
                          if (value) field.onChange(value);
                        }}
                      ></SelectSearch>
                    </FormControl>
                    <FormMessage />
                  </div>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="promptVersionId"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center">
                  <FormLabel className="w-26 justify-end items-center flex-shrink-0 mr-4">
                    {t("prompt.version")}:
                  </FormLabel>
                  <div className="flex-1">
                    <FormControl>
                      <SelectSearch
                        placeholder={t("form.placeholder.select")}
                        options={
                          promptList ? transformPromptToOptions(promptList) : []
                        }
                        popWidthClass="w-[18rem]"
                        modal={false}
                        multiple={false}
                        value={field.value}
                        onChange={(value) => {
                          if (value) field.onChange(value);
                        }}
                      ></SelectSearch>
                    </FormControl>
                    <FormMessage />
                  </div>
                </FormItem>
              )}
            />
          </form>
        </Form>
        <DialogFooter>
          <Button onClick={handleSubmit}>
            {t("common.delete.confirm.confirm")}
          </Button>
          <DialogClose asChild>
            <Button
              onClick={() => {
                onClose();
                resetForm();
              }}
              variant="default"
              color="gray"
            >
              {t("common.delete.confirm.cancel")}
            </Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
