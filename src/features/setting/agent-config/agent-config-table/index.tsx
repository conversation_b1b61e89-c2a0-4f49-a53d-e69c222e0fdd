import {
  AsyncDataTable,
  DataTableToolbar,
  UseTableState,
} from "@/components/common/data-table";
import { ModalEditAddAgent } from "@/features/setting/agent-config/modal-edit-add-agent";
import { useCallback, useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useTranslation } from "react-i18next";
import { useMemo } from "react";
import { UseQueryResult } from "@tanstack/react-query";
import { getTableColumn } from "@/features/setting/agent-config/agent-config-table/get-table-column";
import { AgentConfigTableData } from "@/types/api/agent-config";
import { ModalSelectVersion } from "../modal-select-version";
import { ModalDelAgent } from "../modal-del-agent";
import { ModalCodeEdit } from "../modal-code-deit";
import { usePermission } from "@/hooks/use-permission";
import { Can } from "@/components/common/can";
import { PERMISSION_CODES } from "@/constants/permission";
export type AgentConfigTableProps = UseTableState<AgentConfigTableData> & {
  query: UseQueryResult<{
    data: AgentConfigTableData[];
    totalPage: number;
    totalSize: number;
  }>;
};

interface TableAction {
  type: "modal-config" | "modal-versions" | "edit-code" | "modal-delete";
  data: AgentConfigTableData | null;
  operation: "add" | "edit";
}
export const AgentConfigTable = ({
  query,
  ...tableState
}: AgentConfigTableProps) => {
  const { t } = useTranslation();
  const { hasPermission } = usePermission();
  const [tableAction, setTableAction] = useState<TableAction | null>(null);

  const dataTableProps = {
    virtualization: {
      enabled: true,
    },
  };
  // 刷新数据的方法
  const refreshData = useCallback(() => {
    query.refetch();
  }, [query]);
  const columns = useMemo(
    () => getTableColumn({ t, setTableAction, refreshData, hasPermission }),
    [t, setTableAction, refreshData, hasPermission],
  );

  return (
    <>
      <AsyncDataTable
        className="h-full p-6"
        query={query}
        tableName="agent-config-table"
        dataTableProps={dataTableProps}
        columns={columns}
        getRowId={(row) => row?.aiAgentConfigId || ""}
        initialState={{
          columnPinning: { right: ["actions"] },
        }}
        {...tableState}
      >
        {(table) => (
          <>
            <DataTableToolbar
              table={table}
              filters={tableState.globalFilter}
              setFilterValue={tableState.setFilterValue}
              onResetFilters={tableState.resetFilters}
            >
              <Can permissionCode={PERMISSION_CODES.SHOP_AGENT_EDIT}>
                <Button
                  onClick={() => {
                    setTableAction({
                      type: "modal-config",
                      data: null,
                      operation: "add",
                    });
                  }}
                >
                  {t("agent.add")}
                </Button>
              </Can>
            </DataTableToolbar>
          </>
        )}
      </AsyncDataTable>
      {/* 选择版本 */}
      <ModalSelectVersion
        onClose={() => {
          setTableAction(null);
        }}
        refetch={() => {
          query.refetch();
        }}
        data={tableAction?.data || null}
        open={tableAction?.type === "modal-versions"}
      />
      {/* 编辑修改智能体 */}
      <ModalEditAddAgent
        onClose={() => {
          setTableAction(null);
        }}
        refetch={() => {
          query.refetch();
        }}
        editType={tableAction?.operation || null}
        data={tableAction?.data || null}
        open={tableAction?.type === "modal-config"}
      />
      {/* 删除智能体 */}
      <ModalDelAgent
        onCancel={() => {
          setTableAction(null);
        }}
        refetch={() => {
          query.refetch();
        }}
        data={tableAction?.data || null}
        open={tableAction?.type === "modal-delete"}
      ></ModalDelAgent>
      {/* 编辑代码 */}
      <ModalCodeEdit
        onCancel={() => {
          setTableAction(null);
        }}
        refetch={() => {
          query.refetch();
        }}
        data={tableAction?.data || null}
        open={tableAction?.type === "edit-code"}
      />
    </>
  );
};
