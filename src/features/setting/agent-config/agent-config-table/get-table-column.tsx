import {
  AgentConfigTableData,
  ReceptionModeType,
} from "@/types/api/agent-config";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ColumnDef } from "@tanstack/react-table";
import { TFunction } from "i18next";
import { Tooltip } from "@/components/common/tooltip";
import { AiAgentService } from "@/services/ai-agent-service";
import { DelConfirmModal } from "@/components/common/del-confirm-modal";
import { Can } from "@/components/common/can";
import { PERMISSION_CODES } from "@/constants/permission";
export const getTableColumn = ({
  t,
  setTableAction,
  refreshData,
  hasPermission,
}: {
  t: TFunction;
  setTableAction: (
    action: {
      type: "modal-config" | "edit-code" | "modal-versions" | "modal-delete";
      data: Partial<AgentConfigTableData>;
      operation: "add" | "edit";
    } | null,
  ) => void;
  refreshData: () => void;
  hasPermission: (permission: string) => boolean;
}): ColumnDef<AgentConfigTableData>[] => {
  return [
    {
      accessorKey: "name",
      header: t("agent.config.table.title"),
      meta: {
        label: t("agent.config.table.title"),
      },
      cell: ({ row }) => (
        <Tooltip content={row.original.name}>
          <span className="truncate text-ellipsis max-w-[200px]">
            {row.original.name}
          </span>
        </Tooltip>
      ),
    },
    {
      accessorKey: "description",
      header: t("agent.config.table.description"),
      meta: {
        label: t("agent.config.table.description"),
      },
      cell: ({ row }) => (
        <Tooltip content={row.original.description}>
          <span className="truncate text-ellipsis max-w-[200px]">
            {row.original.description}
          </span>
        </Tooltip>
      ),
    },
    {
      accessorKey: "aiAgentConfigId",
      header: t("agent.config.table.aiAgentConfigId"),
      meta: {
        label: t("agent.config.table.aiAgentConfigId"),
      },
      size: 100,
      cell: ({ row }) => (
        <Tooltip content={row.original.aiAgentConfigId}>
          <span className="truncate text-ellipsis max-w-[200px]">
            {row.original.aiAgentConfigId}
          </span>
        </Tooltip>
      ),
    },
    {
      accessorKey: "userNames",
      header: t("agent.config.table.userNames"),
      meta: {
        label: t("agent.config.table.userNames"),
      },
      cell: ({ row }) => (
        <Tooltip
          content={
            row.original.userNames ? row.original.userNames.join("、") : ""
          }
        >
          <span className="truncate text-ellipsis max-w-[200px]">
            {row.original.userNames ? row.original.userNames.join("、") : ""}
          </span>
        </Tooltip>
      ),
    },
    {
      accessorKey: "createdAt",
      header: t("create.time"),
      meta: {
        label: t("create.time"),
      },
      size: 180,
      cell: ({ row }) => (
        <span className="truncate text-ellipsis max-w-[200px]">
          {row.original.createdAt}
        </span>
      ),
    },
    {
      accessorKey: "receptionMode",
      header: t("agent.config.table.pattern"),
      meta: {
        label: t("agent.config.table.pattern"),
      },
      size: 100,
      cell: ({ row }) => (
        <span className="truncate text-ellipsis max-w-[200px]">
          {row.original.receptionMode
            ? ReceptionModeType[row.original.receptionMode]
            : ""}
        </span>
      ),
    },
    {
      id: "actions",
      size: 180,
      header: t("common.actions"),
      cell: ({ row }) => {
        return (
          <div className="flex flex-row  w-full">
            <Can permissionCode={PERMISSION_CODES.SHOP_AGENT_EDIT}>
              <span
                className="text-primary cursor-pointer mr-2"
                onClick={() => {
                  setTableAction({
                    type: "modal-config",
                    data: row.original,
                    operation: "edit",
                  });
                }}
              >
                {t("common.edit")}
              </span>
            </Can>
            <Can permissionCode={PERMISSION_CODES.SHOP_AGENT_EDIT}>
              <DelConfirmModal
                title={t("agent.config.modal.copy.title")}
                cancelText={t("common.cancel")}
                confirmText={t("common.delete.confirm.confirm")}
                actionButton={
                  <span className="text-primary cursor-pointer mr-2">
                    {t("common.copy.action")}
                  </span>
                }
                onSubmit={() => {
                  if (row.original.aiAgentConfigId) {
                    AiAgentService.copyAiAgentConfig({
                      aiAgentConfigId: row.original.aiAgentConfigId,
                    }).then(() => {
                      refreshData();
                    });
                  }
                }}
              ></DelConfirmModal>
            </Can>
            {hasPermission(PERMISSION_CODES.SHOP_AGENT_EDIT) ||
            hasPermission(PERMISSION_CODES.SHOP_AGENT_VERSION_EDIT) ||
            hasPermission(PERMISSION_CODES.SHOP_AGENT_CODE_EDIT) ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <span className="text-primary cursor-pointer mr-2">
                    {t("common.more")}
                  </span>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  align="end"
                  className="w-fit rounded-md border-gray-200"
                >
                  <Can
                    permissionCode={PERMISSION_CODES.SHOP_AGENT_VERSION_EDIT}
                  >
                    <DropdownMenuItem
                      className="flex items-center gap-2 rounded-sm cursor-pointer"
                      onClick={() => {
                        setTableAction({
                          type: "modal-versions",
                          data: row.original,
                          operation: "edit",
                        });
                      }}
                    >
                      <span className="text-primary cursor-pointer py-1">
                        {t("select.version")}
                      </span>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                  </Can>
                  <Can permissionCode={PERMISSION_CODES.SHOP_AGENT_CODE_EDIT}>
                    <DropdownMenuItem
                      className="flex items-center gap-2 !text-destructive rounded-sm cursor-pointer"
                      onClick={() => {
                        setTableAction({
                          type: "edit-code",
                          data: row.original,
                          operation: "edit",
                        });
                      }}
                    >
                      <span className="text-primary cursor-pointer py-1">
                        {t("edit.code")}
                      </span>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                  </Can>
                  <Can permissionCode={PERMISSION_CODES.SHOP_AGENT_EDIT}>
                    <DropdownMenuItem
                      className="flex items-center gap-2 !text-destructive rounded-sm cursor-pointer"
                      onClick={() => {
                        setTableAction({
                          type: "modal-delete",
                          data: row.original,
                          operation: "edit",
                        });
                      }}
                    >
                      {t("common.delete")}
                    </DropdownMenuItem>
                  </Can>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              ""
            )}
          </div>
        );
      },
    },
  ];
};
