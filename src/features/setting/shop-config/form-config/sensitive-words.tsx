import { ItemsField } from "./item-field-base";
import { useFieldArray } from "react-hook-form";
import { useFormContext } from "../hooks/form-context";
import { FolderOutput, FolderInput, FolderDown } from "lucide-react";
import { Tooltip } from "@/components/common/tooltip";
import { useCsvImportExport } from "../hooks/use-csv-import-export";
import { useRef } from "react";

export const SensitiveWords = () => {
  // 使用表单上下文
  const { form, t } = useFormContext();
  // 使用 useFieldArray 管理动态数组
  const fieldArray = useFieldArray({
    control: form.control,
    name: "shop_configs.sensitive_words",
  });
  const { fields, append, remove } = fieldArray;

  // 文件输入引用
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 使用CSV导入导出hook
  const { createExportTemplate, createExportData, handleFileImport } =
    useCsvImportExport();

  // 导出模板
  const handleExportTemplate = () => {
    createExportTemplate("淘宝", {
      headerRow: "敏感词",
      filename: "敏感词列表模板.csv",
    });
  };

  // 导出当前敏感词数据
  const handleExportData = () => {
    const sensitiveWords = form.watch("shop_configs.sensitive_words");
    createExportData(sensitiveWords, {
      headerRow: "敏感词",
      filename: "敏感词列表.csv",
    });
  };

  // 处理导入按钮点击
  const handleImportClick = () => {
    fileInputRef.current?.click();
  };

  // 处理文件导入
  const onFileImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    handleFileImport(event, fieldArray);
    // 清空文件输入，允许重复选择同一文件
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  return (
    <div className="space-y-4" data-form-section="sensitive_words">
      {/* 隐藏的文件输入 */}
      <input
        ref={fileInputRef}
        type="file"
        accept=".csv"
        onChange={onFileImport}
        style={{ display: "none" }}
      />

      <h2 className="text-lg font-medium flex items-center">
        <span>{t("menu.sensitive.words")}</span>
        <Tooltip content="下载模版">
          <FolderDown
            onClick={handleExportTemplate}
            className="w-4 h-4 ml-4 cursor-pointer"
          />
        </Tooltip>
        <Tooltip content="导入">
          <FolderInput
            onClick={handleImportClick}
            className="w-4 h-4 ml-4 cursor-pointer"
          />
        </Tooltip>
        <Tooltip content="导出">
          <FolderOutput
            onClick={handleExportData}
            className="w-4 h-4 ml-4 cursor-pointer"
          />
        </Tooltip>
      </h2>
      <ItemsField
        control={form.control}
        fieldArray={{
          fields,
          append,
          remove,
          move: fieldArray.move,
        }}
        t={t}
        fieldName="shop_configs.sensitive_words"
        label="敏感词"
      />
    </div>
  );
};
