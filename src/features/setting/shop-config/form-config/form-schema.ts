// 定义表单验证模式
import * as z from "zod";
// 提取公共的时间间隔校验函数
const createTimeIntervalValidator = () => {
  return (
    intervals: Array<{ time?: number | null; unit: string }> | undefined,
  ) => {
    if (!intervals || intervals.length === 0) return { valid: true };

    const convertToMinutes = (time: number, unit: string): number => {
      switch (unit) {
        case "MINUTES":
          return time;
        case "HOURS":
          return time * 60;
        default:
          return time;
      }
    };

    // 校验时间范围
    for (const interval of intervals) {
      if (interval.time === undefined || interval.time === null) {
        return { valid: false, errorType: "time_empty" };
      } // 跳过空值
      if (interval.unit === "HOURS") {
        if (interval.time >= 24) {
          return { valid: false, errorType: "exceed_24_hours" };
        }
      } else if (interval.unit === "MINUTES") {
        if (interval.time > 1440) {
          return { valid: false, errorType: "exceed_1440_minutes" };
        }
      }
    }

    // 校验重复时间
    const minutesSet = new Set<number>();
    for (const interval of intervals) {
      if (interval.time === undefined || interval.time === null) continue; // 跳过空值
      const minutes = convertToMinutes(interval.time, interval.unit);
      if (minutesSet.has(minutes)) {
        return { valid: false, errorType: "duplicate" };
      }
      minutesSet.add(minutes);
    }

    return { valid: true };
  };
};

// 定义 shop_configs_schema
const shop_configs_schema = z.object({
  top_10_items: z.array(
    z.object({
      value: z.string().min(1, { message: "请输入店铺热销商品ID" }),
    }),
  ),
  products: z
    .array(
      z.object({
        value: z.string().min(1, { message: "请输入热销商品类目" }),
      }),
    )
    .optional(),
  shop_name: z.string().min(1, { message: "请输入店铺名称" }),
  default_shop_id: z.string().min(1, { message: "请输入商品库店铺ID" }),
  shop_desc: z.string().optional(),
  default_category: z.string().min(1, { message: "请输入主营商品类目" }),
  transfer_category: z
    .array(
      z.object({
        value: z.string().min(1, { message: "请输入需转人工的商品类目" }),
      }),
    )
    .optional(),
  product_attr_diff: z
    .array(
      z.object({
        value: z.string().min(1, { message: "请输入商品属性黑名单" }),
      }),
    )
    .optional(),
  reason_and_answer_prompt: z.string().optional(),
  sensitive_words: z
    .array(
      z.object({
        value: z.string().min(1, { message: "请输入敏感词" }),
      }),
    )
    .optional(),
  // 添加催付配置相关字段
  backend_config: z.object({
    urgeBuySwitch: z.boolean().optional(),
    urgePaySwitch: z.boolean().optional(),
    urgeBuyIntervals: z
      .array(
        z.object({
          time: z.preprocess((val) => {
            if (val === "" || val === null || val === undefined) {
              return undefined;
            }
            const num = Number(val);
            return isNaN(num) || !isFinite(num) ? undefined : num;
          }, z.number().min(1).optional()),
          unit: z.string().min(1, { message: "请输入时间单位" }),
        }),
      )
      .optional()
      .refine(
        (intervals) => {
          const result = createTimeIntervalValidator()(intervals);
          return result.valid;
        },
        {
          message: ((ctx) => {
            const result = createTimeIntervalValidator()(ctx.input);
            switch (result.errorType) {
              case "exceed_24_hours":
                return "小时数不能超过24小时";
              case "exceed_1440_minutes":
                return "分钟数不能超过1440分钟（24小时）";
              case "duplicate":
                return "催拍时间间隔不能重复，请检查是否存在等价的时间设置（如1小时和60分钟）";
              default:
                return "催拍时间间隔设置不能为空";
            }
          }) as any,
        },
      ),
    urgeBuyStatements: z
      .array(
        z.object({
          value: z.string().min(1, { message: "请输入催拍话术" }),
        }),
      )
      .optional(),
    // 修改 urgePayIntervals
    urgePayIntervals: z
      .array(
        z.object({
          time: z.preprocess((val) => {
            if (val === "" || val === null || val === undefined) {
              return undefined;
            }
            const num = Number(val);
            return isNaN(num) || !isFinite(num) ? undefined : num;
          }, z.number().min(1).optional()),
          unit: z.string().min(1, { message: "请输入时间单位" }),
        }),
      )
      .optional()
      .refine(
        (intervals) => {
          const result = createTimeIntervalValidator()(intervals);
          return result.valid;
        },
        {
          message: ((ctx) => {
            const result = createTimeIntervalValidator()(ctx.input);
            switch (result.errorType) {
              case "exceed_24_hours":
                return "小时数不能超过24小时";
              case "exceed_1440_minutes":
                return "分钟数不能超过1440分钟（24小时）";
              case "duplicate":
                return "催付时间间隔不能重复，请检查是否存在等价的时间设置（如1小时和60分钟）";
              default:
                return "催付时间间隔设置不能为空";
            }
          }) as any,
        },
      ),
    urgePayStatements: z
      .array(
        z.object({
          value: z.string().min(1, { message: "请输入催付话术" }),
        }),
      )
      .optional(),
  }),
});

// 导出 shop_configs 对象
export const shop_configs = shop_configs_schema.shape;

// 定义预设标签配置schema
const preset_tag_config_schema = z
  .array(
    z.object({
      label: z.string().min(1, { message: "请输入生成标签" }),
      regex: z.string().min(1, { message: "请输入正则表达式" }),
    }),
  )
  .optional();

// 定义顾客消息日志信息配置schema
const user_message_log_schema = z
  .array(
    z.object({
      mode: z.string().min(1, { message: "请输入模式" }),
      label: z.string().min(1, { message: "请输入标签" }),
      key_match: z.string().min(1, { message: "请输入字段" }),
      query_match: z.string().min(1, { message: "请输入匹配" }),
      value_match: z.string().min(1, { message: "请输入值" }),
    }),
  )
  .optional();
// 定义AI客服消息日志信息配置schema
const ai_service_log_schema = z.array(
  z.object({
    tool: z.string().min(1, { message: "请输入meta前缀" }),
    label: z.string().min(1, { message: "请输入标准" }),
    match: z
      .array(
        z.object({
          value: z.string().min(1, { message: "请输入敏感词" }),
        }),
      )
      .optional(),
  }),
);

// 在shopConfigFormSchema中添加预设标签配置
export const shopConfigFormSchema = z.object({
  shop_configs: shop_configs_schema,
  preset_configs: z.object({
    preset_label_gen: z
      .object({
        query_reg_matches: preset_tag_config_schema, // 预设标签配置
        user_msg_meta_matches: user_message_log_schema, // 顾客消息日志信息配置
        meta_matches: ai_service_log_schema, // AI客服消息日志信息配置
      })
      .optional(),
    preset_label_resp: z.array(
      z.object({
        preset_name: z.string().min(1, { message: "请输入回复名称" }),
        method: z.string().min(1, { message: "请输入回复方式" }),
        prompt: z.array(z.object({ value: z.string() })).optional(),
      }),
    ),
  }),
  prompt_config: z.object({
    match_category_list: z
      .array(z.string().min(1, { message: "请输入类目" }))
      .min(1, { message: "至少需要选择一个类目" }),
    shop_variable_list: z.array(
      z
        .object({
          key: z.string().min(0, { message: "请输入变量名" }),
          value: z.string().min(0, { message: "请输入变量值" }),
        })
        .optional(),
    ),
  }),
});

export type FormValues = z.infer<typeof shopConfigFormSchema>;
