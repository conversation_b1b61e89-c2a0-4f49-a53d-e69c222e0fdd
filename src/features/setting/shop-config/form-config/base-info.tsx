import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Tooltip } from "@/components/common/tooltip";
import { Input } from "@/components/ui/input";
import { useFormContext } from "../hooks/form-context";
import { CircleHelp } from "lucide-react";
import { GridMultiSelect } from "@/components/common/grid-multi-select/grid-multi-select";
import { useMemo } from "react";

export const BaseInfoForm = () => {
  // 使用表单上下文
  const { form, t, categoryList } = useFormContext();

  // 将categoryList转换为GridMultiSelect所需的格式
  const categoryGroups = useMemo(() => {
    // 转换为GridOption格式
    return categoryList.map((item) => ({
      id: item.categoryName,
      title: item.categoryName,
      options: item.child
        ? item.child.map((v) => ({
            value: v.categoryId || "",
            label: v.categoryName || "",
          }))
        : [],
    }));
  }, [categoryList]);

  // 使用 useFieldArray 管理动态数组
  return (
    <div className="space-y-4" data-form-section="base_info">
      <h2 className="text-lg font-medium">{t("menu.base.info")}</h2>
      <FormField
        control={form.control}
        name="shop_configs.shop_name"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="before:content-['*'] before:text-red-500">
              店铺
            </FormLabel>
            <FormControl>
              <Input placeholder="请输入店铺名称" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="shop_configs.default_shop_id"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="before:content-['*'] before:text-red-500">
              商品库店铺名称
            </FormLabel>
            <FormControl>
              <Input placeholder="请输入商品库店铺名称" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="shop_configs.shop_desc"
        render={({ field }) => (
          <FormItem>
            <FormLabel>
              <span>店铺描述</span>
              <Tooltip content="输入当前电商平台下的店铺描述">
                <CircleHelp className="w-4 h-4" />
              </Tooltip>
            </FormLabel>
            <FormControl>
              <Input placeholder="请输入店铺描述" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="prompt_config.match_category_list"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="before:content-['*'] before:text-red-500">
              电商类目
            </FormLabel>
            <FormControl>
              <GridMultiSelect
                maxDisplayCount={15}
                selectedValues={field.value}
                onSelectChange={field.onChange}
                placeholder="请选择店铺的电商类目"
                groups={categoryGroups}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
};
