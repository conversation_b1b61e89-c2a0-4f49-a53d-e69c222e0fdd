import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Tooltip } from "@/components/common/tooltip";
import { Input } from "@/components/ui/input";
import { ItemsField } from "./item-field-base";
import { useFieldArray } from "react-hook-form";
import { useFormContext } from "../hooks/form-context";
import { CircleHelp } from "lucide-react";
export const ProductInfoForm = () => {
  // 使用表单上下文
  const { form, t } = useFormContext();
  // 使用 useFieldArray 管理动态数组
  const {
    fields: top10Items,
    append: appendTop10Items,
    remove: removeTop10Items,
    move: moveTop10Items,
  } = useFieldArray({
    control: form.control,
    name: "shop_configs.top_10_items",
  });
  const {
    fields: products,
    append: appendProducts,
    remove: removeProducts,
    move: moveProducts,
  } = useFieldArray({
    control: form.control,
    name: "shop_configs.products",
  });
  const {
    fields: transferCategory,
    append: appendTransferCategory,
    remove: removeTransferCategory,
    move: moveTransferCategory,
  } = useFieldArray({
    control: form.control,
    name: "shop_configs.transfer_category",
  });
  const {
    fields: productAttrDiff,
    append: appendProductAttrDiff,
    remove: removeProductAttrDiff,
    move: moveProductAttrDiff,
  } = useFieldArray({
    control: form.control,
    name: "shop_configs.product_attr_diff",
  });
  return (
    <div className="space-y-4" data-form-section="product_info">
      <h2 className="text-lg font-medium">{t("menu.product.info")}</h2>
      <FormField
        control={form.control}
        name="shop_configs.default_category"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="before:content-['*'] before:text-red-500">
              <span>主营商品类目</span>
              <Tooltip content="请选择一个主营商品类目进行填写，例如：床垫">
                <CircleHelp className="w-4 h-4" />
              </Tooltip>
            </FormLabel>
            <FormControl>
              <Input placeholder="请输入主营商品类目" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <ItemsField
        required={true}
        control={form.control}
        fieldArray={{
          append: appendProducts,
          fields: products,
          remove: removeProducts,
          move: moveProducts,
        }}
        t={t}
        fieldName="shop_configs.products"
        label="销售商品类目"
      />
      <ItemsField
        control={form.control}
        fieldArray={{
          fields: transferCategory,
          append: appendTransferCategory,
          remove: removeTransferCategory,
          move: moveTransferCategory,
        }}
        t={t}
        fieldName="shop_configs.transfer_category"
        label="需转人工的商品类目"
      />
      <ItemsField
        required={true}
        control={form.control}
        fieldArray={{
          fields: productAttrDiff,
          append: appendProductAttrDiff,
          remove: removeProductAttrDiff,
          move: moveProductAttrDiff,
        }}
        t={t}
        fieldName="shop_configs.product_attr_diff"
        label="商品属性黑名单"
      />
      <ItemsField
        required={true}
        control={form.control}
        fieldArray={{
          fields: top10Items,
          append: appendTop10Items,
          remove: removeTop10Items,
          move: moveTop10Items,
        }}
        t={t}
        fieldName="shop_configs.top_10_items"
        label="店铺热销商品"
      />
    </div>
  );
};
