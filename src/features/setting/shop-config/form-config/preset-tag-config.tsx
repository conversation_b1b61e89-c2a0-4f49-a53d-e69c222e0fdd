import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useFieldArray } from "react-hook-form";
import { useFormContext } from "../hooks/form-context";
import { GripVertical, PlusCircle, Trash2 } from "lucide-react";
import { Tooltip } from "@/components/common/tooltip";

export const PresetTagConfigForm = () => {
  // 使用表单上下文
  const { form, t } = useFormContext();

  // 使用 useFieldArray 管理预设标签配置数组
  const {
    fields: tagItems,
    append: appendTagItem,
    remove: removeTagItem,
  } = useFieldArray({
    control: form.control,
    name: "preset_configs.preset_label_gen.query_reg_matches",
  });

  return (
    <div className="space-y-2">
      <h2 className="text-lg font-medium">{t("menu.preset.label.config")}</h2>
      {/* 预设标签内容配置 */}
      <div className="space-y-2">
        <h3
          className="text-base font-medium"
          data-form-section="preset_label_matches"
        >
          {t("menu.preset.label.matches")}
        </h3>
        <FormLabel>顾客消息的内容匹配配置</FormLabel>
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4 bg-gray-50 h-[48px] items-center px-4">
            <div className="font-medium text-sm">生成标签</div>
            <div className="font-medium text-sm">正则表达式</div>
          </div>
          <div className="max-h-[440px] overflow-y-auto space-y-4 py-2">
            {tagItems.map((field, index) => (
              <div
                key={field.id}
                className="grid grid-cols-2 gap-4 items-start"
              >
                <div className="flex items-center gap-2">
                  <Tooltip content="拖拽">
                    <div
                      role="button"
                      aria-label="拖拽"
                      className="p-1 rounded-sm hover:bg-gray-100 transition-colors duration-200 flex items-center justify-center mr-1"
                    >
                      <GripVertical className="w-4 h-4 cursor-pointer" />
                    </div>
                  </Tooltip>
                  <FormField
                    control={form.control}
                    name={`preset_configs.preset_label_gen.query_reg_matches.${index}.label`}
                    render={({ field }) => (
                      <FormItem className="flex-1">
                        <FormControl>
                          <Input placeholder="请输入生成标签" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="flex items-center gap-2">
                  <FormField
                    control={form.control}
                    name={`preset_configs.preset_label_gen.query_reg_matches.${index}.regex`}
                    render={({ field }) => (
                      <FormItem className="flex-1">
                        <FormControl>
                          <Input placeholder="请输入正则表达式" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    onClick={() => removeTagItem(index)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>
        <Button
          type="button"
          variant="outline"
          size="sm"
          className="mt-2 w-full"
          onClick={() => appendTagItem({ label: "", regex: "" })}
        >
          <PlusCircle className="mr-2 h-4 w-4" />
          添加条目
        </Button>
      </div>
    </div>
  );
};
