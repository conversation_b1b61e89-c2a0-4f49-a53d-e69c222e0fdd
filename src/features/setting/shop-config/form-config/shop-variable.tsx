import { useEffect, useState, useMemo } from "react";
import { Textarea } from "@/components/ui/textarea";
import { useFormContext } from "../hooks/form-context";
import { LevelLabels } from "@/constants/prompt";
import { Variable, VariableStatus } from "@/types/api/variable";
import { MessageSquareWarning } from "lucide-react";
import {
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";
import { useFieldArray } from "react-hook-form";
import { Tooltip } from "@/components/common/tooltip";
import { Checkbox } from "@/components/ui/checkbox";
import {
  MultiSelectFilter,
  MultiSelectOption,
} from "@/components/common/multi-select-filter";

export const ShopVariable = () => {
  // 使用表单上下文
  const { t, variableList, form } = useFormContext();
  const [selectedValues, setSelectedValues] = useState<string[]>([]);

  // 从 variableList 中提取所有的 abilityNames 生成选项数组
  const options: MultiSelectOption[] = useMemo(() => {
    const abilityNamesSet = new Set<string>();
    variableList.forEach((item) => {
      item.variableDTOS.forEach((v: Variable) => {
        if (v.abilityNames && Array.isArray(v.abilityNames)) {
          v.abilityNames.forEach((abilityName: Record<string, unknown>) => {
            if (abilityName) {
              abilityNamesSet.add(String(abilityName) || "");
            }
          });
        }
      });
    });
    return Array.from(abilityNamesSet).map((abilityName) => ({
      value: abilityName,
      label: abilityName,
    }));
  }, [variableList]);

  const [used, setUsed] = useState<VariableStatus>(VariableStatus.UN_USED);
  // 使用 useFieldArray 管理变量列表
  const { fields, append } = useFieldArray({
    control: form.control,
    name: "prompt_config.shop_variable_list",
  });

  // 当组件挂载时，根据variableList初始化表单字段
  useEffect(() => {
    variableList.forEach((item) => {
      item.variableDTOS.forEach((v) => {
        // 检查是否已经存在相同的key
        const exists = fields.some((field) => field.key === v.name);
        if (!exists) {
          append({ key: v.name, value: "" });
        }
      });
    });
  }, [variableList, append, fields]);

  return (
    <div className="space-y-4 p-[2px]" data-form-section="shop_variables">
      <h2 className="text-base font-medium">{t("menu.shop.variables")}</h2>
      <div className="flex justify-between">
        <MultiSelectFilter
          value={selectedValues}
          options={options}
          onChange={(values) => setSelectedValues(values || [])}
          title="能力名称"
          placeholder="搜索选项..."
        />
        <div className="flex items-center">
          <Checkbox
            id="only_show_referenced"
            checked={used === VariableStatus.USED}
            onCheckedChange={(checked) => {
              setUsed(checked ? VariableStatus.USED : VariableStatus.UN_USED);
            }}
          />
          <label
            htmlFor="only_show_referenced"
            className="text-sm ml-1 cursor-pointer"
          >
            仅展示已引用内容
          </label>
        </div>
      </div>
      {variableList.map((item) => (
        <div
          className="space-y-4"
          key={item.level}
          data-form-section={item.level + "_VARIABLES"}
        >
          <h3 className="text-sm font-medium">{LevelLabels[item.level]}</h3>
          {item.variableDTOS.map((v: Variable) => {
            // 检查是否只显示已引用内容
            if (
              used === VariableStatus.USED &&
              v.status !== VariableStatus.USED
            )
              return null;

            // 检查能力名称筛选
            if (selectedValues.length > 0) {
              const hasMatchingAbility = v.abilityNames?.some((abilityName) =>
                selectedValues.includes(String(abilityName)),
              );
              if (!hasMatchingAbility) return null;
            }

            const fieldIndex = fields.findIndex((f) => f.key === v.name);
            if (fieldIndex === -1) return null;
            return (
              <FormField
                key={v.name}
                control={form.control}
                name={`prompt_config.shop_variable_list.${fieldIndex}.value`}
                render={({ field }) => (
                  <FormItem>
                    <div className="flex flex-col gap-2">
                      <div className="text-sm  flex items-center">
                        <span>{v.name}</span>
                        <Tooltip content={v.definition}>
                          <MessageSquareWarning className="h-4 w-4 text-gray-500 ml-2" />
                        </Tooltip>
                      </div>
                      <div className="text-[12px] text-gray-500">
                        已使用能力:{v.abilityNames?.join("、")}
                      </div>
                      <FormControl>
                        <Textarea placeholder={v.example} {...field} />
                      </FormControl>
                      <FormMessage />
                    </div>
                  </FormItem>
                )}
              />
            );
          })}
        </div>
      ))}
    </div>
  );
};
