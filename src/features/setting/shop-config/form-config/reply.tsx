import { useFormContext } from "../hooks/form-context";
import { ReplyItemsField } from "./replay-item-field";
import { useFieldArray } from "react-hook-form";
export const Reply = () => {
  // 使用表单上下文
  const { form, t } = useFormContext();
  const fieldName = "preset_configs.preset_label_resp";
  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: fieldName,
  });
  return (
    <div className="space-y-4" data-form-section="preset_label_resp">
      <h3 className="text-base font-medium">预设标签回复配置</h3>
      <div className="m-h-[800px] overflow-y-auto">
        <ReplyItemsField
          required={true}
          control={form.control}
          fieldArray={{
            fields,
            append,
            remove,
          }}
          t={t}
          fieldName={fieldName}
        />
      </div>
    </div>
  );
};
