import {
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useFieldArray } from "react-hook-form";
import { GripVertical, CirclePlus, CircleMinus, Trash2 } from "lucide-react";
import { Tooltip } from "@/components/common/tooltip";
import { Textarea } from "@/components/ui/textarea";

// AI客服消息日志项组件
const AIServiceLogItem = ({ field, index, control, remove }) => {
  // 在组件内部直接使用 useFieldArray
  const {
    fields: matchFields,
    append: appendMatch,
    remove: removeMatch,
  } = useFieldArray({
    control,
    name: `preset_configs.preset_label_gen.meta_matches.${index}.match`,
  });

  return (
    <div key={field.id} className="grid grid-cols-3 gap-4 items-start">
      <div className="flex items-center gap-2">
        <Tooltip content="拖拽">
          <div
            role="button"
            aria-label="拖拽"
            className="p-1 rounded-sm hover:bg-gray-100 transition-colors duration-200 flex items-center justify-center mr-1"
          >
            <GripVertical className="w-4 h-4 cursor-pointer" />
          </div>
        </Tooltip>
        <FormField
          control={control}
          name={`preset_configs.preset_label_gen.meta_matches.${index}.tool`}
          render={({ field }) => (
            <FormItem className="flex-1">
              <FormControl>
                <Input placeholder="请输入工具" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <FormField
        control={control}
        name={`preset_configs.preset_label_gen.meta_matches.${index}.label`}
        render={({ field }) => (
          <FormItem className="flex-1">
            <FormControl>
              <Input placeholder="请输入标签" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <div className="flex flex-col gap-2 w-full">
        {matchFields.map((matchField, matchIndex) => (
          <div key={matchField.id} className="flex items-center gap-2">
            <FormField
              control={control}
              name={`preset_configs.preset_label_gen.meta_matches.${index}.match.${matchIndex}.value`}
              render={({ field }) => (
                <FormItem className="flex-1">
                  <FormControl>
                    <div className="flex justify-between items-center">
                      <Textarea
                        className="flex-1 mr-1"
                        placeholder="请输入meta配置匹配"
                        {...field}
                      />
                      <div className="flex ml-1 w-[50px]">
                        <div
                          role="button"
                          aria-label="添加"
                          onClick={() => appendMatch("")}
                          className={`p-1 rounded-sm hover:bg-gray-100 transition-colors duration-200 flex items-center justify-center ${matchIndex === matchFields.length - 1 ? "visible" : "hidden"}`}
                        >
                          <CirclePlus className="w-4 h-4 cursor-pointer text-gray-600 hover:text-primary" />
                        </div>
                        <div
                          role="button"
                          aria-label="删除"
                          onClick={() => removeMatch(matchIndex)}
                          className="p-1 rounded-sm hover:bg-gray-100 transition-colors duration-200 flex items-center justify-center"
                        >
                          <CircleMinus className="w-4 h-4 cursor-pointer text-gray-600 hover:text-red-500" />
                        </div>
                      </div>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        ))}
        <div className="flex justify-end">
          <Button
            type="button"
            variant="ghost"
            size="icon"
            onClick={() => remove(index)}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default AIServiceLogItem;
