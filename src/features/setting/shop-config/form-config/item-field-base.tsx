import { <PERSON><PERSON> } from "@/components/ui/button";
import { Control, UseFieldArrayReturn } from "react-hook-form";
import { Trash2, GripVertical, PlusCircle } from "lucide-react";
import { FormValues } from "./form-schema";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Tooltip } from "@/components/common/tooltip";
import { Input } from "@/components/ui/input";
import { DraggableContainer } from "./draggable-container";

interface ItemsFieldProps {
  control: Control<FormValues>;
  fieldName: string;
  fieldArray: {
    fields: UseFieldArrayReturn<FormValues, any, "id">["fields"];
    append: UseFieldArrayReturn<FormValues, any, "id">["append"];
    remove: UseFieldArrayReturn<FormValues, any, "id">["remove"];
    move: UseFieldArrayReturn<FormValues, any, "id">["move"];
  };
  t: (key: string) => string;
  label?: string;
  required?: boolean;
}

// 单个输入项组件
interface ItemProps {
  control: Control<FormValues>;
  fieldName: string;
  index: number;
  remove: (index: number) => void;
  t: (key: string) => string;
  label?: string;
  fieldsLength: number;
  dragHandleProps: any;
}

const Item = ({
  control,
  fieldName,
  index,
  remove,
  t,
  label,
  fieldsLength,
  dragHandleProps,
}: ItemProps) => {
  return (
    <div className="flex items-center gap-4 rounded-md">
      <div className="flex-1">
        <FormField
          control={control}
          name={`${fieldName}.${index}.value` as any}
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <div className="flex">
                  <Tooltip content="拖拽">
                    <div
                      {...dragHandleProps}
                      role="button"
                      aria-label="拖拽"
                      className={`p-1 rounded-sm hover:bg-gray-100 transition-colors duration-200 flex items-center justify-center mr-1 ${dragHandleProps.className}`}
                    >
                      <GripVertical className="w-4 h-4" />
                    </div>
                  </Tooltip>
                  <Input
                    boxClassName="flex-1 mr-1"
                    placeholder={`请输入${label}`}
                    {...field}
                  />
                  <div
                    role="button"
                    aria-label={t("common.delete")}
                    onClick={() => remove(index)}
                    className={`p-1 rounded-sm hover:bg-gray-100 transition-colors duration-200 flex items-center justify-center ${
                      fieldsLength > 1 ? "visible" : "hidden"
                    }`}
                  >
                    <Trash2 className="w-4 h-4 cursor-pointer text-gray-600 hover:text-red-500" />
                  </div>
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  );
};

export const ItemsField = ({
  control,
  fieldName,
  fieldArray,
  t,
  label,
  required,
}: ItemsFieldProps) => {
  const { fields, append, remove, move } = fieldArray;

  // 使用 useDraggableArray 简化拖拽逻辑
  const { handleDragEnd } = {
    handleDragEnd: (activeIndex: number, overIndex: number) => {
      move(activeIndex, overIndex);
    },
  };

  return (
    <>
      <FormLabel
        className={required ? "before:content-['*'] before:text-red-500" : ""}
      >
        {label}
      </FormLabel>
      <div className="max-h-[440px] overflow-y-auto overflow-x-hidden py-2">
        {fields.length > 0 && (
          <DraggableContainer
            items={fields}
            onDragEnd={handleDragEnd}
            className="space-y-4"
          >
            {(item, index, dragHandleProps) => (
              <Item
                key={item.id}
                control={control}
                fieldName={fieldName}
                index={index}
                remove={remove}
                t={t}
                label={label}
                fieldsLength={fields.length}
                dragHandleProps={dragHandleProps}
              />
            )}
          </DraggableContainer>
        )}
      </div>
      <div className="flex gap-4">
        <Button
          type="button"
          variant="outline"
          size="sm"
          className="mt-2 w-full"
          onClick={() => append({ value: "" })}
        >
          <PlusCircle className="mr-2 h-4 w-4" />
          添加{label}
        </Button>
      </div>
    </>
  );
};
