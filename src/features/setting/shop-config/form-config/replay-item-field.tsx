import { But<PERSON> } from "@/components/ui/button";
import {
  Control,
  UseFieldArrayReturn,
  useFieldArray,
  useFormContext,
  useWatch,
} from "react-hook-form";
import { Trash2, PlusCircle } from "lucide-react";
import { FormValues } from "./form-schema";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import {
  FormControl,
  FormField,
  FormItem,
  FormMessage,
  FormLabel,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";

interface ReplyItemsFieldProps {
  control: Control<FormValues>;
  fieldName: string;
  fieldArray: {
    fields: UseFieldArrayReturn<FormValues, any, "id">["fields"];
    append: UseFieldArrayReturn<FormValues, any, "id">["append"];
    remove: UseFieldArrayReturn<FormValues, any, "id">["remove"];
  };
  t: (key: string) => string;
  required?: boolean;
}

// 子组件处理每个字段项
const ReplyItem = ({
  control,
  fieldName,
  index,
  remove,
  t,
}: {
  control: Control<FormValues>;
  fieldName: string;
  index: number;
  remove: (index: number) => void;
  t: (key: string) => string;
}) => {
  const { getValues } = useFormContext<FormValues>();
  const {
    fields,
    append,
    remove: removePrompt,
    replace,
  } = useFieldArray({
    control,
    name: `${fieldName}.${index}.prompt` as any,
  });

  const method = useWatch({
    control,
    name: `${fieldName}.${index}.method` as any,
    defaultValue: getValues(`${fieldName}.${index}.method` as any),
  });

  return (
    <div className="flex items-center border gap-4 rounded-md">
      <div className="flex-1 pb-6">
        <div className="px-6 py-2 border-b">
          <div className="flex justify-between ">
            <FormField
              control={control}
              name={`${fieldName}.${index}.preset_name` as any}
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input
                      boxClassName="w-[500px]"
                      placeholder="请输入回复名称"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div
              role="button"
              aria-label={t("common.delete")}
              onClick={() => remove(index)} // 直接调用 remove
              className={`p-1 rounded-sm hover:bg-gray-100 transition-colors duration-200 flex items-center justify-center`}
            >
              <Trash2 className="w-4 h-4 cursor-pointer text-gray-600 hover:text-red-500" />
            </div>
          </div>
        </div>

        <FormField
          control={control}
          name={`${fieldName}.${index}.method` as any}
          render={({ field }) => (
            <FormItem className="px-6 mt-4">
              <FormLabel>回复方式</FormLabel>
              <Select
                onValueChange={(value) => {
                  field.onChange(value);
                  if (value === "llm") {
                    replace({ value: "" }); // 切换到大模型时，清空prompt
                  } else if (value === "text") {
                    replace([{ value: "" }]); // 切换到原话回复时，添加一个空的prompt
                  }
                }}
                defaultValue={field.value}
              >
                <FormControl>
                  <SelectTrigger className="w-[200px]">
                    <SelectValue placeholder="请选择平台" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="text">原话回复</SelectItem>
                  <SelectItem value="llm">大模型生成</SelectItem>
                </SelectContent>
              </Select>
            </FormItem>
          )}
        />

        {method === "llm" ? (
          <FormField
            control={control}
            name={`${fieldName}.${index}.prompt.0.value` as any} // 直接绑定到 prompt[0].value 此处有bug
            render={({ field }) => (
              <FormItem className="px-6">
                <FormLabel>回复内容</FormLabel>
                <FormControl>
                  <Textarea
                    className="h-[224px]"
                    placeholder="请输入回复内容"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        ) : (
          <div className="px-6 ">
            <FormLabel>回复内容</FormLabel>
            {fields.length > 0 && (
              <div className="space-y-2 mb-2">
                {fields.map((promptField, promptIndex) => (
                  <div key={promptField.id} className="flex items-center gap-2">
                    <FormField
                      control={control}
                      name={
                        `${fieldName}.${index}.prompt.${promptIndex}.value` as any
                      }
                      render={({ field }) => (
                        <FormItem className="flex-1">
                          <FormControl>
                            <div className="flex justify-between items-center">
                              <Textarea
                                className="flex-1 mr-1"
                                placeholder="请输入回复内容"
                                {...field}
                              />
                              <div className="flex ml-1 w-[50px]">
                                <div
                                  role="button"
                                  aria-label={t("common.add")}
                                  onClick={() => append({ value: "" })}
                                  className="p-1 rounded-sm hover:bg-gray-100 transition-colors duration-200 flex items-center justify-center mr-1"
                                >
                                  <PlusCircle className="w-4 h-4 cursor-pointer text-gray-600 hover:text-green-500" />
                                </div>
                                <div
                                  role="button"
                                  aria-label={t("common.delete")}
                                  onClick={() => removePrompt(promptIndex)}
                                  className={`p-1 rounded-sm hover:bg-gray-100 transition-colors duration-200 flex items-center justify-center ${fields.length <= 1 ? "hidden" : ""}`}
                                >
                                  <Trash2 className="w-4 h-4 cursor-pointer text-gray-600 hover:text-red-500" />
                                </div>
                              </div>
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

// 主组件
export const ReplyItemsField = ({
  control,
  fieldName,
  fieldArray,
  t,
}: ReplyItemsFieldProps) => {
  const { fields, append, remove } = fieldArray;

  return (
    <div>
      <div className="space-y-4">
        {fields.map((field, index) => (
          <ReplyItem
            key={field.id}
            control={control}
            fieldName={fieldName}
            index={index}
            remove={remove}
            t={t}
          />
        ))}
      </div>
      <Button
        type="button"
        variant="outline"
        size="sm"
        className="mt-4 w-full"
        onClick={() =>
          append({
            preset_name: "",
            method: "text",
            prompt: [{ value: "" }],
          })
        }
      >
        <PlusCircle className="w-4 h-4 mr-2" />
        {t("common.add")}
      </Button>
    </div>
  );
};
