import { Form } from "@/components/ui/form";
import { useFormContext } from "../hooks/form-context";
import {
  BaseInfoForm,
  ProductInfoForm,
  Reply,
  SensitiveWords,
  UrgeBuyForm,
  UrgePayForm,
  PresetTagConfigForm,
  UserMessageLogForm,
  AIServiceLogForm,
  ShopVariable,
} from "./config";

// 权限控制组件
const AdminOnly = ({ children }: { children: React.ReactNode }) => {
  const isSuperAdmin = true; // 目前默认所有;
  return isSuperAdmin ? <>{children}</> : null;
};

export const ShopConfigForm = () => {
  const { form } = useFormContext();

  // 定义表单组件配置
  const formSections = [
    // 超级管理员可见的组件
    { Component: BaseInfoForm, requireAdmin: true },
    { Component: ProductInfoForm, requireAdmin: true },
    // 所有用户可见的组件
    { Component: SensitiveWords, requireAdmin: false },
    { Component: UrgeBuyForm, requireAdmin: false },
    { Component: UrgePayForm, requireAdmin: false },
    // 超级管理员可见的组件
    { Component: PresetTagConfigForm, requireAdmin: true },
    { Component: UserMessageLogForm, requireAdmin: true },
    { Component: AIServiceLogForm, requireAdmin: true },
    { Component: Reply, requireAdmin: true },
    { Component: ShopVariable, requireAdmin: true },
  ];

  return (
    <Form {...form}>
      <form className="space-y-8 flex-1 px-1">
        {formSections.map(({ Component, requireAdmin }, index) => {
          return requireAdmin ? (
            <AdminOnly key={index}>
              <Component />
            </AdminOnly>
          ) : (
            <Component key={index} />
          );
        })}
      </form>
    </Form>
  );
};
