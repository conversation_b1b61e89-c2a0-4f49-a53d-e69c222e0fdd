import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useFieldArray } from "react-hook-form";
import { useFormContext } from "../hooks/form-context";
import { GripVertical, PlusCircle, Trash2 } from "lucide-react";
import { Tooltip } from "@/components/common/tooltip";
import { Textarea } from "@/components/ui/textarea";

// 定义组件类型
type UrgeType = "buy" | "pay";

// 定义组件属性
interface UrgeCommonFormProps {
  type: UrgeType;
  title: string;
  switchLabel: string;
  intervalLabel: string;
  messageLabel: string;
  messagePlaceholder: string;
}

export const UrgeCommonForm = ({
  type,
  title,
  switchLabel,
  intervalLabel,
  messageLabel,
  messagePlaceholder,
}: UrgeCommonFormProps) => {
  // 使用表单上下文
  const { form } = useFormContext();

  // 根据类型确定字段名
  const switchFieldName = `shop_configs.backend_config.urge${type === "buy" ? "Buy" : "Pay"}Switch`;
  const intervalsFieldName = `shop_configs.backend_config.urge${type === "buy" ? "Buy" : "Pay"}Intervals`;
  const statementsFieldName = `shop_configs.backend_config.urge${type === "buy" ? "Buy" : "Pay"}Statements`;

  // 使用 useFieldArray 管理时间间隔数组
  const {
    fields: urgeIntervals,
    append: appendUrgeInterval,
    remove: removeUrgeInterval,
  } = useFieldArray({
    control: form.control,
    name: intervalsFieldName as any,
  });

  // 使用 useFieldArray 管理话术数组
  const {
    fields: urgeMessages,
    append: appendUrgeMessage,
    remove: removeUrgeMessage,
  } = useFieldArray({
    control: form.control,
    name: statementsFieldName as any,
  });

  // 时间单位选项
  const timeUnitOptions = [
    { value: "MINUTES", label: "分钟" },
    { value: "HOURS", label: "小时" },
  ];

  return (
    <div className="space-y-2" data-form-section={`urgent_${type}`}>
      <h2 className="text-lg font-medium">{title}</h2>
      {/* 是否开启开关 */}
      <FormField
        control={form.control}
        name={switchFieldName as any}
        render={({ field }) => (
          <FormItem>
            <div className="space-y-0.5">
              <FormLabel className="text-sm">{switchLabel}</FormLabel>
            </div>
            <FormControl>
              <Switch
                checked={field.value || false}
                onCheckedChange={field.onChange}
              />
            </FormControl>
          </FormItem>
        )}
      />

      {/* 间隔时间 */}
      <div className="space-y-2">
        <FormLabel>{intervalLabel}</FormLabel>
        {/* 添加数组级别的错误显示 */}
        <div className="space-y-4">
          {urgeIntervals.map((field, index) => (
            <div key={field.id} className="flex items-center gap-4">
              <div className="flex-1 flex items-center gap-2">
                <Tooltip content="拖拽">
                  <div
                    role="button"
                    aria-label="拖拽"
                    className="p-1 rounded-sm hover:bg-gray-100 transition-colors duration-200 flex items-center justify-center mr-1"
                  >
                    <GripVertical className="w-4 h-4 cursor-pointer" />
                  </div>
                </Tooltip>
                <FormField
                  control={form.control}
                  name={`${intervalsFieldName}.${index}.time` as any}
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <Input
                          type="number"
                          className="w-[224px]"
                          placeholder="请输入时间"
                          {...field}
                          onChange={(e) => {
                            const val = e.target.value;
                            const num = val === "" ? null : Number(val);
                            if (num === null || !isNaN(num)) {
                              field.onChange(num);
                            }
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name={`${intervalsFieldName}.${index}.unit` as any}
                  render={({ field }) => (
                    <Select value={field.value} onValueChange={field.onChange}>
                      <SelectTrigger className="w-[120px]">
                        <SelectValue placeholder="选择单位" {...field} />
                      </SelectTrigger>
                      <SelectContent>
                        {timeUnitOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  onClick={() => removeUrgeInterval(index)}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          ))}
        </div>
        <Button
          type="button"
          variant="outline"
          size="sm"
          className="mt-2 w-full"
          onClick={() => appendUrgeInterval({ time: 5, unit: "MINUTES" })}
        >
          <PlusCircle className="mr-2 h-4 w-4" />
          添加条目
        </Button>
      </div>

      {/* 话术 */}
      <div className="space-y-4">
        <FormLabel>{messageLabel}</FormLabel>
        <div className="space-y-4">
          {urgeMessages.map((field, index) => (
            <div key={field.id} className="flex items-start gap-4">
              <div className="flex-1">
                <div className="flex items-center gap-2">
                  <Tooltip content="拖拽">
                    <div
                      role="button"
                      aria-label="拖拽"
                      className="p-1 rounded-sm hover:bg-gray-100 transition-colors duration-200 flex items-center justify-center mr-1"
                    >
                      <GripVertical className="w-4 h-4 cursor-pointer" />
                    </div>
                  </Tooltip>
                  <FormField
                    control={form.control}
                    name={`${statementsFieldName}.${index}.value` as any}
                    render={({ field }) => (
                      <FormItem className="flex-1">
                        <FormControl>
                          <Textarea
                            className="w-full"
                            placeholder={messagePlaceholder}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="mt-2"
                    onClick={() => removeUrgeMessage(index)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>
        <Button
          type="button"
          variant="outline"
          size="sm"
          className="mt-2 w-full"
          onClick={() => appendUrgeMessage({ value: "" })}
        >
          <PlusCircle className="mr-2 h-4 w-4" />
          添加条目
        </Button>
      </div>
    </div>
  );
};

// 导出催拍组件
export const UrgeBuyForm = () => (
  <UrgeCommonForm
    type="buy"
    title="催拍配置"
    switchLabel="是否开启催拍"
    intervalLabel="催拍间隔时间"
    messageLabel="催拍话术"
    messagePlaceholder="请输入催拍话术"
  />
);

// 导出催付组件
export const UrgePayForm = () => (
  <UrgeCommonForm
    type="pay"
    title="催付配置"
    switchLabel="是否开启催付"
    intervalLabel="催付间隔时间"
    messageLabel="催付话术"
    messagePlaceholder="请输入催付话术"
  />
);
