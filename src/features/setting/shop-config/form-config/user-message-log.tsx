import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useFieldArray } from "react-hook-form";
import { useFormContext } from "../hooks/form-context";
import { GripVertical, PlusCircle, Trash2 } from "lucide-react";
import { Tooltip } from "@/components/common/tooltip";

// 隐藏消息的日志信息配置表单
export const UserMessageLogForm = () => {
  // 使用表单上下文
  const { form } = useFormContext();

  // 使用 useFieldArray 管理隐藏消息日志配置数组
  const {
    fields: logItems,
    append: appendLogItem,
    remove: removeLogItem,
  } = useFieldArray({
    control: form.control,
    name: "preset_configs.preset_label_gen.user_msg_meta_matches",
  });

  return (
    <div className="space-y-2">
      <div className="space-y-2">
        <FormLabel>顾客消息的日志信息匹配配置</FormLabel>
        <div className="space-y-4">
          <div className="grid grid-cols-5 gap-4 bg-gray-50 h-[48px] items-center px-8">
            <div className="font-medium text-sm">模式</div>
            <div className="font-medium text-sm">标签</div>
            <div className="font-medium text-sm">字段</div>
            <div className="font-medium text-sm">匹配</div>
            <div className="font-medium text-sm">值</div>
          </div>
          {logItems.map((field, index) => (
            <div key={field.id} className="grid grid-cols-5 gap-4 items-start">
              <div className="flex items-center gap-2">
                <Tooltip content="拖拽">
                  <div
                    role="button"
                    aria-label="拖拽"
                    className="p-1 rounded-sm hover:bg-gray-100 transition-colors duration-200 flex items-center justify-center mr-1"
                  >
                    <GripVertical className="w-4 h-4 cursor-pointer" />
                  </div>
                </Tooltip>
                <FormField
                  control={form.control}
                  name={`preset_configs.preset_label_gen.user_msg_meta_matches.${index}.mode`}
                  render={({ field }) => (
                    <FormItem className="flex-1">
                      <FormControl>
                        <Input placeholder="请输入模式" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name={`preset_configs.preset_label_gen.user_msg_meta_matches.${index}.label`}
                render={({ field }) => (
                  <FormItem className="flex-1">
                    <FormControl>
                      <Input placeholder="请输入标签" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name={`preset_configs.preset_label_gen.user_msg_meta_matches.${index}.key_match`}
                render={({ field }) => (
                  <FormItem className="flex-1">
                    <FormControl>
                      <Input placeholder="请输入键" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name={`preset_configs.preset_label_gen.user_msg_meta_matches.${index}.query_match`}
                render={({ field }) => (
                  <FormItem className="flex-1">
                    <FormControl>
                      <Input placeholder="请输入匹配" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex items-center gap-2">
                <FormField
                  control={form.control}
                  name={`preset_configs.preset_label_gen.user_msg_meta_matches.${index}.value_match`}
                  render={({ field }) => (
                    <FormItem className="flex-1">
                      <FormControl>
                        <Input placeholder="请输入值" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  onClick={() => removeLogItem(index)}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          ))}
        </div>

        <Button
          type="button"
          variant="outline"
          size="sm"
          className="mt-2 w-full"
          onClick={() =>
            appendLogItem({
              mode: "",
              label: "",
              key_match: "",
              query_match: "",
              value_match: "",
            })
          }
        >
          <PlusCircle className="mr-2 h-4 w-4" />
          添加条目
        </Button>
      </div>
    </div>
  );
};
