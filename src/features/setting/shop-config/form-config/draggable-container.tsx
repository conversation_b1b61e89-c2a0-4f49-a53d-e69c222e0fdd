import React, { useCallback } from "react";
import {
  DndContext,
  closestCenter,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from "@dnd-kit/core";
import {
  SortableContext,
  verticalListSortingStrategy,
  useSortable,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";

// 基础拖拽项接口
export interface DraggableItem {
  id: string;
  [key: string]: any;
}

// 拖拽容器属性接口
export interface DraggableContainerProps<T extends DraggableItem> {
  items: T[];
  onDragEnd: (activeIndex: number, overIndex: number) => void;
  children: (item: T, index: number, dragHandleProps: any) => React.ReactNode;
  className?: string;
}

// 可排序项组件
function SortableItem<T extends DraggableItem>({
  id,
  children,
}: {
  id: string;
  index: number;
  item: T;
  children: (dragHandleProps: any) => React.ReactNode;
}) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  const dragHandleProps = {
    ...attributes,
    ...listeners,
    className: "cursor-grab active:cursor-grabbing",
  };

  return (
    <div ref={setNodeRef} style={style}>
      {children(dragHandleProps)}
    </div>
  );
}

// 主拖拽容器组件
export function DraggableContainer<T extends DraggableItem>({
  items,
  onDragEnd,
  children,
  className = "",
}: DraggableContainerProps<T>) {
  const sensors = useSensors(useSensor(PointerSensor));

  const handleDragEnd = useCallback(
    (event: DragEndEvent) => {
      const { active, over } = event;

      if (over && active.id !== over.id) {
        const activeIndex = items.findIndex((item) => item.id === active.id);
        const overIndex = items.findIndex((item) => item.id === over.id);

        if (activeIndex !== -1 && overIndex !== -1) {
          onDragEnd(activeIndex, overIndex);
        }
      }
    },
    [items, onDragEnd],
  );

  return (
    <div className={className}>
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragEnd={handleDragEnd}
      >
        <SortableContext
          items={items.map((item) => item.id)}
          strategy={verticalListSortingStrategy}
        >
          <div className="space-y-2">
            {items.map((item, index) => (
              <SortableItem
                key={item.id}
                id={item.id}
                index={index}
                item={item}
              >
                {(dragHandleProps) => children(item, index, dragHandleProps)}
              </SortableItem>
            ))}
          </div>
        </SortableContext>
      </DndContext>
    </div>
  );
}

// 简化的 Hook
export function useDraggableArray<T extends DraggableItem>(
  items: T[],
  setItems: (items: T[]) => void,
) {
  const handleDragEnd = useCallback(
    (activeIndex: number, overIndex: number) => {
      const newItems = [...items];
      const [removed] = newItems.splice(activeIndex, 1);
      newItems.splice(overIndex, 0, removed);
      setItems(newItems);
    },
    [items, setItems],
  );

  return { handleDragEnd };
}
