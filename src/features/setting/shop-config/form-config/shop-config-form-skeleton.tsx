import { Skeleton } from "@/components/ui/skeleton";

export const ShopConfigFormSkeleton = () => {
  // 创建表单部分的骨架屏
  const FormSectionSkeleton = ({ itemCount = 3 }: { itemCount?: number }) => (
    <div className="space-y-4">
      {/* 标题骨架屏 */}
      <Skeleton className="h-6 w-40" />

      {/* 表单项骨架屏 */}
      {Array.from({ length: itemCount }).map((_, index) => (
        <div key={index} className="space-y-2">
          <Skeleton className="h-5 w-32" />
          <Skeleton className="h-10 w-full" />
        </div>
      ))}
    </div>
  );

  return (
    <div className="space-y-8 flex-1 px-1 animate-pulse">
      {/* 基础配置 */}
      <FormSectionSkeleton itemCount={4} />

      {/* 商品配置 */}
      <FormSectionSkeleton itemCount={3} />

      {/* 敏感词配置 */}
      <FormSectionSkeleton itemCount={2} />

      {/* 催拍配置 */}
      <FormSectionSkeleton />

      {/* 催付配置 */}
      <FormSectionSkeleton />

      {/* 预设标签配置 */}
      <div className="space-y-4">
        <Skeleton className="h-6 w-40" />
        <div className="grid grid-cols-3 gap-4">
          {Array.from({ length: 6 }).map((_, index) => (
            <Skeleton key={index} className="h-10 w-full" />
          ))}
        </div>
      </div>

      {/* 日志信息配置 */}
      <FormSectionSkeleton itemCount={4} />

      {/* AI客服消息配置 */}
      <FormSectionSkeleton itemCount={4} />

      {/* 回复提示词配置 */}
      <div className="space-y-4">
        <Skeleton className="h-6 w-40" />
        <Skeleton className="h-40 w-full" />
      </div>

      {/* 店铺变量配置 */}
      <div className="space-y-4">
        <Skeleton className="h-6 w-40" />
        <div className="space-y-2">
          <Skeleton className="h-10 w-full" />
          <Skeleton className="h-40 w-full" />
        </div>
      </div>
    </div>
  );
};
