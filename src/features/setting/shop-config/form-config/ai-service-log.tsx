import { FormLabel } from "@/components/ui/form";
import { But<PERSON> } from "@/components/ui/button";
import { useFieldArray } from "react-hook-form";
import { useFormContext } from "../hooks/form-context";
import { PlusCircle } from "lucide-react";
import AIServiceLogItem from "./ai-service-log-item";

// AI客服消息的日志信息配置表单
export const AIServiceLogForm = () => {
  // 使用表单上下文
  const { form } = useFormContext();

  // 使用 useFieldArray 管理AI客服消息日志配置数组
  const {
    fields: logItems,
    append: appendLogItem,
    remove: removeLogItem,
  } = useFieldArray({
    control: form.control,
    name: "preset_configs.preset_label_gen.meta_matches",
  });

  return (
    <div className="space-y-2" data-form-section="ai_service_log">
      <div className="space-y-2">
        <FormLabel>AI客服消息的日志信息配置</FormLabel>
        <div className="space-y-4">
          <div className="grid grid-cols-3 gap-4 bg-gray-50 h-[48px] items-center px-8">
            <div className="font-medium text-sm">meta前缀</div>
            <div className="font-medium text-sm">标签</div>
            <div className="font-medium text-sm">meta配置匹配</div>
          </div>

          {logItems.map((field, index) => (
            <AIServiceLogItem
              key={field.id}
              field={field}
              index={index}
              control={form.control}
              remove={removeLogItem}
            />
          ))}
        </div>
        <Button
          type="button"
          variant="outline"
          size="sm"
          className="mt-2 w-full"
          onClick={() =>
            appendLogItem({
              tool: "",
              label: "",
              match: [{ value: "" }],
            })
          }
        >
          <PlusCircle className="mr-2 h-4 w-4" />
          添加条目
        </Button>
      </div>
    </div>
  );
};
