import { shop<PERSON>tom } from "@/atoms/shop";
import { ScrollArea } from "@/components/ui/scroll-area";
import { CategoryService } from "@/services/category-service";
import { ShopConfigService } from "@/services/config-shop";
import { zodResolver } from "@hookform/resolvers/zod";
import { useQuery } from "@tanstack/react-query";
import { useAtomValue } from "jotai";
import { useForm } from "react-hook-form";
import FormMenu from "./form-menu";
import { ShopConfigForm } from "./form-config";
import { useTranslation } from "./form-config/config";
import { FormValues, shopConfigFormSchema } from "./form-config/form-schema";
import { ShopConfigFormSkeleton } from "./form-config/shop-config-form-skeleton";
import { FormProvider } from "./hooks/form-context";
import { useShopConfigReset } from "./hooks/use-shop-config-reset";
const ShopConfig = () => {
  const form = useForm<FormValues>({
    resolver: zodResolver(shopConfigFormSchema),
    shouldFocusError: false,
    defaultValues: {
      shop_configs: {
        top_10_items: [],
        products: [],
        shop_name: "",
        default_shop_id: "",
        shop_desc: "",
        default_category: "",
        transfer_category: [],
        product_attr_diff: [],
        reason_and_answer_prompt: "",
        sensitive_words: [],
        backend_config: {
          urgeBuySwitch: false,
          urgeBuyIntervals: [],
          urgeBuyStatements: [],
          urgePaySwitch: false,
          urgePayIntervals: [],
          urgePayStatements: [],
        },
      },
    },
  });
  const { t } = useTranslation();
  const shop = useAtomValue(shopAtom);
  const { data: variableList = [] } = useQuery({
    queryKey: [
      "selectShopVariableList",
      shop?.id,
      form.watch("prompt_config.match_category_list"),
    ],
    enabled: !!shop?.id,
    queryFn: () =>
      ShopConfigService.selectShopVariableList({
        shopId: shop?.id || "",
        categoryNames: form.watch("prompt_config.match_category_list"),
      }),
  });
  const {
    data: shopDataConfig,
    isLoading: isShopConfigLoading,
    refetch,
  } = useQuery({
    queryKey: ["getShopConfig", shop?.id],
    enabled: !!shop?.id,
    queryFn: () => ShopConfigService.getShopConfig(shop?.id || ""),
  });
  const { data: categoryList = [], isLoading: isCategoryLoading } = useQuery({
    queryKey: ["getCategoryList"],
    queryFn: () => CategoryService.selectCategoryList(),
  });

  //表单重置
  useShopConfigReset({ form, shopDataConfig, categoryList });

  // 判断是否正在加载数据
  const isLoading = isShopConfigLoading || isCategoryLoading;

  return (
    <FormProvider
      form={form}
      t={t}
      refetch={refetch}
      categoryList={categoryList}
      variableList={variableList}
    >
      <ScrollArea
        horizontal={true}
        className="h-[calc(100vh-64px)] py-6 pl-6 flex pr-[156px] relative"
      >
        {isLoading ? <ShopConfigFormSkeleton /> : <ShopConfigForm />}
        <FormMenu
          configId={shopDataConfig?.configId || ""}
          shopId={shop?.id || ""}
        />
      </ScrollArea>
    </FormProvider>
  );
};

export default ShopConfig;
