import { Button } from "@/components/ui/button";
import { Code } from "lucide-react";
import { useMemo, useCallback, useState, Suspense } from "react";
import { createMenuItems, MenuItem } from "./menu-type";
import { useFormContext } from "../hooks/form-context";
import { ShopConfigService } from "@/services/config-shop";
import { Can } from "@/components/common/can";
import { useShopConfigRestore } from "../hooks/use-shop-config-restore";
import { toast } from "sonner";
import { ModalJsonEdit } from "@/components/common/modal-json-edit";
import { Level } from "@/types/api/prompt";
import FormMenuSkeleton from "./form-menu-skeleton";
import { useScrollMenuHighlight } from "../hooks/use-scroll-menu-highlight";
import { SaveAndPublishButton } from "@/components/common/shop-config-save-publish-button";
const FormMenu = ({
  configId,
  shopId,
}: {
  configId: string;
  shopId: string;
}) => {
  const { form, t, variableList, refetch } = useFormContext();
  const { getRestoredData } = useShopConfigRestore();
  const isSuperAdmin = true; // 默认为超管判断
  const { ADMIN_MENU_ITEMS, COMMON_MENU_ITEMS } = createMenuItems(t);
  const [jsonModalOpen, setJsonModalOpen] = useState(false);

  // 检查变量列表中是否包含特定级别的变量
  const hasLevelVariables = useCallback(
    (level: Level) => {
      return variableList.some(
        (item) => item.level === level && item.variableDTOS.length > 0,
      );
    },
    [variableList],
  );

  const menuList = useMemo(() => {
    // 将ADMIN_MENU_ITEMS分成两部分
    const baseInfoItems = ADMIN_MENU_ITEMS.slice(0, 2);
    const presetLabelItems = ADMIN_MENU_ITEMS.slice(2, 3);
    const variableMenuItem = ADMIN_MENU_ITEMS[3];

    // 根据变量列表筛选子菜单
    const filteredVariableChildren = variableMenuItem.children?.filter(
      (child) => {
        switch (child.key) {
          case "PRODUCT_VARIABLES":
            return hasLevelVariables(Level.Product);
          case "CATEGORY_VARIABLES":
            return hasLevelVariables(Level.Category);
          case "SHOP_VARIABLES":
            return hasLevelVariables(Level.Shop);
          default:
            return true;
        }
      },
    );

    // 创建新的变量菜单项，仅包含存在的级别
    const filteredVariableMenuItem =
      filteredVariableChildren && filteredVariableChildren.length > 0
        ? {
            ...variableMenuItem,
            children: filteredVariableChildren,
          }
        : null;

    // 组合菜单项
    const adminItems = [
      ...baseInfoItems,
      ...COMMON_MENU_ITEMS,
      ...presetLabelItems,
      ...(filteredVariableMenuItem
        ? [filteredVariableMenuItem]
        : [
            {
              title: t("menu.shop.variables"),
              key: "shop_variables",
              children: [],
            },
          ]),
    ];

    return isSuperAdmin ? adminItems : [...baseInfoItems, ...COMMON_MENU_ITEMS];
  }, [ADMIN_MENU_ITEMS, COMMON_MENU_ITEMS, t, isSuperAdmin, hasLevelVariables]);

  // 使用滚动菜单高亮hook
  const { activeMenuItem, handleMenuItemClick } = useScrollMenuHighlight({
    menuList,
    shopId,
  });

  // 渲染父级菜单项 - 提取样式常量
  const getMenuItemClassName = useCallback(
    (isActive: boolean, hasActiveChild: boolean) => {
      const baseClass =
        "flex items-center w-full text-sm p-2 font-medium rounded-sm cursor-pointer transition-colors";

      if (isActive) {
        return `${baseClass} text-primary bg-primary/10`;
      }
      if (hasActiveChild) {
        return `${baseClass} text-primary`;
      }
      return `${baseClass} hover:text-primary`;
    },
    [],
  );

  const getChildMenuItemClassName = useCallback((isActive: boolean) => {
    const baseClass = "text-sm p-1 cursor-pointer transition-colors";

    if (isActive) {
      return `${baseClass} text-primary bg-primary/10 rounded`;
    }
    return `${baseClass} hover:text-primary`;
  }, []);

  const renderParentMenuItem = useCallback(
    (item: MenuItem) => {
      const isActive = activeMenuItem === item.key;
      const hasActiveChild = item.children?.some(
        (child) => activeMenuItem === child.key,
      );

      return (
        <div key={item.key} className="w-full">
          <div
            className={getMenuItemClassName(isActive, hasActiveChild || false)}
            onClick={() => handleMenuItemClick(item.key)}
          >
            <div className="truncate max-w-[140px]">{item.title}</div>
          </div>
          {/* 子菜单 */}
          {item.children && item.children.length > 0 && (
            <div className="pl-1 border-l border-gray-100 ml-2 mt-1 mb-2 space-y-1">
              {item.children.map((child) => {
                const isChildActive = activeMenuItem === child.key;
                return (
                  <div
                    key={child.key}
                    className={getChildMenuItemClassName(isChildActive)}
                    onClick={() => handleMenuItemClick(child.key)}
                  >
                    <div className="truncate max-w-[120px]">{child.title}</div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      );
    },
    [
      handleMenuItemClick,
      activeMenuItem,
      getMenuItemClassName,
      getChildMenuItemClassName,
    ],
  );

  // 优化 JSON 编辑提交处理
  const handleJsonEditSubmit = useCallback(
    async (jsonData: any) => {
      try {
        const res = await ShopConfigService.saveShopConfig({
          configId,
          shopConfig: JSON.stringify(jsonData),
          shopId,
        });

        if (res.success) {
          toast.success("保存成功");
          refetch();
          setJsonModalOpen(false);
        } else {
          toast.error("保存失败");
        }
      } catch (error) {
        toast.error(
          "保存失败: " + (error instanceof Error ? error.message : "未知错误"),
        );
      }
    },
    [configId, refetch, shopId],
  );

  const jsonData = useMemo(() => {
    const formValues = form.getValues();
    return formValues ? JSON.stringify(getRestoredData(formValues)) : null;
  }, [form, getRestoredData]);

  return (
    <Suspense fallback={<FormMenuSkeleton />}>
      <div className="w-[156px] fixed right-0 top-18">
        <div className="flex items-center mb-4">
          <Can permissionCode="shop:config:code:edit">
            <Button
              icon={<Code />}
              variant="outline"
              className="mr-2"
              aria-label="编辑JSON"
              onClick={() => setJsonModalOpen(true)}
            />
          </Can>
          <Can permissionCode="shop:config:edit">
            <SaveAndPublishButton
              configId={configId}
              shopId={shopId}
              form={form}
              getRestoredData={getRestoredData}
              onFinish={() => {
                // 发布完成后的回调
                refetch();
              }}
              refetch={refetch}
            />
          </Can>
        </div>
        <div className="space-y-1 flex flex-col">
          {menuList.map(renderParentMenuItem)}
        </div>

        {/* JSON编辑模态框 */}
        <ModalJsonEdit
          type="edit"
          open={jsonModalOpen}
          onCancel={() => setJsonModalOpen(false)}
          onSubmit={handleJsonEditSubmit}
          data={jsonData}
          title="编辑店铺配置JSON"
        />
      </div>
    </Suspense>
  );
};

export default FormMenu;
