// 将菜单项定义为常量，避免在组件渲染时重复创建
export interface MenuItem {
  title: string;
  key: string;
  children?: MenuItem[];
}

// 创建菜单项工厂函数，使用i18n

export const createMenuItems = (t: (key: string) => string) => {
  const ADMIN_MENU_ITEMS: MenuItem[] = [
    {
      title: t("menu.base.info"),
      key: "base_info",
    },
    {
      title: t("menu.product.info"),
      key: "product_info",
    },
    {
      title: t("menu.preset.label.config"),
      key: "preset_configs",
      children: [
        {
          title: t("menu.preset.label.matches"),
          key: "preset_label_matches",
        },
        {
          title: t("menu.preset.label.resp"),
          key: "preset_label_resp",
        },
      ],
    },
    {
      title: t("menu.shop.variables"),
      key: "shop_variables",
      children: [
        {
          title: t("menu.product.variables"),
          key: "PRODUCT_VARIABLES",
        },
        {
          title: t("menu.category.variables"),
          key: "CATEGORY_VARIABLES",
        },
        {
          title: t("menu.shop.level.variables"),
          key: "SHOP_VARIABLES",
        },
      ],
    },
  ];

  const COMMON_MENU_ITEMS: MenuItem[] = [
    {
      title: t("menu.sensitive.words"),
      key: "sensitive_words",
    },
    {
      title: t("menu.urgent.buy"),
      key: "urgent_buy",
    },
    {
      title: t("menu.urgent.pay"),
      key: "urgent_pay",
    },
  ];

  return { ADMIN_MENU_ITEMS, COMMON_MENU_ITEMS };
};
