import { Skeleton } from "@/components/ui/skeleton";

const FormMenuSkeleton = () => {
  return (
    <div className="w-[156px] fixed right-0 top-18">
      {/* 按钮区域骨架屏 */}
      <div className="flex items-center mb-4">
        <Skeleton className="h-9 w-9 mr-2" />
        <Skeleton className="h-9 w-20" />
      </div>

      {/* 菜单项骨架屏 */}
      <div className="space-y-1 flex flex-col">
        {/* 生成5个父级菜单项 */}
        {Array.from({ length: 5 }).map((_, index) => (
          <div key={index} className="w-full">
            {/* 父级菜单项 */}
            <div className="flex items-center w-full p-2 rounded-sm">
              <Skeleton className="h-5 w-[140px]" />
            </div>

            {/* 子菜单项 - 只为部分父菜单添加子菜单 */}
            {(index === 0 || index === 2) && (
              <div className="pl-1 border-l border-gray-100 ml-2 mt-1 mb-2 space-y-1">
                {Array.from({ length: index === 0 ? 3 : 2 }).map(
                  (_, childIndex) => (
                    <div key={childIndex} className="p-1">
                      <Skeleton className="h-4 w-[120px]" />
                    </div>
                  ),
                )}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default FormMenuSkeleton;
