import { UseFieldArrayReturn } from "react-hook-form";
import { toast } from "sonner";

type CsvExportOptions = {
  headerRow?: string;
  filename?: string;
};

type CsvImportOptions<T> = {
  onSuccess?: (items: string[], count: number) => void;
  onError?: (error: Error) => void;
  onEmpty?: () => void;
  transform?: (value: string) => T;
};

type UseCsvImportExportReturn<T> = {
  exportToCSV: (
    data: (string | T)[] | string,
    options?: CsvExportOptions,
  ) => void;
  parseCSV: (content: string) => string[];
  handleFileImport: (
    event: React.ChangeEvent<HTMLInputElement>,
    fieldArray: UseFieldArrayReturn<any, any, "value">,
  ) => void;
  createExportTemplate: (
    templateData: string,
    options?: CsvExportOptions,
  ) => void;
  createExportData: <K extends { value: string }>(
    data: K[] | undefined,
    options?: CsvExportOptions,
  ) => void;
};

/**
 * Hook for CSV import and export functionality
 * @returns Functions for CSV operations
 */
export const useCsvImportExport = <
  T extends { value: string },
>(): UseCsvImportExportReturn<T> => {
  /**
   * Export data to CSV file
   * @param data Array of data or string to export
   * @param options Export options
   */
  const exportToCSV = (
    data: (string | T)[] | string,
    options?: CsvExportOptions,
  ) => {
    const { headerRow = "", filename = "export.csv" } = options || {};
    let csvContent = headerRow ? headerRow + "\n" : "";

    if (Array.isArray(data)) {
      csvContent += data
        .map((item) => {
          if (item && typeof item === "object" && "value" in item) {
            return item.value;
          }
          return item;
        })
        .join("\n");
    } else if (typeof data === "string") {
      csvContent += data;
    }

    // 添加 BOM 头以确保中文正确显示
    const blob = new Blob(["\uFEFF" + csvContent], {
      type: "text/csv;charset=utf-8",
    });

    const link = document.createElement("a");
    link.href = URL.createObjectURL(blob);
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  /**
   * Parse CSV content into array of strings
   * @param content CSV content as string
   * @returns Array of parsed strings
   */
  const parseCSV = (content: string): string[] => {
    // 移除BOM头
    const cleanContent = content.replace(/^\uFEFF/, "");
    // 按行分割
    const lines = cleanContent.split(/\r?\n/);
    // 过滤空行和表头行
    return lines
      .filter((line, index) => {
        // 跳过第一行（表头）和空行
        return index > 0 && line.trim() !== "";
      })
      .map((line) => line.trim());
  };

  /**
   * Handle file import event
   * @param event File input change event
   * @param fieldArray Field array from useFieldArray
   * @param options Import options
   */
  const handleFileImport = (
    event: React.ChangeEvent<HTMLInputElement>,
    fieldArray: UseFieldArrayReturn<any, any, "value">,
    options?: CsvImportOptions<T>,
  ) => {
    const { append, remove, fields } = fieldArray;
    const {
      onSuccess,
      onError,
      onEmpty,
      transform = (value: string) => ({ value }) as T,
    } = options || {};

    const file = event.target.files?.[0];
    if (!file) return;

    // 检查文件类型
    if (file.type !== "text/csv" && !file.name.endsWith(".csv")) {
      toast.error("文件格式错误", {
        description: "请上传CSV格式的文件",
      });
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const content = e.target?.result as string;
        const words = parseCSV(content);

        if (words.length === 0) {
          toast.warning("导入失败", {
            description: "未找到有效的数据",
          });
          onEmpty?.();
          return;
        }

        // 优化：安全的批量清除现有数据
        const currentLength = fields.length;
        for (let i = currentLength - 1; i >= 0; i--) {
          remove(i);
        }

        // 添加新数据
        words.forEach((word) => {
          append(transform(word));
        });

        toast.success("导入成功", {
          description: `已导入 ${words.length} 条数据`,
        });

        onSuccess?.(words, words.length);
      } catch (error) {
        console.error("导入CSV文件出错:", error);
        toast.error("导入失败", {
          description: "解析CSV文件时出错",
        });
        onError?.(error instanceof Error ? error : new Error(String(error)));
      }
    };

    reader.onerror = () => {
      toast.error("导入失败", {
        description: "读取文件时出错",
      });
      onError?.(new Error("File read error"));
    };

    reader.readAsText(file);
    // 重置文件输入，以便可以重新选择同一个文件
    event.target.value = "";
  };

  /**
   * Create and export a template CSV file
   * @param templateData Example data for the template
   * @param options Export options
   */
  const createExportTemplate = (
    templateData: string,
    options?: CsvExportOptions,
  ) => {
    const { headerRow = "数据", filename = "模板.csv" } = options || {};
    exportToCSV(templateData, { headerRow, filename });
  };

  /**
   * Export current data to CSV file
   * @param data Array of data objects with value property
   * @param options Export options
   */
  const createExportData = <K extends { value: string }>(
    data: K[] | undefined,
    options?: CsvExportOptions,
  ) => {
    const { headerRow = "数据", filename = "数据导出.csv" } = options || {};

    if (data && data.length > 0) {
      // 修复类型错误：将 K[] 类型转换为 exportToCSV 接受的类型
      exportToCSV(data as unknown as (string | T)[], { headerRow, filename });
    } else {
      toast.warning("没有数据可导出");
    }
  };

  return {
    exportToCSV,
    parseCSV,
    handleFileImport,
    createExportTemplate,
    createExportData,
  };
};
