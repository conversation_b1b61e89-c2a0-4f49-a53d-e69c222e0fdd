import { useEffect } from "react";
import { UseFormReturn } from "react-hook-form";
import { FormValues } from "../form-config/form-schema";
import { Category } from "@/types/api/shop";

// 从 ShopConfig/index.tsx 中提取的辅助函数
function convertPresetLabelResp(
  preset_label_resp: Record<string, { method?: string; prompt: any }>,
) {
  return Object.entries(preset_label_resp)?.map(([key, value]) => ({
    preset_name: key,
    method: value.method || "",
    prompt: Array.isArray(value.prompt)
      ? value.prompt.map((item) => ({ value: item }))
      : [{ value: value.prompt }],
  }));
}

interface UseShopConfigResetProps {
  form: UseFormReturn<FormValues>;
  shopDataConfig:
    | {
        shopConfig: string;
        configId?: string;
      }
    | null
    | undefined;
  categoryList: Category[];
}

export const useShopConfigReset = ({
  form,
  shopDataConfig,
  categoryList,
}: UseShopConfigResetProps) => {
  useEffect(() => {
    setTimeout(() => {
      if (shopDataConfig) {
        const {
          shop_configs = {},
          preset_configs,
          prompt_config,
        } = JSON.parse(shopDataConfig.shopConfig);
        console.log(JSON.parse(shopDataConfig.shopConfig));
        form.reset({
          shop_configs: {
            shop_name: shop_configs.shop_name,
            shop_desc: shop_configs.shop_desc,
            default_category: shop_configs.default_category,
            top_10_items: shop_configs.top_10_items?.map((item) => ({
              value: item,
            })),
            transfer_category: shop_configs.transfer_category?.map((item) => ({
              value: item,
            })),
            sensitive_words: shop_configs.sensitive_words?.map((item) => ({
              value: item,
            })),
            product_attr_diff: shop_configs.product_attr_diff?.map((item) => ({
              value: item,
            })),
            default_shop_id: shop_configs.default_shop_id,
            products: shop_configs.products?.split("、")?.map((item) => {
              return { value: item };
            }),
            backend_config: {
              urgeBuySwitch: shop_configs.backend_config?.urgeBuySwitch,
              urgeBuyIntervals: shop_configs.backend_config?.urgeBuyIntervals,
              urgeBuyStatements:
                shop_configs.backend_config?.urgeBuyStatements?.map((item) => ({
                  value: item,
                })),
              urgePaySwitch: shop_configs.backend_config?.urgePaySwitch,
              urgePayIntervals: shop_configs.backend_config?.urgePayIntervals,
              urgePayStatements:
                shop_configs.backend_config?.urgePayStatements?.map((item) => ({
                  value: item,
                })),
            },
          },
          preset_configs: {
            preset_label_gen: {
              query_reg_matches:
                preset_configs?.preset_label_gen?.query_reg_matches,
              user_msg_meta_matches:
                preset_configs?.preset_label_gen?.user_msg_meta_matches,
              meta_matches: preset_configs?.preset_label_gen.meta_matches?.map(
                (item) => {
                  return {
                    ...item,
                    match: item.match.map((v) => {
                      return {
                        value: v,
                      };
                    }),
                  };
                },
              ),
            },
            preset_label_resp: convertPresetLabelResp(
              preset_configs?.preset_label_resp || [],
            ),
          },
          prompt_config: {
            match_category_list: prompt_config?.match_category_list,
            shop_variable_list: prompt_config?.shop_variable_list,
          },
        });
      }
    });
  }, [shopDataConfig, form, categoryList]);
};
