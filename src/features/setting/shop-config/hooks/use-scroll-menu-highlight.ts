import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { MenuItem } from "../form-menu/menu-type";

export interface UseScrollMenuHighlightOptions {
  menuList: MenuItem[];
  scrollViewportSelector?: string;
  shopId?: string;
}

export const useScrollMenuHighlight = ({
  menuList,
  scrollViewportSelector = '[data-slot="scroll-area-viewport"]',
  shopId,
}: UseScrollMenuHighlightOptions) => {
  const [activeMenuItem, setActiveMenuItem] = useState<string>("");
  const scrollViewportRef = useRef<Element | null>(null);
  const isScrollListenerActiveRef = useRef(false);
  const prevShopIdRef = useRef<string | null>(null);

  // 监听 shopId 变化，滚动到顶部
  useEffect(() => {
    if (shopId && prevShopIdRef.current !== shopId) {
      prevShopIdRef.current = shopId;
      if (scrollViewportRef.current) {
        scrollViewportRef.current.scrollTo({
          top: 0,
          behavior: "smooth",
        });
      }
    }
  }, [shopId]);

  // 获取所有菜单项的key（包括子菜单）
  const allMenuKeys = useMemo(() => {
    const keys: string[] = [];
    const extractKeys = (menuItems: MenuItem[]) => {
      menuItems.forEach((item) => {
        keys.push(item.key);
        if (item.children) {
          item.children.forEach((child) => {
            keys.push(child.key);
          });
        }
      });
    };

    extractKeys(menuList);
    return keys;
  }, [menuList]);

  // 动态获取变量类型的key
  const variableKeys = useMemo(() => {
    const variableMenuItem = menuList.find(
      (item) => item.key === "shop_variables",
    );
    return variableMenuItem?.children?.map((child) => child.key) || [];
  }, [menuList]);

  // 滚动处理函数
  const handleScroll = useCallback(() => {
    if (!scrollViewportRef.current || !isScrollListenerActiveRef.current)
      return;

    const scrollViewport = scrollViewportRef.current;
    const viewportRect = scrollViewport.getBoundingClientRect();
    const viewportCenter = viewportRect.top + viewportRect.height / 2;

    let closestElement: { key: string; distance: number } | null = null;

    // 使用 for 循环提升性能
    for (const key of allMenuKeys) {
      const element = document.querySelector(`[data-form-section="${key}"]`);
      if (!element) continue;

      const elementRect = element.getBoundingClientRect();

      // 检查元素是否在视口中可见
      if (
        elementRect.bottom > viewportRect.top &&
        elementRect.top < viewportRect.bottom
      ) {
        const elementCenter = elementRect.top + elementRect.height / 2;
        const distance = Math.abs(elementCenter - viewportCenter);

        if (!closestElement || distance < closestElement.distance) {
          closestElement = { key, distance };
        }
      }
    }

    // 新增：处理父子菜单关系
    if (closestElement) {
      let finalKey = closestElement.key;

      // 动态检查是否是变量子菜单项
      if (variableKeys.includes(closestElement.key)) {
        // 检查是否有多个变量区域在视口中
        const variableElements = variableKeys
          .map((key) => document.querySelector(`[data-form-section="${key}"]`))
          .filter((el) => {
            if (!el) return false;
            const rect = el.getBoundingClientRect();
            return (
              rect.bottom > viewportRect.top && rect.top < viewportRect.bottom
            );
          });

        // 如果有多个变量区域可见，或者用户滚动到了变量区域的顶部，高亮父菜单
        if (variableElements.length > 1) {
          const shopVariablesContainer = document.querySelector(
            '[data-form-section="shop_variables"]',
          );
          if (shopVariablesContainer) {
            const containerRect =
              shopVariablesContainer.getBoundingClientRect();
            const containerCenter =
              containerRect.top + containerRect.height / 2;
            const containerDistance = Math.abs(
              containerCenter - viewportCenter,
            );

            // 如果容器整体更接近视口中心，高亮父菜单
            if (containerDistance < closestElement.distance * 1.2) {
              finalKey = "shop_variables";
            }
          }
        }
      }

      if (finalKey !== activeMenuItem) {
        setActiveMenuItem(finalKey);
      }
    }
  }, [allMenuKeys, activeMenuItem, variableKeys]);

  // 设置滚动监听器
  useEffect(() => {
    const scrollViewport = document.querySelector(scrollViewportSelector);

    if (!scrollViewport) return;

    scrollViewportRef.current = scrollViewport;
    isScrollListenerActiveRef.current = true;

    // 使用节流优化滚动性能
    let timeoutId: NodeJS.Timeout;
    const throttledHandleScroll = () => {
      if (timeoutId) clearTimeout(timeoutId);
      timeoutId = setTimeout(handleScroll, 16); // ~60fps
    };

    scrollViewport.addEventListener("scroll", throttledHandleScroll, {
      passive: true,
    });
    handleScroll(); // 初始检查

    return () => {
      isScrollListenerActiveRef.current = false;
      scrollViewport.removeEventListener("scroll", throttledHandleScroll);
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [handleScroll, scrollViewportSelector]);

  // 处理菜单项点击，滚动到对应的表单区域
  const handleMenuItemClick = useCallback((key: string) => {
    const targetElement = document.querySelector(
      `[data-form-section="${key}"]`,
    );

    if (!targetElement || !scrollViewportRef.current) return;

    const targetRect = targetElement.getBoundingClientRect();
    const viewportRect = scrollViewportRef.current.getBoundingClientRect();
    const scrollTop =
      targetRect.top - viewportRect.top + scrollViewportRef.current.scrollTop;

    scrollViewportRef.current.scrollTo({
      top: scrollTop,
      behavior: "smooth",
    });

    setActiveMenuItem(key);
  }, []);

  return {
    activeMenuItem,
    handleMenuItemClick,
    setActiveMenuItem,
  };
};
