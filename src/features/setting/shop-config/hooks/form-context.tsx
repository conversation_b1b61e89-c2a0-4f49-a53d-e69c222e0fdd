import { createContext, useContext, ReactNode } from "react";
import { UseFormReturn } from "react-hook-form";
import { FormValues } from "../form-config/form-schema";
import { Category } from "@/types/api/shop";
import { ShopConfig, ShopVariable } from "@/types/api/config";
import { QueryObserverResult } from "@tanstack/react-query";
type FormContextType = {
  form: UseFormReturn<FormValues>;
  t: (key: string) => string;
  categoryList: Category[];
  variableList: ShopVariable[];
  refetch: () => Promise<QueryObserverResult<ShopConfig | null>>;
};

const FormContext = createContext<FormContextType | undefined>(undefined);

export const FormProvider = ({
  children,
  form,
  t,
  categoryList,
  variableList,
  refetch,
}: {
  children: ReactNode;
  form: UseFormReturn<FormValues>;
  t: (key: string) => string;
  categoryList: Category[];
  variableList: ShopVariable[];
  refetch: () => Promise<QueryObserverResult<ShopConfig | null>>;
}) => {
  return (
    <FormContext.Provider
      value={{ form, t, categoryList, variableList, refetch }}
    >
      {children}
    </FormContext.Provider>
  );
};

export const useFormContext = () => {
  const context = useContext(FormContext);

  if (context === undefined) {
    throw new Error("useFormContext must be used within a FormProvider");
  }

  return context;
};
