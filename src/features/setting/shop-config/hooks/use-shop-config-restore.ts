import { FormValues } from "../form-config/form-schema";

// 将预设标签响应数据从数组格式转换回对象格式
function restorePresetLabelResp(presetLabelResp: any[]) {
  return presetLabelResp.reduce((acc, item) => {
    acc[item.preset_name] = {
      method: item.method,
      prompt:
        item.method === "text" && Array.isArray(item.prompt)
          ? item.prompt.map((p) => p.value)
          : item.prompt
            ? item.prompt[0].value
            : "",
    };
    return acc;
  }, {});
}

export const useShopConfigRestore = () => {
  // 将表单数据还原为原始的 API 格式
  const getRestoredData = (data: FormValues) => {
    const shopConfigParams = JSON.parse(JSON.stringify(data));

    // 处理 top_10_items 字段，将对象数组转换为字符串数组
    if (shopConfigParams.shop_configs?.top_10_items) {
      shopConfigParams.shop_configs.top_10_items =
        shopConfigParams.shop_configs.top_10_items.map((item) => item.value);
    }

    // 处理 transfer_category 字段
    if (shopConfigParams.shop_configs?.transfer_category) {
      shopConfigParams.shop_configs.transfer_category =
        shopConfigParams.shop_configs.transfer_category.map(
          (item) => item.value,
        );
    }

    // 处理 sensitive_words 字段
    if (shopConfigParams.shop_configs?.sensitive_words) {
      shopConfigParams.shop_configs.sensitive_words =
        shopConfigParams.shop_configs.sensitive_words.map((item) => item.value);
    }

    // 处理 product_attr_diff 字段
    if (shopConfigParams.shop_configs?.product_attr_diff) {
      shopConfigParams.shop_configs.product_attr_diff =
        shopConfigParams.shop_configs.product_attr_diff.map(
          (item) => item.value,
        );
    }

    // 处理 products 字段，将对象数组转换为以顿号分隔的字符串
    if (shopConfigParams.shop_configs?.products) {
      shopConfigParams.shop_configs.products =
        shopConfigParams.shop_configs.products
          .map((item) => item.value)
          .join("、");
    }

    // 处理 backend_config.urgeBuyStatements 字段
    if (shopConfigParams.shop_configs?.backend_config?.urgeBuyStatements) {
      shopConfigParams.shop_configs.backend_config.urgeBuyStatements =
        shopConfigParams.shop_configs.backend_config.urgeBuyStatements.map(
          (item) => item.value,
        );
    }

    // 处理 backend_config.urgePayStatements 字段
    if (shopConfigParams.shop_configs?.backend_config?.urgePayStatements) {
      shopConfigParams.shop_configs.backend_config.urgePayStatements =
        shopConfigParams.shop_configs.backend_config.urgePayStatements.map(
          (item) => item.value,
        );
    }

    // 处理 preset_configs.preset_label_gen.meta_matches 字段
    if (shopConfigParams.preset_configs?.preset_label_gen?.meta_matches) {
      shopConfigParams.preset_configs.preset_label_gen.meta_matches =
        shopConfigParams.preset_configs.preset_label_gen.meta_matches.map(
          (item) => ({
            ...item,
            match: item.match.map((v) => v.value),
          }),
        );
    }

    // 处理 preset_configs.preset_label_resp 字段
    if (shopConfigParams.preset_configs?.preset_label_resp) {
      shopConfigParams.preset_configs.preset_label_resp =
        restorePresetLabelResp(
          shopConfigParams.preset_configs.preset_label_resp,
        );
    }

    return shopConfigParams;
  };

  return { getRestoredData };
};
