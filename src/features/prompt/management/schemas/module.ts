import { Level } from "@/types/api/prompt";
import { z } from "zod";
import { promptNodeSchema } from "./node";

/**
 * 模块表单验证 Schema
 */
export const moduleFormSchema = z.object({
  name: z
    .string()
    .min(1, { message: "请输入模块名称" })
    .max(100, { message: "模块名称最多100个字符" })
    .trim(),
  content: z.array(promptNodeSchema),
  level: z.enum(Level, "请选择等级"),
  moduleLabelIds: z.array(z.string()).default([]),
  scopeDetail: z.array(z.string()).default([]),
});

/**
 * 模块表单数据类型
 */
export type ModuleFormData = z.infer<typeof moduleFormSchema>;

/**
 * 模块标签 ID 数组 Schema
 */
export const moduleLabelIdsSchema = z.array(z.string());

/**
 * 单个字段验证模式（用于表格内编辑）
 */
export const moduleFieldValidationSchemas = {
  // 从完整 schema 中提取单个字段
  name: moduleFormSchema.shape.name,
  level: moduleFormSchema.shape.level,
  moduleLabelIds: moduleFormSchema.shape.moduleLabelIds,
  scopeDetail: moduleFormSchema.shape.scopeDetail,
};

/**
 * 验证单个模块字段值
 */
export const validateModuleField = (
  fieldName: keyof typeof moduleFieldValidationSchemas,
  value: any,
) => {
  try {
    moduleFieldValidationSchemas[fieldName].parse(value);
    return { success: true, error: null };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, error: error.issues[0]?.message || "验证失败" };
    }
    return { success: false, error: "验证失败" };
  }
};
