import { describe, it, expect } from "vitest";
import { Level, VariableType } from "@/types/api/prompt";
import { Variable } from "@/types/api/variable";
import { SharedVariable } from "@/types/api/shared-variable";
import { UniversalSharedVariable } from "../../types";
import { createSharedVariableFormSchema } from "../shared-variable";

describe("shared-variable schema", () => {
  const systemVariables: Variable[] = [
    {
      variableId: "sys-1",
      name: "现有系统变量",
      promptName: "existingSysVar",
      level: Level.Product,
      type: VariableType.System,
      definition: "系统变量定义",
      example: "系统变量示例",
    },
  ];

  const shopVariables: Variable[] = [
    {
      variableId: "shop-1",
      name: "现有店铺变量",
      promptName: "existingShopVar",
      level: Level.Shop,
      type: VariableType.Shop,
      definition: "店铺变量定义",
      example: "店铺变量示例",
      scopeDetail: '["shop1"]',
    },
  ];

  const sharedVariables: SharedVariable[] = [
    {
      sharedVariableId: "shared-1",
      promptVersionId: "version-1",
      name: "现有共享变量",
      promptName: "existingSharedVar",
      level: Level.Product,
      definition: "共享变量定义",
      content: "共享变量内容",
      status: 1,
    },
  ];

  const universalSharedVariables: UniversalSharedVariable[] = [
    {
      id: "universal-1",
      name: "现有通用共享变量",
      promptName: "existingUniversalVar",
      level: Level.Product,
      definition: "通用共享变量定义",
      content: "通用共享变量内容",
      promptVersionId: "version-2",
    },
  ];

  describe("createSharedVariableFormSchema", () => {
    it("应该通过有效的数据验证", async () => {
      const schema = createSharedVariableFormSchema(
        systemVariables,
        shopVariables,
        sharedVariables,
      );

      const validData = {
        name: "新共享变量",
        promptName: "newSharedVar",
        level: Level.Product,
        definition: "新共享变量定义",
        scopeDetail: [],
      };

      const result = await schema.safeParseAsync(validData);
      expect(result.success).toBe(true);
    });

    it("应该拒绝与系统变量重复的名称", async () => {
      const schema = createSharedVariableFormSchema(
        systemVariables,
        shopVariables,
        sharedVariables,
      );

      const invalidData = {
        name: "现有系统变量", // 与系统变量重复
        promptName: "newSharedVar",
        level: Level.Product,
        definition: "新共享变量定义",
        scopeDetail: [],
      };

      const result = await schema.safeParseAsync(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        const nameError = result.error.issues.find((issue) =>
          issue.path.includes("name"),
        );
        expect(nameError?.message).toBe("变量名称与系统变量重复");
      }
    });

    it("应该拒绝与店铺变量重复的提示词变量名", async () => {
      const schema = createSharedVariableFormSchema(
        systemVariables,
        shopVariables,
        sharedVariables,
      );

      const invalidData = {
        name: "新共享变量",
        promptName: "existingShopVar", // 与店铺变量重复
        level: Level.Product,
        definition: "新共享变量定义",
        scopeDetail: [],
      };

      const result = await schema.safeParseAsync(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        const promptNameError = result.error.issues.find((issue) =>
          issue.path.includes("promptName"),
        );
        expect(promptNameError?.message).toBe("提示词变量名称与店铺变量重复");
      }
    });

    it("应该拒绝与其他共享变量重复的名称", async () => {
      const schema = createSharedVariableFormSchema(
        systemVariables,
        shopVariables,
        sharedVariables,
      );

      const invalidData = {
        name: "现有共享变量", // 与共享变量重复
        promptName: "newSharedVar",
        level: Level.Product,
        definition: "新共享变量定义",
        scopeDetail: [],
      };

      const result = await schema.safeParseAsync(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        const nameError = result.error.issues.find((issue) =>
          issue.path.includes("name"),
        );
        expect(nameError?.message).toBe("变量名称与共享变量重复");
      }
    });

    it("应该支持通用共享变量类型", async () => {
      const schema = createSharedVariableFormSchema(
        systemVariables,
        shopVariables,
        universalSharedVariables,
      );

      const invalidData = {
        name: "现有通用共享变量", // 与通用共享变量重复
        promptName: "newSharedVar",
        level: Level.Product,
        definition: "新共享变量定义",
        scopeDetail: [],
      };

      const result = await schema.safeParseAsync(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        const nameError = result.error.issues.find((issue) =>
          issue.path.includes("name"),
        );
        expect(nameError?.message).toBe("变量名称与共享变量重复");
      }
    });

    it("应该在编辑模式下排除当前共享变量", async () => {
      const schema = createSharedVariableFormSchema(
        systemVariables,
        shopVariables,
        sharedVariables,
        "shared-1", // 排除当前编辑的共享变量
      );

      const validData = {
        name: "现有共享变量", // 与当前编辑的共享变量同名，应该被排除
        promptName: "existingSharedVar",
        level: Level.Product,
        definition: "共享变量定义",
        scopeDetail: [],
      };

      const result = await schema.safeParseAsync(validData);
      expect(result.success).toBe(true);
    });

    it("应该在编辑模式下排除当前通用共享变量", async () => {
      const schema = createSharedVariableFormSchema(
        systemVariables,
        shopVariables,
        universalSharedVariables,
        "universal-1", // 排除当前编辑的通用共享变量
      );

      const validData = {
        name: "现有通用共享变量", // 与当前编辑的通用共享变量同名，应该被排除
        promptName: "existingUniversalVar",
        level: Level.Product,
        definition: "通用共享变量定义",
        scopeDetail: [],
      };

      const result = await schema.safeParseAsync(validData);
      expect(result.success).toBe(true);
    });

    it("应该保持原有的范围验证规则", async () => {
      const schema = createSharedVariableFormSchema(
        systemVariables,
        shopVariables,
        sharedVariables,
      );

      // 测试店铺级别需要选择范围
      const invalidData = {
        name: "新共享变量",
        promptName: "newSharedVar",
        level: Level.Shop, // 店铺级别
        definition: "新共享变量定义",
        scopeDetail: [], // 空范围，应该失败
      };

      const result = await schema.safeParseAsync(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        const scopeError = result.error.issues.find((issue) =>
          issue.path.includes("scopeDetail"),
        );
        expect(scopeError?.message).toBe("请选择具体的范围");
      }
    });

    it("应该同时检测多种重复", async () => {
      const schema = createSharedVariableFormSchema(
        systemVariables,
        shopVariables,
        sharedVariables,
      );

      const invalidData = {
        name: "现有系统变量", // 与系统变量重复
        promptName: "existingSharedVar", // 与共享变量重复
        level: Level.Product,
        definition: "新共享变量定义",
        scopeDetail: [],
      };

      const result = await schema.safeParseAsync(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        const nameError = result.error.issues.find((issue) =>
          issue.path.includes("name"),
        );
        const promptNameError = result.error.issues.find((issue) =>
          issue.path.includes("promptName"),
        );

        expect(nameError?.message).toBe("变量名称与系统变量重复");
        expect(promptNameError?.message).toBe("提示词变量名称与共享变量重复");
      }
    });
  });
});
