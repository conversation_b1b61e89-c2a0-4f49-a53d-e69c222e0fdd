import { AbilityItemType, Level } from "@/types/api/prompt";
import { safeParseJson } from "@/utils";
import { z } from "zod";

// 基础节点属性
const baseAttrsSchema = z.object({
  level: z.enum(Level).optional(),
});

// 字符串变量节点
const stringVariableNodeSchema = z.object({
  type: z.literal("stringVariable"),
  attrs: z.object({
    id: z.string(),
  }),
});

// prompt 节点
export const promptNodeSchema = z.object({
  type: z.literal("prompt"),
  content: z.array(z.looseObject({})),
});

// module 节点
export const moduleNodeSchema = z.object({
  type: z.literal("module"),
  attrs: baseAttrsSchema.extend({
    name: z.string(),
    tags: z.array(z.string()).optional(),
  }),
  content: z.array(promptNodeSchema),
});

// ability module 引用节点
export const abilityReferenceModuleNodeSchema = z.object({
  type: z.literal("module"),
  attrs: z.object({
    id: z.string(),
  }),
});

// abilityItem 节点
export const abilityItemNodeSchema = z.object({
  type: z.literal("abilityItem"),
  attrs: z.object({
    type: z.enum(AbilityItemType),
  }),
  content: z.array(abilityReferenceModuleNodeSchema),
});

// ability 节点
export const abilityNodeSchema = z.object({
  type: z.literal("ability"),
  attrs: baseAttrsSchema.extend({
    name: z.string(),
  }),
  content: z.array(abilityItemNodeSchema),
});

// 共享变量节点
export const sharedVariableNodeSchema = z.object({
  type: z.literal("tableVariable"),
  content: z.array(z.looseObject({})),
});

// 共享变量引用节点
export const sharedVariableReferenceNodeSchema = z.object({
  type: z.literal("sharedVariableReference"),
  attrs: z.object({
    sharedVariableId: z.string(),
  }),
});

// 从 JSON 字符串解析内容
export const parseAbilityContent = (content: string) =>
  abilityNodeSchema.safeParse(safeParseJson(content, undefined));

// 将内容序列化为 JSON 字符串
export const stringifyAbilityNode = (
  content: z.infer<typeof abilityNodeSchema> | undefined,
): string => (content ? JSON.stringify(content) : "");

// 从 JSON 字符串解析内容
export const parseModuleNode = (content: unknown) =>
  moduleNodeSchema.safeParse(content);

// 将内容序列化为 JSON 字符串
export const stringifyModuleNode = (
  content: z.infer<typeof moduleNodeSchema> | undefined,
): string => (content ? JSON.stringify(content) : "");

// 类型导出
export type AbilityNodeType = z.infer<typeof abilityNodeSchema>;
export type AbilityItemNodeType = z.infer<typeof abilityItemNodeSchema>;
export type AbilityReferenceModuleNodeType = z.infer<
  typeof abilityReferenceModuleNodeSchema
>;
export type ModuleNodeType = z.infer<typeof moduleNodeSchema>;
export type PromptNodeType = z.infer<typeof promptNodeSchema>;
export type StringVariableNodeType = z.infer<typeof stringVariableNodeSchema>;
export type SharedVariableNodeType = z.infer<typeof sharedVariableNodeSchema>;
export type SharedVariableReferenceNodeType = z.infer<
  typeof sharedVariableReferenceNodeSchema
>;
