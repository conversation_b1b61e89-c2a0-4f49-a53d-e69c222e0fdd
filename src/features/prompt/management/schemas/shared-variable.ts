import { Level } from "@/types/api/prompt";
import { SharedVariable } from "@/types/api/shared-variable";
import { Variable } from "@/types/api/variable";
import { z } from "zod";
import {
  checkVariableNameDuplication,
  generateDuplicateVariableNameMessage,
} from "../../utils/variable-validation";
import { UniversalSharedVariable } from "../types";

/**
 * 共享变量表单基础验证 Schema
 */
const baseSharedVariableSchema = z
  .object({
    // 变量名称
    name: z
      .string()
      .min(1, { message: "变量名称不能为空" })
      .max(50, { message: "变量名称最多50个字符" })
      .regex(/^[a-zA-Z0-9_\u4e00-\u9fa5]+$/, {
        message: "变量名称只能包含字母、数字、下划线和中文",
      }),

    // 提示词变量名称
    promptName: z
      .string()
      .min(1, { message: "提示词变量名称不能为空" })
      .max(50, { message: "提示词变量名称最多50个字符" })
      .regex(/^[a-zA-Z][a-zA-Z0-9_]*$/, {
        message: "提示词变量名称必须以字母开头，只能包含字母、数字和下划线",
      }),

    // 等级
    level: z.enum(Level, {
      message: "请选择等级",
    }),

    // 变量定义
    definition: z
      .string()
      .max(500, { message: "变量定义最多500个字符" })
      .optional(),

    // 范围详情（店铺ID数组或类目ID数组）
    scopeDetail: z.array(z.string()).optional(),
  })
  .check((ctx) => {
    const data = ctx.value;

    // 如果选择了店铺级或类目级，必须选择具体范围
    if (data.level === Level.Shop || data.level === Level.Category) {
      if (!data.scopeDetail || data.scopeDetail.length === 0) {
        ctx.issues.push({
          code: "custom",
          message: "请选择具体的范围",
          path: ["scopeDetail"],
          input: data.scopeDetail,
        });
      }
    }
  });

/**
 * 创建带有变量名重复检查的共享变量表单验证模式
 */
export function createSharedVariableFormSchema(
  systemVariables: Variable[] = [],
  shopVariables: Variable[] = [],
  sharedVariables: (SharedVariable | UniversalSharedVariable)[] = [],
  excludeId?: string,
) {
  return baseSharedVariableSchema.check((ctx) => {
    const data = ctx.value;
    // 检查变量名重复
    const checkResult = checkVariableNameDuplication(
      data.name,
      data.promptName,
      systemVariables,
      shopVariables,
      sharedVariables,
      excludeId,
    );

    if (checkResult.isNameDuplicate) {
      ctx.issues.push({
        code: "custom",
        message: generateDuplicateVariableNameMessage(
          "变量名称",
          checkResult.duplicateNameSource!,
        ),
        path: ["name"],
        input: data.name,
      });
    }

    if (checkResult.isPromptNameDuplicate) {
      ctx.issues.push({
        code: "custom",
        message: generateDuplicateVariableNameMessage(
          "提示词变量名称",
          checkResult.duplicatePromptNameSource!,
        ),
        path: ["promptName"],
        input: data.promptName,
      });
    }
  });
}

/**
 * 默认的共享变量表单验证 Schema（不包含重复检查）
 */
export const sharedVariableFormSchema = baseSharedVariableSchema;

/**
 * 共享变量表单数据类型
 */
export type SharedVariableFormData = z.infer<typeof sharedVariableFormSchema>;

/**
 * 单个字段验证模式（用于表格内编辑）
 */
export const sharedVariableFieldValidationSchemas = {
  // 从基础 schema 中提取单个字段
  name: baseSharedVariableSchema.shape.name,
  promptName: baseSharedVariableSchema.shape.promptName,
  level: baseSharedVariableSchema.shape.level,
  definition: baseSharedVariableSchema.shape.definition,
  scopeDetail: baseSharedVariableSchema.shape.scopeDetail,
};

/**
 * 验证单个共享变量字段值
 */
export const validateSharedVariableField = (
  fieldName: keyof typeof sharedVariableFieldValidationSchemas,
  value: any,
) => {
  try {
    sharedVariableFieldValidationSchemas[fieldName].parse(value);

    return { success: true, error: null };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, error: error.issues[0]?.message || "验证失败" };
    }

    return { success: false, error: "验证失败" };
  }
};
