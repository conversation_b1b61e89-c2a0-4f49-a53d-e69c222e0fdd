import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from "@/components/ui/resizable";
import { usePanelSize } from "@/hooks/use-panel-size";
import { useAtom } from "jotai";
import React from "react";
import { autoSelectSharedVariableEffect } from "./atoms";
import { SharedVariableEditor, SharedVariableList } from "./components";

export const SharedVariableManagement: React.FC = () => {
  const leftPanelProps = usePanelSize({
    panelId: "shared-variable-management-left-panel",
    defaultSize: 20,
    minSize: 15,
  });

  useAtom(autoSelectSharedVariableEffect);

  return (
    <ResizablePanelGroup
      className="h-full"
      direction="horizontal"
      id="shared-variable-management-panel"
    >
      <ResizablePanel
        maxSize={40}
        className="flex flex-col"
        {...leftPanelProps}
      >
        <SharedVariableList />
      </ResizablePanel>

      <ResizableHandle />

      <ResizablePanel>
        <SharedVariableEditor />
      </ResizablePanel>
    </ResizablePanelGroup>
  );
};
