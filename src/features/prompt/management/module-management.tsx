import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from "@/components/ui/resizable";
import { usePanelSize } from "@/hooks/use-panel-size";
import { useSetAtom } from "jotai";
import React, { useState } from "react";
import { deleteModuleAtom } from "./atoms";
import { ModuleLabelPanel, ModuleTable } from "./components";

export const ModuleManagement: React.FC = () => {
  const leftPanelProps = usePanelSize({
    panelId: "module-management-left-panel",
    defaultSize: 20,
    minSize: 15,
  });
  const [deleteConfirm, setDeleteConfirm] = useState<{
    moduleId: string;
    moduleName: string;
  } | null>(null);

  // 删除操作
  const deleteModule = useSetAtom(deleteModuleAtom);

  // 确认删除
  const confirmDelete = () => {
    if (!deleteConfirm) return;

    deleteModule(deleteConfirm.moduleId.toString());
    setDeleteConfirm(null);
  };

  return (
    <>
      <ResizablePanelGroup
        className="h-full"
        direction="horizontal"
        id="module-management-panel"
      >
        {/* 左侧标签面板 */}
        <ResizablePanel
          maxSize={40}
          className="flex flex-col"
          {...leftPanelProps}
        >
          <ModuleLabelPanel />
        </ResizablePanel>

        <ResizableHandle />

        {/* 右侧表格面板 */}
        <ResizablePanel>
          <div className="overflow-hidden flex flex-col h-full bg-white">
            <ModuleTable />
          </div>
        </ResizablePanel>
      </ResizablePanelGroup>

      {/* 删除确认对话框 */}
      <AlertDialog
        open={deleteConfirm !== null}
        onOpenChange={(open) => !open && setDeleteConfirm(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              {deleteConfirm && (
                <span>
                  确定要删除模块
                  <strong>"{deleteConfirm.moduleName}"</strong> 吗？
                </span>
              )}
              <div className="mt-2 text-sm text-muted-foreground">
                此操作不可撤销。
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              确认删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};
