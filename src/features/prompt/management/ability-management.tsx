import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from "@/components/ui/resizable";
import { usePanelSize } from "@/hooks/use-panel-size";
import { useAtom } from "jotai";
import React from "react";
import { autoSelectAbilityEffect, autoSelectVersionEffect } from "./atoms";
import { AbilityContent, AbilityPanel, OutlinePanel } from "./components";

export const AbilityManagement: React.FC = () => {
  const leftPanelProps = usePanelSize({
    panelId: `prompt-management-left-page`,
    defaultSize: 20,
    minSize: 15,
  });
  const capabilityPanelProps = usePanelSize({
    panelId: `prompt-management-capability-panel`,
    minSize: 30,
    defaultSize: 60,
  });

  useAtom(autoSelectVersionEffect);
  useAtom(autoSelectAbilityEffect);

  return (
    <ResizablePanelGroup
      className="h-full"
      direction="horizontal"
      id="prompt-library-panel"
    >
      <ResizablePanel
        maxSize={40}
        className="flex flex-col"
        {...leftPanelProps}
      >
        <ResizablePanelGroup direction="vertical" className="h-full">
          <ResizablePanel className="flex flex-col" {...capabilityPanelProps}>
            <AbilityPanel />
          </ResizablePanel>

          <ResizableHandle />

          <ResizablePanel minSize={20} className="flex flex-col">
            <OutlinePanel />
          </ResizablePanel>
        </ResizablePanelGroup>
      </ResizablePanel>

      <ResizableHandle />

      <ResizablePanel>
        <AbilityContent />
      </ResizablePanel>
    </ResizablePanelGroup>
  );
};
