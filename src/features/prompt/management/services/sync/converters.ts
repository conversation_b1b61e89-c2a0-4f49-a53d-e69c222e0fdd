/**
 * 数据转换器 - 将草稿数据转换为服务端接口参数
 */

import { AddOrUpdateAbilityParams } from "@/types/api/ability";
import {
  AddOrUpdateModuleLabelParams,
  AddOrUpdateModuleParams,
} from "@/types/api/module";
import {
  AddOrUpdatePromptVersionParams,
  PromptVersion,
} from "@/types/api/prompt-version";
import { AddOrUpdateSharedVariableParams } from "@/types/api/shared-variable";
import { safeParseJson } from "@/utils";
import {
  CompleteDraftVersionData,
  DraftAbility,
  DraftModule,
  DraftModuleLabel,
  DraftSharedVariable,
  DraftVersion,
} from "../../types";
import {
  createDraftData,
  getCompleteVersionData,
  VersionChangeContent,
  VersionChangeTracker,
  VersionDataSnapshot,
} from "../../utils";
import { SyncError, SyncErrorType } from "./types";

/**
 * 将 CompleteVersionData 转换为 VersionDataSnapshot
 * 只保留用户关心的核心业务字段，过滤掉技术性字段
 */
function convertToSnapshot(
  data: Omit<CompleteDraftVersionData, "draftVersion"> | null,
): VersionDataSnapshot {
  if (!data) {
    return {
      abilities: [],
      modules: [],
      moduleLabels: [],
      sharedVariables: [],
    };
  }

  return {
    abilities: data.draftAbilities.map((ability) => ({
      id: ability.id,
      name: ability.name,
      content: ability.content,
    })),
    modules: data.draftModules.map((module) => ({
      id: module.id,
      name: module.name,
      level: module.level,
      prompt: module.prompt,
      moduleLabelIds: module.moduleLabelIds,
      scopeDetail: module.scopeDetail,
    })),
    moduleLabels: data.draftModuleLabels.map((label) => ({
      id: label.id,
      name: label.name,
    })),
    sharedVariables: data.draftSharedVariables.map((variable) => ({
      id: variable.id,
      name: variable.name,
      content: variable.content,
      definition: variable.definition,
      level: variable.level,
      scopeDetail: variable.scopeDetail,
      promptName: variable.promptName,
    })),
  };
}

/**
 * 生成或更新变更内容
 */
async function generateOrUpdateChangeContent(
  draftVersion: DraftVersion,
  draftData: CompleteDraftVersionData | null,
  baseVersionDetail: PromptVersion | null,
): Promise<VersionChangeContent | null> {
  const tracker = new VersionChangeTracker({
    debug: process.env.NODE_ENV === "development",
  });

  try {
    // 获取当前草稿的完整数据
    const currentSnapshot = convertToSnapshot(draftData);

    const existingChangeContent = baseVersionDetail
      ? safeParseJson(baseVersionDetail.versionChange?.content, null)
      : null;

    if (!existingChangeContent) {
      // 首次创建：获取基准版本数据并创建初始 changeContent
      let baseData: Omit<CompleteDraftVersionData, "draftVersion"> = {
        draftAbilities: [],
        draftModules: [],
        draftModuleLabels: [],
        draftSharedVariables: [],
      };

      if (draftVersion.baseVersionId) {
        try {
          const completeVersionData = await getCompleteVersionData(
            draftVersion.baseVersionId,
          );

          if (completeVersionData != null) {
            baseData = createDraftData(
              completeVersionData,
              draftVersion.baseVersionId,
              draftVersion.versionName,
              draftVersion.id,
              draftVersion.createdAt,
            );
          }
        } catch (error) {
          console.warn("获取基准版本数据失败，使用空数据:", error);
        }
      }

      const baseSnapshot = convertToSnapshot(baseData);
      const result = tracker.createInitialChangeContent(
        baseSnapshot,
        currentSnapshot,
        {
          baseVersionId: draftVersion.baseVersionId,
          baseVersion: draftVersion.baseVersion,
        },
      );

      if (!result.success) {
        console.error("创建初始变更内容失败:", result.error);
        return null;
      }

      return result.changeContent!;
    } else {
      // 更新：计算新的 patch 并添加到现有的 patches 中
      const result = tracker.updateChangeContent(
        existingChangeContent,
        currentSnapshot,
        `保存变更 ${new Date().toLocaleString()}`,
      );

      if (!result.success) {
        console.error("更新变更内容失败:", result.error);
        return null;
      }

      return result.changeContent!;
    }
  } catch (error) {
    console.error("生成变更内容失败:", error);
    return null;
  }
}

/**
 * 版本转换器
 */
export class VersionConverter {
  /**
   * 将草稿版本转换为服务端参数
   */
  static async toServerParams(
    draftVersion: DraftVersion,
    draftData: CompleteDraftVersionData | null,
    baseVersionDetail: PromptVersion | null,
    versionInfo: {
      versionName: string;
      remark: string;
    },
    isCreateNewVersion: boolean,
  ): Promise<AddOrUpdatePromptVersionParams> {
    const params: AddOrUpdatePromptVersionParams = {
      versionName: versionInfo.versionName,
      remark: versionInfo.remark,
    };

    // 如果有基准版本ID，需要判断是创建还是更新
    if (draftVersion.baseVersionId) {
      if (baseVersionDetail) {
        params.basePromptVersionId = draftVersion.baseVersionId;
        params.baseVersion = draftVersion.baseVersion;

        if (!isCreateNewVersion) {
          params.promptVersionId = draftVersion.baseVersionId;
        }
      }
    }

    // 生成变更内容
    const changeContent = await generateOrUpdateChangeContent(
      draftVersion,
      draftData,
      baseVersionDetail,
    );

    if (changeContent) {
      params.changeContent = JSON.stringify(changeContent);
    }

    return params;
  }

  /**
   * 验证草稿版本数据
   */
  static validate(draftVersion: DraftVersion): SyncError[] {
    const errors: SyncError[] = [];

    if (!draftVersion.versionName?.trim()) {
      errors.push({
        type: SyncErrorType.VALIDATION_ERROR,
        message: "版本名称不能为空",
        dataId: draftVersion.id,
      });
    }

    if (draftVersion.versionName && draftVersion.versionName.length > 100) {
      errors.push({
        type: SyncErrorType.VALIDATION_ERROR,
        message: "版本名称不能超过100个字符",
        dataId: draftVersion.id,
      });
    }

    return errors;
  }
}

/**
 * 模块标签转换器
 */
export class ModuleLabelConverter {
  /**
   * 将草稿模块标签转换为服务端参数
   */
  static toServerParams(
    draftLabel: DraftModuleLabel,
    isCreateNewVersion: boolean,
  ): AddOrUpdateModuleLabelParams {
    const params: AddOrUpdateModuleLabelParams = {
      name: draftLabel.name,
      promptVersionId: draftLabel.promptVersionId,
    };

    // 如果存在sourceVersionId，说明是基于线上数据的草稿，调用更新接口
    // 如果是创建新版本，不需要传ID
    if (draftLabel.sourceVersionId && !isCreateNewVersion) {
      params.moduleLabelId = draftLabel.id;
    }

    return params;
  }

  /**
   * 验证草稿模块标签数据
   */
  static validate(draftLabel: DraftModuleLabel): SyncError[] {
    const errors: SyncError[] = [];

    if (!draftLabel.name?.trim()) {
      errors.push({
        type: SyncErrorType.VALIDATION_ERROR,
        message: "模块标签名称不能为空",
        dataId: draftLabel.id,
      });
    }

    if (draftLabel.name && draftLabel.name.length > 50) {
      errors.push({
        type: SyncErrorType.VALIDATION_ERROR,
        message: "模块标签名称不能超过50个字符",
        dataId: draftLabel.id,
      });
    }

    return errors;
  }
}

/**
 * 共享变量转换器
 */
export class SharedVariableConverter {
  /**
   * 将草稿共享变量转换为服务端参数
   */
  static toServerParams(
    draftVariable: DraftSharedVariable,
    isCreateNewVersion: boolean,
  ): AddOrUpdateSharedVariableParams {
    const params: AddOrUpdateSharedVariableParams = {
      name: draftVariable.name,
      level: draftVariable.level,
      scopeDetail: draftVariable.scopeDetail,
      promptName: draftVariable.promptName,
      definition: draftVariable.definition,
      content: draftVariable.content,
      promptVersionId: draftVariable.promptVersionId,
    };

    // 如果存在sourceVersionId，说明是基于线上数据的草稿，调用更新接口
    // 如果是创建新版本，不需要传ID
    if (draftVariable.sourceVersionId && !isCreateNewVersion) {
      params.sharedVariableId = draftVariable.id;
    }

    return params;
  }

  /**
   * 验证草稿共享变量数据
   */
  static validate(draftVariable: DraftSharedVariable): SyncError[] {
    const errors: SyncError[] = [];

    if (!draftVariable.name?.trim()) {
      errors.push({
        type: SyncErrorType.VALIDATION_ERROR,
        message: "共享变量名称不能为空",
        dataId: draftVariable.id,
      });
    }

    if (!draftVariable.content?.trim()) {
      errors.push({
        type: SyncErrorType.VALIDATION_ERROR,
        message: "共享变量内容不能为空",
        dataId: draftVariable.id,
      });
    }

    return errors;
  }
}

/**
 * 模块转换器
 */
export class ModuleConverter {
  /**
   * 将草稿模块转换为服务端参数
   */
  static toServerParams(
    draftModule: DraftModule,
    isCreateNewVersion: boolean,
  ): AddOrUpdateModuleParams {
    const params: AddOrUpdateModuleParams = {
      name: draftModule.name,
      level: draftModule.level,
      prompt: draftModule.prompt,
      scopeDetail: draftModule.scopeDetail,
      moduleLabelIds: draftModule.moduleLabelIds || "",
      promptVersionId: draftModule.promptVersionId,
    };

    // 如果存在sourceVersionId，说明是基于线上数据的草稿，调用更新接口
    // 如果是创建新版本，不需要传ID
    if (draftModule.sourceVersionId && !isCreateNewVersion) {
      params.moduleId = draftModule.id;
    }

    return params;
  }

  /**
   * 验证草稿模块数据
   */
  static validate(draftModule: DraftModule): SyncError[] {
    const errors: SyncError[] = [];

    if (!draftModule.name?.trim()) {
      errors.push({
        type: SyncErrorType.VALIDATION_ERROR,
        message: "模块名称不能为空",
        dataId: draftModule.id,
      });
    }

    if (!draftModule.prompt?.trim()) {
      errors.push({
        type: SyncErrorType.VALIDATION_ERROR,
        message: "模块提示词不能为空",
        dataId: draftModule.id,
      });
    }

    return errors;
  }
}

/**
 * 能力转换器
 */
export class AbilityConverter {
  /**
   * 将草稿能力转换为服务端参数
   */
  static toServerParams(
    draftAbility: DraftAbility,
    isCreateNewVersion: boolean,
  ): AddOrUpdateAbilityParams {
    const params: AddOrUpdateAbilityParams = {
      name: draftAbility.name,
      content: draftAbility.content,
      promptVersionId: draftAbility.promptVersionId,
    };

    // 如果存在sourceVersionId，说明是基于线上数据的草稿，调用更新接口
    // 如果是创建新版本，不需要传ID
    if (draftAbility.sourceVersionId && !isCreateNewVersion) {
      params.abilityId = draftAbility.id;
    }

    return params;
  }

  /**
   * 验证草稿能力数据
   */
  static validate(draftAbility: DraftAbility): SyncError[] {
    const errors: SyncError[] = [];

    if (!draftAbility.name?.trim()) {
      errors.push({
        type: SyncErrorType.VALIDATION_ERROR,
        message: "能力名称不能为空",
        dataId: draftAbility.id,
      });
    }

    if (!draftAbility.content?.trim()) {
      errors.push({
        type: SyncErrorType.VALIDATION_ERROR,
        message: "能力内容不能为空",
        dataId: draftAbility.id,
      });
    }

    return errors;
  }
}
