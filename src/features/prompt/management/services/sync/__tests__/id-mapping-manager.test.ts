/**
 * IdMappingManager 测试
 */

import { describe, expect, it } from "vitest";
import { IdMappingManager } from "../id-mapping-manager";
import { SyncDataType } from "../types";

describe("IdMappingManager", () => {
  it("应该创建空的映射管理器", () => {
    const manager = new IdMappingManager();
    const mapping = manager.getMapping();

    expect(mapping.versionIds.size).toBe(0);
    expect(mapping.moduleLabelIds.size).toBe(0);
    expect(mapping.sharedVariableIds.size).toBe(0);
    expect(mapping.moduleIds.size).toBe(0);
    expect(mapping.abilityIds.size).toBe(0);
  });

  it("应该从序列化数据创建映射管理器", () => {
    const serialized = {
      versionIds: { "draft-1": "server-1" },
      moduleLabelIds: { "draft-2": "server-2" },
      sharedVariableIds: {},
      moduleIds: {},
      abilityIds: {},
    };

    const manager = new IdMappingManager(serialized);
    const mapping = manager.getMapping();

    expect(mapping.versionIds.get("draft-1")).toBe("server-1");
    expect(mapping.moduleLabelIds.get("draft-2")).toBe("server-2");
  });

  it("应该更新ID映射", () => {
    const manager = new IdMappingManager();

    manager.updateMapping(
      SyncDataType.VERSION,
      "draft-version",
      "server-version",
    );
    manager.updateMapping(SyncDataType.MODULE, "draft-module", "server-module");

    const mapping = manager.getMapping();
    expect(mapping.versionIds.get("draft-version")).toBe("server-version");
    expect(mapping.moduleIds.get("draft-module")).toBe("server-module");
  });

  it("应该获取映射的ID", () => {
    const manager = new IdMappingManager();
    manager.updateMapping(
      SyncDataType.ABILITY,
      "draft-ability",
      "server-ability",
    );

    const mappedId = manager.getMappedId(SyncDataType.ABILITY, "draft-ability");
    expect(mappedId).toBe("server-ability");

    const notFoundId = manager.getMappedId(SyncDataType.ABILITY, "not-found");
    expect(notFoundId).toBeUndefined();
  });

  it("应该获取序列化映射", () => {
    const manager = new IdMappingManager();
    manager.updateMapping(
      SyncDataType.SHARED_VARIABLE,
      "draft-var",
      "server-var",
    );

    const serialized = manager.getSerializedMapping();
    expect(serialized.sharedVariableIds["draft-var"]).toBe("server-var");
    expect(Object.keys(serialized.versionIds)).toHaveLength(0);
  });

  it("应该复制映射管理器", () => {
    const original = new IdMappingManager();
    original.updateMapping(
      SyncDataType.MODULE_LABEL,
      "draft-label",
      "server-label",
    );

    const cloned = original.clone();
    const clonedMapping = cloned.getMapping();

    expect(clonedMapping.moduleLabelIds.get("draft-label")).toBe(
      "server-label",
    );

    // 修改原始映射不应该影响克隆
    original.updateMapping(
      SyncDataType.MODULE_LABEL,
      "draft-label-2",
      "server-label-2",
    );
    expect(clonedMapping.moduleLabelIds.get("draft-label-2")).toBeUndefined();
  });

  it("应该清空所有映射", () => {
    const manager = new IdMappingManager();
    manager.updateMapping(SyncDataType.VERSION, "draft-1", "server-1");
    manager.updateMapping(SyncDataType.MODULE, "draft-2", "server-2");

    manager.clear();
    const mapping = manager.getMapping();

    expect(mapping.versionIds.size).toBe(0);
    expect(mapping.moduleIds.size).toBe(0);
  });

  it("应该创建空的序列化映射", () => {
    const empty = IdMappingManager.createEmptySerializedMapping();

    expect(Object.keys(empty.versionIds)).toHaveLength(0);
    expect(Object.keys(empty.moduleLabelIds)).toHaveLength(0);
    expect(Object.keys(empty.sharedVariableIds)).toHaveLength(0);
    expect(Object.keys(empty.moduleIds)).toHaveLength(0);
    expect(Object.keys(empty.abilityIds)).toHaveLength(0);
  });
});
