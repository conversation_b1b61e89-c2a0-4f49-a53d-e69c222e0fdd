import { Level } from "@/types/api/prompt";
import { PromptVersionStatus } from "@/types/api/prompt-version";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { CompleteDraftVersionData, CompleteVersionData } from "../../../types";
import { QueueSyncService } from "../queue-sync-service";
import {
  QueueStatus,
  QueueTaskStatus,
  SyncDataType,
  SyncOptions,
} from "../types";

// Mock 所有服务
vi.mock("@/services/ability-service", () => ({
  AbilityService: {
    deleteAbility: vi.fn(),
    addOrUpdateAbility: vi.fn(),
  },
}));

vi.mock("@/services/module-service", () => ({
  ModuleService: {
    deleteModule: vi.fn(),
    deleteModuleLabel: vi.fn(),
    addOrUpdateModule: vi.fn(),
    addOrUpdateModuleLabel: vi.fn(),
  },
}));

vi.mock("@/services/shared-variable-service", () => ({
  SharedVariableService: {
    deleteSharedVariable: vi.fn(),
    addOrUpdateSharedVariable: vi.fn(),
  },
}));

vi.mock("@/services/prompt-version-service", () => ({
  PromptVersionService: {
    addOrUpdatePromptVersion: vi.fn(),
    getPromptVersionDetail: vi.fn(),
  },
}));

// Mock utils
vi.mock("../../../utils", async (importOriginal) => {
  const actual = (await importOriginal()) as typeof import("../../../utils");

  return {
    ...actual,
    getCompleteVersionData: vi.fn(),
  };
});

describe("删除同步集成测试", () => {
  let syncService: QueueSyncService;
  const mockOptions: SyncOptions = {
    cleanupOnSuccess: false,
    timeout: 5000,
    retryCount: 1,
    retryDelay: 100,
    autoStart: false,
  };

  // Mock 基准版本数据
  const mockBaseData: CompleteVersionData = {
    version: {
      promptVersionId: "base-version-1",
      versionName: "基准版本",
      version: "1.0.0",
      status: PromptVersionStatus.ONLINE,
      remark: "基准版本",
      submitDate: "2024-01-01",
      offlineDate: undefined,
      onlineDate: undefined,
      versionChange: undefined,
      evaluationTasks: [],
    },
    abilities: [
      {
        id: "ability-1",
        name: "能力1",
        content: "能力内容1",
        promptVersionId: "base-version-1",
      },
      {
        id: "ability-2",
        name: "能力2",
        content: "能力内容2",
        promptVersionId: "base-version-1",
      },
    ],
    modules: [
      {
        id: "module-1",
        name: "模块1",
        moduleLabelIds: null,
        promptVersionId: "base-version-1",
        level: Level.Product,
        prompt: "模块内容1",
        scopeDetail: undefined,
      },
    ],
    moduleLabels: [
      {
        id: "label-1",
        promptVersionId: "base-version-1",
        name: "标签1",
      },
    ],
    sharedVariables: [
      {
        id: "variable-1",
        name: "变量1",
        content: "变量内容1",
        definition: "变量定义1",
        level: Level.Product,
        scopeDetail: undefined,
        promptName: "var1",
        promptVersionId: "base-version-1",
      },
    ],
  };

  // Mock 草稿数据（删除了 ability-2）
  const mockDraftData: CompleteDraftVersionData = {
    draftVersion: {
      id: "draft-version-1",
      versionName: "草稿版本",
      baseVersionId: "base-version-1",
      createdAt: "2024-01-01T00:00:00Z",
      updatedAt: "2024-01-01T00:00:00Z",
    },
    draftModuleLabels: [
      {
        id: "label-1",
        promptVersionId: "draft-version-1",
        name: "标签1",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
      },
    ],
    draftSharedVariables: [
      {
        id: "variable-1",
        name: "变量1",
        content: "变量内容1",
        definition: "变量定义1",
        level: Level.Product,
        scopeDetail: undefined,
        promptName: "var1",
        promptVersionId: "draft-version-1",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
      },
    ],
    draftModules: [
      {
        id: "module-1",
        name: "模块1",
        moduleLabelIds: null,
        promptVersionId: "draft-version-1",
        level: Level.Product,
        prompt: "模块内容1",
        scopeDetail: undefined,
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
      },
    ],
    draftAbilities: [
      // 只保留 ability-1，删除了 ability-2
      {
        id: "ability-1",
        name: "能力1",
        content: "能力内容1",
        promptVersionId: "draft-version-1",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
      },
    ],
  };

  const mockVersionInfo = {
    remark: "测试备注",
    versionName: "草稿版本",
  };

  beforeEach(async () => {
    vi.clearAllMocks();

    // 创建带有回调函数的同步服务
    const getDraftDataCallback = vi.fn().mockReturnValue(null);
    const checkDataChangedCallback = vi.fn().mockReturnValue(false);
    const getDraftDataByVersionCallback = vi
      .fn()
      .mockReturnValue(mockDraftData);

    syncService = new QueueSyncService(
      mockOptions,
      undefined,
      getDraftDataCallback,
      checkDataChangedCallback,
      getDraftDataByVersionCallback,
    );

    // Mock getCompleteVersionData
    const utils = await import("../../../utils");

    vi.mocked(utils.getCompleteVersionData).mockResolvedValue(mockBaseData);
  });

  it("应该创建包含删除任务的同步队列", async () => {
    // 直接测试删除检测函数
    const { detectDeletedItems } = await import("../detect-deleted-items");
    const deletedItems = detectDeletedItems(mockBaseData, mockDraftData);

    // 应该检测到删除了 ability-2
    expect(deletedItems.deletedAbilities).toHaveLength(1);
    expect(deletedItems.deletedAbilities[0].id).toBe("ability-2");
    expect(deletedItems.deletedAbilities[0].name).toBe("能力2");

    const queue = await syncService.createSyncQueue(
      "draft-version-1",
      mockDraftData,
      mockVersionInfo,
    );

    expect(queue.tasks).toHaveLength(6); // 1个删除任务 + 5个创建/更新任务

    // 验证删除任务
    const deleteTask = queue.tasks.find(
      (t) => t.type === SyncDataType.DELETE_ABILITY,
    );
    expect(deleteTask).toBeDefined();
    expect(deleteTask!.draftId).toBe("ability-2");
    expect(deleteTask!.displayName).toBe("删除能力: 能力2");
    expect(deleteTask!.dependencies).toHaveLength(0);

    // 验证版本任务依赖删除任务
    const versionTask = queue.tasks.find(
      (t) => t.type === SyncDataType.VERSION,
    );
    expect(versionTask).toBeDefined();
    expect(versionTask!.dependencies).toContain(deleteTask!.id);
  });

  it("应该成功执行包含删除任务的同步队列", async () => {
    // Mock 所有服务调用成功
    const { AbilityService } = await import("@/services/ability-service");
    const { ModuleService } = await import("@/services/module-service");
    const { SharedVariableService } = await import(
      "@/services/shared-variable-service"
    );
    const { PromptVersionService } = await import(
      "@/services/prompt-version-service"
    );

    (AbilityService.deleteAbility as any).mockResolvedValue({ success: true });
    (PromptVersionService.addOrUpdatePromptVersion as any).mockResolvedValue({
      success: true,
      data: "new-version-id",
    });
    (ModuleService.addOrUpdateModuleLabel as any).mockResolvedValue({
      success: true,
      data: "new-label-id",
    });
    (SharedVariableService.addOrUpdateSharedVariable as any).mockResolvedValue({
      success: true,
      data: "new-variable-id",
    });
    (ModuleService.addOrUpdateModule as any).mockResolvedValue({
      success: true,
      data: "new-module-id",
    });
    (AbilityService.addOrUpdateAbility as any).mockResolvedValue({
      success: true,
      data: "new-ability-id",
    });

    const queue = await syncService.createSyncQueue(
      "draft-version-1",
      mockDraftData,
      mockVersionInfo,
    );

    const result = await syncService.executeQueue(queue);

    expect(result.success).toBe(true);
    expect(queue.status).toBe(QueueStatus.SUCCESS);

    // 验证删除任务被执行
    expect(AbilityService.deleteAbility).toHaveBeenCalledWith({
      abilityId: "ability-2",
    });

    // 验证所有任务都成功完成
    const allTasksSuccessful = queue.tasks.every(
      (task) => task.status === QueueTaskStatus.SUCCESS,
    );
    expect(allTasksSuccessful).toBe(true);
  });

  it("应该在删除任务失败时正确处理错误", async () => {
    const { AbilityService } = await import("@/services/ability-service");
    (AbilityService.deleteAbility as any).mockResolvedValue({ success: false });

    const queue = await syncService.createSyncQueue(
      "draft-version-1",
      mockDraftData,
      mockVersionInfo,
    );

    const result = await syncService.executeQueue(queue);

    expect(result.success).toBe(false);
    expect(queue.status).toBe(QueueStatus.FAILED);

    // 验证删除任务失败
    const deleteTask = queue.tasks.find(
      (t) => t.type === SyncDataType.DELETE_ABILITY,
    );
    expect(deleteTask!.status).toBe(QueueTaskStatus.FAILED);
    expect(deleteTask!.error).toBeDefined();
    expect(deleteTask!.error!.message).toContain("能力删除失败");
  });

  it("应该在没有基准版本时跳过删除检测", async () => {
    const draftDataWithoutBase: CompleteDraftVersionData = {
      ...mockDraftData,
      draftVersion: {
        ...mockDraftData.draftVersion,
        baseVersionId: undefined,
      },
    };

    const queue = await syncService.createSyncQueue(
      "draft-version-1",
      draftDataWithoutBase,
      mockVersionInfo,
    );

    // 应该只有创建/更新任务，没有删除任务
    const deleteTasks = queue.tasks.filter((t) =>
      t.type.toString().startsWith("DELETE_"),
    );
    expect(deleteTasks).toHaveLength(0);

    // 版本任务不应该有依赖
    const versionTask = queue.tasks.find(
      (t) => t.type === SyncDataType.VERSION,
    );
    expect(versionTask!.dependencies).toHaveLength(0);
  });
});
