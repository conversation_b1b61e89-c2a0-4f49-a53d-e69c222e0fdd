/**
 * 队列同步服务单元测试
 */

import { Level } from "@/types/api/prompt";
import { beforeEach, describe, expect, it, vi } from "vitest";
import {
  CompleteDraftVersionData,
  DraftAbility,
  DraftModule,
  DraftModuleLabel,
  DraftSharedVariable,
  DraftVersion,
} from "../../../types";
import { QueueSyncService } from "../queue-sync-service";
import { QueueStatus, QueueTaskStatus, SyncOptions } from "../types";

// Mock 服务
vi.mock("@/services/prompt-version-service", () => ({
  PromptVersionService: {
    addOrUpdatePromptVersion: vi.fn(),
  },
}));

vi.mock("@/services/module-service", () => ({
  ModuleService: {
    addOrUpdateModuleLabel: vi.fn(),
    addOrUpdateModule: vi.fn(),
  },
}));

vi.mock("@/services/shared-variable-service", () => ({
  SharedVariableService: {
    addOrUpdateSharedVariable: vi.fn(),
  },
}));

vi.mock("@/services/ability-service", () => ({
  AbilityService: {
    addOrUpdateAbility: vi.fn(),
  },
}));

import { AbilityService } from "@/services/ability-service";
import { ModuleService } from "@/services/module-service";
import { PromptVersionService } from "@/services/prompt-version-service";
import { SharedVariableService } from "@/services/shared-variable-service";

describe("QueueSyncService", () => {
  let syncService: QueueSyncService;
  let progressCallback: ReturnType<typeof vi.fn>;
  let getDraftDataCallback: ReturnType<typeof vi.fn>;
  let checkDataChangedCallback: ReturnType<typeof vi.fn>;
  let getDraftDataByVersionCallback: ReturnType<typeof vi.fn>;

  // 测试数据
  const mockVersion: DraftVersion = {
    id: "draft-version-1",
    versionName: "测试版本",
    createdAt: "2024-01-01T00:00:00.000Z",
    updatedAt: "2024-01-01T00:00:00.000Z",
  };

  const mockModuleLabel: DraftModuleLabel = {
    id: "draft-label-1",
    name: "测试标签",
    promptVersionId: "draft-version-1",
    createdAt: "2024-01-01T00:00:00.000Z",
    updatedAt: "2024-01-01T00:00:00.000Z",
  };

  const mockSharedVariable: DraftSharedVariable = {
    id: "draft-variable-1",
    name: "测试变量",
    content: "测试变量内容",
    promptVersionId: "draft-version-1",
    level: Level.Product,
    definition: "测试变量定义",
    promptName: "测试变量提示词",
    createdAt: "2024-01-01T00:00:00.000Z",
    updatedAt: "2024-01-01T00:00:00.000Z",
  };

  const mockModule: DraftModule = {
    id: "draft-module-1",
    name: "测试模块",
    prompt: "测试内容",
    level: Level.Product,
    promptVersionId: "draft-version-1",
    moduleLabelIds: "",
    createdAt: "2024-01-01T00:00:00.000Z",
    updatedAt: "2024-01-01T00:00:00.000Z",
  };

  const mockAbility: DraftAbility = {
    id: "draft-ability-1",
    name: "测试能力",
    content: "测试内容",
    promptVersionId: "draft-version-1",
    createdAt: "2024-01-01T00:00:00.000Z",
    updatedAt: "2024-01-01T00:00:00.000Z",
  };

  const mockData: CompleteDraftVersionData = {
    draftVersion: mockVersion,
    draftModuleLabels: [mockModuleLabel],
    draftSharedVariables: [mockSharedVariable],
    draftModules: [mockModule],
    draftAbilities: [mockAbility],
  };

  beforeEach(() => {
    vi.clearAllMocks();
    progressCallback = vi.fn();

    // 创建数据获取回调 mock
    getDraftDataCallback = vi.fn((task) => {
      switch (task.type) {
        case "VERSION":
          return mockData.draftVersion;
        case "MODULE_LABEL":
          return mockData.draftModuleLabels.find((l) => l.id === task.draftId);
        case "SHARED_VARIABLE":
          return mockData.draftSharedVariables.find(
            (v) => v.id === task.draftId,
          );
        case "MODULE":
          return mockData.draftModules.find((m) => m.id === task.draftId);
        case "ABILITY":
          return mockData.draftAbilities.find((a) => a.id === task.draftId);
        default:
          return null;
      }
    });

    // 创建数据变更检查回调 mock
    checkDataChangedCallback = vi.fn(() => false);

    getDraftDataByVersionCallback = vi.fn((versionId) => {
      if (versionId === "draft-version-1") {
        return mockData;
      }

      return null;
    });

    syncService = new QueueSyncService(
      {},
      progressCallback,
      getDraftDataCallback,
      checkDataChangedCallback,
      getDraftDataByVersionCallback,
    );

    // Mock 成功响应
    vi.mocked(PromptVersionService.addOrUpdatePromptVersion).mockResolvedValue({
      success: true,
      data: "synced-version-123",
      message: "成功",
    });

    vi.mocked(ModuleService.addOrUpdateModuleLabel).mockResolvedValue({
      success: true,
      data: "synced-label-123",
      message: "成功",
    });

    vi.mocked(
      SharedVariableService.addOrUpdateSharedVariable,
    ).mockResolvedValue({
      success: true,
      data: "synced-variable-123",
      message: "成功",
    });

    vi.mocked(ModuleService.addOrUpdateModule).mockResolvedValue({
      success: true,
      data: "synced-module-123",
      message: "成功",
    });

    vi.mocked(AbilityService.addOrUpdateAbility).mockResolvedValue({
      success: true,
      data: "synced-ability-123",
      message: "成功",
    });
  });

  describe("createSyncQueue", () => {
    it("应该创建包含正确任务的队列", async () => {
      const queue = await syncService.createSyncQueue(
        "draft-version-1",
        mockData,
        {
          remark: "测试备注",
          versionName: "测试版本",
        },
      );

      expect(queue.id).toMatch(/^queue-/);
      expect(queue.versionId).toBe("draft-version-1");
      expect(queue.status).toBe(QueueStatus.PENDING);
      expect(queue.tasks).toHaveLength(5); // 版本 + 标签 + 变量 + 模块 + 能力

      // 检查任务类型和依赖关系
      const versionTask = queue.tasks.find((t) => t.type === "VERSION");
      const labelTask = queue.tasks.find((t) => t.type === "MODULE_LABEL");
      const variableTask = queue.tasks.find(
        (t) => t.type === "SHARED_VARIABLE",
      );
      const moduleTask = queue.tasks.find((t) => t.type === "MODULE");
      const abilityTask = queue.tasks.find((t) => t.type === "ABILITY");

      expect(versionTask?.dependencies).toEqual([]);
      expect(labelTask?.dependencies).toEqual([versionTask?.id]);
      expect(variableTask?.dependencies).toEqual([versionTask?.id]);
      expect(moduleTask?.dependencies).toContain(versionTask?.id);
      expect(moduleTask?.dependencies).toContain(labelTask?.id);
      expect(moduleTask?.dependencies).toContain(variableTask?.id);
      expect(abilityTask?.dependencies).toContain(versionTask?.id);
      expect(abilityTask?.dependencies).toContain(moduleTask?.id);
    });

    it("应该验证数据并抛出错误", async () => {
      const invalidData = {
        ...mockData,
        draftVersion: { ...mockVersion, versionName: "" }, // 无效的版本名称
      };

      await expect(
        syncService.createSyncQueue("draft-version-1", invalidData, {
          remark: "测试备注",
          versionName: "测试版本",
        }),
      ).rejects.toThrow("数据验证失败");
    });
  });

  describe("executeQueue", () => {
    it("应该成功执行队列中的所有任务", async () => {
      const queue = await syncService.createSyncQueue(
        "draft-version-1",
        mockData,
        {
          remark: "测试备注",
          versionName: "测试版本",
        },
      );
      const result = await syncService.executeQueue(queue);

      expect(result.success).toBe(true);
      expect(result.syncedVersionId).toBe("synced-version-123");
      expect(result.queueId).toBe(queue.id);

      // 检查所有任务都成功完成
      expect(
        queue.tasks.every((t) => t.status === QueueTaskStatus.SUCCESS),
      ).toBe(true);
      expect(queue.status).toBe(QueueStatus.SUCCESS);

      // 检查进度回调被调用
      expect(progressCallback).toHaveBeenCalled();
    });

    it("应该处理任务失败", async () => {
      // Mock 模块标签同步失败
      vi.mocked(ModuleService.addOrUpdateModuleLabel).mockRejectedValue(
        new Error("模块标签同步失败"),
      );

      // 使用较短的重试延迟避免测试超时
      const fastSyncService = new QueueSyncService(
        { retryDelay: 1 },
        progressCallback,
        getDraftDataCallback,
        checkDataChangedCallback,
        getDraftDataByVersionCallback,
      );
      const queue = await fastSyncService.createSyncQueue(
        "draft-version-1",
        mockData,
        {
          remark: "测试备注",
          versionName: "测试版本",
        },
      );
      const result = await fastSyncService.executeQueue(queue);

      expect(result.success).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors![0].message).toContain("模块标签同步失败");
      expect(queue.status).toBe(QueueStatus.FAILED);

      // 检查失败任务的状态
      const labelTask = queue.tasks.find((t) => t.type === "MODULE_LABEL");
      expect(labelTask?.status).toBe(QueueTaskStatus.FAILED);
    }, 10000); // 增加超时时间

    it("应该跳过依赖失败的任务", async () => {
      // Mock 版本同步失败
      vi.mocked(
        PromptVersionService.addOrUpdatePromptVersion,
      ).mockRejectedValue(new Error("版本同步失败"));

      // 使用较短的重试延迟避免测试超时
      const fastSyncService = new QueueSyncService(
        { retryDelay: 1 },
        progressCallback,
        getDraftDataCallback,
        checkDataChangedCallback,
      );
      const queue = await fastSyncService.createSyncQueue(
        "draft-version-1",
        mockData,
        {
          remark: "测试备注",
          versionName: "测试版本",
        },
      );
      const result = await fastSyncService.executeQueue(queue);

      expect(result.success).toBe(false);
      expect(queue.status).toBe(QueueStatus.FAILED);

      // 检查版本任务失败
      const versionTask = queue.tasks.find((t) => t.type === "VERSION");
      expect(versionTask?.status).toBe(QueueTaskStatus.FAILED);

      // 检查其他任务被跳过
      const otherTasks = queue.tasks.filter((t) => t.type !== "VERSION");
      expect(
        otherTasks.every((t) => t.status === QueueTaskStatus.SKIPPED),
      ).toBe(true);
    }, 10000); // 增加超时时间

    it("应该正确更新ID映射", async () => {
      const queue = await syncService.createSyncQueue(
        "draft-version-1",
        mockData,
        {
          remark: "测试备注",
          versionName: "测试版本",
        },
      );
      await syncService.executeQueue(queue);

      expect(queue.idMapping.versionIds["draft-version-1"]).toBe(
        "synced-version-123",
      );
      expect(queue.idMapping.moduleLabelIds["draft-label-1"]).toBe(
        "synced-label-123",
      );
      expect(queue.idMapping.sharedVariableIds["draft-variable-1"]).toBe(
        "synced-variable-123",
      );
      expect(queue.idMapping.moduleIds["draft-module-1"]).toBe(
        "synced-module-123",
      );
      expect(queue.idMapping.abilityIds["draft-ability-1"]).toBe(
        "synced-ability-123",
      );
    });
  });

  describe("重试机制", () => {
    it("应该重试失败的任务", async () => {
      let callCount = 0;
      vi.mocked(ModuleService.addOrUpdateModuleLabel).mockImplementation(() => {
        callCount++;
        if (callCount <= 2) {
          // 前两次失败
          return Promise.reject(new Error("临时错误"));
        }
        return Promise.resolve({
          success: true,
          data: "synced-label-123",
          message: "成功",
        });
      });

      const options: SyncOptions = { retryCount: 2, retryDelay: 1 };
      const retryService = new QueueSyncService(
        options,
        progressCallback,
        getDraftDataCallback,
        checkDataChangedCallback,
        getDraftDataByVersionCallback,
      );

      const queue = await retryService.createSyncQueue(
        "draft-version-1",
        mockData,
        {
          remark: "测试备注",
          versionName: "测试版本",
        },
      );
      const result = await retryService.executeQueue(queue);

      expect(result.success).toBe(true);
      expect(callCount).toBe(3); // 1次原始调用 + 2次重试
    });

    it("应该在重试次数用完后失败", async () => {
      // 重置mock计数
      vi.clearAllMocks();

      // 只让模块标签失败，其他都成功
      vi.mocked(
        PromptVersionService.addOrUpdatePromptVersion,
      ).mockResolvedValue({
        success: true,
        data: "synced-version-123",
        message: "成功",
      });

      vi.mocked(ModuleService.addOrUpdateModuleLabel).mockRejectedValue(
        new Error("持续错误"),
      );

      vi.mocked(
        SharedVariableService.addOrUpdateSharedVariable,
      ).mockResolvedValue({
        success: true,
        data: "synced-variable-123",
        message: "成功",
      });

      vi.mocked(ModuleService.addOrUpdateModule).mockResolvedValue({
        success: true,
        data: "synced-module-123",
        message: "成功",
      });

      vi.mocked(AbilityService.addOrUpdateAbility).mockResolvedValue({
        success: true,
        data: "synced-ability-123",
        message: "成功",
      });

      const options: SyncOptions = { retryCount: 1, retryDelay: 1 };
      const retryService = new QueueSyncService(
        options,
        progressCallback,
        getDraftDataCallback,
        checkDataChangedCallback,
        getDraftDataByVersionCallback,
      );

      const queue = await retryService.createSyncQueue(
        "draft-version-1",
        mockData,
        {
          remark: "测试备注",
          versionName: "测试版本",
        },
      );
      const result = await retryService.executeQueue(queue);

      expect(result.success).toBe(false);
      expect(ModuleService.addOrUpdateModuleLabel).toHaveBeenCalledTimes(2); // 1次原始 + 1次重试
    });
  });
});
