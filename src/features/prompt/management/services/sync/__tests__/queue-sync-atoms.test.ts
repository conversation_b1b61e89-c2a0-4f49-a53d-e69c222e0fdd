/**
 * 队列同步状态管理单元测试
 */

import { Level } from "@/types/api/prompt";
import "fake-indexeddb/auto";
import { createStore } from "jotai";
import { beforeEach, describe, expect, it, vi } from "vitest";
import {
  abilityDb,
  moduleDb,
  moduleLabelDb,
  selectedVersionIdAtom,
  sharedVariableDb,
  syncQueueDb,
  versionDb,
} from "../../../atoms/core";
import {
  DraftModule,
  DraftModuleLabel,
  DraftSharedVariable,
  DraftVersion,
} from "../../../types";
import {
  activeQueueIdAtom,
  canRetryAtom,
  cleanupDraftDataAtom,
  createSyncQueueAtom,
  executeSyncQueueAtom,
  getAllQueuesAtom,
  getDraftDataByVersionAtom,
  getQueueProgressTextAtom,
  hasDataToSyncAtom,
  isCurrentVersionQueueRunningAtom,
  quickSyncAtom,
  runningQueueIdAtom,
} from "../queue-sync-atoms";
import {
  QueueStatus,
  QueueTaskStatus,
  SyncDataType,
  SyncQueue,
} from "../types";

// Mock 服务
vi.mock("@/services/prompt-version-service", () => ({
  PromptVersionService: {
    addOrUpdatePromptVersion: vi.fn(),
  },
}));

vi.mock("@/services/module-service", () => ({
  ModuleService: {
    addOrUpdateModuleLabel: vi.fn(),
    addOrUpdateModule: vi.fn(),
  },
}));

vi.mock("@/services/shared-variable-service", () => ({
  SharedVariableService: {
    addOrUpdateSharedVariable: vi.fn(),
  },
}));

vi.mock("@/services/ability-service", () => ({
  AbilityService: {
    addOrUpdateAbility: vi.fn(),
  },
}));

vi.mock("../../atoms", () => ({
  versionsAtom: {
    refetch: vi.fn(),
  },
}));

import { AbilityService } from "@/services/ability-service";
import { ModuleService } from "@/services/module-service";
import { PromptVersionService } from "@/services/prompt-version-service";
import { SharedVariableService } from "@/services/shared-variable-service";

describe("队列同步状态管理", () => {
  let store: ReturnType<typeof createStore>;

  // 测试数据
  const mockVersion: DraftVersion = {
    id: "draft-version-1",
    versionName: "测试版本",
    createdAt: "2024-01-01T00:00:00.000Z",
    updatedAt: "2024-01-01T00:00:00.000Z",
  };

  const mockModuleLabel: DraftModuleLabel = {
    id: "draft-label-1",
    name: "测试标签",
    promptVersionId: "draft-version-1",
    createdAt: "2024-01-01T00:00:00.000Z",
    updatedAt: "2024-01-01T00:00:00.000Z",
  };

  const mockSharedVariable: DraftSharedVariable = {
    id: "draft-variable-1",
    name: "测试变量",
    content: "测试变量内容",
    level: Level.Product,
    definition: "测试变量定义",
    promptName: "测试变量提示词",
    promptVersionId: "draft-version-1",
    createdAt: "2024-01-01T00:00:00.000Z",
    updatedAt: "2024-01-01T00:00:00.000Z",
  };

  const mockModule: DraftModule = {
    id: "draft-module-1",
    name: "测试模块",
    prompt: "测试内容",
    level: Level.Product,
    promptVersionId: "draft-version-1",
    moduleLabelIds: "",
    createdAt: "2024-01-01T00:00:00.000Z",
    updatedAt: "2024-01-01T00:00:00.000Z",
  };

  beforeEach(async () => {
    vi.clearAllMocks();
    store = createStore();

    // 清理所有数据
    await store.set(versionDb.clear);
    await store.set(moduleLabelDb.clear);
    await store.set(sharedVariableDb.clear);
    await store.set(moduleDb.clear);
    await store.set(abilityDb.clear);
    await store.set(syncQueueDb.clear);

    // Mock 成功响应
    vi.mocked(PromptVersionService.addOrUpdatePromptVersion).mockResolvedValue({
      success: true,
      data: "synced-version-123",
      message: "成功",
    });

    vi.mocked(ModuleService.addOrUpdateModuleLabel).mockResolvedValue({
      success: true,
      data: "synced-label-123",
      message: "成功",
    });

    vi.mocked(
      SharedVariableService.addOrUpdateSharedVariable,
    ).mockResolvedValue({
      success: true,
      data: "synced-variable-123",
      message: "成功",
    });

    vi.mocked(ModuleService.addOrUpdateModule).mockResolvedValue({
      success: true,
      data: "synced-module-123",
      message: "成功",
    });

    vi.mocked(AbilityService.addOrUpdateAbility).mockResolvedValue({
      success: true,
      data: "synced-ability-123",
      message: "成功",
    });
  });

  describe("getDraftDataByVersionAtom", () => {
    it("应该获取指定版本的所有草稿数据", async () => {
      // 添加测试数据
      await store.set(versionDb.set, mockVersion.id, mockVersion);
      await store.set(moduleLabelDb.set, mockModuleLabel.id, mockModuleLabel);
      await store.set(moduleDb.set, mockModule.id, mockModule);

      const getDraftData = store.get(getDraftDataByVersionAtom);
      const data = await getDraftData("draft-version-1")!;

      expect(data?.draftVersion).toEqual(mockVersion);
      expect(data?.draftModuleLabels).toHaveLength(1);
      expect(data?.draftModuleLabels[0]).toEqual(mockModuleLabel);
      expect(data?.draftModules).toHaveLength(1);
      expect(data?.draftModules[0]).toEqual(mockModule);
    });

    it("应该在版本不存在时为空", async () => {
      const getDraftData = store.get(getDraftDataByVersionAtom);

      expect(await getDraftData("non-existent")).toBeNull();
    });
  });

  describe("hasDataToSyncAtom", () => {
    it("应该检查版本是否有数据可同步", async () => {
      await store.set(versionDb.set, mockVersion.id, mockVersion);
      await store.set(moduleLabelDb.set, mockModuleLabel.id, mockModuleLabel);

      const hasDataToSync = store.get(hasDataToSyncAtom);
      const result = hasDataToSync("draft-version-1");

      expect(result).toBe(true);
    });

    it("应该在没有数据时返回false", () => {
      const hasDataToSync = store.get(hasDataToSyncAtom);
      const result = hasDataToSync("draft-version-1");

      expect(result).toBe(false);
    });
  });

  describe("createSyncQueueAtom", () => {
    it("应该创建同步队列", async () => {
      // 设置当前选中的版本ID
      store.set(selectedVersionIdAtom, "draft-version-1");

      await store.set(versionDb.set, mockVersion.id, mockVersion);
      await store.set(moduleLabelDb.set, mockModuleLabel.id, mockModuleLabel);

      const queue = await store.set(
        createSyncQueueAtom,
        "draft-version-1",
        undefined,
        {
          remark: "测试备注",
          versionName: "测试版本",
        },
      );

      expect(queue.id).toMatch(/^queue-/);
      expect(queue.versionId).toBe("draft-version-1");
      expect(queue.status).toBe(QueueStatus.PENDING);
      expect(queue.tasks.length).toBeGreaterThan(0);

      // 检查队列是否保存到数据库
      const savedQueues = store.get(getAllQueuesAtom);
      expect(savedQueues).toHaveLength(1);
      expect(savedQueues[0].id).toBe(queue.id);

      // 检查活跃队列ID是否设置
      const activeQueueId = store.get(activeQueueIdAtom);
      expect(activeQueueId).toBe(queue.id);
    });
  });

  describe("executeSyncQueueAtom", () => {
    it("应该执行同步队列", async () => {
      // 设置当前选中的版本ID
      store.set(selectedVersionIdAtom, "draft-version-1");

      await store.set(versionDb.set, mockVersion.id, mockVersion);
      await store.set(moduleLabelDb.set, mockModuleLabel.id, mockModuleLabel);

      const queue = await store.set(
        createSyncQueueAtom,
        "draft-version-1",
        undefined,
        {
          remark: "测试备注",
          versionName: "测试版本",
        },
      );
      const result = await store.set(executeSyncQueueAtom, queue);

      expect(result.success).toBe(true);
      expect(result.queueId).toBe(queue.id);
      expect(result.syncedVersionId).toBe("synced-version-123");
    });
  });

  describe("getQueueProgressTextAtom", () => {
    it("应该返回队列进度文本", async () => {
      // 设置当前选中的版本ID
      store.set(selectedVersionIdAtom, "draft-version-1");

      await store.set(versionDb.set, mockVersion.id, mockVersion);
      await store.set(moduleLabelDb.set, mockModuleLabel.id, mockModuleLabel);

      // 创建一个队列来测试进度
      const queue = await store.set(
        createSyncQueueAtom,
        "draft-version-1",
        undefined,
        {
          remark: "测试备注",
          versionName: "测试版本",
        },
      );

      // 模拟队列正在运行
      const runningQueue = { ...queue, status: QueueStatus.RUNNING };
      store.set(syncQueueDb.set, queue.id, runningQueue);

      const progressText = store.get(getQueueProgressTextAtom);
      expect(progressText).toBe("同步中...");
    });

    it("应该在没有队列时返回空字符串", () => {
      // 设置当前选中的版本ID
      store.set(selectedVersionIdAtom, "draft-version-1");

      const progressText = store.get(getQueueProgressTextAtom);
      expect(progressText).toBe("");
    });
  });

  describe("canRetryAtom", () => {
    it("应该检查是否可以重试", async () => {
      // 先设置版本数据
      await store.set(versionDb.set, mockVersion.id, mockVersion);

      // 计算关键数据哈希（版本只包含ID）
      const criticalData = { id: mockVersion.id };
      const dataHash = JSON.stringify(
        criticalData,
        Object.keys(criticalData).sort(),
      );
      let hash = 0;
      for (let i = 0; i < dataHash.length; i++) {
        const char = dataHash.charCodeAt(i);
        hash = (hash << 5) - hash + char;
        hash = hash & hash;
      }
      const hashStr = hash.toString(36);

      // 创建一个失败的队列
      const queue: SyncQueue = {
        id: "test-queue",
        versionId: "draft-version-1",
        status: QueueStatus.FAILED,
        options: {},
        versionInfo: {
          remark: "测试备注",
          versionName: "测试版本",
        },
        tasks: [
          {
            id: "task-1",
            type: SyncDataType.VERSION,
            status: QueueTaskStatus.FAILED,
            draftId: "draft-version-1",
            dataHash: hashStr,
            dependencies: [],
            retryCount: 0,
            maxRetries: 3,
            createdAt: Date.now(),
            displayName: "测试版本",
          },
        ],
        idMapping: {
          versionIds: {},
          moduleLabelIds: {},
          sharedVariableIds: {},
          moduleIds: {},
          abilityIds: {},
        },
        createdAt: Date.now(),
      };

      await store.set(syncQueueDb.set, queue.id, queue);

      const canRetry = store.get(canRetryAtom);
      expect(canRetry(queue.id)).toBe(true);
    });

    it("应该允许在非关键字段变更后重试", async () => {
      // 先设置版本数据
      await store.set(versionDb.set, mockVersion.id, mockVersion);

      // 计算关键数据哈希（版本只包含ID）
      const criticalData = { id: mockVersion.id };
      const dataHash = JSON.stringify(
        criticalData,
        Object.keys(criticalData).sort(),
      );
      let hash = 0;
      for (let i = 0; i < dataHash.length; i++) {
        const char = dataHash.charCodeAt(i);
        hash = (hash << 5) - hash + char;
        hash = hash & hash;
      }
      const hashStr = hash.toString(36);

      // 创建一个失败的队列
      const queue: SyncQueue = {
        id: "test-queue",
        versionId: "draft-version-1",
        status: QueueStatus.FAILED,
        options: {},
        versionInfo: {
          remark: "测试备注",
          versionName: "测试版本",
        },
        tasks: [
          {
            id: "task-1",
            type: SyncDataType.VERSION,
            status: QueueTaskStatus.FAILED,
            draftId: "draft-version-1",
            dataHash: hashStr,
            dependencies: [],
            retryCount: 0,
            maxRetries: 3,
            createdAt: Date.now(),
            displayName: "测试版本",
          },
        ],
        idMapping: {
          versionIds: {},
          moduleLabelIds: {},
          sharedVariableIds: {},
          moduleIds: {},
          abilityIds: {},
        },
        createdAt: Date.now(),
      };

      await store.set(syncQueueDb.set, queue.id, queue);

      // 修改版本名称（非关键字段）
      const updatedVersion = {
        ...mockVersion,
        versionName: "修改后的版本名称",
      };
      await store.set(versionDb.set, mockVersion.id, updatedVersion);

      // 应该仍然可以重试，因为只是名称变更，不影响依赖关系
      const canRetry = store.get(canRetryAtom);
      expect(canRetry(queue.id)).toBe(true);
    });
  });

  describe("cleanupDraftDataAtom", () => {
    it("应该清理指定版本的所有草稿数据", async () => {
      await store.set(versionDb.set, mockVersion.id, mockVersion);
      await store.set(moduleLabelDb.set, mockModuleLabel.id, mockModuleLabel);
      await store.set(moduleDb.set, mockModule.id, mockModule);

      store.set(cleanupDraftDataAtom, "draft-version-1");

      const versions = store.get(versionDb.values);
      const labels = store.get(moduleLabelDb.values);
      const modules = store.get(moduleDb.values);

      expect(Array.from(versions)).toHaveLength(0);
      expect(Array.from(labels)).toHaveLength(0);
      expect(Array.from(modules)).toHaveLength(0);
    });
  });

  describe("quickSyncAtom", () => {
    it("应该快速同步（创建并执行队列）", async () => {
      // 设置当前选中的版本ID
      store.set(selectedVersionIdAtom, "draft-version-1");

      await store.set(versionDb.set, mockVersion.id, mockVersion);
      await store.set(moduleLabelDb.set, mockModuleLabel.id, mockModuleLabel);
      await store.set(
        sharedVariableDb.set,
        mockSharedVariable.id,
        mockSharedVariable,
      );

      const result = await store.set(
        quickSyncAtom,
        "draft-version-1",
        undefined,
        {
          remark: "测试备注",
          versionName: "测试版本",
        },
      );

      if (!result.success) {
        console.log("同步失败:", result.errors);
      }

      expect(result.success).toBe(true);
      expect(result.syncedVersionId).toBe("synced-version-123");

      // 检查队列是否创建
      const queues = store.get(getAllQueuesAtom);
      expect(queues).toHaveLength(1);
    });
  });

  describe("版本关联功能", () => {
    it("应该支持多版本队列管理", async () => {
      // 创建两个不同的版本
      const version1 = { ...mockVersion, id: "draft-version-1" };
      const version2 = { ...mockVersion, id: "draft-version-2" };

      await store.set(versionDb.set, version1.id, version1);
      await store.set(versionDb.set, version2.id, version2);
      await store.set(moduleLabelDb.set, mockModuleLabel.id, mockModuleLabel);

      // 为版本1创建队列
      store.set(selectedVersionIdAtom, "draft-version-1");
      const queue1 = await store.set(
        createSyncQueueAtom,
        "draft-version-1",
        {},
        {
          remark: "测试备注",
          versionName: "测试版本",
        },
      );

      // 为版本2创建队列
      store.set(selectedVersionIdAtom, "draft-version-2");
      const queue2 = await store.set(
        createSyncQueueAtom,
        "draft-version-2",
        {},
        {
          remark: "测试备注",
          versionName: "测试版本",
        },
      );

      // 验证两个队列都存在
      const allQueues = store.get(getAllQueuesAtom);
      expect(allQueues).toHaveLength(2);

      // 验证版本1的活跃队列
      store.set(selectedVersionIdAtom, "draft-version-1");
      const activeQueue1 = store.get(activeQueueIdAtom);
      expect(activeQueue1).toBe(queue1.id);

      // 验证版本2的活跃队列
      store.set(selectedVersionIdAtom, "draft-version-2");
      const activeQueue2 = store.get(activeQueueIdAtom);
      expect(activeQueue2).toBe(queue2.id);

      // 切换回版本1，验证状态恢复
      store.set(selectedVersionIdAtom, "draft-version-1");
      const restoredActiveQueue1 = store.get(activeQueueIdAtom);
      expect(restoredActiveQueue1).toBe(queue1.id);
    });
  });

  describe("运行状态管理", () => {
    it("应该正确管理队列运行状态", async () => {
      // 设置当前选中的版本ID
      store.set(selectedVersionIdAtom, "draft-version-1");

      await store.set(versionDb.set, mockVersion.id, mockVersion);
      await store.set(moduleLabelDb.set, mockModuleLabel.id, mockModuleLabel);

      // 创建队列
      const queue = await store.set(
        createSyncQueueAtom,
        "draft-version-1",
        {},
        {
          remark: "测试备注",
          versionName: "测试版本",
        },
      );

      // 初始状态：没有队列在运行
      expect(store.get(runningQueueIdAtom)).toBeNull();
      expect(store.get(isCurrentVersionQueueRunningAtom)).toBe(false);

      // 手动设置运行状态来测试
      store.set(runningQueueIdAtom, queue.id);

      // 验证运行状态
      expect(store.get(runningQueueIdAtom)).toBe(queue.id);
      expect(store.get(isCurrentVersionQueueRunningAtom)).toBe(true);

      // 清除运行状态
      store.set(runningQueueIdAtom, null);

      // 验证状态已清除
      expect(store.get(runningQueueIdAtom)).toBeNull();
      expect(store.get(isCurrentVersionQueueRunningAtom)).toBe(false);
    });
  });
});
