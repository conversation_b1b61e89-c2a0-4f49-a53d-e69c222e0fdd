import { beforeEach, describe, expect, it, vi } from "vitest";
import { QueueSyncService } from "../queue-sync-service";
import { SyncDataType, SyncOptions } from "../types";

// Mock 服务
vi.mock("@/services/ability-service", () => ({
  AbilityService: {
    deleteAbility: vi.fn(),
  },
}));

vi.mock("@/services/module-service", () => ({
  ModuleService: {
    deleteModule: vi.fn(),
    deleteModuleLabel: vi.fn(),
  },
}));

vi.mock("@/services/shared-variable-service", () => ({
  SharedVariableService: {
    deleteSharedVariable: vi.fn(),
  },
}));

vi.mock("@/services/prompt-version-service", () => ({
  PromptVersionService: {
    addOrUpdatePromptVersion: vi.fn(),
  },
}));

// Mock utils
vi.mock("../../../utils", () => ({
  getCompleteVersionData: vi.fn(),
}));

describe("删除任务执行", () => {
  let syncService: QueueSyncService;
  const mockOptions: SyncOptions = {
    cleanupOnSuccess: false,
    timeout: 5000,
    retryCount: 1,
    retryDelay: 100,
    autoStart: false,
  };

  beforeEach(() => {
    vi.clearAllMocks();
    syncService = new QueueSyncService(mockOptions);
  });

  describe("executeDeleteAbilityTask", () => {
    it("应该成功删除能力", async () => {
      const { AbilityService } = await import("@/services/ability-service");
      (AbilityService.deleteAbility as any).mockResolvedValue({
        success: true,
      });

      // 通过反射访问私有方法进行测试
      const result = await (syncService as any).executeDeleteAbilityTask(
        "ability-1",
      );

      expect(AbilityService.deleteAbility).toHaveBeenCalledWith({
        abilityId: "ability-1",
      });
      expect(result).toBe("ability-1");
    });

    it("应该在删除失败时抛出错误", async () => {
      const { AbilityService } = await import("@/services/ability-service");
      (AbilityService.deleteAbility as any).mockResolvedValue({
        success: false,
      });

      await expect(
        (syncService as any).executeDeleteAbilityTask("ability-1"),
      ).rejects.toThrow("能力删除失败：ID=ability-1");
    });
  });

  describe("executeDeleteModuleTask", () => {
    it("应该成功删除模块", async () => {
      const { ModuleService } = await import("@/services/module-service");
      (ModuleService.deleteModule as any).mockResolvedValue({ success: true });

      const result = await (syncService as any).executeDeleteModuleTask(
        "module-1",
      );

      expect(ModuleService.deleteModule).toHaveBeenCalledWith({
        moduleId: "module-1",
      });
      expect(result).toBe("module-1");
    });

    it("应该在删除失败时抛出错误", async () => {
      const { ModuleService } = await import("@/services/module-service");
      (ModuleService.deleteModule as any).mockResolvedValue({ success: false });

      await expect(
        (syncService as any).executeDeleteModuleTask("module-1"),
      ).rejects.toThrow("模块删除失败：ID=module-1");
    });
  });

  describe("executeDeleteSharedVariableTask", () => {
    it("应该成功删除共享变量", async () => {
      const { SharedVariableService } = await import(
        "@/services/shared-variable-service"
      );
      (SharedVariableService.deleteSharedVariable as any).mockResolvedValue({
        success: true,
      });

      const result = await (syncService as any).executeDeleteSharedVariableTask(
        "variable-1",
      );

      expect(SharedVariableService.deleteSharedVariable).toHaveBeenCalledWith({
        sharedVariableId: "variable-1",
      });
      expect(result).toBe("variable-1");
    });

    it("应该在删除失败时抛出错误", async () => {
      const { SharedVariableService } = await import(
        "@/services/shared-variable-service"
      );
      (SharedVariableService.deleteSharedVariable as any).mockResolvedValue({
        success: false,
      });

      await expect(
        (syncService as any).executeDeleteSharedVariableTask("variable-1"),
      ).rejects.toThrow("共享变量删除失败：ID=variable-1");
    });
  });

  describe("executeDeleteModuleLabelTask", () => {
    it("应该成功删除模块标签", async () => {
      const { ModuleService } = await import("@/services/module-service");
      (ModuleService.deleteModuleLabel as any).mockResolvedValue({
        success: true,
      });

      const result = await (syncService as any).executeDeleteModuleLabelTask(
        "label-1",
      );

      expect(ModuleService.deleteModuleLabel).toHaveBeenCalledWith({
        moduleLabelId: "label-1",
      });
      expect(result).toBe("label-1");
    });

    it("应该在删除失败时抛出错误", async () => {
      const { ModuleService } = await import("@/services/module-service");
      (ModuleService.deleteModuleLabel as any).mockResolvedValue({
        success: false,
      });

      await expect(
        (syncService as any).executeDeleteModuleLabelTask("label-1"),
      ).rejects.toThrow("模块标签删除失败：ID=label-1");
    });
  });

  describe("createDeleteTasks", () => {
    it("应该创建正确的删除任务", () => {
      const deletedItems = {
        deletedAbilities: [{ id: "ability-1", name: "能力1" }],
        deletedModules: [{ id: "module-1", name: "模块1" }],
        deletedSharedVariables: [{ id: "variable-1", name: "变量1" }],
        deletedModuleLabels: [{ id: "label-1", name: "标签1" }],
      };

      const tasks = (syncService as any).createDeleteTasks(deletedItems, 0);

      expect(tasks).toHaveLength(4);

      // 验证能力删除任务
      const abilityDeleteTask = tasks.find(
        (t: any) => t.type === SyncDataType.DELETE_ABILITY,
      );
      expect(abilityDeleteTask).toBeDefined();
      expect(abilityDeleteTask.draftId).toBe("ability-1");
      expect(abilityDeleteTask.displayName).toBe("删除能力: 能力1");
      expect(abilityDeleteTask.dependencies).toHaveLength(0);

      // 验证模块删除任务
      const moduleDeleteTask = tasks.find(
        (t: any) => t.type === SyncDataType.DELETE_MODULE,
      );
      expect(moduleDeleteTask).toBeDefined();
      expect(moduleDeleteTask.draftId).toBe("module-1");
      expect(moduleDeleteTask.displayName).toBe("删除模块: 模块1");
      expect(moduleDeleteTask.dependencies).toContain(abilityDeleteTask.id);

      // 验证共享变量删除任务
      const variableDeleteTask = tasks.find(
        (t: any) => t.type === SyncDataType.DELETE_SHARED_VARIABLE,
      );
      expect(variableDeleteTask).toBeDefined();
      expect(variableDeleteTask.draftId).toBe("variable-1");
      expect(variableDeleteTask.displayName).toBe("删除共享变量: 变量1");
      expect(variableDeleteTask.dependencies).toContain(abilityDeleteTask.id);
      expect(variableDeleteTask.dependencies).toContain(moduleDeleteTask.id);

      // 验证模块标签删除任务
      const labelDeleteTask = tasks.find(
        (t: any) => t.type === SyncDataType.DELETE_MODULE_LABEL,
      );
      expect(labelDeleteTask).toBeDefined();
      expect(labelDeleteTask.draftId).toBe("label-1");
      expect(labelDeleteTask.displayName).toBe("删除模块标签: 标签1");
      expect(labelDeleteTask.dependencies).toContain(moduleDeleteTask.id);
    });

    it("应该在没有删除项时返回空数组", () => {
      const deletedItems = {
        deletedAbilities: [],
        deletedModules: [],
        deletedSharedVariables: [],
        deletedModuleLabels: [],
      };

      const tasks = (syncService as any).createDeleteTasks(deletedItems, 0);

      expect(tasks).toHaveLength(0);
    });
  });
});
