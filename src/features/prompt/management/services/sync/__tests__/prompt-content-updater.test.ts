import { describe, expect, it } from "vitest";
import { PromptContentUpdater } from "../prompt-content-updater";

describe("PromptContentUpdater", () => {
  describe("updateSharedVariableIds", () => {
    it("应该更新模块内容中的共享变量ID引用", () => {
      const promptContent = JSON.stringify({
        type: "module",
        attrs: {
          name: "测试模块",
          tags: ["tag1"],
        },
        content: [
          {
            type: "prompt",
            content: [
              {
                type: "paragraph",
                content: [
                  {
                    type: "text",
                    text: "这是一个测试：",
                  },
                  {
                    type: "sharedVariableReference",
                    attrs: {
                      sharedVariableId: "draft-variable-1",
                    },
                  },
                  {
                    type: "text",
                    text: " 结束",
                  },
                ],
              },
            ],
          },
        ],
      });

      const idMapping = new Map([["draft-variable-1", "synced-variable-123"]]);

      const result = PromptContentUpdater.updateSharedVariableIds(
        promptContent,
        idMapping,
      );
      const parsedResult = JSON.parse(result);

      expect(
        parsedResult.content[0].content[0].content[1].attrs.sharedVariableId,
      ).toBe("synced-variable-123");
    });

    it("应该处理嵌套结构中的多个共享变量引用", () => {
      const promptContent = JSON.stringify({
        type: "module",
        content: [
          {
            type: "prompt",
            content: [
              {
                type: "paragraph",
                content: [
                  {
                    type: "sharedVariableReference",
                    attrs: {
                      sharedVariableId: "draft-variable-1",
                    },
                  },
                ],
              },
              {
                type: "paragraph",
                content: [
                  {
                    type: "sharedVariableReference",
                    attrs: {
                      sharedVariableId: "draft-variable-2",
                    },
                  },
                ],
              },
            ],
          },
        ],
      });

      const idMapping = new Map([
        ["draft-variable-1", "synced-variable-123"],
        ["draft-variable-2", "synced-variable-456"],
      ]);

      const result = PromptContentUpdater.updateSharedVariableIds(
        promptContent,
        idMapping,
      );
      const parsedResult = JSON.parse(result);

      expect(
        parsedResult.content[0].content[0].content[0].attrs.sharedVariableId,
      ).toBe("synced-variable-123");
      expect(
        parsedResult.content[0].content[1].content[0].attrs.sharedVariableId,
      ).toBe("synced-variable-456");
    });

    it("应该保持没有映射的ID不变", () => {
      const promptContent = JSON.stringify({
        type: "module",
        content: [
          {
            type: "prompt",
            content: [
              {
                type: "paragraph",
                content: [
                  {
                    type: "sharedVariableReference",
                    attrs: {
                      sharedVariableId: "draft-variable-1",
                    },
                  },
                  {
                    type: "sharedVariableReference",
                    attrs: {
                      sharedVariableId: "draft-variable-unknown",
                    },
                  },
                ],
              },
            ],
          },
        ],
      });

      const idMapping = new Map([["draft-variable-1", "synced-variable-123"]]);

      const result = PromptContentUpdater.updateSharedVariableIds(
        promptContent,
        idMapping,
      );
      const parsedResult = JSON.parse(result);

      expect(
        parsedResult.content[0].content[0].content[0].attrs.sharedVariableId,
      ).toBe("synced-variable-123");
      expect(
        parsedResult.content[0].content[0].content[1].attrs.sharedVariableId,
      ).toBe("draft-variable-unknown");
    });

    it("应该处理无效JSON输入", () => {
      const invalidJson = "invalid json";
      const idMapping = new Map([["draft-variable-1", "synced-variable-123"]]);

      const result = PromptContentUpdater.updateSharedVariableIds(
        invalidJson,
        idMapping,
      );

      expect(result).toBe(invalidJson);
    });

    it("应该处理空内容", () => {
      const emptyContent = "";
      const idMapping = new Map([["draft-variable-1", "synced-variable-123"]]);

      const result = PromptContentUpdater.updateSharedVariableIds(
        emptyContent,
        idMapping,
      );

      expect(result).toBe(emptyContent);
    });

    it("应该处理没有共享变量引用的内容", () => {
      const promptContent = JSON.stringify({
        type: "module",
        content: [
          {
            type: "prompt",
            content: [
              {
                type: "paragraph",
                content: [
                  {
                    type: "text",
                    text: "这是纯文本内容",
                  },
                ],
              },
            ],
          },
        ],
      });

      const idMapping = new Map([["draft-variable-1", "synced-variable-123"]]);

      const result = PromptContentUpdater.updateSharedVariableIds(
        promptContent,
        idMapping,
      );

      expect(result).toBe(promptContent);
    });
  });

  describe("updateModuleIds", () => {
    it("应该更新能力内容中的模块ID引用", () => {
      const abilityContent = JSON.stringify({
        type: "ability",
        attrs: {
          name: "测试能力",
        },
        content: [
          {
            type: "abilityItem",
            attrs: {
              type: "SYSTEM",
            },
            content: [
              {
                type: "module",
                attrs: {
                  id: "draft-module-1",
                },
              },
            ],
          },
        ],
      });

      const idMapping = new Map([["draft-module-1", "synced-module-123"]]);

      const result = PromptContentUpdater.updateModuleIds(
        abilityContent,
        idMapping,
      );
      const parsedResult = JSON.parse(result);

      expect(parsedResult.content[0].content[0].attrs.id).toBe(
        "synced-module-123",
      );
    });

    it("应该处理多个能力项中的模块引用", () => {
      const abilityContent = JSON.stringify({
        type: "ability",
        attrs: {
          name: "测试能力",
        },
        content: [
          {
            type: "abilityItem",
            attrs: {
              type: "SYSTEM",
            },
            content: [
              {
                type: "module",
                attrs: {
                  id: "draft-module-1",
                },
              },
            ],
          },
          {
            type: "abilityItem",
            attrs: {
              type: "USER",
            },
            content: [
              {
                type: "module",
                attrs: {
                  id: "draft-module-2",
                },
              },
            ],
          },
        ],
      });

      const idMapping = new Map([
        ["draft-module-1", "synced-module-123"],
        ["draft-module-2", "synced-module-456"],
      ]);

      const result = PromptContentUpdater.updateModuleIds(
        abilityContent,
        idMapping,
      );
      const parsedResult = JSON.parse(result);

      expect(parsedResult.content[0].content[0].attrs.id).toBe(
        "synced-module-123",
      );
      expect(parsedResult.content[1].content[0].attrs.id).toBe(
        "synced-module-456",
      );
    });

    it("应该保持没有映射的模块ID不变", () => {
      const abilityContent = JSON.stringify({
        type: "ability",
        content: [
          {
            type: "abilityItem",
            attrs: {
              type: "SYSTEM",
            },
            content: [
              {
                type: "module",
                attrs: {
                  id: "draft-module-1",
                },
              },
              {
                type: "module",
                attrs: {
                  id: "draft-module-unknown",
                },
              },
            ],
          },
        ],
      });

      const idMapping = new Map([["draft-module-1", "synced-module-123"]]);

      const result = PromptContentUpdater.updateModuleIds(
        abilityContent,
        idMapping,
      );
      const parsedResult = JSON.parse(result);

      expect(parsedResult.content[0].content[0].attrs.id).toBe(
        "synced-module-123",
      );
      expect(parsedResult.content[0].content[1].attrs.id).toBe(
        "draft-module-unknown",
      );
    });

    it("应该处理无效JSON输入", () => {
      const invalidJson = "invalid json";
      const idMapping = new Map([["draft-module-1", "synced-module-123"]]);

      const result = PromptContentUpdater.updateModuleIds(
        invalidJson,
        idMapping,
      );

      expect(result).toBe(invalidJson);
    });

    it("应该处理空内容", () => {
      const emptyContent = "";
      const idMapping = new Map([["draft-module-1", "synced-module-123"]]);

      const result = PromptContentUpdater.updateModuleIds(
        emptyContent,
        idMapping,
      );

      expect(result).toBe(emptyContent);
    });

    it("应该处理没有模块引用的能力内容", () => {
      const abilityContent = JSON.stringify({
        type: "ability",
        attrs: {
          name: "测试能力",
        },
        content: [
          {
            type: "abilityItem",
            attrs: {
              type: "SYSTEM",
            },
            content: [],
          },
        ],
      });

      const idMapping = new Map([["draft-module-1", "synced-module-123"]]);

      const result = PromptContentUpdater.updateModuleIds(
        abilityContent,
        idMapping,
      );

      expect(result).toBe(abilityContent);
    });
  });
});
