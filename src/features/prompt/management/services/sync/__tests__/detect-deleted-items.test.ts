import { Level } from "@/types/api/prompt";
import { PromptVersionStatus } from "@/types/api/prompt-version";
import { describe, expect, it } from "vitest";
import {
  CompleteDraftVersionData,
  CompleteVersionData,
  DraftAbility,
  DraftModule,
  DraftModuleLabel,
  DraftSharedVariable,
  DraftVersion,
} from "../../../types";
import { detectDeletedItems } from "../detect-deleted-items";

describe("detectDeletedItems", () => {
  // 模拟基准版本数据
  const mockBaseData: CompleteVersionData = {
    version: {
      promptVersionId: "base-version-1",
      versionName: "基准版本",
      version: "1.0.0",
      status: PromptVersionStatus.ONLINE,
      remark: "基准版本",
      submitDate: "2024-01-01",
      offlineDate: undefined,
      onlineDate: undefined,
      versionChange: undefined,
      evaluationTasks: [],
    },
    abilities: [
      {
        id: "ability-1",
        name: "能力1",
        content: "能力内容1",
        promptVersionId: "base-version-1",
      },
      {
        id: "ability-2",
        name: "能力2",
        content: "能力内容2",
        promptVersionId: "base-version-1",
      },
    ],
    modules: [
      {
        id: "module-1",
        name: "模块1",
        moduleLabelIds: null,
        promptVersionId: "base-version-1",
        level: Level.Product,
        prompt: "模块内容1",
        scopeDetail: undefined,
      },
      {
        id: "module-2",
        name: "模块2",
        moduleLabelIds: null,
        promptVersionId: "base-version-1",
        level: Level.Product,
        prompt: "模块内容2",
        scopeDetail: undefined,
      },
    ],
    moduleLabels: [
      {
        id: "label-1",
        promptVersionId: "base-version-1",
        name: "标签1",
      },
      {
        id: "label-2",
        promptVersionId: "base-version-1",
        name: "标签2",
      },
    ],
    sharedVariables: [
      {
        id: "variable-1",
        name: "变量1",
        content: "变量内容1",
        definition: "变量定义1",
        level: Level.Product,
        scopeDetail: undefined,
        promptName: "var1",
        promptVersionId: "base-version-1",
      },
      {
        id: "variable-2",
        name: "变量2",
        content: "变量内容2",
        definition: "变量定义2",
        level: Level.Product,
        scopeDetail: undefined,
        promptName: "var2",
        promptVersionId: "base-version-1",
      },
    ],
  };

  it("应该检测到所有类型的删除项", () => {
    // 草稿数据中删除了一些项目
    const draftData: CompleteDraftVersionData = {
      draftVersion: { id: "draft-version-1" } as DraftVersion,
      draftAbilities: [
        // 只保留 ability-1，删除了 ability-2
        {
          id: "ability-1",
          name: "能力1",
          content: "能力内容1",
          promptVersionId: "draft-version-1",
          createdAt: "2024-01-01T00:00:00Z",
          updatedAt: "2024-01-01T00:00:00Z",
        },
      ],
      draftModules: [
        // 只保留 module-1，删除了 module-2
        {
          id: "module-1",
          name: "模块1",
          moduleLabelIds: null,
          promptVersionId: "draft-version-1",
          level: Level.Product,
          prompt: "模块内容1",
          scopeDetail: undefined,
          createdAt: "2024-01-01T00:00:00Z",
          updatedAt: "2024-01-01T00:00:00Z",
        },
      ],
      draftModuleLabels: [
        // 只保留 label-1，删除了 label-2
        {
          id: "label-1",
          promptVersionId: "draft-version-1",
          name: "标签1",
          createdAt: "2024-01-01T00:00:00Z",
          updatedAt: "2024-01-01T00:00:00Z",
        },
      ],
      draftSharedVariables: [
        // 只保留 variable-1，删除了 variable-2
        {
          id: "variable-1",
          name: "变量1",
          content: "变量内容1",
          definition: "变量定义1",
          level: Level.Product,
          scopeDetail: undefined,
          promptName: "var1",
          promptVersionId: "draft-version-1",
          createdAt: "2024-01-01T00:00:00Z",
          updatedAt: "2024-01-01T00:00:00Z",
        },
      ],
    };

    const result = detectDeletedItems(mockBaseData, draftData);

    // 验证删除的能力
    expect(result.deletedAbilities).toHaveLength(1);
    expect(result.deletedAbilities[0]).toEqual({
      id: "ability-2",
      name: "能力2",
    });

    // 验证删除的模块
    expect(result.deletedModules).toHaveLength(1);
    expect(result.deletedModules[0]).toEqual({
      id: "module-2",
      name: "模块2",
    });

    // 验证删除的模块标签
    expect(result.deletedModuleLabels).toHaveLength(1);
    expect(result.deletedModuleLabels[0]).toEqual({
      id: "label-2",
      name: "标签2",
    });

    // 验证删除的共享变量
    expect(result.deletedSharedVariables).toHaveLength(1);
    expect(result.deletedSharedVariables[0]).toEqual({
      id: "variable-2",
      name: "变量2",
    });
  });

  it("应该忽略草稿ID的数据", () => {
    // 草稿数据中包含新创建的草稿项目
    const draftData: CompleteDraftVersionData = {
      draftVersion: { id: "draft-version-1" } as DraftVersion,
      draftAbilities: [
        {
          id: "ability-1",
          name: "能力1",
          content: "能力内容1",
          promptVersionId: "draft-version-1",
          createdAt: "2024-01-01T00:00:00Z",
          updatedAt: "2024-01-01T00:00:00Z",
        },
        // 新创建的草稿能力，应该被忽略
        {
          id: "draft-new-ability",
          name: "新能力",
          content: "新能力内容",
          promptVersionId: "draft-version-1",
          createdAt: "2024-01-01T00:00:00Z",
          updatedAt: "2024-01-01T00:00:00Z",
        } as DraftAbility,
      ],
      draftModules: [
        {
          id: "module-1",
          name: "模块1",
          moduleLabelIds: null,
          promptVersionId: "draft-version-1",
          level: Level.Product,
          prompt: "模块内容1",
          scopeDetail: undefined,
          createdAt: "2024-01-01T00:00:00Z",
          updatedAt: "2024-01-01T00:00:00Z",
        } as DraftModule,
      ],
      draftModuleLabels: [
        {
          id: "label-1",
          promptVersionId: "draft-version-1",
          name: "标签1",
          createdAt: "2024-01-01T00:00:00Z",
          updatedAt: "2024-01-01T00:00:00Z",
        } as DraftModuleLabel,
      ],
      draftSharedVariables: [
        {
          id: "variable-1",
          name: "变量1",
          content: "变量内容1",
          definition: "变量定义1",
          level: Level.Product,
          scopeDetail: undefined,
          promptName: "var1",
          promptVersionId: "draft-version-1",
          createdAt: "2024-01-01T00:00:00Z",
          updatedAt: "2024-01-01T00:00:00Z",
        },
      ],
    };

    const result = detectDeletedItems(mockBaseData, draftData);

    // 应该检测到删除的项目（不包括新创建的草稿项目）
    expect(result.deletedAbilities).toHaveLength(1);
    expect(result.deletedAbilities[0].id).toBe("ability-2");

    expect(result.deletedModules).toHaveLength(1);
    expect(result.deletedModules[0].id).toBe("module-2");

    expect(result.deletedModuleLabels).toHaveLength(1);
    expect(result.deletedModuleLabels[0].id).toBe("label-2");

    expect(result.deletedSharedVariables).toHaveLength(1);
    expect(result.deletedSharedVariables[0].id).toBe("variable-2");
  });

  it("应该在没有删除项时返回空数组", () => {
    // 草稿数据包含所有基准版本的项目
    const draftData: CompleteDraftVersionData = {
      draftVersion: { id: "draft-version-1" } as DraftVersion,
      draftAbilities: [
        {
          id: "ability-1",
          name: "能力1",
          content: "能力内容1",
          promptVersionId: "draft-version-1",
          createdAt: "2024-01-01T00:00:00Z",
          updatedAt: "2024-01-01T00:00:00Z",
        } as DraftAbility,
        {
          id: "ability-2",
          name: "能力2",
          content: "能力内容2",
          promptVersionId: "draft-version-1",
          createdAt: "2024-01-01T00:00:00Z",
          updatedAt: "2024-01-01T00:00:00Z",
        } as DraftAbility,
      ],
      draftModules: [
        {
          id: "module-1",
          name: "模块1",
          moduleLabelIds: null,
          promptVersionId: "draft-version-1",
          level: Level.Product,
          prompt: "模块内容1",
          scopeDetail: undefined,
          createdAt: "2024-01-01T00:00:00Z",
          updatedAt: "2024-01-01T00:00:00Z",
        } as DraftModule,
        {
          id: "module-2",
          name: "模块2",
          moduleLabelIds: null,
          promptVersionId: "draft-version-1",
          level: Level.Product,
          prompt: "模块内容2",
          scopeDetail: undefined,
          createdAt: "2024-01-01T00:00:00Z",
          updatedAt: "2024-01-01T00:00:00Z",
        } as DraftModule,
      ],
      draftModuleLabels: [
        {
          id: "label-1",
          promptVersionId: "draft-version-1",
          name: "标签1",
          createdAt: "2024-01-01T00:00:00Z",
          updatedAt: "2024-01-01T00:00:00Z",
        } as DraftModuleLabel,
        {
          id: "label-2",
          promptVersionId: "draft-version-1",
          name: "标签2",
          createdAt: "2024-01-01T00:00:00Z",
          updatedAt: "2024-01-01T00:00:00Z",
        } as DraftModuleLabel,
      ],
      draftSharedVariables: [
        {
          id: "variable-1",
          name: "变量1",
          content: "变量内容1",
          definition: "变量定义1",
          level: Level.Product,
          scopeDetail: undefined,
          promptName: "var1",
          promptVersionId: "draft-version-1",
          createdAt: "2024-01-01T00:00:00Z",
          updatedAt: "2024-01-01T00:00:00Z",
        } as DraftSharedVariable,
        {
          id: "variable-2",
          name: "变量2",
          content: "变量内容2",
          definition: "变量定义2",
          level: Level.Product,
          scopeDetail: undefined,
          promptName: "var2",
          promptVersionId: "draft-version-1",
          createdAt: "2024-01-01T00:00:00Z",
          updatedAt: "2024-01-01T00:00:00Z",
        } as DraftSharedVariable,
      ],
    };

    const result = detectDeletedItems(mockBaseData, draftData);

    expect(result.deletedAbilities).toHaveLength(0);
    expect(result.deletedModules).toHaveLength(0);
    expect(result.deletedModuleLabels).toHaveLength(0);
    expect(result.deletedSharedVariables).toHaveLength(0);
  });
});
