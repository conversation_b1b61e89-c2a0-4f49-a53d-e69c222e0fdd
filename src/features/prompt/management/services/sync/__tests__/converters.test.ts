/**
 * 数据转换器单元测试
 */

import { Level } from "@/types/api/prompt";
import { PromptVersionStatus } from "@/types/api/prompt-version";
import { describe, expect, it, vi } from "vitest";
import {
  DraftAbility,
  DraftModule,
  DraftModuleLabel,
  DraftSharedVariable,
  DraftVersion,
} from "../../../types";
import {
  AbilityConverter,
  ModuleConverter,
  ModuleLabelConverter,
  SharedVariableConverter,
  VersionConverter,
} from "../converters";
import { SyncErrorType } from "../types";

// Mock PromptVersionService
vi.mock("@/services/prompt-version-service", () => ({
  PromptVersionService: {
    getPromptVersionDetail: vi.fn(),
  },
}));

describe("VersionConverter", () => {
  const mockVersion: DraftVersion = {
    id: "draft-version-1",
    versionName: "aaaa",
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z",
  };

  describe("toServerParams", () => {
    beforeEach(() => {
      vi.clearAllMocks();
    });

    it("应该正确转换版本数据（无基准版本）", async () => {
      const mockDraftData = {
        draftVersion: mockVersion,
        draftAbilities: [],
        draftModules: [],
        draftModuleLabels: [],
        draftSharedVariables: [],
      };

      const result = await VersionConverter.toServerParams(
        mockVersion,
        mockDraftData,
        {
          versionName: "aaaa",
          remark: "测试备注",
        },
      );

      expect(result).toEqual({
        remark: "测试备注",
        versionName: "aaaa",
        changeContent: expect.any(String),
      });
    });

    it("应该正确处理基准版本为线上状态（创建新版本）", async () => {
      const { PromptVersionService } = await import(
        "@/services/prompt-version-service"
      );
      vi.mocked(PromptVersionService.getPromptVersionDetail).mockResolvedValue({
        promptVersionId: "base-version-1",
        versionName: "基准版本",
        version: "0.9.0",
        status: PromptVersionStatus.ONLINE,
        remark: "线上版本",
        evaluationTasks: [],
      });

      const versionWithBase: DraftVersion = {
        ...mockVersion,
        baseVersionId: "base-version-1",
        baseVersion: "0.9.0",
      };

      const mockDraftDataWithBase = {
        draftVersion: versionWithBase,
        draftAbilities: [],
        draftModules: [],
        draftModuleLabels: [],
        draftSharedVariables: [],
      };

      const result = await VersionConverter.toServerParams(
        versionWithBase,
        mockDraftDataWithBase,
        {
          versionName: "aaaa",
          remark: "测试备注",
        },
      );

      expect(result).toEqual({
        remark: "测试备注",
        versionName: "aaaa",
        basePromptVersionId: "base-version-1",
        baseVersion: "0.9.0",
        changeContent: expect.any(String),
      });
    });

    it("应该正确处理基准版本为非线上状态（更新版本）", async () => {
      const { PromptVersionService } = await import(
        "@/services/prompt-version-service"
      );
      vi.mocked(PromptVersionService.getPromptVersionDetail).mockResolvedValue({
        promptVersionId: "base-version-1",
        versionName: "基准版本",
        version: "0.9.0",
        status: PromptVersionStatus.SUBMIT,
        remark: "提交版本",
        evaluationTasks: [],
      });

      const versionWithBase: DraftVersion = {
        ...mockVersion,
        baseVersionId: "base-version-1",
        baseVersion: "0.9.0",
      };

      const mockDraftDataWithBase = {
        draftVersion: versionWithBase,
        draftAbilities: [],
        draftModules: [],
        draftModuleLabels: [],
        draftSharedVariables: [],
      };

      const result = await VersionConverter.toServerParams(
        versionWithBase,
        mockDraftDataWithBase,
        {
          versionName: "aaaa",
          remark: "测试备注",
        },
      );

      expect(result).toEqual({
        remark: "测试备注",
        versionName: "aaaa",
        basePromptVersionId: "base-version-1",
        baseVersion: "0.9.0",
        promptVersionId: "base-version-1", // 包含 promptVersionId，因为是更新版本
        changeContent: expect.any(String),
      });
    });
  });

  describe("validate", () => {
    it("应该通过有效数据的验证", () => {
      const errors = VersionConverter.validate(mockVersion);
      expect(errors).toHaveLength(0);
    });

    it("应该检测空版本名称", () => {
      const invalidVersion = { ...mockVersion, versionName: "" };
      const errors = VersionConverter.validate(invalidVersion);

      expect(errors).toHaveLength(1);
      expect(errors[0].type).toBe(SyncErrorType.VALIDATION_ERROR);
      expect(errors[0].message).toContain("版本名称不能为空");
    });

    it("应该检测过长的版本名称", () => {
      const invalidVersion = { ...mockVersion, versionName: "a".repeat(101) };
      const errors = VersionConverter.validate(invalidVersion);

      expect(errors).toHaveLength(1);
      expect(errors[0].type).toBe(SyncErrorType.VALIDATION_ERROR);
      expect(errors[0].message).toContain("版本名称不能超过100个字符");
    });
  });
});

describe("ModuleLabelConverter", () => {
  const mockLabel: DraftModuleLabel = {
    id: "draft-label-1",
    name: "测试标签",
    promptVersionId: "draft-version-1",
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z",
  };

  describe("toServerParams", () => {
    it("应该正确转换模块标签数据（新增）", () => {
      const result = ModuleLabelConverter.toServerParams(mockLabel);

      expect(result).toEqual({
        name: "测试标签",
        promptVersionId: "draft-version-1",
      });
    });

    it("应该正确处理有sourceVersionId的情况（更新）", () => {
      const labelWithSource: DraftModuleLabel = {
        ...mockLabel,
        sourceVersionId: "source-version-1",
      };

      const result = ModuleLabelConverter.toServerParams(labelWithSource);

      expect(result).toEqual({
        name: "测试标签",
        promptVersionId: "draft-version-1",
        moduleLabelId: "draft-label-1", // 包含ID，表示更新操作
      });
    });
  });

  describe("validate", () => {
    it("应该通过有效数据的验证", () => {
      const errors = ModuleLabelConverter.validate(mockLabel);
      expect(errors).toHaveLength(0);
    });

    it("应该检测空标签名称", () => {
      const invalidLabel = { ...mockLabel, name: "" };
      const errors = ModuleLabelConverter.validate(invalidLabel);

      expect(errors).toHaveLength(1);
      expect(errors[0].type).toBe(SyncErrorType.VALIDATION_ERROR);
      expect(errors[0].message).toContain("模块标签名称不能为空");
    });
  });
});

describe("SharedVariableConverter", () => {
  const mockVariable: DraftSharedVariable = {
    id: "draft-variable-1",
    name: "测试变量",
    content: "变量内容",
    definition: "变量定义",
    level: Level.Product,
    scopeDetail: "详细范围",
    promptName: "提示词名称",
    promptVersionId: "draft-version-1",
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z",
  };

  describe("toServerParams", () => {
    it("应该正确转换共享变量数据（新增）", () => {
      const result = SharedVariableConverter.toServerParams(mockVariable);

      expect(result).toEqual({
        name: "测试变量",
        content: "变量内容",
        definition: "变量定义",
        level: Level.Product,
        scopeDetail: "详细范围",
        promptName: "提示词名称",
        promptVersionId: "draft-version-1",
      });
    });

    it("应该正确处理有sourceVersionId的情况（更新）", () => {
      const variableWithSource: DraftSharedVariable = {
        ...mockVariable,
        sourceVersionId: "source-version-1",
      };

      const result = SharedVariableConverter.toServerParams(variableWithSource);

      expect(result).toEqual({
        name: "测试变量",
        content: "变量内容",
        definition: "变量定义",
        level: Level.Product,
        scopeDetail: "详细范围",
        promptName: "提示词名称",
        promptVersionId: "draft-version-1",
        sharedVariableId: "draft-variable-1", // 包含ID，表示更新操作
      });
    });
  });

  describe("validate", () => {
    it("应该通过有效数据的验证", () => {
      const errors = SharedVariableConverter.validate(mockVariable);
      expect(errors).toHaveLength(0);
    });

    it("应该检测空变量名称", () => {
      const invalidVariable = { ...mockVariable, name: "" };
      const errors = SharedVariableConverter.validate(invalidVariable);

      expect(errors).toHaveLength(1);
      expect(errors[0].type).toBe(SyncErrorType.VALIDATION_ERROR);
      expect(errors[0].message).toContain("共享变量名称不能为空");
    });

    it("应该检测空变量内容", () => {
      const invalidVariable = { ...mockVariable, content: "" };
      const errors = SharedVariableConverter.validate(invalidVariable);

      expect(errors).toHaveLength(1);
      expect(errors[0].type).toBe(SyncErrorType.VALIDATION_ERROR);
      expect(errors[0].message).toContain("共享变量内容不能为空");
    });
  });
});

describe("ModuleConverter", () => {
  const mockModule: DraftModule = {
    id: "draft-module-1",
    name: "测试模块",
    level: Level.Product,
    prompt: "模块提示词",
    moduleLabelIds: '["draft-label-1"]',
    promptVersionId: "draft-version-1",
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z",
  };

  describe("toServerParams", () => {
    it("应该正确转换模块数据（新增）", () => {
      const result = ModuleConverter.toServerParams(mockModule);

      expect(result).toEqual({
        name: "测试模块",
        level: Level.Product,
        prompt: "模块提示词",
        moduleLabelIds: '["draft-label-1"]',
        promptVersionId: "draft-version-1",
      });
    });

    it("应该正确处理有sourceVersionId的情况（更新）", () => {
      const moduleWithSource: DraftModule = {
        ...mockModule,
        sourceVersionId: "source-version-1",
      };

      const result = ModuleConverter.toServerParams(moduleWithSource);

      expect(result).toEqual({
        name: "测试模块",
        level: Level.Product,
        prompt: "模块提示词",
        moduleLabelIds: '["draft-label-1"]',
        promptVersionId: "draft-version-1",
        moduleId: "draft-module-1", // 包含ID，表示更新操作
      });
    });

    it("应该处理空的模块标签ID", () => {
      const moduleWithoutLabels = { ...mockModule, moduleLabelIds: "" };
      const result = ModuleConverter.toServerParams(moduleWithoutLabels);

      expect(result.moduleLabelIds).toBe("");
    });
  });

  describe("validate", () => {
    it("应该通过有效数据的验证", () => {
      const errors = ModuleConverter.validate(mockModule);
      expect(errors).toHaveLength(0);
    });

    it("应该检测空模块名称", () => {
      const invalidModule = { ...mockModule, name: "" };
      const errors = ModuleConverter.validate(invalidModule);

      expect(errors).toHaveLength(1);
      expect(errors[0].type).toBe(SyncErrorType.VALIDATION_ERROR);
      expect(errors[0].message).toContain("模块名称不能为空");
    });

    it("应该检测空提示词", () => {
      const invalidModule = { ...mockModule, prompt: "" };
      const errors = ModuleConverter.validate(invalidModule);

      expect(errors).toHaveLength(1);
      expect(errors[0].type).toBe(SyncErrorType.VALIDATION_ERROR);
      expect(errors[0].message).toContain("模块提示词不能为空");
    });
  });
});

describe("AbilityConverter", () => {
  const mockAbility: DraftAbility = {
    id: "draft-ability-1",
    name: "测试能力",
    content: "能力内容",
    promptVersionId: "draft-version-1",
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z",
  };

  describe("toServerParams", () => {
    it("应该正确转换能力数据（新增）", () => {
      const result = AbilityConverter.toServerParams(mockAbility);

      expect(result).toEqual({
        name: "测试能力",
        content: "能力内容",
        promptVersionId: "draft-version-1",
      });
    });

    it("应该正确处理有sourceVersionId的情况（更新）", () => {
      const abilityWithSource: DraftAbility = {
        ...mockAbility,
        sourceVersionId: "source-version-1",
      };

      const result = AbilityConverter.toServerParams(abilityWithSource);

      expect(result).toEqual({
        name: "测试能力",
        content: "能力内容",
        promptVersionId: "draft-version-1",
        abilityId: "draft-ability-1", // 包含ID，表示更新操作
      });
    });
  });

  describe("validate", () => {
    it("应该通过有效数据的验证", () => {
      const errors = AbilityConverter.validate(mockAbility);
      expect(errors).toHaveLength(0);
    });

    it("应该检测空能力名称", () => {
      const invalidAbility = { ...mockAbility, name: "" };
      const errors = AbilityConverter.validate(invalidAbility);

      expect(errors).toHaveLength(1);
      expect(errors[0].type).toBe(SyncErrorType.VALIDATION_ERROR);
      expect(errors[0].message).toContain("能力名称不能为空");
    });

    it("应该检测空能力内容", () => {
      const invalidAbility = { ...mockAbility, content: "" };
      const errors = AbilityConverter.validate(invalidAbility);

      expect(errors).toHaveLength(1);
      expect(errors[0].type).toBe(SyncErrorType.VALIDATION_ERROR);
      expect(errors[0].message).toContain("能力内容不能为空");
    });
  });
});
