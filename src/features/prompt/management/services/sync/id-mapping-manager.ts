/**
 * ID映射管理器
 * 统一管理所有ID映射相关操作
 */

import { SyncDataType } from "./types";
import type { IdMapping, SerializedIdMapping } from "./types";

/**
 * ID映射管理器
 */
export class IdMappingManager {
  private idMapping: IdMapping;

  constructor(initialMapping?: SerializedIdMapping) {
    this.idMapping = this.createEmptyMapping();

    if (initialMapping) {
      this.loadFromSerialized(initialMapping);
    }
  }

  /**
   * 创建空的ID映射
   */
  private createEmptyMapping(): IdMapping {
    return {
      versionIds: new Map<string, string>(),
      moduleLabelIds: new Map<string, string>(),
      sharedVariableIds: new Map<string, string>(),
      moduleIds: new Map<string, string>(),
      abilityIds: new Map<string, string>(),
    };
  }

  /**
   * 从序列化数据加载映射
   */
  private loadFromSerialized(serialized: SerializedIdMapping): void {
    this.idMapping.versionIds = new Map(Object.entries(serialized.versionIds));
    this.idMapping.moduleLabelIds = new Map(
      Object.entries(serialized.moduleLabelIds),
    );
    this.idMapping.sharedVariableIds = new Map(
      Object.entries(serialized.sharedVariableIds),
    );
    this.idMapping.moduleIds = new Map(Object.entries(serialized.moduleIds));
    this.idMapping.abilityIds = new Map(Object.entries(serialized.abilityIds));
  }

  /**
   * 获取运行时ID映射
   */
  getMapping(): IdMapping {
    return this.idMapping;
  }

  /**
   * 获取序列化的ID映射
   */
  getSerializedMapping(): SerializedIdMapping {
    return {
      versionIds: Object.fromEntries(this.idMapping.versionIds),
      moduleLabelIds: Object.fromEntries(this.idMapping.moduleLabelIds),
      sharedVariableIds: Object.fromEntries(this.idMapping.sharedVariableIds),
      moduleIds: Object.fromEntries(this.idMapping.moduleIds),
      abilityIds: Object.fromEntries(this.idMapping.abilityIds),
    };
  }

  /**
   * 创建空的序列化映射
   */
  static createEmptySerializedMapping(): SerializedIdMapping {
    return {
      versionIds: {},
      moduleLabelIds: {},
      sharedVariableIds: {},
      moduleIds: {},
      abilityIds: {},
    };
  }

  /**
   * 更新ID映射
   */
  updateMapping(
    dataType: SyncDataType,
    draftId: string,
    syncedId: string,
  ): void {
    switch (dataType) {
      case SyncDataType.VERSION:
        this.idMapping.versionIds.set(draftId, syncedId);
        break;
      case SyncDataType.MODULE_LABEL:
        this.idMapping.moduleLabelIds.set(draftId, syncedId);
        break;
      case SyncDataType.SHARED_VARIABLE:
        this.idMapping.sharedVariableIds.set(draftId, syncedId);
        break;
      case SyncDataType.MODULE:
        this.idMapping.moduleIds.set(draftId, syncedId);
        break;
      case SyncDataType.ABILITY:
        this.idMapping.abilityIds.set(draftId, syncedId);
        break;
    }
  }

  /**
   * 获取映射的ID
   */
  getMappedId(dataType: SyncDataType, draftId: string): string | undefined {
    switch (dataType) {
      case SyncDataType.VERSION:
        return this.idMapping.versionIds.get(draftId);
      case SyncDataType.MODULE_LABEL:
        return this.idMapping.moduleLabelIds.get(draftId);
      case SyncDataType.SHARED_VARIABLE:
        return this.idMapping.sharedVariableIds.get(draftId);
      case SyncDataType.MODULE:
        return this.idMapping.moduleIds.get(draftId);
      case SyncDataType.ABILITY:
        return this.idMapping.abilityIds.get(draftId);
    }
  }

  /**
   * 复制映射管理器
   */
  clone(): IdMappingManager {
    return new IdMappingManager(this.getSerializedMapping());
  }

  /**
   * 清空所有映射
   */
  clear(): void {
    this.idMapping.versionIds.clear();
    this.idMapping.moduleLabelIds.clear();
    this.idMapping.sharedVariableIds.clear();
    this.idMapping.moduleIds.clear();
    this.idMapping.abilityIds.clear();
  }
}
