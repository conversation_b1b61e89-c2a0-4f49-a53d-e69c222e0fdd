/**
 * 基于队列的数据同步服务
 */

import { AbilityService } from "@/services/ability-service";
import { ModuleService } from "@/services/module-service";
import { PromptVersionService } from "@/services/prompt-version-service";
import { SharedVariableService } from "@/services/shared-variable-service";
import { v4 as uuidv4 } from "uuid";
import {
  CompleteDraftVersionData,
  DraftAbility,
  DraftModule,
  DraftModuleLabel,
  DraftSharedVariable,
  DraftVersion,
} from "../../types";
import { getCompleteVersionData } from "../../utils";
import {
  AbilityConverter,
  ModuleConverter,
  ModuleLabelConverter,
  SharedVariableConverter,
  VersionConverter,
} from "./converters";
import { detectDeletedItems } from "./detect-deleted-items";
import { IdMappingManager } from "./id-mapping-manager";
import { PromptContentUpdater } from "./prompt-content-updater";
import {
  QueueProgress,
  QueueStatus,
  QueueTask,
  QueueTaskStatus,
  SyncDataType,
  SyncError,
  SyncErrorType,
  SyncOptions,
  SyncQueue,
  SyncResult,
  SyncStats,
  TaskExecutionContext,
} from "./types";

/**
 * 计算数据的关键字段哈希值（只包含影响同步依赖关系的字段）
 */
function calculateCriticalDataHash(data: any, type: SyncDataType): string {
  let criticalData: any;

  switch (type) {
    case SyncDataType.VERSION:
      // 版本的关键字段：ID和基本结构
      criticalData = {
        id: data.id,
        // 版本名称变更不影响依赖关系，所以不包含
      };
      break;

    case SyncDataType.MODULE_LABEL:
      // 模块标签的关键字段：ID
      criticalData = {
        id: data.id,
        // 名称变更不影响依赖关系
      };
      break;

    case SyncDataType.SHARED_VARIABLE:
      // 共享变量的关键字段：ID和变量名（影响引用）
      criticalData = {
        id: data.id,
        name: data.name, // 变量名影响其他模块的引用
      };
      break;

    case SyncDataType.MODULE:
      // 模块的关键字段：ID、标签关联、共享变量引用
      criticalData = {
        id: data.id,
        labelIds: data.labelIds || [], // 标签关联变更影响依赖
        // 内容变更不影响依赖关系，所以不包含content
      };
      break;

    case SyncDataType.ABILITY:
      // 能力的关键字段：ID、模块关联
      criticalData = {
        id: data.id,
        moduleIds: data.moduleIds || [], // 模块关联变更影响依赖
      };
      break;

    // 删除任务不需要数据哈希，因为它们基于服务端ID
    case SyncDataType.DELETE_ABILITY:
    case SyncDataType.DELETE_MODULE:
    case SyncDataType.DELETE_SHARED_VARIABLE:
    case SyncDataType.DELETE_MODULE_LABEL:
      criticalData = { id: data };
      break;

    default:
      criticalData = { id: data.id };
  }

  // 计算哈希
  const jsonStr = JSON.stringify(
    criticalData,
    Object.keys(criticalData).sort(),
  );
  let hash = 0;
  for (let i = 0; i < jsonStr.length; i++) {
    const char = jsonStr.charCodeAt(i);
    hash = (hash << 5) - hash + char;
    hash = hash & hash; // 转换为32位整数
  }
  return hash.toString(36);
}

/**
 * 基于队列的数据同步服务
 */
export class QueueSyncService {
  private options: Required<SyncOptions>;
  private progressCallback?: (progress: QueueProgress) => void;
  private getDraftDataCallback?: (
    task: QueueTask,
  ) =>
    | DraftVersion
    | DraftModule
    | DraftAbility
    | DraftModuleLabel
    | DraftSharedVariable
    | null
    | undefined;
  private checkDataChangedCallback?: (task: QueueTask) => boolean;
  private getDraftDataByVersionCallback?: (
    versionId: string,
  ) => Promise<CompleteDraftVersionData | null>;

  constructor(
    options: SyncOptions = {},
    progressCallback?: (progress: QueueProgress) => void,
    getDraftDataCallback?: (
      task: QueueTask,
    ) =>
      | DraftVersion
      | DraftModule
      | DraftAbility
      | DraftModuleLabel
      | DraftSharedVariable
      | null
      | undefined,
    checkDataChangedCallback?: (task: QueueTask) => boolean,
    getDraftDataByVersionCallback?: (
      versionId: string,
    ) => Promise<CompleteDraftVersionData | null>,
  ) {
    this.options = {
      cleanupOnSuccess: true,
      timeout: 30000,
      retryCount: 3,
      retryDelay: 1000,
      autoStart: true,
      ...options,
    };
    this.progressCallback = progressCallback;
    this.getDraftDataCallback = getDraftDataCallback;
    this.checkDataChangedCallback = checkDataChangedCallback;
    this.getDraftDataByVersionCallback = getDraftDataByVersionCallback;
  }

  /**
   * 创建同步队列
   */
  async createSyncQueue(
    versionId: string,
    draftVersionData: CompleteDraftVersionData,
    versionInfo: {
      remark: string;
      versionName: string;
    },
  ): Promise<SyncQueue> {
    const queueId = `queue-${uuidv4()}`;
    const now = Date.now();

    // 验证所有数据
    const validationErrors = this.validateAllData(draftVersionData);

    if (validationErrors.length > 0) {
      throw new Error(
        `数据验证失败: ${validationErrors.map((e) => e.message).join(", ")}`,
      );
    }

    // 创建任务列表
    const tasks: QueueTask[] = [];
    let taskIndex = 0;

    // 如果有基准版本，检测删除项并创建删除任务
    if (draftVersionData.draftVersion.baseVersionId) {
      try {
        const baseData = await getCompleteVersionData(
          draftVersionData.draftVersion.baseVersionId,
        );

        if (baseData) {
          const deletedItems = detectDeletedItems(baseData, draftVersionData);
          // 为删除项创建删除任务（按依赖关系倒序：能力 -> 模块 -> 共享变量 -> 模块标签）
          const deleteTasks = this.createDeleteTasks(deletedItems, taskIndex);

          tasks.push(...deleteTasks);
          taskIndex += deleteTasks.length;
        }
      } catch (error) {
        console.warn(
          `获取基准版本数据失败，跳过删除项检测：${draftVersionData.draftVersion.baseVersionId}`,
          error,
        );
      }
    }

    // 获取所有删除任务的ID，版本任务需要等待删除任务完成
    const deleteTaskIds = tasks.map((t) => t.id);

    // 1. 版本任务（依赖所有删除任务）
    const versionTask: QueueTask = {
      id: `task-${taskIndex++}`,
      type: SyncDataType.VERSION,
      status: QueueTaskStatus.PENDING,
      draftId: draftVersionData.draftVersion.id,
      dataHash: calculateCriticalDataHash(
        draftVersionData.draftVersion,
        SyncDataType.VERSION,
      ),
      dependencies: deleteTaskIds,
      retryCount: 0,
      maxRetries: this.options.retryCount,
      createdAt: now,
      displayName: versionInfo.versionName,
    };
    tasks.push(versionTask);

    // 2. 模块标签任务（依赖版本）
    draftVersionData.draftModuleLabels.forEach((label) => {
      const task: QueueTask = {
        id: `task-${taskIndex++}`,
        type: SyncDataType.MODULE_LABEL,
        status: QueueTaskStatus.PENDING,
        draftId: label.id,
        dataHash: calculateCriticalDataHash(label, SyncDataType.MODULE_LABEL),
        dependencies: [versionTask.id],
        retryCount: 0,
        maxRetries: this.options.retryCount,
        createdAt: now,
        displayName: label.name,
      };
      tasks.push(task);
    });

    // 3. 共享变量任务（依赖版本）
    draftVersionData.draftSharedVariables.forEach((variable) => {
      const task: QueueTask = {
        id: `task-${taskIndex++}`,
        type: SyncDataType.SHARED_VARIABLE,
        status: QueueTaskStatus.PENDING,
        draftId: variable.id,
        dataHash: calculateCriticalDataHash(
          variable,
          SyncDataType.SHARED_VARIABLE,
        ),
        dependencies: [versionTask.id],
        retryCount: 0,
        maxRetries: this.options.retryCount,
        createdAt: now,
        displayName: variable.name,
      };
      tasks.push(task);
    });

    // 4. 模块任务（依赖版本、模块标签、共享变量）
    const moduleLabelTaskIds = tasks
      .filter((t) => t.type === SyncDataType.MODULE_LABEL)
      .map((t) => t.id);
    const sharedVariableTaskIds = tasks
      .filter((t) => t.type === SyncDataType.SHARED_VARIABLE)
      .map((t) => t.id);

    draftVersionData.draftModules.forEach((module) => {
      const task: QueueTask = {
        id: `task-${taskIndex++}`,
        type: SyncDataType.MODULE,
        status: QueueTaskStatus.PENDING,
        draftId: module.id,
        dataHash: calculateCriticalDataHash(module, SyncDataType.MODULE),
        dependencies: [
          versionTask.id,
          ...moduleLabelTaskIds,
          ...sharedVariableTaskIds,
        ],
        retryCount: 0,
        maxRetries: this.options.retryCount,
        createdAt: now,
        displayName: module.name,
      };
      tasks.push(task);
    });

    // 5. 能力任务（依赖版本、模块）
    const moduleTaskIds = tasks
      .filter((t) => t.type === SyncDataType.MODULE)
      .map((t) => t.id);

    draftVersionData.draftAbilities.forEach((ability) => {
      const task: QueueTask = {
        id: `task-${taskIndex++}`,
        type: SyncDataType.ABILITY,
        status: QueueTaskStatus.PENDING,
        draftId: ability.id,
        dataHash: calculateCriticalDataHash(ability, SyncDataType.ABILITY),
        dependencies: [versionTask.id, ...moduleTaskIds],
        retryCount: 0,
        maxRetries: this.options.retryCount,
        createdAt: now,
        displayName: ability.name,
      };
      tasks.push(task);
    });

    // 创建队列
    const queue: SyncQueue = {
      id: queueId,
      versionId,
      status: QueueStatus.PENDING,
      tasks,
      idMapping: IdMappingManager.createEmptySerializedMapping(),
      options: this.options,
      createdAt: now,
      versionInfo,
    };

    return queue;
  }

  /**
   * 执行同步队列
   */
  async executeQueue(queue: SyncQueue): Promise<SyncResult> {
    const stats: SyncStats = {
      versionCount: queue.tasks.filter((t) => t.type === SyncDataType.VERSION)
        .length,
      moduleLabelCount: queue.tasks.filter(
        (t) => t.type === SyncDataType.MODULE_LABEL,
      ).length,
      sharedVariableCount: queue.tasks.filter(
        (t) => t.type === SyncDataType.SHARED_VARIABLE,
      ).length,
      moduleCount: queue.tasks.filter((t) => t.type === SyncDataType.MODULE)
        .length,
      abilityCount: queue.tasks.filter((t) => t.type === SyncDataType.ABILITY)
        .length,
    };

    try {
      // 更新队列状态
      queue.status = QueueStatus.RUNNING;
      queue.startedAt = Date.now();

      // 创建执行上下文
      const context = this.createExecutionContext(queue);

      // 执行任务
      await this.executeTasks(queue, context);

      // 检查执行结果
      const failedTasks = queue.tasks.filter(
        (t) => t.status === QueueTaskStatus.FAILED,
      );

      if (failedTasks.length > 0) {
        queue.status = QueueStatus.FAILED;
        queue.completedAt = Date.now();

        return {
          success: false,
          queueId: queue.id,
          errors: failedTasks.map((task) => task.error!),
          stats,
        };
      }

      // 成功完成
      queue.status = QueueStatus.SUCCESS;
      queue.completedAt = Date.now();

      // 获取同步后的版本ID
      const versionTask = queue.tasks.find(
        (t) => t.type === SyncDataType.VERSION,
      );
      const syncedVersionId = versionTask?.syncedId;

      return {
        success: true,
        queueId: queue.id,
        syncedVersionId,
        stats,
      };
    } catch (error) {
      queue.status = QueueStatus.FAILED;
      queue.completedAt = Date.now();

      const syncError: SyncError = {
        type: SyncErrorType.SERVER_ERROR,
        message:
          error instanceof Error ? error.message : "队列执行过程中发生未知错误",
        originalError: error,
      };

      return {
        success: false,
        queueId: queue.id,
        errors: [syncError],
        stats,
      };
    }
  }

  /**
   * 创建删除任务
   */
  private createDeleteTasks(
    deletedItems: import("./detect-deleted-items").DeletedItems,
    startIndex: number,
  ): QueueTask[] {
    const tasks: QueueTask[] = [];
    const now = Date.now();
    let taskIndex = startIndex;

    // 按依赖关系倒序创建删除任务：能力 -> 模块 -> 共享变量 -> 模块标签

    // 1. 删除能力任务（无依赖，可以直接删除）
    deletedItems.deletedAbilities.forEach((ability) => {
      const task: QueueTask = {
        id: `task-${taskIndex++}`,
        type: SyncDataType.DELETE_ABILITY,
        status: QueueTaskStatus.PENDING,
        draftId: ability.id, // 使用服务端ID
        dataHash: "", // 删除任务不需要数据哈希
        dependencies: [],
        retryCount: 0,
        maxRetries: this.options.retryCount,
        createdAt: now,
        displayName: `删除能力: ${ability.name}`,
      };
      tasks.push(task);
    });

    // 2. 删除模块任务（依赖能力删除完成）
    const abilityDeleteTaskIds = tasks
      .filter((t) => t.type === SyncDataType.DELETE_ABILITY)
      .map((t) => t.id);

    deletedItems.deletedModules.forEach((module) => {
      const task: QueueTask = {
        id: `task-${taskIndex++}`,
        type: SyncDataType.DELETE_MODULE,
        status: QueueTaskStatus.PENDING,
        draftId: module.id,
        dataHash: "",
        dependencies: abilityDeleteTaskIds,
        retryCount: 0,
        maxRetries: this.options.retryCount,
        createdAt: now,
        displayName: `删除模块: ${module.name}`,
      };
      tasks.push(task);
    });

    // 3. 删除共享变量任务（依赖能力和模块删除完成，因为共享变量可能被能力和模块引用）
    const moduleDeleteTaskIds = tasks
      .filter((t) => t.type === SyncDataType.DELETE_MODULE)
      .map((t) => t.id);

    deletedItems.deletedSharedVariables.forEach((variable) => {
      const task: QueueTask = {
        id: `task-${taskIndex++}`,
        type: SyncDataType.DELETE_SHARED_VARIABLE,
        status: QueueTaskStatus.PENDING,
        draftId: variable.id,
        dataHash: "",
        dependencies: [...abilityDeleteTaskIds, ...moduleDeleteTaskIds],
        retryCount: 0,
        maxRetries: this.options.retryCount,
        createdAt: now,
        displayName: `删除共享变量: ${variable.name}`,
      };
      tasks.push(task);
    });

    // 4. 删除模块标签任务（依赖模块删除完成）
    deletedItems.deletedModuleLabels.forEach((label) => {
      const task: QueueTask = {
        id: `task-${taskIndex++}`,
        type: SyncDataType.DELETE_MODULE_LABEL,
        status: QueueTaskStatus.PENDING,
        draftId: label.id,
        dataHash: "",
        dependencies: moduleDeleteTaskIds,
        retryCount: 0,
        maxRetries: this.options.retryCount,
        createdAt: now,
        displayName: `删除模块标签: ${label.name}`,
      };
      tasks.push(task);
    });

    return tasks;
  }

  /**
   * 验证所有数据
   */
  private validateAllData(data: CompleteDraftVersionData): SyncError[] {
    const errors: SyncError[] = [];

    // 验证版本
    errors.push(...VersionConverter.validate(data.draftVersion));

    // 验证模块标签
    data.draftModuleLabels.forEach((label) => {
      errors.push(...ModuleLabelConverter.validate(label));
    });

    // 验证共享变量
    data.draftSharedVariables.forEach((variable) => {
      errors.push(...SharedVariableConverter.validate(variable));
    });

    // 验证模块
    data.draftModules.forEach((module) => {
      errors.push(...ModuleConverter.validate(module));
    });

    // 验证能力
    data.draftAbilities.forEach((ability) => {
      errors.push(...AbilityConverter.validate(ability));
    });

    return errors;
  }

  /**
   * 创建执行上下文
   */
  private createExecutionContext(queue: SyncQueue): TaskExecutionContext {
    const idMappingManager = new IdMappingManager(queue.idMapping);

    return {
      idMapping: idMappingManager.getMapping(),
      idMappingManager,
      options: this.options,
      onProgress: this.progressCallback,
    };
  }

  /**
   * 执行任务队列
   */
  private async executeTasks(
    queue: SyncQueue,
    context: TaskExecutionContext,
  ): Promise<void> {
    while (true) {
      // 查找可执行的任务
      const executableTask = this.findExecutableTask(queue);

      if (!executableTask) {
        // 没有可执行的任务，检查是否还有待执行的任务
        const pendingTasks = queue.tasks.filter(
          (t) => t.status === QueueTaskStatus.PENDING,
        );

        if (pendingTasks.length > 0) {
          // 有待执行任务但都被依赖阻塞，标记为跳过
          pendingTasks.forEach((task) => {
            task.status = QueueTaskStatus.SKIPPED;
            task.error = {
              type: SyncErrorType.DEPENDENCY_ERROR,
              message: "依赖任务执行失败",
              taskId: task.id,
            };
          });
        }

        break;
      }

      // 执行任务
      await this.executeTask(executableTask, queue, context);

      // 更新进度
      this.updateProgress(queue);
    }
  }

  /**
   * 查找可执行的任务
   */
  private findExecutableTask(queue: SyncQueue): QueueTask | null {
    return (
      queue.tasks.find((task) => {
        if (task.status !== QueueTaskStatus.PENDING) {
          return false;
        }

        // 检查依赖是否都已成功完成
        return task.dependencies.every((depId) => {
          const depTask = queue.tasks.find((t) => t.id === depId);
          return depTask?.status === QueueTaskStatus.SUCCESS;
        });
      }) || null
    );
  }

  /**
   * 根据任务信息获取最新的草稿数据
   */
  private getDraftDataForTask(
    task: QueueTask,
  ):
    | DraftVersion
    | DraftModule
    | DraftAbility
    | DraftModuleLabel
    | DraftSharedVariable
    | null
    | undefined {
    if (!this.getDraftDataCallback) {
      throw new Error("未提供数据获取回调函数");
    }

    return this.getDraftDataCallback(task);
  }

  /**
   * 获取版本的完整草稿数据
   */
  private getDraftDataForVersion(versionId: string) {
    if (!this.getDraftDataByVersionCallback) {
      throw new Error("未提供获取草稿数据的回调函数");
    }

    return this.getDraftDataByVersionCallback(versionId);
  }

  /**
   * 检查数据是否发生变更
   */
  private checkDataChanged(task: QueueTask): boolean {
    if (!this.checkDataChangedCallback) {
      // 如果没有提供检查回调，默认认为数据未变更
      return false;
    }

    return this.checkDataChangedCallback(task);
  }

  /**
   * 执行单个任务
   */
  private async executeTask(
    task: QueueTask,
    queue: SyncQueue,
    context: TaskExecutionContext,
  ): Promise<void> {
    // 任务级别重试循环
    for (let attempt = 0; attempt <= task.maxRetries; attempt++) {
      task.status = QueueTaskStatus.RUNNING;
      task.startedAt = Date.now();
      task.retryCount = attempt;

      try {
        // 删除任务不需要检查数据变更，因为删除的数据已经不在草稿中了
        const isDeleteTask = task.type.toString().startsWith("DELETE_");

        if (!isDeleteTask) {
          // 检查数据是否发生变更
          const dataChanged = this.checkDataChanged(task);

          if (dataChanged) {
            throw new Error(
              `数据已发生变更，无法执行任务。请重新创建同步队列。任务: ${task.displayName}`,
            );
          }
        }

        // 获取最新数据（删除任务不需要草稿数据）
        const currentData = isDeleteTask
          ? null
          : this.getDraftDataForTask(task);
        let syncedId: string;

        switch (task.type) {
          case SyncDataType.VERSION:
            syncedId = await this.executeVersionTask(
              currentData as DraftVersion,
              queue,
            );
            break;
          case SyncDataType.MODULE_LABEL:
            syncedId = await this.executeModuleLabelTask(
              currentData as DraftModuleLabel,
              context,
            );
            break;
          case SyncDataType.SHARED_VARIABLE:
            syncedId = await this.executeSharedVariableTask(
              currentData as DraftSharedVariable,
              context,
            );
            break;
          case SyncDataType.MODULE:
            syncedId = await this.executeModuleTask(
              currentData as DraftModule,
              context,
            );
            break;
          case SyncDataType.ABILITY:
            syncedId = await this.executeAbilityTask(
              currentData as DraftAbility,
              context,
            );
            break;
          // 删除任务
          case SyncDataType.DELETE_ABILITY:
            syncedId = await this.executeDeleteAbilityTask(task.draftId);
            break;
          case SyncDataType.DELETE_MODULE:
            syncedId = await this.executeDeleteModuleTask(task.draftId);
            break;
          case SyncDataType.DELETE_SHARED_VARIABLE:
            syncedId = await this.executeDeleteSharedVariableTask(task.draftId);
            break;
          case SyncDataType.DELETE_MODULE_LABEL:
            syncedId = await this.executeDeleteModuleLabelTask(task.draftId);
            break;
          default:
            throw new Error(`未知的任务类型: ${task.type}`);
        }

        // 任务成功
        task.status = QueueTaskStatus.SUCCESS;
        task.syncedId = syncedId;
        task.completedAt = Date.now();

        // 只有创建/更新任务需要更新ID映射，删除任务不需要
        if (!task.type.toString().startsWith("DELETE_")) {
          this.updateIdMapping(task, syncedId, context, queue);
        }
        return; // 成功，退出重试循环
      } catch (error) {
        // 如果还有重试机会，等待后重试
        if (attempt < task.maxRetries) {
          await new Promise((resolve) =>
            setTimeout(resolve, this.options.retryDelay),
          );
          continue;
        }

        // 重试次数用完，任务失败
        task.status = QueueTaskStatus.FAILED;
        task.completedAt = Date.now();
        task.error = {
          type: SyncErrorType.SERVER_ERROR,
          message: error instanceof Error ? error.message : "任务执行失败",
          taskId: task.id,
          dataId: task.draftId,
          dataType: task.type,
          originalError: error,
        };
      }
    }
  }

  /**
   * 更新ID映射
   */
  private updateIdMapping(
    task: QueueTask,
    syncedId: string,
    context: TaskExecutionContext,
    queue: SyncQueue,
  ): void {
    // 更新运行时映射
    context.idMappingManager.updateMapping(task.type, task.draftId, syncedId);

    // 更新队列中的序列化映射
    queue.idMapping = context.idMappingManager.getSerializedMapping();
  }

  /**
   * 更新进度
   */
  private updateProgress(queue: SyncQueue): void {
    if (!this.progressCallback) {
      return;
    }

    const totalTasks = queue.tasks.length;
    const completedTasks = queue.tasks.filter(
      (t) =>
        t.status === QueueTaskStatus.SUCCESS ||
        t.status === QueueTaskStatus.FAILED ||
        t.status === QueueTaskStatus.SKIPPED,
    ).length;
    const failedTasks = queue.tasks.filter(
      (t) => t.status === QueueTaskStatus.FAILED,
    ).length;
    const currentTask = queue.tasks.find(
      (t) => t.status === QueueTaskStatus.RUNNING,
    );

    const progress: QueueProgress = {
      queueId: queue.id,
      status: queue.status,
      totalTasks,
      completedTasks,
      failedTasks,
      currentTask,
      overallProgress: totalTasks > 0 ? completedTasks / totalTasks : 0,
    };

    this.progressCallback(progress);
  }

  /**
   * 执行版本任务
   */
  private async executeVersionTask(
    draftVersion: DraftVersion,
    queue: SyncQueue,
  ): Promise<string> {
    const version = { ...draftVersion };

    // 获取完整的草稿数据
    const draftData = await this.getDraftDataForVersion(queue.versionId);

    const params = await VersionConverter.toServerParams(
      version,
      draftData,
      queue.versionInfo,
    );

    const response = await this.retryRequest(() =>
      PromptVersionService.addOrUpdatePromptVersion(params),
    );

    if (response.success && typeof response.data === "string") {
      return response.data;
    }

    throw new Error("版本同步失败：服务端未返回有效的版本ID");
  }

  /**
   * 执行模块标签任务
   */
  private async executeModuleLabelTask(
    draftModuleLabel: DraftModuleLabel,
    context: TaskExecutionContext,
  ): Promise<string> {
    const label = { ...draftModuleLabel };

    // 更新版本ID引用
    const newVersionId = context.idMapping.versionIds.get(
      label.promptVersionId,
    );

    if (newVersionId) {
      label.promptVersionId = newVersionId;
    }

    const params = ModuleLabelConverter.toServerParams(label);
    const response = await this.retryRequest(() =>
      ModuleService.addOrUpdateModuleLabel(params),
    );

    if (response?.success && typeof response.data === "string") {
      return response.data;
    }

    throw new Error(
      `模块标签同步失败：服务端未返回有效的标签ID，标签名称：${label.name}`,
    );
  }

  /**
   * 执行共享变量任务
   */
  private async executeSharedVariableTask(
    draftSharedVariable: DraftSharedVariable,
    context: TaskExecutionContext,
  ): Promise<string> {
    const variable = { ...draftSharedVariable };

    // 更新版本ID引用
    const newVersionId = context.idMapping.versionIds.get(
      variable.promptVersionId,
    );
    if (newVersionId) {
      variable.promptVersionId = newVersionId;
    }

    const params = SharedVariableConverter.toServerParams(variable);
    const response = await this.retryRequest(() =>
      SharedVariableService.addOrUpdateSharedVariable(params),
    );

    if (response?.success && typeof response.data === "string") {
      return response.data;
    }

    throw new Error(
      `共享变量同步失败：服务端未返回有效的变量ID，变量名称：${variable.name}`,
    );
  }

  /**
   * 执行模块任务
   */
  private async executeModuleTask(
    draftModule: DraftModule,
    context: TaskExecutionContext,
  ): Promise<string> {
    const module = { ...draftModule };

    // 更新版本ID引用
    const newVersionId = context.idMapping.versionIds.get(
      module.promptVersionId,
    );
    if (newVersionId) {
      module.promptVersionId = newVersionId;
    }

    // 更新模块标签ID引用
    if (module.moduleLabelIds) {
      try {
        const labelIds = JSON.parse(module.moduleLabelIds) as string[];
        const updatedLabelIds = labelIds.map((draftId) => {
          const newId = context.idMapping.moduleLabelIds.get(draftId);
          return newId || draftId;
        });
        module.moduleLabelIds = JSON.stringify(updatedLabelIds);
      } catch (error) {
        // 保持原值
      }
    }

    // 更新模块内容中的共享变量ID引用
    if (module.prompt) {
      module.prompt = PromptContentUpdater.updateSharedVariableIds(
        module.prompt,
        context.idMapping.sharedVariableIds,
      );
    }

    const params = ModuleConverter.toServerParams(module);
    const response = await this.retryRequest(() =>
      ModuleService.addOrUpdateModule(params),
    );

    if (response?.success && typeof response.data === "string") {
      return response.data;
    }

    throw new Error(
      `模块同步失败：服务端未返回有效的模块ID，模块名称：${module.name}`,
    );
  }

  /**
   * 执行能力任务
   */
  private async executeAbilityTask(
    draftData: DraftAbility,
    context: TaskExecutionContext,
  ): Promise<string> {
    const ability = { ...draftData };

    // 更新版本ID引用
    const newVersionId = context.idMapping.versionIds.get(
      ability.promptVersionId,
    );
    if (newVersionId) {
      ability.promptVersionId = newVersionId;
    }

    // 更新能力内容中的模块ID引用
    if (ability.content) {
      ability.content = PromptContentUpdater.updateModuleIds(
        ability.content,
        context.idMapping.moduleIds,
      );
    }

    const params = AbilityConverter.toServerParams(ability);
    const response = await this.retryRequest(() =>
      AbilityService.addOrUpdateAbility(params),
    );

    if (response?.success && typeof response.data === "string") {
      return response.data;
    }

    throw new Error(
      `能力同步失败：服务端未返回有效的能力ID，能力名称：${ability.name}`,
    );
  }

  /**
   * 执行删除能力任务
   */
  private async executeDeleteAbilityTask(abilityId: string): Promise<string> {
    const response = await this.retryRequest(() =>
      AbilityService.deleteAbility({ abilityId }),
    );

    if (response?.success) {
      return abilityId;
    }

    throw new Error(
      `能力删除失败：ID=${abilityId}，服务端响应：${JSON.stringify(response)}`,
    );
  }

  /**
   * 执行删除模块任务
   */
  private async executeDeleteModuleTask(moduleId: string): Promise<string> {
    const response = await this.retryRequest(() =>
      ModuleService.deleteModule({ moduleId }),
    );

    if (response?.success) {
      return moduleId;
    }

    throw new Error(
      `模块删除失败：ID=${moduleId}，服务端响应：${JSON.stringify(response)}`,
    );
  }

  /**
   * 执行删除共享变量任务
   */
  private async executeDeleteSharedVariableTask(
    sharedVariableId: string,
  ): Promise<string> {
    const response = await this.retryRequest(() =>
      SharedVariableService.deleteSharedVariable({ sharedVariableId }),
    );

    if (response?.success) {
      return sharedVariableId;
    }

    throw new Error(
      `共享变量删除失败：ID=${sharedVariableId}，服务端响应：${JSON.stringify(response)}`,
    );
  }

  /**
   * 执行删除模块标签任务
   */
  private async executeDeleteModuleLabelTask(
    moduleLabelId: string,
  ): Promise<string> {
    const response = await this.retryRequest(() =>
      ModuleService.deleteModuleLabel({ moduleLabelId }),
    );

    if (response?.success) {
      return moduleLabelId;
    }

    throw new Error(
      `模块标签删除失败：ID=${moduleLabelId}，服务端响应：${JSON.stringify(response)}`,
    );
  }

  /**
   * 执行请求（不重试，重试逻辑在任务级别处理）
   */
  private async retryRequest<T>(requestFn: () => Promise<T>): Promise<T> {
    return await requestFn();
  }
}
