/**
 * 基于队列的数据同步状态管理
 */

import { PromptVersionStatus } from "@/types/api/prompt-version";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import {
  abilityDb,
  moduleDb,
  moduleLabelDb,
  selectedVersionIdAtom,
  sharedVariableDb,
  syncQueueDb,
  versionDb,
} from "../../atoms/core";
import {
  CompleteDraftVersionData,
  CompleteVersionData,
  DraftAbility,
  DraftModule,
  DraftModuleLabel,
  DraftSharedVariable,
  DraftVersion,
} from "../../types";
import { QueueSyncService } from "./queue-sync-service";
import {
  QueueProgress,
  QueueStatus,
  QueueTask,
  QueueTaskStatus,
  SyncDataType,
  SyncErrorType,
  SyncOptions,
  SyncQueue,
  SyncResult,
} from "./types";

/**
 * 当前正在运行的队列ID
 */
export const runningQueueIdAtom = atomWithReset<string | null>(null);

/**
 * 计算数据的关键字段哈希值（只包含影响同步依赖关系的字段）
 */
function calculateCriticalDataHash(data: any, type: SyncDataType): string {
  let criticalData: Record<string, any>;

  switch (type) {
    case SyncDataType.VERSION:
      // 版本的关键字段：ID和基本结构
      criticalData = {
        id: data.id,
        // 版本名称变更不影响依赖关系，所以不包含
      };
      break;

    case SyncDataType.MODULE_LABEL:
      // 模块标签的关键字段：ID
      criticalData = {
        id: data.id,
        // 名称变更不影响依赖关系
      };
      break;

    case SyncDataType.SHARED_VARIABLE:
      // 共享变量的关键字段：ID和变量名（影响引用）
      criticalData = {
        id: data.id,
        name: data.name, // 变量名影响其他模块的引用
      };
      break;

    case SyncDataType.MODULE:
      // 模块的关键字段：ID、标签关联、共享变量引用
      criticalData = {
        id: data.id,
        labelIds: data.labelIds || [], // 标签关联变更影响依赖
        // 内容变更不影响依赖关系，所以不包含content
      };
      break;

    case SyncDataType.ABILITY:
      // 能力的关键字段：ID、模块关联
      criticalData = {
        id: data.id,
        moduleIds: data.moduleIds || [], // 模块关联变更影响依赖
      };
      break;

    default:
      criticalData = { id: data.id };
  }

  // 计算哈希
  const jsonStr = JSON.stringify(
    criticalData,
    Object.keys(criticalData).sort(),
  );

  let hash = 0;

  for (let i = 0; i < jsonStr.length; i++) {
    const char = jsonStr.charCodeAt(i);
    hash = (hash << 5) - hash + char;
    hash = hash & hash; // 转换为32位整数
  }

  return hash.toString(36);
}

/**
 * 根据任务信息获取最新的草稿数据
 */
export const getDraftDataForTaskAtom = atom(
  (get) =>
    (
      task: QueueTask,
    ):
      | DraftVersion
      | DraftModule
      | DraftAbility
      | DraftModuleLabel
      | DraftSharedVariable
      | null
      | undefined => {
      switch (task.type) {
        case SyncDataType.VERSION: {
          const versions = get(versionDb.values);

          return Array.from(versions).find((v) => v.id === task.draftId);
        }
        case SyncDataType.MODULE_LABEL: {
          const labels = get(moduleLabelDb.values);

          return Array.from(labels).find((l) => l.id === task.draftId);
        }
        case SyncDataType.SHARED_VARIABLE: {
          const variables = get(sharedVariableDb.values);

          return Array.from(variables).find((v) => v.id === task.draftId);
        }
        case SyncDataType.MODULE: {
          const modules = get(moduleDb.values);

          return Array.from(modules).find((m) => m.id === task.draftId);
        }
        case SyncDataType.ABILITY: {
          const abilities = get(abilityDb.values);

          return Array.from(abilities).find((a) => a.id === task.draftId);
        }
        // 删除任务不需要草稿数据，返回null
        case SyncDataType.DELETE_ABILITY:
        case SyncDataType.DELETE_MODULE:
        case SyncDataType.DELETE_SHARED_VARIABLE:
        case SyncDataType.DELETE_MODULE_LABEL:
          return null;
        default:
          return null;
      }
    },
);

/**
 * 检查任务数据是否发生关键变更（影响依赖关系的变更）
 */
export const checkTaskDataChangedAtom = atom(
  (get) =>
    (task: QueueTask): boolean => {
      const getDraftData = get(getDraftDataForTaskAtom);
      const currentData = getDraftData(task);

      if (!currentData) {
        // 数据不存在，认为已变更
        return true;
      }

      const currentHash = calculateCriticalDataHash(currentData, task.type);

      return currentHash !== task.dataHash;
    },
);

/**
 * 检查是否有队列正在运行
 */
export const isAnyQueueRunningAtom = atom((get) => {
  const runningQueueId = get(runningQueueIdAtom);
  return runningQueueId !== null;
});

/**
 * 检查当前版本是否有队列正在运行
 */
export const isCurrentVersionQueueRunningAtom = atom((get) => {
  const runningQueueId = get(runningQueueIdAtom);
  const activeQueueId = get(activeQueueIdAtom);
  return runningQueueId === activeQueueId;
});

/**
 * 获取当前选中版本的队列列表
 */
export const getCurrentVersionQueuesAtom = atom((get) => {
  const selectedVersionId = get(selectedVersionIdAtom);

  if (!selectedVersionId) {
    return [];
  }

  const queues = get(syncQueueDb.values);

  return Array.from(queues)
    .filter((q) => q.versionId === selectedVersionId)
    .sort((a, b) => b.createdAt - a.createdAt);
});

/**
 * 获取当前选中版本的最新队列（活跃队列）
 */
export const getCurrentVersionLatestQueueAtom = atom((get) => {
  const queues = get(getCurrentVersionQueuesAtom);

  return queues.length > 0 ? queues[0] : null;
});

/**
 * 获取当前选中版本的活跃队列ID
 */
export const activeQueueIdAtom = atom((get) => {
  const latestQueue = get(getCurrentVersionLatestQueueAtom);

  return latestQueue?.id || null;
});

/**
 * 获取当前选中版本的队列进度状态
 */
export const queueProgressAtom = atom((get) => {
  const latestQueue = get(getCurrentVersionLatestQueueAtom);

  if (!latestQueue) {
    return null;
  }

  const runningQueueId = get(runningQueueIdAtom);

  // 从队列数据计算进度
  const completedTasks = latestQueue.tasks.filter(
    (task) => task.status === QueueTaskStatus.SUCCESS,
  ).length;

  const failedTasks = latestQueue.tasks.filter(
    (task) => task.status === QueueTaskStatus.FAILED,
  ).length;

  const totalTasks = latestQueue.tasks.length;
  const overallProgress = totalTasks > 0 ? completedTasks / totalTasks : 0;

  // 找到当前正在执行的任务
  const currentTask = latestQueue.tasks.find(
    (task) => task.status === QueueTaskStatus.RUNNING,
  );

  // 确定队列状态：如果当前队列正在运行，则状态为RUNNING，否则使用持久化状态
  const queueStatus =
    runningQueueId === latestQueue.id
      ? QueueStatus.RUNNING
      : latestQueue.status;

  return {
    queueId: latestQueue.id,
    status: queueStatus,
    totalTasks,
    completedTasks,
    failedTasks,
    overallProgress,
    currentTask,
  } as QueueProgress;
});

/**
 * 获取当前选中版本的同步结果状态
 */
export const syncResultAtom = atom((get) => {
  const latestQueue = get(getCurrentVersionLatestQueueAtom);

  if (!latestQueue) {
    return null;
  }

  const runningQueueId = get(runningQueueIdAtom);

  // 如果队列正在运行，不返回结果
  if (runningQueueId === latestQueue.id) {
    return null;
  }

  // 根据队列状态生成同步结果
  if (latestQueue.status === QueueStatus.SUCCESS) {
    // 从版本任务中获取同步后的版本ID
    const versionTask = latestQueue.tasks.find(
      (task) => task.type === SyncDataType.VERSION,
    );

    return {
      success: true,
      queueId: latestQueue.id,
      syncedVersionId: versionTask?.syncedId || latestQueue.id,
    } as SyncResult;
  } else if (latestQueue.status === QueueStatus.FAILED) {
    // 收集失败任务的错误信息
    const errors = latestQueue.tasks
      .filter((task) => task.status === QueueTaskStatus.FAILED && task.error)
      .map((task) => ({
        type: task.error!.type,
        message: task.error!.message,
        dataId: task.draftId,
        taskId: task.id,
        originalError: task.error!.originalError,
      }));

    return {
      success: false,
      queueId: latestQueue.id,
      errors,
    } as SyncResult;
  }

  // 等待中或暂停的队列没有结果
  return null;
});

/**
 * 获取草稿数据
 */
/**
 * 获取草稿数据
 */
export const getDraftDataByVersionAtom = atom(
  (get) =>
    async (versionId: string): Promise<CompleteDraftVersionData | null> => {
      await get(versionDb.suspendBeforeInit);
      await get(moduleLabelDb.suspendBeforeInit);
      await get(sharedVariableDb.suspendBeforeInit);
      await get(moduleDb.suspendBeforeInit);
      await get(abilityDb.suspendBeforeInit);

      const allVersions = get(versionDb.values);
      const allModuleLabels = get(moduleLabelDb.values);
      const allSharedVariables = get(sharedVariableDb.values);
      const allModules = get(moduleDb.values);
      const allAbilities = get(abilityDb.values);
      const draftVersion = Array.from(allVersions).find(
        (v) => v.id === versionId,
      );

      if (!draftVersion) {
        return null;
      }

      const draftModuleLabels = Array.from(allModuleLabels).filter(
        (label) => label.promptVersionId === versionId,
      );
      const draftSharedVariables = Array.from(allSharedVariables).filter(
        (variable) => variable.promptVersionId === versionId,
      );
      const draftModules = Array.from(allModules).filter(
        (module) => module.promptVersionId === versionId,
      );
      const draftAbilities = Array.from(allAbilities).filter(
        (ability) => ability.promptVersionId === versionId,
      );

      return {
        draftVersion,
        draftModuleLabels,
        draftSharedVariables,
        draftModules,
        draftAbilities,
      };
    },
);

export const getDraftDataAtom = atom(
  (get) =>
    async (draftId: string): Promise<CompleteVersionData | null> => {
      const draftData = await get(getDraftDataByVersionAtom)(draftId);

      if (!draftData) {
        return null;
      }

      const {
        draftVersion,
        draftModuleLabels,
        draftSharedVariables,
        draftModules,
        draftAbilities,
      } = draftData;

      return {
        version: {
          ...draftVersion,
          promptVersionId: draftVersion.id,
          remark: "",
          version: "",
          status: PromptVersionStatus.SUBMIT,
        },
        abilities: draftAbilities,
        modules: draftModules,
        moduleLabels: draftModuleLabels,
        sharedVariables: draftSharedVariables,
      };
    },
);

/**
 * 清理草稿数据
 */
export const cleanupDraftDataAtom = atom(
  null,
  (get, set, versionId: string) => {
    const allModuleLabels = get(moduleLabelDb.values);
    const allSharedVariables = get(sharedVariableDb.values);
    const allModules = get(moduleDb.values);
    const allAbilities = get(abilityDb.values);

    // 删除相关的模块标签
    Array.from(allModuleLabels)
      .filter((label) => label.promptVersionId === versionId)
      .forEach((label) => set(moduleLabelDb.delete, label.id));

    // 删除相关的共享变量
    Array.from(allSharedVariables)
      .filter((variable) => variable.promptVersionId === versionId)
      .forEach((variable) => set(sharedVariableDb.delete, variable.id));

    // 删除相关的模块
    Array.from(allModules)
      .filter((module) => module.promptVersionId === versionId)
      .forEach((module) => set(moduleDb.delete, module.id));

    // 删除相关的能力
    Array.from(allAbilities)
      .filter((ability) => ability.promptVersionId === versionId)
      .forEach((ability) => set(abilityDb.delete, ability.id));

    // 删除版本
    set(versionDb.delete, versionId);
  },
);

/**
 * 创建同步队列
 */
export const createSyncQueueAtom = atom(
  null,
  async (
    get,
    set,
    versionId: string,
    options: SyncOptions = {},
    versionInfo: {
      remark: string;
      versionName: string;
    },
  ) => {
    // 获取草稿数据
    const getDraftDataByVersion = get(getDraftDataByVersionAtom);
    const draftData = await getDraftDataByVersion(versionId);

    if (!draftData) {
      throw new Error(`草稿数据不存在: ${versionId}`);
    }

    // 获取数据获取和检查函数
    const getDraftDataForTask = get(getDraftDataForTaskAtom);
    const checkDataChanged = get(checkTaskDataChangedAtom);

    // 创建同步服务（不需要进度回调，因为进度从队列数据计算）
    const syncService = new QueueSyncService(
      options,
      undefined,
      getDraftDataForTask,
      checkDataChanged,
      getDraftDataByVersion,
    );

    // 创建队列
    const queue = await syncService.createSyncQueue(
      versionId,
      draftData,
      versionInfo,
    );

    // 保存队列到数据库
    set(syncQueueDb.set, queue.id, queue);

    return queue;
  },
);

/**
 * 执行同步队列
 */
export const executeSyncQueueAtom = atom(
  null,
  async (get, set, queue: SyncQueue) => {
    try {
      // 设置当前正在运行的队列ID
      set(runningQueueIdAtom, queue.id);

      // 获取数据获取和检查函数
      const getDraftDataForTask = get(getDraftDataForTaskAtom);
      const checkDataChanged = get(checkTaskDataChangedAtom);
      const getDraftDataByVersion = get(getDraftDataByVersionAtom);

      // 创建同步服务
      const syncService = new QueueSyncService(
        {},
        () => {
          const updatedQueue = { ...queue };

          set(syncQueueDb.set, queue.id, updatedQueue);
        },
        getDraftDataForTask,
        checkDataChanged,
        getDraftDataByVersion,
      );

      // 执行队列
      const result = await syncService.executeQueue(queue);

      // 更新队列到数据库
      set(syncQueueDb.set, queue.id, queue);

      // 如果同步成功且需要清理本地数据
      if (result.success && queue.options.cleanupOnSuccess !== false) {
        set(cleanupDraftDataAtom, queue.versionId);
      }

      return result;
    } catch (error) {
      const errorResult: SyncResult = {
        success: false,
        errors: [
          {
            type: SyncErrorType.SERVER_ERROR,
            message:
              error instanceof Error ? error.message : "执行队列时发生未知错误",
            originalError: error,
          },
        ],
      };

      return errorResult;
    } finally {
      // 无论成功还是失败，都清除运行状态
      set(runningQueueIdAtom, null);
    }
  },
);

/**
 * 重试失败的任务
 */
export const retryFailedTasksAtom = atom(
  null,
  async (get, set, queueId: string) => {
    try {
      // 获取队列
      const queues = get(syncQueueDb.values);
      const queue = Array.from(queues).find((q) => q.id === queueId);

      if (!queue) {
        throw new Error(`队列不存在: ${queueId}`);
      }

      // 设置当前正在运行的队列ID
      set(runningQueueIdAtom, queueId);

      // 重置失败的任务状态
      queue.tasks.forEach((task) => {
        if (task.status === QueueTaskStatus.FAILED) {
          task.status = QueueTaskStatus.PENDING;
          task.error = undefined;
          task.startedAt = undefined;
          task.completedAt = undefined;
          task.retryCount = 0;
        }
      });

      // 重置队列状态
      queue.status = QueueStatus.PENDING;
      queue.startedAt = undefined;
      queue.completedAt = undefined;

      // 更新队列到数据库
      set(syncQueueDb.set, queueId, queue);

      // 重新执行队列
      return await set(executeSyncQueueAtom, queue);
    } catch (error) {
      const errorResult: SyncResult = {
        success: false,
        errors: [
          {
            type: SyncErrorType.SERVER_ERROR,
            message:
              error instanceof Error ? error.message : "重试任务时发生未知错误",
            originalError: error,
          },
        ],
      };

      return errorResult;
    } finally {
      // 无论成功还是失败，都清除运行状态
      set(runningQueueIdAtom, null);
    }
  },
);

/**
 * 获取队列详情
 */
export const getQueueAtom = atom((get) => (queueId: string) => {
  const queues = get(syncQueueDb.values);

  return Array.from(queues).find((q) => q.id === queueId) || null;
});

/**
 * 获取所有队列
 */
export const getAllQueuesAtom = atom((get) => {
  const queues = get(syncQueueDb.values);

  return Array.from(queues).sort((a, b) => b.createdAt - a.createdAt);
});

/**
 * 删除队列
 */
export const deleteQueueAtom = atom(null, (_get, set, queueId: string) => {
  // 直接删除队列，状态会自动从队列数据计算
  set(syncQueueDb.delete, queueId);
});

/**
 * 检查版本是否有数据可同步
 */
export const hasDataToSyncAtom = atom((get) => (versionId: string) => {
  const allModuleLabels = get(moduleLabelDb.values);
  const allSharedVariables = get(sharedVariableDb.values);
  const allModules = get(moduleDb.values);
  const allAbilities = get(abilityDb.values);
  const hasModuleLabels = Array.from(allModuleLabels).some(
    (label) => label.promptVersionId === versionId,
  );
  const hasSharedVariables = Array.from(allSharedVariables).some(
    (variable) => variable.promptVersionId === versionId,
  );
  const hasModules = Array.from(allModules).some(
    (module) => module.promptVersionId === versionId,
  );
  const hasAbilities = Array.from(allAbilities).some(
    (ability) => ability.promptVersionId === versionId,
  );

  return hasModuleLabels || hasSharedVariables || hasModules || hasAbilities;
});

/**
 * 获取失败的任务
 */
export const getFailedTasksAtom = atom((get) => (queueId: string) => {
  const queue = get(getQueueAtom)(queueId);

  if (!queue) {
    return [];
  }

  return queue.tasks.filter((task) => task.status === QueueTaskStatus.FAILED);
});

/**
 * 获取队列进度文本
 */
export const getQueueProgressTextAtom = atom((get) => {
  const progress = get(queueProgressAtom);

  if (!progress) {
    return "";
  }

  const statusTexts = {
    [QueueStatus.PENDING]: "等待开始...",
    [QueueStatus.RUNNING]: "同步中...",
    [QueueStatus.PAUSED]: "已暂停",
    [QueueStatus.SUCCESS]: "同步完成",
    [QueueStatus.FAILED]: "同步失败",
  };

  let text = statusTexts[progress.status] || "同步中...";

  if (progress.currentTask && progress.status === QueueStatus.RUNNING) {
    text += ` (${progress.currentTask.displayName})`;
  }

  return text;
});

/**
 * 检查是否可以重试
 */
export const canRetryAtom = atom((get) => (queueId: string) => {
  const queue = get(getQueueAtom)(queueId);

  if (!queue) {
    return false;
  }

  // 检查是否有失败的任务
  const hasFailedTasks = queue.tasks.some(
    (task) => task.status === QueueTaskStatus.FAILED,
  );

  if (!hasFailedTasks || queue.status !== QueueStatus.FAILED) {
    return false;
  }

  // 检查是否有任务数据发生变更
  const checkDataChanged = get(checkTaskDataChangedAtom);
  const hasDataChanged = queue.tasks.some((task) => checkDataChanged(task));

  // 只有在数据未变更的情况下才允许重试
  return !hasDataChanged;
});

/**
 * 获取当前版本的队列状态摘要
 */
export const currentVersionQueueSummaryAtom = atom((get) => {
  const selectedVersionId = get(selectedVersionIdAtom);

  if (!selectedVersionId) {
    return null;
  }

  const activeQueueId = get(activeQueueIdAtom);
  const queueProgress = get(queueProgressAtom);
  const syncResult = get(syncResultAtom);
  const queues = get(getCurrentVersionQueuesAtom);

  return {
    versionId: selectedVersionId,
    activeQueueId,
    queueProgress,
    syncResult,
    totalQueues: queues.length,
    hasActiveQueue: !!activeQueueId,
    canRetry: activeQueueId ? get(canRetryAtom)(activeQueueId) : false,
  };
});

/**
 * 一键同步（创建并执行队列）
 */
export const quickSyncAtom = atom(
  null,
  async (
    _,
    set,
    versionId: string,
    options: SyncOptions = {},
    versionInfo: {
      remark: string;
      versionName: string;
    },
  ) => {
    try {
      // 如果没有提供版本ID，使用当前选中的版本ID
      const targetVersionId = versionId;

      if (!targetVersionId) {
        throw new Error("未指定版本ID，且当前没有选中的版本");
      }

      // 创建队列
      const queue = await set(
        createSyncQueueAtom,
        targetVersionId,
        options,
        versionInfo,
      );

      // 如果设置了自动开始，立即执行
      if (options.autoStart !== false) {
        return await set(executeSyncQueueAtom, queue);
      }

      return {
        success: true,
        queueId: queue.id,
        syncedVersionId: queue.id,
        versionId: targetVersionId,
      } as SyncResult;
    } catch (error) {
      return {
        success: false,
        errors: [
          {
            type: SyncErrorType.SERVER_ERROR,
            message: error instanceof Error ? error.message : "快速同步失败",
            originalError: error,
          },
        ],
      } as SyncResult;
    }
  },
);
