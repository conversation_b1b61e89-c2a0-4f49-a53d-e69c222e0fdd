/**
 * 提示词内容ID更新器
 */
export class PromptContentUpdater {
  /**
   * 更新模块内容中的共享变量ID引用
   */
  static updateSharedVariableIds(
    promptContent: string,
    sharedVariableIdMapping: Map<string, string>,
  ): string {
    if (!promptContent || !promptContent.trim()) {
      return promptContent;
    }

    try {
      const content = JSON.parse(promptContent);
      const updatedContent = this.updateSharedVariableIdsInObject(
        content,
        sharedVariableIdMapping,
      );

      return JSON.stringify(updatedContent);
    } catch (error) {
      // JSON 解析失败，返回原内容
      return promptContent;
    }
  }

  /**
   * 更新能力内容中的模块ID引用
   */
  static updateModuleIds(
    abilityContent: string,
    moduleIdMapping: Map<string, string>,
  ): string {
    if (!abilityContent || !abilityContent.trim()) {
      return abilityContent;
    }

    try {
      const content = JSON.parse(abilityContent);
      const updatedContent = this.updateModuleIdsInObject(
        content,
        moduleIdMapping,
      );

      return JSON.stringify(updatedContent);
    } catch (error) {
      // JSON 解析失败，返回原内容
      return abilityContent;
    }
  }

  /**
   * 递归更新对象中的共享变量ID引用
   */
  private static updateSharedVariableIdsInObject(
    obj: any,
    idMapping: Map<string, string>,
  ): any {
    if (!obj || typeof obj !== "object") {
      return obj;
    }

    if (Array.isArray(obj)) {
      return obj.map((item) =>
        this.updateSharedVariableIdsInObject(item, idMapping),
      );
    }

    // 检查是否是共享变量引用节点
    if (obj.type === "sharedVariableReference" && obj.attrs?.sharedVariableId) {
      const currentId = obj.attrs.sharedVariableId;
      const newId = idMapping.get(currentId);

      if (newId) {
        return {
          ...obj,
          attrs: {
            ...obj.attrs,
            sharedVariableId: newId,
          },
        };
      }
    }

    // 递归处理对象的所有属性
    const result: any = {};

    for (const [key, value] of Object.entries(obj)) {
      result[key] = this.updateSharedVariableIdsInObject(value, idMapping);
    }

    return result;
  }

  /**
   * 递归更新对象中的模块ID引用
   */
  private static updateModuleIdsInObject(
    obj: any,
    idMapping: Map<string, string>,
  ): any {
    if (!obj || typeof obj !== "object") {
      return obj;
    }

    if (Array.isArray(obj)) {
      return obj.map((item) => this.updateModuleIdsInObject(item, idMapping));
    }

    // 检查是否是模块引用节点（在 abilityItem 的 content 中）
    if (obj.type === "module" && obj.attrs?.id) {
      const currentId = obj.attrs.id;
      const newId = idMapping.get(currentId);

      if (newId) {
        return {
          ...obj,
          attrs: {
            ...obj.attrs,
            id: newId,
          },
        };
      }
    }

    // 递归处理对象的所有属性
    const result: any = {};

    for (const [key, value] of Object.entries(obj)) {
      result[key] = this.updateModuleIdsInObject(value, idMapping);
    }

    return result;
  }
}
