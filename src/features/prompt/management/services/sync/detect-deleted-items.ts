/**
 * 删除项检测工具函数
 */

import { CompleteDraftVersionData, CompleteVersionData } from "../../types";
import { isDraftId } from "../../utils";

/**
 * 删除项检测结果
 */
export interface DeletedItems {
  deletedAbilities: Array<{ id: string; name: string }>;
  deletedModules: Array<{ id: string; name: string }>;
  deletedSharedVariables: Array<{ id: string; name: string }>;
  deletedModuleLabels: Array<{ id: string; name: string }>;
}

/**
 * 检测删除的项目
 * 通过对比基准版本数据和草稿数据，识别被删除的项目
 */
export function detectDeletedItems(
  baseData: CompleteVersionData,
  draftData: CompleteDraftVersionData,
): DeletedItems {
  // 如果基准数据为空，返回空结果
  if (!baseData || !draftData) {
    return {
      deletedAbilities: [],
      deletedModules: [],
      deletedSharedVariables: [],
      deletedModuleLabels: [],
    };
  }

  // 检测删除的能力
  const deletedAbilities = (baseData.abilities || [])
    .filter((baseAbility) => {
      // 在草稿数据中查找对应的能力
      // 只考虑非草稿ID的数据（即来自基准版本的数据）
      return !(draftData.draftAbilities || []).find((draftAbility) => {
        // 如果草稿数据的ID是草稿ID，跳过（这是新创建的）
        if (isDraftId(draftAbility.id)) {
          return false;
        }

        // 匹配服务端ID
        return draftAbility.id === baseAbility.id;
      });
    })
    .map((ability) => ({
      id: ability.id,
      name: ability.name,
    }));

  // 检测删除的模块
  const deletedModules = (baseData.modules || [])
    .filter((baseModule) => {
      return !(draftData.draftModules || []).find((draftModule) => {
        if (isDraftId(draftModule.id)) {
          return false;
        }
        return draftModule.id === baseModule.id;
      });
    })
    .map((module) => ({
      id: module.id,
      name: module.name,
    }));

  // 检测删除的共享变量
  const deletedSharedVariables = (baseData.sharedVariables || [])
    .filter((baseVariable) => {
      return !(draftData.draftSharedVariables || []).find((draftVariable) => {
        if (isDraftId(draftVariable.id)) {
          return false;
        }
        return draftVariable.id === baseVariable.id;
      });
    })
    .map((variable) => ({
      id: variable.id,
      name: variable.name,
    }));

  // 检测删除的模块标签
  const deletedModuleLabels = (baseData.moduleLabels || [])
    .filter((baseLabel) => {
      return !(draftData.draftModuleLabels || []).find((draftLabel) => {
        if (isDraftId(draftLabel.id)) {
          return false;
        }
        return draftLabel.id === baseLabel.id;
      });
    })
    .map((label) => ({
      id: label.id,
      name: label.name,
    }));

  return {
    deletedAbilities,
    deletedModules,
    deletedSharedVariables,
    deletedModuleLabels,
  };
}
