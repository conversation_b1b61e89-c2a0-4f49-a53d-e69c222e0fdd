/**
 * 基于队列的数据同步相关类型定义
 */

/**
 * 同步结果
 */
export interface SyncResult {
  /**
   * 是否成功
   */
  success: boolean;
  /**
   * 同步后的服务端版本ID
   */
  syncedVersionId?: string;
  /**
   * 队列ID
   */
  queueId?: string;
  /**
   * 错误信息
   */
  errors?: SyncError[];
  /**
   * 同步的数据统计
   */
  stats?: SyncStats;
}

/**
 * 同步错误
 */
export interface SyncError {
  /**
   * 错误类型
   */
  type: SyncErrorType;
  /**
   * 错误消息
   */
  message: string;
  /**
   * 相关的数据ID
   */
  dataId?: string;
  /**
   * 数据类型
   */
  dataType?: SyncDataType;
  /**
   * 任务ID
   */
  taskId?: string;
  /**
   * 原始错误
   */
  originalError?: unknown;
}

/**
 * 同步错误类型
 */
export enum SyncErrorType {
  /**
   * 网络错误
   */
  NETWORK_ERROR = "NETWORK_ERROR",
  /**
   * 数据验证错误
   */
  VALIDATION_ERROR = "VALIDATION_ERROR",
  /**
   * 服务端错误
   */
  SERVER_ERROR = "SERVER_ERROR",
  /**
   * 数据转换错误
   */
  CONVERSION_ERROR = "CONVERSION_ERROR",
  /**
   * 依赖错误（如关联数据不存在）
   */
  DEPENDENCY_ERROR = "DEPENDENCY_ERROR",
  /**
   * 任务超时错误
   */
  TIMEOUT_ERROR = "TIMEOUT_ERROR",
}

/**
 * 同步数据类型
 */
export enum SyncDataType {
  VERSION = "VERSION",
  MODULE_LABEL = "MODULE_LABEL",
  SHARED_VARIABLE = "SHARED_VARIABLE",
  MODULE = "MODULE",
  ABILITY = "ABILITY",
  DELETE_MODULE_LABEL = "DELETE_MODULE_LABEL",
  DELETE_SHARED_VARIABLE = "DELETE_SHARED_VARIABLE",
  DELETE_MODULE = "DELETE_MODULE",
  DELETE_ABILITY = "DELETE_ABILITY",
}

/**
 * 同步统计信息
 */
export interface SyncStats {
  /**
   * 版本数量
   */
  versionCount: number;
  /**
   * 模块标签数量
   */
  moduleLabelCount: number;
  /**
   * 共享变量数量
   */
  sharedVariableCount: number;
  /**
   * 模块数量
   */
  moduleCount: number;
  /**
   * 能力数量
   */
  abilityCount: number;
}

/**
 * 队列任务状态
 */
export enum QueueTaskStatus {
  /**
   * 等待执行
   */
  PENDING = "PENDING",
  /**
   * 正在执行
   */
  RUNNING = "RUNNING",
  /**
   * 执行成功
   */
  SUCCESS = "SUCCESS",
  /**
   * 执行失败
   */
  FAILED = "FAILED",
  /**
   * 已跳过（依赖失败）
   */
  SKIPPED = "SKIPPED",
}

/**
 * 队列任务基础
 */
export interface QueueTaskBase {
  /**
   * 任务ID
   */
  id: string;
  /**
   * 任务状态
   */
  status: QueueTaskStatus;
  /**
   * 草稿数据ID
   */
  draftId: string;
  /**
   * 数据版本哈希（用于检测数据变更）
   */
  dataHash: string;
  /**
   * 同步后的服务端ID
   */
  syncedId?: string;
  /**
   * 依赖的任务ID列表
   */
  dependencies: string[];
  /**
   * 错误信息
   */
  error?: SyncError;
  /**
   * 重试次数
   */
  retryCount: number;
  /**
   * 最大重试次数
   */
  maxRetries: number;
  /**
   * 创建时间
   */
  createdAt: number;
  /**
   * 开始执行时间
   */
  startedAt?: number;
  /**
   * 完成时间
   */
  completedAt?: number;
  /**
   * 任务名称（用于显示）
   */
  displayName: string;
}

/**
 * 队列任务
 */
export type QueueTask = QueueTaskBase & {
  type: SyncDataType;
};

/**
 * 同步队列
 */
export interface SyncQueue {
  /**
   * 队列ID
   */
  id: string;
  /**
   * 版本ID
   */
  versionId: string;
  /**
   * 队列状态
   */
  status: QueueStatus;
  /**
   * 任务列表
   */
  tasks: QueueTask[];
  /**
   * ID映射表（序列化存储）
   */
  idMapping: SerializedIdMapping;
  /**
   * 同步选项
   */
  options: SyncOptions;
  /**
   * 创建时间
   */
  createdAt: number;
  /**
   * 开始执行时间
   */
  startedAt?: number;
  /**
   * 完成时间
   */
  completedAt?: number;
  /**
   * 版本信息
   */
  versionInfo: {
    remark: string;
    versionName: string;
  };
}

/**
 * 队列状态
 */
export enum QueueStatus {
  /**
   * 等待执行
   */
  PENDING = "PENDING",
  /**
   * 正在执行
   */
  RUNNING = "RUNNING",
  /**
   * 已暂停
   */
  PAUSED = "PAUSED",
  /**
   * 执行成功
   */
  SUCCESS = "SUCCESS",
  /**
   * 执行失败
   */
  FAILED = "FAILED",
}

/**
 * 队列进度信息
 */
export interface QueueProgress {
  /**
   * 队列ID
   */
  queueId: string;
  /**
   * 队列状态
   */
  status: QueueStatus;
  /**
   * 总任务数
   */
  totalTasks: number;
  /**
   * 已完成任务数
   */
  completedTasks: number;
  /**
   * 失败任务数
   */
  failedTasks: number;
  /**
   * 当前执行的任务
   */
  currentTask?: QueueTask;
  /**
   * 整体进度 (0-1)
   */
  overallProgress: number;
}

/**
 * 同步选项
 */
export interface SyncOptions {
  /**
   * 是否在同步成功后清理本地数据
   */
  cleanupOnSuccess?: boolean;
  /**
   * 网络请求超时时间（毫秒）
   */
  timeout?: number;
  /**
   * 重试次数
   */
  retryCount?: number;
  /**
   * 重试间隔（毫秒）
   */
  retryDelay?: number;
  /**
   * 是否自动开始执行队列
   */
  autoStart?: boolean;
}

/**
 * ID映射表（序列化存储）
 */
export interface SerializedIdMapping {
  /**
   * 版本ID映射 (草稿ID -> 服务端ID)
   */
  versionIds: Record<string, string>;
  /**
   * 模块标签ID映射
   */
  moduleLabelIds: Record<string, string>;
  /**
   * 共享变量ID映射
   */
  sharedVariableIds: Record<string, string>;
  /**
   * 模块ID映射
   */
  moduleIds: Record<string, string>;
  /**
   * 能力ID映射
   */
  abilityIds: Record<string, string>;
}

/**
 * ID映射表
 */
export interface IdMapping {
  /**
   * 版本ID映射 (草稿ID -> 服务端ID)
   */
  versionIds: Map<string, string>;
  /**
   * 模块标签ID映射
   */
  moduleLabelIds: Map<string, string>;
  /**
   * 共享变量ID映射
   */
  sharedVariableIds: Map<string, string>;
  /**
   * 模块ID映射
   */
  moduleIds: Map<string, string>;
  /**
   * 能力ID映射
   */
  abilityIds: Map<string, string>;
}

/**
 * 任务执行上下文
 */
export interface TaskExecutionContext {
  /**
   * ID映射表
   */
  idMapping: IdMapping;
  /**
   * ID映射管理器
   */
  idMappingManager: import("./id-mapping-manager").IdMappingManager;
  /**
   * 同步选项
   */
  options: Required<SyncOptions>;
  /**
   * 进度回调
   */
  onProgress?: (progress: QueueProgress) => void;
}
