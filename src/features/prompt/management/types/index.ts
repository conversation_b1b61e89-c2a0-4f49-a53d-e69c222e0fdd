import { Ability } from "@/types/api/ability";
import { Module } from "@/types/api/module";
import { Level } from "@/types/api/prompt";
import { PromptVersion } from "@/types/api/prompt-version";
import { SharedVariable } from "@/types/api/shared-variable";

export interface DraftBase {
  /**
   * uuid
   */
  id: string;
  /**
   * 创建时间
   */
  createdAt: string;
  /**
   * 更新时间
   */
  updatedAt: string;
  /**
   * 源版本ID，用于追踪来源和计算变更
   */
  sourceVersionId?: string;
  /**
   * 草稿名称，区分同一版本的多个草稿
   */
  draftName?: string;
}

/**
 * 草稿版本
 */
export type DraftVersion = DraftBase &
  Omit<
    PromptVersion,
    | "promptVersionId"
    | "version"
    | "status"
    | "versionChange"
    | "evaluationTasks"
    | "submitDate"
    | "offlineDate"
    | "onlineDate"
    | "remark"
  > & {
    /**
     * 基准版本ID，用于追踪草稿的来源版本
     */
    baseVersionId?: string;
  };

/**
 * 草稿能力
 */
export interface DraftAbility
  extends DraftBase,
    Omit<Ability, "abilityId" | "promptVersionId"> {
  /**
   * 版本 id
   */
  promptVersionId: string;
}

/**
 * 草稿模块
 */
export interface DraftModule
  extends DraftBase,
    Omit<Module, "moduleId" | "promptVersionId" | "status"> {
  /**
   * 版本 id
   */
  promptVersionId: string;
}

/**
 * 草稿模块标签
 */
export interface DraftModuleLabel extends DraftBase {
  /**
   * 草稿版本 id
   */
  promptVersionId: string;

  /**
   * 模块标签名称
   */
  name: string;
}

/**
 * 草稿共享变量
 */
export interface DraftSharedVariable
  extends DraftBase,
    Omit<SharedVariable, "sharedVariableId" | "promptVersionId" | "status"> {
  /**
   * 版本 id
   */
  promptVersionId: string;
}

/**
 * 通用能力数据结构
 */
export interface UniversalAbility {
  id: string;
  name: string;
  content: string;
  promptVersionId: string;
}

/**
 * 通用共享变量
 */
export interface UniversalSharedVariable
  extends Pick<
    SharedVariable,
    "name" | "content" | "definition" | "level" | "scopeDetail" | "promptName"
  > {
  id: string;
  promptVersionId: string;
}

/**
 * 通用模块标签数据结构
 */
export interface UniversalModuleLabel {
  id: string;
  promptVersionId: string;
  name: string;
}

/**
 * 通用模块数据结构
 */
export interface UniversalModule {
  id: string;
  name: string;
  moduleLabelIds?: string | null;
  promptVersionId: string;
  level: Level;
  prompt: string;
  scopeDetail?: string;
}

/**
 * 数据变更类型
 */
export enum ChangeType {
  ADDED = "added",
  REMOVED = "removed",
  MODIFIED = "modified",
}

/**
 * 完整的草稿版本数据
 */
export interface CompleteDraftVersionData {
  draftVersion: DraftVersion;
  draftModuleLabels: DraftModuleLabel[];
  draftSharedVariables: DraftSharedVariable[];
  draftModules: DraftModule[];
  draftAbilities: DraftAbility[];
}

/**
 * 完整的版本数据结构
 */
export interface CompleteVersionData {
  version: PromptVersion;
  abilities: UniversalAbility[];
  modules: UniversalModule[];
  moduleLabels: UniversalModuleLabel[];
  sharedVariables: UniversalSharedVariable[];
}
