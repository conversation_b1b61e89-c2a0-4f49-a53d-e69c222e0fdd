import { cn } from "@/lib/utils";
import { Eye, Lock } from "lucide-react";

export interface ReadOnlyNoticeProps {
  /** 自定义类名 */
  className?: string;
  /** 提示标题 */
  title?: string;
  /** 提示描述 */
  description?: string;
  /** 是否显示图标 */
  showIcon?: boolean;
  /** 变体样式 */
  variant?: "elegant" | "minimal" | "gradient" | "compact";
}

/**
 * 只读状态提示组件
 * 用于在非草稿版本时显示优雅的只读提示
 */
export const ReadOnlyNotice: React.FC<ReadOnlyNoticeProps> = ({
  className,
  title = "当前为只读模式",
  description = "只有草稿版本才能进行编辑操作，请切换到草稿版本或创建新的草稿版本。",
  showIcon = true,
  variant = "gradient",
}) => {
  const variants = {
    elegant: {
      container:
        "relative overflow-hidden bg-gradient-to-r from-slate-50 to-slate-100/50 border border-slate-200/60 backdrop-blur-sm",
      icon: "text-slate-500",
      title: "text-slate-800 font-semibold",
      description: "text-slate-600",
      decoration:
        "absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-slate-200/30 to-transparent rounded-full -translate-y-16 translate-x-16",
    },
    minimal: {
      container: "bg-white border-l-4 border-l-blue-400",
      icon: "text-blue-500",
      title: "text-gray-900 font-medium",
      description: "text-gray-600",
      decoration: "",
    },
    gradient: {
      container:
        "relative overflow-hidden bg-gradient-to-br from-blue-50 via-white to-indigo-50 border border-blue-200/50",
      icon: "text-blue-600",
      title: "text-blue-900 font-semibold",
      description: "text-blue-700/80",
      decoration:
        "absolute -top-4 -right-4 w-24 h-24 bg-gradient-to-br from-blue-200/40 to-indigo-200/40 rounded-full blur-xl",
    },
    compact: {
      container:
        "bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-200/60",
      icon: "text-purple-600",
      title: "text-purple-900 font-medium",
      description: "text-purple-700/90",
      decoration: "",
    },
  };

  const currentVariant = variants[variant];

  return (
    <div
      className={cn(
        "relative rounded-xl transition-all duration-300",
        variant === "compact" ? "p-3" : "p-4",
        currentVariant.container,
        className,
      )}
    >
      {/* 装饰元素 */}
      {currentVariant.decoration && (
        <div className={currentVariant.decoration} />
      )}

      {/* 主要内容 */}
      <div className="relative flex items-start gap-3">
        {showIcon && (
          <div className="flex-shrink-0 mt-0.5">
            {variant === "gradient" ? (
              <div className="relative bg-white rounded-full p-2 border border-blue-200">
                <Eye className={cn("h-4 w-4", currentVariant.icon)} />
              </div>
            ) : variant === "minimal" ? (
              <Lock className={cn("h-5 w-5", currentVariant.icon)} />
            ) : variant === "compact" ? (
              <div className="bg-purple-100 rounded-full p-1.5">
                <Lock className={cn("h-3.5 w-3.5", currentVariant.icon)} />
              </div>
            ) : (
              <div className="relative">
                <div className="absolute inset-0 bg-slate-300/20 rounded-full blur-sm" />
                <div className="relative bg-white/80 rounded-full p-2 border border-slate-200/50">
                  <Lock className={cn("h-4 w-4", currentVariant.icon)} />
                </div>
              </div>
            )}
          </div>
        )}

        <div className="flex-1 min-w-0">
          <div
            className={cn(
              variant === "compact"
                ? "text-xs leading-snug mb-0.5"
                : "text-sm leading-relaxed mb-1",
              currentVariant.title,
            )}
          >
            {title}
          </div>
          <div
            className={cn(
              variant === "compact"
                ? "text-xs leading-snug"
                : "text-xs leading-relaxed",
              currentVariant.description,
            )}
          >
            {description}
          </div>
        </div>
      </div>
    </div>
  );
};
