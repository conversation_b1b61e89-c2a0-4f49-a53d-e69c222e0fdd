import { TagBadge } from "@/components/common/prompt";
import { UniversalModuleLabel } from "../../types";
import { parseModuleLabelIds } from "../../utils";

export const ModuleLabelsView = ({
  moduleLabelIds,
  moduleLabels,
  showPlaceholder = true,
}: {
  moduleLabelIds?: string | null;
  moduleLabels?: UniversalModuleLabel[];
  showPlaceholder?: boolean;
}) => {
  const labelIds = parseModuleLabelIds(moduleLabelIds);
  const labels = labelIds
    .map((id) => (moduleLabels ?? []).find((label) => label.id === id))
    .filter(Boolean) as UniversalModuleLabel[];
  const hasLabels = labels.length > 0;

  if (!showPlaceholder && !hasLabels) {
    return null;
  }

  return (
    <div className="flex gap-1 flex-wrap items-center min-h-[30px]">
      {hasLabels ? (
        labels.map((label) => <TagBadge key={label.id} tag={label.name} />)
      ) : (
        <span className="text-description text-xs">暂无标签</span>
      )}
    </div>
  );
};
