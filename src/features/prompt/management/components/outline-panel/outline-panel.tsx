import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import {
  AbilityItemTypeIcons,
  AbilityItemTypeLabels,
} from "@/constants/prompt";
import { useAtomValue } from "jotai";
import { FileText, Folder, Search } from "lucide-react";
import { useCallback, useMemo, useState } from "react";
import { abilitiesAtom, modulesAtom, selectedAbilityIdAtom } from "../../atoms";
import { parseAbilityContent } from "../../schemas/node";

interface TreeItem {
  id: string;
  name: string;
  icon: React.ComponentType<any>;
  level: number;
  onClick: () => void;
}

export const OutlinePanel = () => {
  const abilitiesQuery = useAtomValue(abilitiesAtom);
  const modulesQuery = useAtomValue(modulesAtom);
  const selectedAbilityId = useAtomValue(selectedAbilityIdAtom);
  const [searchQuery, setSearchQuery] = useState("");

  // 跳转到锚点的函数
  const scrollToAnchor = useCallback((anchorId: string) => {
    const element = document.getElementById(anchorId);
    if (element) {
      element.scrollIntoView({ behavior: "smooth", block: "start" });
    }
  }, []);

  // 构建扁平的树形数据
  const flatTreeData = useMemo(() => {
    if (!abilitiesQuery.data || !modulesQuery.data) {
      return [];
    }

    const abilities = abilitiesQuery.data;
    const modules = modulesQuery.data;
    const items: TreeItem[] = [];

    abilities
      .filter((ability) => ability.id === selectedAbilityId)
      .forEach((ability) => {
        // 添加能力级别
        items.push({
          id: `ability-${ability.id}`,
          name: ability.name,
          icon: Folder,
          level: 0,
          onClick: () => scrollToAnchor(`ability-${ability.id}`),
        });

        // 解析能力内容获取能力项和模块关系
        const parseResult = parseAbilityContent(ability.content);

        if (parseResult.success && parseResult.data.content) {
          parseResult.data.content.forEach((abilityItem) => {
            // 添加能力项级别
            items.push({
              id: `ability-${ability.id}-item-${abilityItem.attrs.type}`,
              name: AbilityItemTypeLabels[abilityItem.attrs.type],
              icon: AbilityItemTypeIcons[abilityItem.attrs.type],
              level: 1,
              onClick: () =>
                scrollToAnchor(
                  `ability-${ability.id}-item-${abilityItem.attrs.type}`,
                ),
            });

            // 添加模块级别
            abilityItem.content.forEach((moduleRef, moduleIndex) => {
              const module = modules.find(
                (module) => module.id === moduleRef.attrs.id,
              );

              if (module) {
                items.push({
                  id: `module-${module.id}-${ability.id}-${abilityItem.attrs.type}-${moduleIndex}`,
                  name: module.name,
                  icon: FileText,
                  level: 2,
                  onClick: () => scrollToAnchor(`module-${module.id}`),
                });
              }
            });
          });
        }
      });

    return items;
  }, [
    abilitiesQuery.data,
    modulesQuery.data,
    scrollToAnchor,
    selectedAbilityId,
  ]);

  // 过滤搜索结果
  const filteredData = useMemo(() => {
    if (!searchQuery.trim()) {
      return flatTreeData;
    }

    const query = searchQuery.toLowerCase();
    return flatTreeData.filter((item) =>
      item.name.toLowerCase().includes(query),
    );
  }, [flatTreeData, searchQuery]);

  return (
    <div className="flex-1 min-h-0 flex flex-col bg-white overflow-hidden text-sm">
      <div className="flex flex-col px-4 pt-3">
        <Input
          placeholder="搜索..."
          boxClassName="w-full"
          icon={<Search className="size-4 text-gray-400" />}
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />

        <Separator className="my-3" />
      </div>

      <div className="flex-1 min-h-0">
        <ScrollArea className="h-full px-2 pb-3 mx-1">
          {filteredData.length === 0 ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-sm text-gray-500">
                {searchQuery ? "未找到匹配项" : "暂无数据"}
              </div>
            </div>
          ) : (
            <div className="space-y-0.5">
              {filteredData.map((item) => {
                const Icon = item.icon;
                const paddingLeft = item.level * 16 + 12; // 每级缩进16px，基础12px

                return (
                  <div
                    key={item.id}
                    className="flex items-center py-1.5 px-3 hover:bg-gray-50 cursor-pointer transition-colors"
                    style={{ paddingLeft: `${paddingLeft}px` }}
                    onClick={item.onClick}
                  >
                    <Icon className="h-4 w-4 shrink-0 mr-2 text-gray-600" />
                    <span className="text-sm truncate text-gray-900">
                      {item.name}
                    </span>
                  </div>
                );
              })}
            </div>
          )}
        </ScrollArea>
      </div>
    </div>
  );
};
