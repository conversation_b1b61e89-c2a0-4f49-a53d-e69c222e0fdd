import { useAtomValue, useSet<PERSON>tom } from "jotai";
import { get } from "lodash";
import { useMemo } from "react";
import {
  hasVersionsAtom,
  selectedSharedVariableIdAtom,
  selectedVersionIdAtom,
  sharedVariablesAtom,
  updateSharedVariableContentAtom,
} from "../../atoms";
import { useEditPermission } from "../../hooks";
import { createEditorSharedVariableContent } from "../../utils";
import { SharedVariableEditorWithVariables } from "../editors";
import { ReadOnlyNotice } from "../read-only-notice";
import { VersionEmpty } from "../version-empty";
import { SharedVariableEmpty } from "./shared-variable-empty";

export const SharedVariableEditor = () => {
  const selectedVersionId = useAtomValue(selectedVersionIdAtom);
  const hasVersions = useAtomValue(hasVersionsAtom);
  const selectedSharedVariableId = useAtomValue(selectedSharedVariableIdAtom);

  // 检查版本状态
  const shouldShowEditor = hasVersions && selectedVersionId;

  if (!shouldShowEditor) {
    return (
      <div className="h-full overflow-hidden flex flex-col bg-white">
        <div className="p-4">
          <VersionEmpty
            className="mt-[10%]"
            hasVersions={hasVersions}
            hasSelectedVersion={!!selectedVersionId}
            title={{
              noVersions: "创建第一个版本",
              noSelection: "选择一个版本开始管理",
            }}
            description={{
              noVersions: "您需要先创建一个提示版本才能管理共享变量。",
              noSelection: "请从上方下拉菜单中选择一个版本。",
            }}
          />
        </div>
      </div>
    );
  }

  if (!selectedSharedVariableId) {
    return <SharedVariableEmpty />;
  }

  return <SharedVariableEditorContent variableId={selectedSharedVariableId} />;
};

const SharedVariableEditorContent = ({
  variableId,
}: {
  variableId: string;
}) => {
  const sharedVariables = useAtomValue(sharedVariablesAtom);
  const sharedVariable = sharedVariables.data?.find(
    (variable) => variable.id === variableId,
  );
  const updateSharedVariableContent = useSetAtom(
    updateSharedVariableContentAtom,
  );
  const { canEdit } = useEditPermission();

  // 解析共享变量内容为编辑器格式
  const editorContent = useMemo(
    () => createEditorSharedVariableContent(sharedVariable?.content || ""),
    [sharedVariable?.content],
  );

  // 处理内容变化
  const handleContentChange = (content: string) => {
    updateSharedVariableContent(variableId, content);
  };

  return (
    <div className="overflow-hidden flex flex-col h-full bg-white">
      {sharedVariable ? (
        <div className="flex flex-col h-full">
          {!canEdit && (
            <div className="flex-shrink-0 p-4 pb-0">
              <ReadOnlyNotice />
            </div>
          )}

          <div className="flex-shrink-0 px-4 py-4">
            <div className="flex items-center justify-between">
              <div className="flex-1 min-w-0">
                <h1 className="text-lg font-normal text-gray-900 truncate">
                  {sharedVariable.name}
                </h1>
                {sharedVariable.definition && (
                  <p className="text-sm text-gray-500 mt-1 line-clamp-2">
                    {sharedVariable.definition}
                  </p>
                )}
              </div>
            </div>
          </div>

          <div className="flex-1 min-h-0 px-4 pt-2 pb-4 overflow-hidden">
            <SharedVariableEditorWithVariables
              key={sharedVariable.id}
              content={editorContent}
              onChange={(json) => {
                handleContentChange(JSON.stringify(get(json, ["content", 0])));
              }}
              editable={canEdit}
              className="h-full"
              currentSharedVariableId={variableId}
            />
          </div>
        </div>
      ) : (
        <SharedVariableEmpty />
      )}
    </div>
  );
};
