import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

// 定义表单验证模式
const versionCreateSchema = z.object({
  versionName: z
    .string()
    .min(1, { message: "版本名称不能为空" })
    .max(50, { message: "版本名称不能超过50个字符" })
    .regex(/^[a-zA-Z0-9\u4e00-\u9fa5\s\-_.]+$/, {
      message: "版本名称只能包含中英文、数字、空格和常用符号（-_.）",
    }),
});

// 推断表单数据类型
type VersionCreateFormValues = z.infer<typeof versionCreateSchema>;

interface VersionCreateDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: (versionName: string) => void;
}

export const VersionCreateDialog = ({
  open,
  onOpenChange,
  onConfirm,
}: VersionCreateDialogProps) => {
  // 初始化表单
  const form = useForm<VersionCreateFormValues>({
    resolver: zodResolver(versionCreateSchema),
    mode: "onChange",
    defaultValues: {
      versionName: "",
    },
  });

  // 提交表单
  const handleSubmit = async () => {
    const isValid = await form.trigger();

    if (!isValid) {
      return;
    }

    onConfirm(form.getValues().versionName);
    handleClose();
  };

  // 关闭对话框
  const handleClose = () => {
    onOpenChange(false);
  };

  // 取消操作
  const handleCancel = () => {
    handleClose();
  };

  useEffect(() => {
    if (open) {
      form.reset();
    }
  }, [form, open]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>创建新版本</DialogTitle>
          <DialogDescription>
            请输入新版本的名称，可以使用中英文、数字和常用符号
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <div className="space-y-4">
            <FormField
              control={form.control}
              name="versionName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>版本名称</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="请输入版本名称"
                      {...field}
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          handleSubmit();
                        }
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button variant="outline" onClick={handleCancel}>
                取消
              </Button>
              <Button onClick={handleSubmit}>确认创建</Button>
            </DialogFooter>
          </div>
        </Form>
      </DialogContent>
    </Dialog>
  );
};
