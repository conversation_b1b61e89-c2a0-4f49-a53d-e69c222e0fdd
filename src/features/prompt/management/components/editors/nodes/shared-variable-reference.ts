import { Node, mergeAttributes } from "@tiptap/core";
import { ReactNodeViewRenderer } from "@tiptap/react";
import { SharedVariableReferenceView } from "../views";

export const SharedVariableReference = Node.create({
  name: "sharedVariableReference",

  group: "block",

  atom: true,

  addAttributes() {
    return {
      sharedVariableId: {
        default: null,
        parseHTML: (element: HTMLElement) =>
          element.getAttribute("data-shared-variable-id"),
        renderHTML: (attributes: { sharedVariableId?: string }) => ({
          "data-shared-variable-id": attributes.sharedVariableId,
        }),
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: "div[data-shared-variable-reference]",
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    return [
      "div",
      mergeAttributes(HTMLAttributes, {
        "data-shared-variable-reference": "",
      }),
    ];
  },

  addNodeView() {
    return ReactNodeViewRenderer(SharedVariableReferenceView);
  },
});
