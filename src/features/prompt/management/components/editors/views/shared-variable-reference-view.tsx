import {
  SharedVariablePromptEditor,
  useVariableContext,
} from "@/components/common/prompt-editor";
import { isVariableVisible } from "@/components/common/prompt-editor/utils/variable-filter";
import { sharedVariablesAtom } from "@/features/prompt/management/atoms";
import { NodeViewProps, NodeViewWrapper } from "@tiptap/react";
import { useAtomValue } from "jotai";
import { AlertTriangle } from "lucide-react";
import { useMemo } from "react";

export const SharedVariableReferenceView = ({ node }: NodeViewProps) => {
  const { sharedVariableId } = node.attrs;
  // 获取共享变量数据
  const sharedVariables = useAtomValue(sharedVariablesAtom);
  const sharedVariable = sharedVariables.data?.find(
    (variable) => variable.id === sharedVariableId,
  );

  // 获取当前编辑上下文
  const { currentEditContext } = useVariableContext();

  // 检查共享变量在当前上下文中是否合法
  const isValid = sharedVariable
    ? isVariableVisible(
        sharedVariable.level,
        sharedVariable.scopeDetail,
        currentEditContext,
      )
    : false;

  // 解析共享变量内容
  const editorContent = useMemo(() => {
    if (!sharedVariable?.content) {
      return;
    }

    try {
      return {
        type: "doc",
        content: [JSON.parse(sharedVariable.content)],
      };
    } catch {
      return;
    }
  }, [sharedVariable?.content]);

  return (
    <NodeViewWrapper>
      <div className="relative">
        {/* 错误状态浮层 */}
        {(!sharedVariable || !isValid) && (
          <div className="absolute inset-8 z-10 bg-black/50 rounded-lg flex items-center justify-center">
            <div className="flex items-center gap-2 text-white bg-red-600 px-3 py-2 rounded-md shadow-lg">
              <AlertTriangle className="size-4" />
              <span className="text-sm font-medium">
                变量在当前影响范围不可用
              </span>
            </div>
          </div>
        )}

        {/* 原始内容 */}
        {editorContent ? (
          <SharedVariablePromptEditor
            className="border-none"
            content={editorContent}
            editable={false}
          />
        ) : (
          <div className="m-4 px-2 py-3 border border-dashed border-gray-300 rounded-lg">
            <p className="p-0 text-sm text-muted-foreground">
              {!sharedVariable
                ? `共享变量 ${sharedVariableId} 不存在`
                : "共享变量暂无内容"}
            </p>
          </div>
        )}
      </div>
    </NodeViewWrapper>
  );
};
