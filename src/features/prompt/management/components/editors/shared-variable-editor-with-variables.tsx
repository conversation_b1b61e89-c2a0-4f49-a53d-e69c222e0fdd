import {
  CurrentEditContext,
  PromptEditorProps,
  PromptEditorSkeleton,
  SharedVariablePromptEditor,
  UniversalDataSource,
} from "@/components/common/prompt-editor";
import { useAtomValue } from "jotai";
import { useMemo } from "react";
import {
  sharedVariablesAtom,
  shopVariablesAtom,
  systemVariablesAtom,
} from "../../atoms";

export type SharedVariableEditorWithVariablesProps = Omit<
  PromptEditorProps,
  "extensions"
> & {
  /** 当前编辑的共享变量ID（用于过滤其他共享变量） */
  currentSharedVariableId?: string;
};

export const SharedVariableEditorWithVariables = ({
  content,
  editable = true,
  className,
  currentSharedVariableId,
  onChange,
  ...props
}: SharedVariableEditorWithVariablesProps) => {
  const systemVariablesQuery = useAtomValue(systemVariablesAtom);
  const shopVariablesQuery = useAtomValue(shopVariablesAtom);
  const sharedVariablesQuery = useAtomValue(sharedVariablesAtom);

  // 获取当前编辑的共享变量信息
  const currentSharedVariable = useMemo(() => {
    if (!currentSharedVariableId || !sharedVariablesQuery.data) {
      return null;
    }

    return sharedVariablesQuery.data.find(
      (variable) => variable.id === currentSharedVariableId,
    );
  }, [currentSharedVariableId, sharedVariablesQuery.data]);

  // 构建当前编辑上下文
  const currentEditContext = useMemo((): CurrentEditContext | undefined => {
    if (!currentSharedVariable) {
      return undefined;
    }

    return {
      level: currentSharedVariable.level,
      scopeDetail: currentSharedVariable.scopeDetail,
    };
  }, [currentSharedVariable]);

  // 配置两种变量数据源
  const dataSources = useMemo(
    (): UniversalDataSource[] => [
      // 系统变量
      {
        data: systemVariablesQuery.data || [],
        label: "system",
        variableType: "system",
      },
      // 店铺变量
      {
        data: shopVariablesQuery.data || [],
        label: "shop",
        variableType: "shop",
      },
    ],
    [systemVariablesQuery.data, shopVariablesQuery.data],
  );

  if (
    systemVariablesQuery.isLoading ||
    shopVariablesQuery.isLoading ||
    sharedVariablesQuery.isLoading
  ) {
    return <PromptEditorSkeleton />;
  }

  return (
    <SharedVariablePromptEditor
      content={content}
      editable={editable}
      className={className}
      dataSources={dataSources}
      currentEditContext={currentEditContext}
      onChange={onChange}
      {...props}
    />
  );
};
