import {
  ModulePromptEditor,
  ModulePromptEditorProps,
  PromptEditorContentSkeleton,
} from "@/components/common/prompt-editor";
import { CurrentEditContext } from "@/components/common/prompt-editor/contexts";
import { UniversalDataSource } from "@/components/common/prompt-editor/hooks";
import { Level } from "@/types/api/prompt";
import { useAtomValue } from "jotai";
import { useMemo } from "react";
import {
  sharedVariablesAtom,
  shopVariablesAtom,
  systemVariablesAtom,
} from "../../atoms";
import { SharedVariableReference } from "./nodes";

export interface ModuleEditorWithVariablesProps
  extends Omit<ModulePromptEditorProps, "extensions"> {
  level: Level | null;
  scopeDetail?: string;
}

export const ModuleEditorWithVariables = ({
  level,
  scopeDetail,
  ...props
}: ModuleEditorWithVariablesProps) => {
  const systemVariablesQuery = useAtomValue(systemVariablesAtom);
  const shopVariablesQuery = useAtomValue(shopVariablesAtom);
  const sharedVariablesQuery = useAtomValue(sharedVariablesAtom);
  const extensions = useMemo(() => [SharedVariableReference], []);

  const dataSources = useMemo(
    (): UniversalDataSource[] => [
      // 系统变量
      {
        data: systemVariablesQuery.data || [],
        label: "system",
        variableType: "system",
      },
      // 店铺变量
      {
        data: shopVariablesQuery.data || [],
        label: "shop",
        variableType: "shop",
      },
      // 共享变量
      {
        data: sharedVariablesQuery.data || [],
        label: "shared",
        variableType: "shared",
      },
    ],
    [
      systemVariablesQuery.data,
      shopVariablesQuery.data,
      sharedVariablesQuery.data,
    ],
  );

  const currentEditContext = useMemo((): CurrentEditContext | undefined => {
    if (!level) {
      return undefined;
    }

    return {
      level,
      scopeDetail,
    };
  }, [level, scopeDetail]);

  if (
    systemVariablesQuery.isLoading ||
    shopVariablesQuery.isLoading ||
    sharedVariablesQuery.isLoading
  ) {
    return <PromptEditorContentSkeleton />;
  }

  return (
    <ModulePromptEditor
      extensions={extensions}
      dataSources={dataSources}
      currentEditContext={currentEditContext}
      {...props}
    />
  );
};
