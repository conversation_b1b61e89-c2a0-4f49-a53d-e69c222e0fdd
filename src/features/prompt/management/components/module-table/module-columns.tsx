import { CategoryDropdownSelector } from "@/components/common/category-dropdown-selector";
import { EditableField } from "@/components/common/editable-field";
import { LevelScopeSelector } from "@/components/common/level-scope-selector";
import {
  LevelBadge,
  LevelTip,
  ScopeBadge,
  ScopeTip,
} from "@/components/common/prompt";
import { ShopMultiSelector } from "@/components/common/shop-multi-selector";
import { Button } from "@/components/ui/button";
import { DropdownMenuSeparator } from "@/components/ui/dropdown-menu";
import { Level } from "@/types/api/prompt";
import { ColumnDef } from "@tanstack/react-table";
import { validateModuleField } from "../../schemas/module";
import { UniversalModule, UniversalModuleLabel } from "../../types";
import { parseModuleLabelIds, stringifyModuleLabelIds } from "../../utils";
import { ModuleLabelsView } from "../module-labels-view";

interface ModuleColumnsProps {
  moduleLabels?: UniversalModuleLabel[];
  onEdit?: (module: UniversalModule) => void;
  onDelete?: (moduleId: string, moduleName: string) => void;
  onUpdate?: (
    moduleId: string,
    field: string,
    value: string | Level | string[],
  ) => Promise<void>;
}

export const getModuleColumns = ({
  onEdit,
  onDelete,
  onUpdate,
  moduleLabels,
}: ModuleColumnsProps): ColumnDef<UniversalModule>[] => [
  {
    accessorKey: "name",
    header: "模块名称",
    minSize: 180,
    cell: ({ row, getValue }) => (
      <EditableField
        value={getValue() as string}
        type="text"
        onSave={
          onUpdate
            ? (value: string) => onUpdate(row.original.id, "name", value)
            : undefined
        }
        validate={(value: string) => validateModuleField("name", value)}
        placeholder="请输入模块名称"
      />
    ),
    meta: {
      label: "模块名称",
    },
  },
  {
    accessorKey: "moduleLabelIds",
    header: "模块标签",
    minSize: 200,
    cell: ({ row, getValue }) => {
      const moduleLabelIds = getValue() as string;
      const currentLabelIds = parseModuleLabelIds(moduleLabelIds);

      // 将模块标签转换为选项格式
      const labelOptions = (moduleLabels || []).map((label) => ({
        label: label.name,
        value: label.id,
      }));

      return (
        <EditableField
          value={currentLabelIds}
          type="multiselect"
          options={labelOptions}
          onSave={
            onUpdate
              ? (value: string[]) =>
                  onUpdate(
                    row.original.id,
                    "moduleLabelIds",
                    stringifyModuleLabelIds(value),
                  )
              : undefined
          }
          validate={(value: string[]) =>
            validateModuleField("moduleLabelIds", value)
          }
          placeholder="请选择模块标签"
          renderDisplay={(value: string[]) => (
            <ModuleLabelsView
              moduleLabelIds={stringifyModuleLabelIds(value)}
              moduleLabels={moduleLabels}
            />
          )}
        />
      );
    },
    meta: {
      label: "模块标签",
      align: "left",
    },
  },
  {
    accessorKey: "level",
    header: () => (
      <div className="flex items-center justify-center gap-2">
        <span>等级</span>
        <LevelTip />
      </div>
    ),
    minSize: 180,
    cell: ({ row, getValue }) => {
      const currentScopeDetail = row.original.scopeDetail
        ? JSON.parse(row.original.scopeDetail)
        : [];

      return (
        <EditableField
          value={getValue() as Level}
          type="custom"
          align="center"
          placeholder="请选择等级"
          renderDisplay={(value: Level) => <LevelBadge level={value} />}
          renderEditor={(value: Level, onChange: (value: Level) => void) => (
            <LevelScopeSelector
              value={value}
              currentScopeDetail={currentScopeDetail}
              onLevelChange={onChange}
              onScopeChange={(level, scopeDetail) => {
                // 同时更新等级和范围
                onUpdate?.(row.original.id, "level", level);
                onUpdate?.(
                  row.original.id,
                  "scopeDetail",
                  JSON.stringify(scopeDetail),
                );
              }}
              renderDisplay={(level: Level) => <LevelBadge level={level} />}
            />
          )}
          onSave={
            onUpdate
              ? (value: Level) => onUpdate(row.original.id, "level", value)
              : undefined
          }
        />
      );
    },
    meta: {
      label: "等级",
      align: "center",
    },
  },
  {
    accessorKey: "scopeDetail",
    header: () => (
      <div className="flex items-center justify-center gap-2">
        <span>影响范围</span>
        <ScopeTip />
      </div>
    ),
    minSize: 180,
    cell: ({ row, getValue }) => {
      const variable = row.original;
      const scopeDetail = getValue() as string;

      return (
        <EditableField
          value={scopeDetail ? JSON.parse(scopeDetail) : []}
          type="custom"
          align="center"
          disabled={variable.level === Level.Product}
          placeholder="请选择影响范围"
          renderDisplay={(value: string[]) => (
            <ScopeBadge
              level={variable.level}
              scopeDetail={JSON.stringify(value)}
            />
          )}
          renderEditor={(
            value: string[],
            onChange: (value: string[]) => void,
          ) => {
            if (variable.level === Level.Product) {
              return null;
            }

            if (variable.level === Level.Shop) {
              return (
                <ShopMultiSelector
                  value={value}
                  onChange={onChange}
                  placeholder="请选择店铺"
                  triggerClassName="w-full"
                />
              );
            }

            if (variable.level === Level.Category) {
              return (
                <CategoryDropdownSelector
                  value={value}
                  onChange={onChange}
                  placeholder="请选择类目"
                  triggerClassName="w-full"
                />
              );
            }

            return null;
          }}
          onSave={
            onUpdate && variable.level !== Level.Product
              ? (value: string[]) =>
                  onUpdate(
                    row.original.id,
                    "scopeDetail",
                    JSON.stringify(value),
                  )
              : undefined
          }
        />
      );
    },
    meta: {
      label: "影响范围",
      align: "center",
    },
  },
  {
    id: "actions",
    header: "操作",
    size: 160,
    cell: ({ row }) => {
      const module = row.original;

      return (
        <div className="flex items-center gap-4">
          {onEdit && (
            <Button variant="link" onClick={() => onEdit(module)}>
              编辑
            </Button>
          )}
          {onEdit && onDelete && <DropdownMenuSeparator />}
          {onDelete && (
            <Button
              variant="link"
              className="text-destructive"
              onClick={() => onDelete(module.id, module.name)}
            >
              删除
            </Button>
          )}
        </div>
      );
    },
    enableHiding: false,
    meta: {
      label: "操作",
      align: "center",
    },
  },
];
