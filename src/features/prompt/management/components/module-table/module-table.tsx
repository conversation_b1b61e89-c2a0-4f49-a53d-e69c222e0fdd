import {
  AsyncDataTable,
  DataTableToolbar,
  useTableState,
} from "@/components/common/data-table";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Module } from "@/types/api/module";
import { useAtomValue, useSetAtom } from "jotai";
import { Plus } from "lucide-react";
import { useCallback, useMemo, useState } from "react";
import { toast } from "sonner";
import {
  deleteModule<PERSON>tom,
  hasVersionsAtom,
  moduleLabelsAtom,
  modulesFilteredByLabelIdAtom,
  selectedVersionIdAtom,
  updateModuleAtom,
} from "../../atoms";
import { useEditPermission } from "../../hooks";
import { UniversalModule } from "../../types";
import { ModuleForm } from "../module-form";
import { ReadOnlyNotice } from "../read-only-notice";
import { VersionEmpty } from "../version-empty";
import { getModuleColumns } from "./module-columns";

export const ModuleTable = () => {
  // 状态管理
  const [editingModule, setEditingModule] = useState<UniversalModule | null>(
    null,
  );
  const [isFormDrawerOpen, setIsFormDrawerOpen] = useState(false);
  const [deleteConfirm, setDeleteConfirm] = useState<{
    moduleId: string;
    moduleName: string;
  } | null>(null);

  // 表格状态
  const tableState = useTableState<Module>({
    enablePagination: false,
  });

  // Atoms
  const selectedVersionId = useAtomValue(selectedVersionIdAtom);
  const hasVersions = useAtomValue(hasVersionsAtom);
  const modulesQuery = useAtomValue(modulesFilteredByLabelIdAtom);
  const { data: moduleLabels } = useAtomValue(moduleLabelsAtom);
  const deleteModule = useSetAtom(deleteModuleAtom);
  const updateModule = useSetAtom(updateModuleAtom);
  const { canEdit } = useEditPermission();

  // 处理编辑模块
  const handleEditModule = useCallback((module: UniversalModule) => {
    setEditingModule(module);
    setIsFormDrawerOpen(true);
  }, []);

  // 处理删除模块
  const handleDeleteModule = useCallback(
    (moduleId: string, moduleName: string) => {
      setDeleteConfirm({ moduleId, moduleName });
    },
    [],
  );

  // 处理更新模块字段
  const handleUpdateModule = useCallback(
    async (moduleId: string, field: string, value: any) => {
      updateModule(moduleId, { [field]: value });
      toast.success("模块更新成功");
    },
    [updateModule],
  );

  // 处理创建新模块
  const handleCreateModule = useCallback(() => {
    if (!selectedVersionId) {
      toast.error("请先选择一个版本");
      return;
    }

    // 打开抽屉创建新模块
    setEditingModule(null);
    setIsFormDrawerOpen(true);
  }, [selectedVersionId]);

  // 确认删除
  const confirmDelete = () => {
    if (!deleteConfirm) {
      return;
    }

    deleteModule(deleteConfirm.moduleId.toString());
    setDeleteConfirm(null);
  };

  // 表格列配置
  const columns = useMemo(
    () =>
      getModuleColumns({
        onEdit: canEdit ? handleEditModule : undefined,
        onDelete: canEdit ? handleDeleteModule : undefined,
        onUpdate: canEdit ? handleUpdateModule : undefined,
        moduleLabels,
      }),
    [
      canEdit,
      handleEditModule,
      handleDeleteModule,
      handleUpdateModule,
      moduleLabels,
    ],
  );

  // 检查版本状态
  const shouldShowTable = hasVersions && selectedVersionId;

  if (!shouldShowTable) {
    return (
      <div className="overflow-hidden flex flex-col h-full bg-white">
        <div className="p-4">
          <VersionEmpty
            className="mt-[10%]"
            hasVersions={hasVersions}
            hasSelectedVersion={!!selectedVersionId}
            title={{
              noVersions: "创建第一个版本",
              noSelection: "选择一个版本开始管理",
            }}
            description={{
              noVersions: "您需要先创建一个提示版本才能管理模块。",
              noSelection: "请从上方下拉菜单中选择一个版本。",
            }}
          />
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="flex flex-col h-full p-4 space-y-4">
        {/* 只读状态提示 */}
        {!canEdit && <ReadOnlyNotice />}

        <AsyncDataTable<UniversalModule>
          query={modulesQuery}
          columns={columns}
          className="flex-1 min-h-0"
          tableName="module-table"
          initialState={{
            columnPinning: {
              right: ["actions"],
            },
          }}
          getRowId={(row) => row.id.toString()}
          {...tableState}
        >
          {(table) => (
            <DataTableToolbar
              table={table}
              enableSort={false}
              filters={tableState.globalFilter}
              setFilterValue={tableState.setFilterValue}
              onResetFilters={tableState.resetFilters}
            >
              <Button
                size="sm"
                icon={<Plus />}
                onClick={handleCreateModule}
                disabled={!canEdit}
              >
                创建模块
              </Button>
            </DataTableToolbar>
          )}
        </AsyncDataTable>
      </div>

      <ModuleForm
        open={isFormDrawerOpen}
        module={editingModule}
        onOpenChange={setIsFormDrawerOpen}
      />

      <AlertDialog
        open={deleteConfirm !== null}
        onOpenChange={(open) => !open && setDeleteConfirm(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              确认删除模块<strong>"{deleteConfirm?.moduleName}"</strong>
              ？删除并保存后，会使该模块从当前版本正在使用的能力中移除。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              确认删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};
