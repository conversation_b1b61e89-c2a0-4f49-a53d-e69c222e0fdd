import { EditableList, type ListItem } from "@/components/common/editable-list";
import { Button } from "@/components/ui/button";
import { useAtom, useAtomValue, useSetAtom } from "jotai";
import { Plus } from "lucide-react";
import { useCallback, useState } from "react";
import { toast } from "sonner";
import {
  createModuleLabelAtom,
  deleteModuleLabelAtom,
  moduleLabelsAtom,
  selectedModuleLabelIdAtom,
  selectedVersionIdAtom,
  updateModuleLabelAtom,
} from "../../atoms";
import { useEditPermission } from "../../hooks";
import { UniversalModuleLabel } from "../../types";

// 将 SelectModuleLabel 适配为 ListItem
interface LabelListItem extends ListItem {
  id: string;
  name: string;
}

const adaptLabelsToListItems = (
  labels: UniversalModuleLabel[] | undefined,
): LabelListItem[] =>
  (labels ?? []).map((label) => ({
    id: label.id,
    name: label.name,
  }));

export const ModuleLabelPanel = () => {
  const [isAddingMode, setIsAddingMode] = useState(false);
  const selectedVersionId = useAtomValue(selectedVersionIdAtom);
  const [selectedModuleLabelId, setSelectedModuleLabelId] = useAtom(
    selectedModuleLabelIdAtom,
  );
  const { data: labels, isLoading: isLabelsLoading } =
    useAtomValue(moduleLabelsAtom);
  const createLabel = useSetAtom(createModuleLabelAtom);
  const updateLabel = useSetAtom(updateModuleLabelAtom);
  const deleteLabel = useSetAtom(deleteModuleLabelAtom);
  const { canEdit } = useEditPermission();

  // 添加标签
  const handleAddLabel = useCallback(
    async (name: string) => {
      if (!selectedVersionId) {
        toast.error("请先选择一个版本");
        return false;
      }

      const success = await createLabel(selectedVersionId, name);

      if (success) {
        setIsAddingMode(false);
      }

      return success != null;
    },
    [selectedVersionId, createLabel],
  );

  // 编辑标签
  const handleEditLabelName = useCallback(
    async (id: string, name: string) => {
      return (await updateLabel(id, name)) != null;
    },
    [updateLabel],
  );

  // 删除标签
  const handleDeleteLabel = useCallback(
    async (id: string) => {
      deleteLabel(id);
      return true;
    },
    [deleteLabel],
  );

  // 选中标签
  const handleSelectLabel = useCallback(
    (id: number | string | null) => {
      if (id === null) {
        setSelectedModuleLabelId(null);
      } else {
        setSelectedModuleLabelId(id.toString());
      }
    },
    [setSelectedModuleLabelId],
  );

  // 处理添加按钮点击
  const handleAddClick = useCallback(() => {
    setIsAddingMode(true);
  }, []);

  const listItems = adaptLabelsToListItems(labels);

  return (
    <div className="flex flex-col h-full bg-white">
      <div className="flex-shrink-0 flex items-center justify-between px-3 py-2 border-b border-border">
        <h3 className="text-sm">模块标签</h3>
        <Button
          size="sm"
          variant="outline"
          icon={<Plus />}
          onClick={handleAddClick}
          disabled={!canEdit}
        >
          创建模块标签
        </Button>
      </div>

      <EditableList
        items={listItems}
        selectedId={selectedModuleLabelId}
        isLoading={isLabelsLoading}
        showAllOption={true}
        maxLength={50}
        allOptionTitle="全部标签"
        showAddButton={false}
        forceAddingMode={isAddingMode}
        disabled={!canEdit}
        addPlaceholder="输入标签名称"
        deleteConfirmText="确认删除该模块标签？删除后，会使该标签从各模块内容上移除，无标签的内容将归入「全部」。"
        className="flex-1"
        onCancelAdding={() => setIsAddingMode(false)}
        onSelectItem={handleSelectLabel}
        onAddItem={canEdit ? handleAddLabel : undefined}
        onEditItemName={canEdit ? handleEditLabelName : undefined}
        onDeleteItem={canEdit ? handleDeleteLabel : undefined}
      />
    </div>
  );
};
