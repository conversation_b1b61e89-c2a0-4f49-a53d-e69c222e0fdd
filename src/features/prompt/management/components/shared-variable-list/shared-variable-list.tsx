import { EditableList, type ListItem } from "@/components/common/editable-list";
import { But<PERSON> } from "@/components/ui/button";
import { useAtom, useAtomValue, useSetAtom } from "jotai";
import { Plus } from "lucide-react";
import { useCallback, useState } from "react";
import { toast } from "sonner";
import {
  checkVariableNameDuplication,
  generateDuplicateVariableNameMessage,
} from "../../../utils";
import {
  deleteSharedVariableAtom,
  selectedSharedVariableIdAtom,
  sharedVariablesAtom,
  shopVariablesAtom,
  systemVariablesAtom,
  updateSharedVariableNameAtom,
} from "../../atoms";
import { useEditPermission } from "../../hooks";
import { UniversalSharedVariable } from "../../types";
import { SharedVariableForm } from "../shared-variable-form";

// 将 UniversalSharedVariable 适配为 ListItem
interface SharedVariableListItem extends ListItem {
  id: string;
  name: string;
  definition?: string;
  level: string;
  scopeDetail?: string;
  promptName?: string;
}

const adaptVariablesToListItems = (
  variables: UniversalSharedVariable[] | undefined,
): SharedVariableListItem[] =>
  (variables ?? []).map((variable) => ({
    id: variable.id,
    name: variable.name,
    definition: variable.definition,
    level: variable.level,
    scopeDetail: variable.scopeDetail,
    promptName: variable.promptName,
  }));

export const SharedVariableList = () => {
  const [isFormDrawerOpen, setIsFormDrawerOpen] = useState(false);
  const [editingVariable, setEditingVariable] =
    useState<UniversalSharedVariable | null>(null);
  const { canEdit } = useEditPermission();

  //  atoms
  const [selectedSharedVariableId, setSelectedSharedVariableId] = useAtom(
    selectedSharedVariableIdAtom,
  );
  const { data: variables, isLoading: isVariablesLoading } =
    useAtomValue(sharedVariablesAtom);
  const deleteSharedVariable = useSetAtom(deleteSharedVariableAtom);
  const updateSharedVariableName = useSetAtom(updateSharedVariableNameAtom);

  // 获取所有变量数据用于重复检查
  const { data: systemVariables = [] } = useAtomValue(systemVariablesAtom);
  const { data: shopVariables = [] } = useAtomValue(shopVariablesAtom);

  // 处理打开表单弹窗（创建模式）
  const handleOpenFormDrawer = useCallback(() => {
    setEditingVariable(null);
    setIsFormDrawerOpen(true);
  }, []);

  // 处理编辑变量
  const handleEditVariable = useCallback(
    async (item: SharedVariableListItem) => {
      // 从原始数据中找到完整的变量信息
      const fullVariable = variables?.find((v) => v.id === item.id);

      if (fullVariable) {
        setEditingVariable(fullVariable);
        setIsFormDrawerOpen(true);
      }
    },
    [variables],
  );

  // 处理表单弹窗状态变化
  const handleFormDrawerOpenChange = useCallback((open: boolean) => {
    setIsFormDrawerOpen(open);
    if (!open) {
      setEditingVariable(null);
    }
  }, []);

  // 处理删除变量
  const handleDeleteVariable = useCallback(
    async (id: string) => {
      try {
        return deleteSharedVariable(id);
      } catch (error) {
        console.error("删除共享变量失败:", error);
        throw error;
      }
    },
    [deleteSharedVariable],
  );

  // 处理选择变量
  const handleSelectVariable = useCallback(
    (id: string | null) => {
      const selectedId = id == null ? null : id.toString();

      setSelectedSharedVariableId(selectedId);
    },
    [setSelectedSharedVariableId],
  );

  // 处理编辑变量名称
  const handleEditVariableName = useCallback(
    async (id: string, name: string) => {
      // 从原始数据中找到完整的变量信息
      const fullVariable = variables?.find((v) => v.id === id);

      if (!fullVariable) {
        toast.error("变量不存在");
        throw new Error("变量不存在");
      }

      // 检查变量名重复
      const checkResult = checkVariableNameDuplication(
        name,
        fullVariable.promptName,
        systemVariables,
        shopVariables,
        variables || [],
        id,
      );

      if (checkResult.isNameDuplicate) {
        const errorMessage = generateDuplicateVariableNameMessage(
          "变量名称",
          checkResult.duplicateNameSource!,
        );
        toast.error(errorMessage);

        throw new Error(errorMessage);
      }

      // 如果没有重复，则更新变量名称
      const result = updateSharedVariableName(fullVariable.id, name);

      return result != null;
    },
    [variables, updateSharedVariableName, systemVariables, shopVariables],
  );

  const listItems = adaptVariablesToListItems(variables);

  return (
    <div className="flex flex-col h-full bg-white">
      <div className="flex-shrink-0 flex items-center justify-between px-3 py-2 border-b border-border">
        <h3 className="text-sm">共享变量</h3>
        <Button
          size="sm"
          variant="outline"
          icon={<Plus />}
          onClick={handleOpenFormDrawer}
          disabled={!canEdit}
        >
          创建共享变量
        </Button>
      </div>

      <EditableList
        items={listItems}
        selectedId={selectedSharedVariableId}
        isLoading={isVariablesLoading}
        showAllOption={false}
        maxLength={50}
        showAddButton={false}
        deleteConfirmText="确认删除该变量？删除并保存后，会使该变量从当前版本正在使用的模块/能力中移除。"
        emptyText="暂无共享变量"
        className="flex-1"
        disabled={!canEdit}
        onSelectItem={handleSelectVariable}
        onEditItemName={canEdit ? handleEditVariableName : undefined}
        onEditItem={canEdit ? handleEditVariable : undefined}
        onDeleteItem={canEdit ? handleDeleteVariable : undefined}
      />

      <SharedVariableForm
        sharedVariable={editingVariable}
        open={isFormDrawerOpen}
        onOpenChange={handleFormDrawerOpenChange}
      />
    </div>
  );
};
