import { PromptVersionSelect } from "@/components/common/version-select";
import { Separator } from "@/components/ui/separator";
import { useAtom, useAtomValue } from "jotai";
import { useCallback } from "react";
import {
  autoSelectAbilityEffect,
  autoSelectVersionEffect,
  draftVersionsAtom,
  selectedVersionIdAtom,
  versionsAtom,
} from "../../atoms";
import { AbilityList } from "./ability-list";

export const AbilityPanel = () => {
  const { data: versions } = useAtomValue(versionsAtom);
  const draftVersions = useAtomValue(draftVersionsAtom);
  // 当前版本 id
  const [selectedVersionId, setSelectedVersionId] = useAtom(
    selectedVersionIdAtom,
  );

  // 处理版本选择
  const handleVersionSelect = useCallback(
    (versionId: string | undefined) => {
      setSelectedVersionId(versionId ?? null);
    },
    [setSelectedVersionId],
  );

  useAtom(autoSelectVersionEffect);
  useAtom(autoSelectAbilityEffect);

  return (
    <div className="flex-1 min-h-0 flex flex-col bg-white overflow-hidden text-sm">
      <div className="flex items-center px-4 py-3 gap-3">
        <PromptVersionSelect
          className="min-w-0 flex-1"
          value={selectedVersionId?.toString()}
          versions={versions}
          draftVersions={draftVersions}
          onChange={handleVersionSelect}
        />
      </div>

      <Separator />

      <AbilityList />
    </div>
  );
};
