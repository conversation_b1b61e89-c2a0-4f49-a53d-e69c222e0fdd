import { EditableList } from "@/components/common/editable-list/editable-list";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { useAtom, useAtomValue, useSetAtom } from "jotai";
import { Plus, Search } from "lucide-react";
import { useCallback, useDeferredValue, useMemo, useState } from "react";
import { toast } from "sonner";
import {
  abilitiesAtom,
  createAbilityAtom,
  deleteAbilityAtom,
  hasVersionsAtom,
  selectedAbilityAtom,
  selectedVersionIdAtom,
  updateAbilityNameAtom,
} from "../../atoms";
import { useEditPermission } from "../../hooks";
import { VersionEmpty } from "../version-empty";

export const AbilityList = () => {
  const abilities = useAtomValue(abilitiesAtom);
  const [selectedAbility, setSelectedAbility] = useAtom(selectedAbilityAtom);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [isAddingMode, setIsAddingMode] = useState(false);
  const deferredSearchQuery = useDeferredValue(searchQuery);
  const hasVersions = useAtomValue(hasVersionsAtom);
  const createAbility = useSetAtom(createAbilityAtom);
  const updateAbilityName = useSetAtom(updateAbilityNameAtom);
  const deleteAbility = useSetAtom(deleteAbilityAtom);
  const selectedVersionId = useAtomValue(selectedVersionIdAtom);
  const { canEdit } = useEditPermission();

  // 检查是否需要显示版本提示
  const shouldShowVersionPrompt = !hasVersions || !selectedVersionId;

  // 过滤能力列表
  const filteredAbilities = useMemo(() => {
    if (!abilities.data) {
      return [];
    }

    if (!deferredSearchQuery.trim()) {
      return abilities.data;
    }

    return abilities.data.filter((ability) =>
      ability.name.toLowerCase().includes(deferredSearchQuery.toLowerCase()),
    );
  }, [abilities.data, deferredSearchQuery]);

  // 处理能力选择
  const handleSelectAbility = useCallback(
    (id: string | number | null) => {
      if (id === null) {
        setSelectedAbility(null);
      } else {
        const ability = abilities.data?.find((a) => a.id === id);

        if (ability) {
          setSelectedAbility(ability);
        }
      }
    },
    [abilities.data, setSelectedAbility],
  );

  // 创建能力
  const handleAddAbility = useCallback(
    async (name: string) => {
      if (!selectedVersionId) {
        toast.error("请先选择一个版本");
        return false;
      }

      const result = await createAbility(selectedVersionId, name);

      setIsAddingMode(false);

      return result != null;
    },
    [createAbility, selectedVersionId],
  );

  // 编辑能力名称
  const handleEditAbilityName = useCallback(
    async (id: string, name: string) => {
      const result = await updateAbilityName(id, name);

      return result != null;
    },
    [updateAbilityName],
  );

  // 删除能力
  const handleDeleteAbility = useCallback(
    async (id: string) => {
      deleteAbility(id);
      return true;
    },
    [deleteAbility],
  );

  // 处理添加按钮点击
  const handleAddClick = useCallback(() => {
    setIsAddingMode(true);
  }, []);

  if (shouldShowVersionPrompt) {
    return (
      <VersionEmpty
        hasVersions={hasVersions}
        hasSelectedVersion={!!selectedVersionId}
      />
    );
  }

  return (
    <div className="flex flex-col h-full">
      <div className="flex flex-col gap-3 px-4 pt-3">
        <div className="flex-shrink-0 flex items-center gap-3">
          <Input
            boxClassName="flex-1 min-w-0"
            type="text"
            placeholder="搜索能力名称"
            value={searchQuery}
            icon={<Search className="size-4 text-gray-400" />}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          <Button
            variant="outline"
            size="sm"
            icon={<Plus />}
            onClick={handleAddClick}
            disabled={!canEdit}
          >
            创建能力
          </Button>
        </div>
        <Separator />
      </div>

      <div className="flex-1 min-h-0">
        <EditableList
          items={filteredAbilities}
          selectedId={selectedAbility?.id || null}
          isLoading={abilities.isLoading}
          showAllOption={false}
          addPlaceholder="输入能力名称"
          deleteConfirmText="确认要删除这个能力吗？"
          showAddButton={false}
          forceAddingMode={isAddingMode}
          disabled={!canEdit}
          className="flex-1"
          emptyText="暂无能力数据"
          noResultsText="未找到匹配的能力"
          isFiltered={!!searchQuery.trim()}
          onCancelAdding={() => setIsAddingMode(false)}
          onSelectItem={handleSelectAbility}
          onAddItem={canEdit ? handleAddAbility : undefined}
          onEditItemName={canEdit ? handleEditAbilityName : undefined}
          onDeleteItem={canEdit ? handleDeleteAbility : undefined}
        />
      </div>
    </div>
  );
};
