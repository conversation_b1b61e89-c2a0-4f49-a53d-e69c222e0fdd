import { cn } from "@/lib/utils";
import { FolderOpen, MousePointer2, Plus } from "lucide-react";

interface VersionPromptEmptyProps {
  hasVersions: boolean;
  hasSelectedVersion: boolean;
  hasSelectedAbility?: boolean;
  className?: string;
  title?: {
    noVersions?: string;
    noSelection?: string;
    noAbility?: string;
  };
  description?: {
    noVersions?: string;
    noSelection?: string;
    noAbility?: string;
  };
  onCreateVersion?: () => void;
}

export const VersionEmpty = ({
  hasVersions,
  hasSelectedVersion,
  hasSelectedAbility = true,
  className,
  title = {
    noVersions: "创建第一个版本",
    noSelection: "选择一个版本",
    noAbility: "选择一个能力",
  },
  description = {
    noVersions: "您需要先创建一个提示版本才能管理能力",
    noSelection: "请从上方下拉菜单中选择一个版本",
    noAbility: "请从左侧面板选择一个能力进行编辑",
  },
}: VersionPromptEmptyProps) => {
  const shouldShow = !hasVersions || !hasSelectedVersion || !hasSelectedAbility;

  if (!shouldShow) {
    return null;
  }

  // 确定显示的内容优先级：版本 > 版本选择 > 能力选择
  const getDisplayContent = () => {
    if (!hasVersions) {
      return {
        title: title.noVersions,
        description: description.noVersions,
        icon: Plus,
      };
    }
    if (!hasSelectedVersion) {
      return {
        title: title.noSelection,
        description: description.noSelection,
        icon: FolderOpen,
      };
    }
    return {
      title: title.noAbility,
      description: description.noAbility,
      icon: MousePointer2,
    };
  };

  const displayContent = getDisplayContent();
  const IconComponent = displayContent.icon;

  return (
    <div
      className={cn("flex-1 flex items-center justify-center p-8", className)}
    >
      <div className="text-center space-y-4 max-w-md">
        <div className="w-16 h-16 mx-auto bg-blue-50 rounded-full flex items-center justify-center">
          <IconComponent className="size-8 text-primary" />
        </div>
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {displayContent.title}
          </h3>
          <p className="text-gray-500 text-sm leading-relaxed">
            {displayContent.description}
          </p>
        </div>
      </div>
    </div>
  );
};
