/**
 * 能力差异查看器组件
 */

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { AbilityItemTypeIcons } from "@/constants/prompt";
import { cn } from "@/lib/utils";
import { AbilityItemType } from "@/types/api/prompt";
import { ChevronDown } from "lucide-react";
import { useState } from "react";
import { ChangeType } from "../../../types";
import { AbilityChangeItem } from "../../../utils";
import { ChangeBadge } from "../shared";
import { ModuleDiffViewer } from "./module-diff-viewer";

interface AbilityDiffViewerProps {
  /**
   * 能力变更项
   */
  change: AbilityChangeItem;
}

/**
 * 能力项变更展示组件
 */
interface AbilityItemChangeViewProps {
  abilityItemChange: AbilityChangeItem["abilityItemChanges"][0];
}

function AbilityItemChangeView({
  abilityItemChange,
}: AbilityItemChangeViewProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const IconComponent =
    AbilityItemTypeIcons[abilityItemChange.type as AbilityItemType];

  return (
    <Collapsible
      open={isExpanded}
      onOpenChange={setIsExpanded}
      className="group"
    >
      <div className="border border-gray-200 rounded-lg overflow-hidden">
        <CollapsibleTrigger asChild>
          <div className="flex items-center justify-between px-3 py-2 bg-gray-50 hover:bg-gray-100 cursor-pointer transition-colors">
            <div className="flex items-center gap-3 flex-1 min-w-0">
              <div className="flex items-center gap-2">
                {IconComponent && (
                  <IconComponent className="h-4 w-4 text-gray-600" />
                )}
                <span className="text-sm font-medium text-gray-900">
                  {abilityItemChange.name}
                </span>
              </div>
              <div className="flex items-center gap-2 text-xs">
                {abilityItemChange.summary.added > 0 && (
                  <ChangeBadge
                    changeType={ChangeType.ADDED}
                    variant="count"
                    size="sm"
                    count={abilityItemChange.summary.added}
                  />
                )}
                {abilityItemChange.summary.modified > 0 && (
                  <ChangeBadge
                    changeType={ChangeType.MODIFIED}
                    variant="count"
                    size="sm"
                    count={abilityItemChange.summary.modified}
                  />
                )}
                {abilityItemChange.summary.removed > 0 && (
                  <ChangeBadge
                    changeType={ChangeType.REMOVED}
                    variant="count"
                    size="sm"
                    count={abilityItemChange.summary.removed}
                  />
                )}
              </div>
            </div>
            <ChevronDown
              className={cn(
                "h-4 w-4 text-gray-500 transition-transform",
                isExpanded && "rotate-180",
              )}
            />
          </div>
        </CollapsibleTrigger>

        <CollapsibleContent>
          <div className="bg-white p-3 space-y-3">
            {abilityItemChange.moduleChanges.map((moduleChange) => (
              <div key={moduleChange.moduleId} className="space-y-2">
                <div className="flex items-center gap-2 py-1">
                  <div className="flex items-center gap-2">
                    <ChangeBadge
                      changeType={moduleChange.changeType}
                      variant="primary"
                      size="sm"
                    />
                    <span className="text-sm text-gray-900">
                      {moduleChange.moduleName}
                    </span>
                  </div>
                </div>

                {/* 显示模块内容的 diff */}
                {(moduleChange.oldPrompt || moduleChange.newPrompt) && (
                  <ModuleDiffViewer
                    change={{
                      id: moduleChange.moduleId,
                      name: moduleChange.moduleName,
                      type: "module",
                      changeType: moduleChange.changeType,
                      level: moduleChange.level,
                      oldPrompt: moduleChange.oldPrompt,
                      newPrompt: moduleChange.newPrompt,
                    }}
                  />
                )}
              </div>
            ))}
          </div>
        </CollapsibleContent>
      </div>
    </Collapsible>
  );
}

export function AbilityDiffViewer({ change }: AbilityDiffViewerProps) {
  if (!change.abilityItemChanges || change.abilityItemChanges.length === 0) {
    return (
      <div className="bg-gray-50 px-3 py-2 rounded-lg">
        <div className="flex items-center justify-between text-xs">
          <span className="text-gray-500">无详细变更</span>
          <div className="flex items-center gap-2">
            {change.moduleChanges.added > 0 && (
              <ChangeBadge
                changeType={ChangeType.ADDED}
                variant="count"
                size="sm"
                count={change.moduleChanges.added}
              />
            )}
            {change.moduleChanges.modified > 0 && (
              <ChangeBadge
                changeType={ChangeType.MODIFIED}
                variant="count"
                size="sm"
                count={change.moduleChanges.modified}
              />
            )}
            {change.moduleChanges.removed > 0 && (
              <ChangeBadge
                changeType={ChangeType.REMOVED}
                variant="count"
                size="sm"
                count={change.moduleChanges.removed}
              />
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {/* 总体统计 */}
      <div className="bg-gray-50 px-3 py-2 rounded-lg">
        <div className="flex items-center justify-between text-xs">
          <span className="text-gray-700 font-medium">
            共 {change.abilityItemChanges.length} 个能力项有变更
          </span>
          <div className="flex items-center gap-3">
            {change.moduleChanges.added > 0 && (
              <ChangeBadge
                changeType={ChangeType.ADDED}
                variant="primary"
                size="sm"
                count={change.moduleChanges.added}
                text={`新增 ${change.moduleChanges.added}`}
              />
            )}
            {change.moduleChanges.modified > 0 && (
              <ChangeBadge
                changeType={ChangeType.MODIFIED}
                variant="primary"
                size="sm"
                count={change.moduleChanges.modified}
                text={`修改 ${change.moduleChanges.modified}`}
              />
            )}
            {change.moduleChanges.removed > 0 && (
              <ChangeBadge
                changeType={ChangeType.REMOVED}
                variant="primary"
                size="sm"
                count={change.moduleChanges.removed}
                text={`删除 ${change.moduleChanges.removed}`}
              />
            )}
          </div>
        </div>
      </div>

      {/* 按能力项分组展示详细变更 */}
      <div className="space-y-2">
        {change.abilityItemChanges.map((abilityItemChange) => (
          <AbilityItemChangeView
            key={abilityItemChange.type}
            abilityItemChange={abilityItemChange}
          />
        ))}
      </div>
    </div>
  );
}
