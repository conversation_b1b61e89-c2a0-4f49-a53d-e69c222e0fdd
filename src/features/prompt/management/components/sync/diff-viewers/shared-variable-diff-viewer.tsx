/**
 * 共享变量差异查看器组件
 */

import { useMemo } from "react";
import {
  createEditorSharedVariableContent,
  SharedVariableChangeItem,
} from "../../../utils";
import { SharedVariableEditorWithVariables } from "../../editors";

interface SharedVariableDiffViewerProps {
  /**
   * 共享变量变更项
   */
  change: SharedVariableChangeItem;
}

export function SharedVariableDiffViewer({
  change,
}: SharedVariableDiffViewerProps) {
  const content = useMemo(
    () => createEditorSharedVariableContent(change.newContent || ""),
    [change.newContent],
  );
  const previousContent = useMemo(
    () => createEditorSharedVariableContent(change.oldContent || ""),
    [change.oldContent],
  );

  return (
    <SharedVariableEditorWithVariables
      content={content}
      previousContent={previousContent}
      editable={false}
      enableDiff={true}
      className="min-h-[100px] text-xs"
    />
  );
}
