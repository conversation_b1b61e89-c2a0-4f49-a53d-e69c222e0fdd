/**
 * 模块标签差异查看器组件
 */

import { Tag } from "lucide-react";
import { ChangeItem } from "../../../utils";
import { ChangeBadge } from "../shared";

interface ModuleLabelDiffViewerProps {
  /**
   * 模块标签变更项
   */
  change: ChangeItem;
}

export function ModuleLabelDiffViewer({ change }: ModuleLabelDiffViewerProps) {
  return (
    <div className="bg-gray-50 px-3 py-2 rounded-lg">
      <div className="flex items-center justify-between text-sm">
        <div className="flex items-center gap-2 flex-1 min-w-0">
          <Tag className="h-4 w-4 text-gray-600 shrink-0" />
          <span className="text-gray-700 font-medium truncate">
            {change.name}
          </span>
        </div>
        <div className="flex items-center gap-2 shrink-0">
          <ChangeBadge
            changeType={change.changeType}
            variant="primary"
            size="sm"
          />
        </div>
      </div>
    </div>
  );
}
