/**
 * 模块差异查看器组件
 */

import { useMemo } from "react";
import { createEditorModuleContent, ModuleChangeItem } from "../../../utils";
import { ModuleEditorWithVariables } from "../../editors";

interface ModuleDiffViewerProps {
  /**
   * 模块变更项
   */
  change: ModuleChangeItem;
}

export function ModuleDiffViewer({ change }: ModuleDiffViewerProps) {
  const content = useMemo(
    () => createEditorModuleContent(change.newPrompt || ""),
    [change.newPrompt],
  );
  const previousContent = useMemo(
    () => createEditorModuleContent(change.oldPrompt || ""),
    [change.oldPrompt],
  );

  return (
    <ModuleEditorWithVariables
      content={content}
      previousContent={previousContent}
      editable={false}
      enableDiff={true}
      level={change.level}
      className="min-h-[100px] text-xs"
    />
  );
}
