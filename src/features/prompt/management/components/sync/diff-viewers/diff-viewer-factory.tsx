/**
 * 差异查看器工厂组件
 * 根据变更类型返回对应的差异查看器
 */

import { ChangeItem } from "../../../utils";
import { AbilityDiffViewer } from "./ability-diff-viewer";
import { ModuleDiffViewer } from "./module-diff-viewer";
import { ModuleLabelDiffViewer } from "./module-label-diff-viewer";
import { SharedVariableDiffViewer } from "./shared-variable-diff-viewer";

interface DiffViewerFactoryProps {
  /**
   * 变更项
   */
  change: ChangeItem;
}

export function DiffViewerFactory({ change }: DiffViewerFactoryProps) {
  switch (change.type) {
    case "ability":
      return <AbilityDiffViewer change={change} />;
    case "module":
      return <ModuleDiffViewer change={change} />;
    case "sharedVariable":
      return <SharedVariableDiffViewer change={change} />;
    case "moduleLabel":
      return <ModuleLabelDiffViewer change={change} />;
  }
}
