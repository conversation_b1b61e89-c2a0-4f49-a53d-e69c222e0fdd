import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { cn } from "@/lib/utils";
import { useAtomValue } from "jotai";
import { Loader2Icon } from "lucide-react";
import {
  getQueueProgressTextAtom,
  queueProgressAtom,
  QueueStatus,
} from "../../services";

interface SyncProgressViewProps {
  /**
   * 自定义样式类名
   */
  className?: string;
}

export function SyncProgressView({ className }: SyncProgressViewProps) {
  const queueProgress = useAtomValue(queueProgressAtom);
  const progressText = useAtomValue(getQueueProgressTextAtom);

  // 计算同步状态
  const isSyncing = queueProgress?.status === QueueStatus.RUNNING;
  const progressPercent = Math.round(
    (queueProgress?.overallProgress || 0) * 100,
  );

  if (!queueProgress) {
    return null;
  }

  return (
    <Card className={cn("border-border/50", className)}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-base">
          {isSyncing && <Loader2Icon className="h-4 w-4 animate-spin" />}
          <span>{isSyncing ? "正在同步数据" : "同步状态"}</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* 状态描述 */}
        <div className="text-sm text-muted-foreground">
          {progressText || "准备同步..."}
        </div>

        {/* 进度条 */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="font-medium">同步进度</span>
            <span className="font-mono text-primary">{progressPercent}%</span>
          </div>
          <Progress value={progressPercent} className="h-2" />
        </div>

        {/* 详细进度信息 */}
        <div className="grid grid-cols-3 gap-4 pt-2 border-t border-border/50">
          <div className="text-center">
            <div className="text-lg font-semibold text-green-600">
              {queueProgress.completedTasks}
            </div>
            <div className="text-xs text-muted-foreground">已完成</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold">
              {queueProgress.totalTasks}
            </div>
            <div className="text-xs text-muted-foreground">总任务</div>
          </div>
          {queueProgress.failedTasks > 0 && (
            <div className="text-center">
              <div className="text-lg font-semibold text-destructive">
                {queueProgress.failedTasks}
              </div>
              <div className="text-xs text-muted-foreground">失败</div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
