import { z } from "zod";

/**
 * 版本同步表单验证模式
 */
export const versionSyncSchema = z.object({
  versionName: z
    .string()
    .min(1, { message: "版本名称不能为空" })
    .max(50, { message: "版本名称不能超过50个字符" }),
  remark: z
    .string()
    .min(1, { message: "版本备注不能为空" })
    .max(200, { message: "版本备注不能超过200个字符" }),
});

/**
 * 版本同步表单值类型
 */
export type VersionSyncFormValues = z.infer<typeof versionSyncSchema>;
