/**
 * 通用变更项组件
 */

import { cn } from "@/lib/utils";
import { ReactNode } from "react";
import { ChangeItem } from "../../../utils";
import { ChangeBadge } from "./change-badge";

interface ChangeItemViewProps {
  /**
   * 变更项数据
   */
  change: ChangeItem;
  /**
   * 详情内容
   */
  children?: ReactNode;
}

export function ChangeItemView({ change, children }: ChangeItemViewProps) {
  return (
    <>
      <div
        className={cn(
          "flex items-center justify-between px-4 py-2 border-b border-b-border",
        )}
      >
        <div className="flex items-center gap-3 flex-1 min-w-0">
          <div className={cn("flex items-center justify-center gap-2 text-sm")}>
            <ChangeBadge
              changeType={change.changeType}
              variant="primary"
              size="sm"
            />
            <span className="flex-1 min-w-0 truncate">{change.name}</span>
          </div>
        </div>
      </div>

      {children && <div className="p-4">{children}</div>}
    </>
  );
}
