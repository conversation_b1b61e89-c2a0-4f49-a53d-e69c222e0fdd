/**
 * 同步组件共享常量
 */

import { Diff, Minus, Plus } from "lucide-react";
import { ChangeType } from "../../../types";

/**
 * 变更类型配置映射
 */
export const changeTypeConfig = {
  [ChangeType.ADDED]: {
    iconComponent: Plus,
    label: "新增",
    foregroundColor: "bg-green-100",
    color: "text-green-700",
  },
  [ChangeType.REMOVED]: {
    iconComponent: Minus,
    label: "删除",
    foregroundColor: "bg-red-100",
    color: "text-red-700",
  },
  [ChangeType.MODIFIED]: {
    iconComponent: Diff,
    label: "修改",
    foregroundColor: "bg-blue-100",
    color: "text-blue-700",
  },
};

/**
 * 数据类型配置映射
 */
export const dataTypeConfig = {
  ability: {
    label: "能力",
  },
  module: {
    label: "模块",
  },
  moduleLabel: {
    label: "模块标签",
  },
  sharedVariable: {
    label: "共享变量",
  },
};
