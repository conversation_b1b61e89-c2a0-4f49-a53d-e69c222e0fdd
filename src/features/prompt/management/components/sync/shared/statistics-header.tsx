interface StatisticsHeaderProps {
  /**
   * 标题
   */
  title: string;
  /**
   * 变更总数
   */
  totalChanges: number;
}

export function StatisticsHeader({
  title,
  totalChanges,
}: StatisticsHeaderProps) {
  return (
    <div className="flex items-center gap-2 py-3">
      <div className="text-sm font-medium">{title}</div>
      <div className="ml-auto flex items-center gap-4 text-xs text-gray-500">
        <span>共 {totalChanges} 项变更</span>
      </div>
    </div>
  );
}
