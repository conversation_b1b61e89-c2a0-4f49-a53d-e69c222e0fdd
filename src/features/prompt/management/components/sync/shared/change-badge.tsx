import { cn } from "@/lib/utils";
import { cva, type VariantProps } from "class-variance-authority";
import { LucideIcon } from "lucide-react";
import { ChangeType } from "../../../types";
import { changeTypeConfig } from "./constants";

const changeBadgeVariants = cva(
  "inline-flex items-center justify-center font-medium transition-colors",
  {
    variants: {
      variant: {
        // 主要层级：带图标和文字
        primary: "gap-1 rounded-full px-2 py-1",
        // 统计层级：数量统计
        count: "rounded px-1.5 py-0.5",
        // 简化层级：仅图标
        icon: "rounded-full p-0.5",
        // 简化层级：仅数量
        number: "rounded px-1 py-0.5",
      },
      size: {
        sm: "",
        md: "",
        lg: "",
      },
      changeType: {
        [ChangeType.ADDED]: "",
        [ChangeType.REMOVED]: "",
        [ChangeType.MODIFIED]: "",
      },
    },
    compoundVariants: [
      // 主要层级尺寸
      {
        variant: "primary",
        size: "sm",
        className: "text-xs",
      },
      {
        variant: "primary",
        size: "md",
        className: "text-sm",
      },
      {
        variant: "primary",
        size: "lg",
        className: "text-base",
      },
      // 统计层级尺寸
      {
        variant: "count",
        size: "sm",
        className: "text-xs",
      },
      {
        variant: "count",
        size: "md",
        className: "text-sm",
      },
      {
        variant: "count",
        size: "lg",
        className: "text-base",
      },
      // 图标层级尺寸
      {
        variant: "icon",
        size: "sm",
        className: "text-xs",
      },
      {
        variant: "icon",
        size: "md",
        className: "text-sm",
      },
      {
        variant: "icon",
        size: "lg",
        className: "text-base",
      },
      // 数量层级尺寸
      {
        variant: "number",
        size: "sm",
        className: "text-xs",
      },
      {
        variant: "number",
        size: "md",
        className: "text-sm",
      },
      {
        variant: "number",
        size: "lg",
        className: "text-base",
      },
      // 新增样式
      {
        changeType: ChangeType.ADDED,
        className: "bg-green-100 text-green-700",
      },
      // 删除样式
      {
        changeType: ChangeType.REMOVED,
        className: "bg-red-100 text-red-700",
      },
      // 修改样式
      {
        changeType: ChangeType.MODIFIED,
        className: "bg-blue-100 text-blue-700",
      },
    ],
    defaultVariants: {
      variant: "primary",
      size: "md",
    },
  },
);

const iconSizeMap = {
  sm: "size-3",
  md: "size-4",
  lg: "size-5",
};

export interface ChangeBadgeProps
  extends React.HTMLAttributes<HTMLSpanElement>,
    VariantProps<typeof changeBadgeVariants> {
  /**
   * 变更类型
   */
  changeType: ChangeType;
  /**
   * 数量（用于统计和数量变体）
   */
  count?: number;
  /**
   * 自定义图标组件
   */
  icon?: LucideIcon;
  /**
   * 自定义文本
   */
  text?: string;
}

export function ChangeBadge({
  className,
  variant = "primary",
  size = "md",
  changeType,
  count,
  icon,
  text,
  ...props
}: ChangeBadgeProps) {
  const config = changeTypeConfig[changeType];
  const IconComponent = icon || config.iconComponent;
  const iconSize = iconSizeMap[size || "md"];

  // 根据变体渲染不同内容
  const renderContent = () => {
    switch (variant) {
      case "primary":
        return (
          <>
            <IconComponent className={iconSize} />
            <span>{text || config.label}</span>
          </>
        );
      case "count": {
        const prefix =
          changeType === ChangeType.ADDED
            ? "+"
            : changeType === ChangeType.REMOVED
              ? "-"
              : "~";
        return `${prefix}${count || 0}`;
      }
      case "icon":
        return <IconComponent className={iconSize} />;
      case "number":
        return count?.toString() || "0";
      default:
        return null;
    }
  };

  return (
    <span
      className={cn(
        changeBadgeVariants({ variant, size, changeType }),
        className,
      )}
      {...props}
    >
      {renderContent()}
    </span>
  );
}
