/**
 * 通用差异内容组件
 */

import { Button } from "@/components/ui/button";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { cn } from "@/lib/utils";
import { ChevronDown, ChevronRight } from "lucide-react";
import { ReactNode, useState } from "react";

interface DiffContentProps {
  /**
   * 差异内容
   */
  children: ReactNode;
  /**
   * 展开按钮文本
   */
  expandText?: string;
  /**
   * 收起按钮文本
   */
  collapseText?: string;
  /**
   * 自定义样式类名
   */
  className?: string;
}

export function DiffContent({
  children,
  expandText = "查看差异",
  collapseText = "隐藏差异",
  className,
}: DiffContentProps) {
  const [expanded, setExpanded] = useState(false);

  return (
    <div className={cn("rounded-lg p-3", className)}>
      <Collapsible open={expanded} onOpenChange={setExpanded}>
        <CollapsibleTrigger asChild>
          <Button variant="ghost" size="sm" className="h-6 px-2 text-xs">
            {expanded ? (
              <>
                <ChevronDown className="h-3 w-3 mr-1" />
                {collapseText}
              </>
            ) : (
              <>
                <ChevronRight className="h-3 w-3 mr-1" />
                {expandText}
              </>
            )}
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent className="mt-2">
          <div className="bg-white/50 rounded p-2">{children}</div>
        </CollapsibleContent>
      </Collapsible>
    </div>
  );
}
