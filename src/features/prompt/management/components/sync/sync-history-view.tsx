import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import dayjs from "dayjs";
import { useAtomValue } from "jotai";
import {
  CheckCircle2Icon,
  ClockIcon,
  Loader2Icon,
  XCircleIcon,
} from "lucide-react";
import {
  activeQueueIdAtom,
  getCurrentVersionQueuesAtom,
  QueueStatus,
} from "../../services";

interface SyncHistoryViewProps {
  /**
   * 自定义样式类名
   */
  className?: string;
  /**
   * 显示的历史记录数量
   */
  maxItems?: number;
}

export function SyncHistoryView({
  className,
  maxItems = 5,
}: SyncHistoryViewProps) {
  const currentVersionQueues = useAtomValue(getCurrentVersionQueuesAtom);
  const activeQueueId = useAtomValue(activeQueueIdAtom);

  // 如果只有一个队列或没有队列，不显示历史记录
  if (currentVersionQueues.length <= 1) {
    return null;
  }

  const getStatusIcon = (status: QueueStatus) => {
    switch (status) {
      case QueueStatus.SUCCESS:
        return <CheckCircle2Icon className="h-3 w-3 text-green-600" />;
      case QueueStatus.FAILED:
        return <XCircleIcon className="h-3 w-3 text-destructive" />;
      case QueueStatus.RUNNING:
        return <Loader2Icon className="h-3 w-3 text-blue-600 animate-spin" />;
      case QueueStatus.PENDING:
        return <ClockIcon className="h-3 w-3 text-yellow-600" />;
      default:
        return null;
    }
  };

  const getStatusBadge = (status: QueueStatus) => {
    switch (status) {
      case QueueStatus.SUCCESS:
        return (
          <Badge
            variant="success"
            className="bg-green-100 text-green-800 border-green-200"
          >
            成功
          </Badge>
        );
      case QueueStatus.FAILED:
        return (
          <Badge
            variant="destructive"
            className="bg-red-100 text-red-800 border-red-200"
          >
            失败
          </Badge>
        );
      case QueueStatus.RUNNING:
        return (
          <Badge
            variant="info"
            className="bg-blue-100 text-blue-800 border-blue-200"
          >
            进行中
          </Badge>
        );
      case QueueStatus.PENDING:
        return (
          <Badge
            variant="warning"
            className="bg-yellow-100 text-yellow-800 border-yellow-200"
          >
            等待中
          </Badge>
        );
      default:
        return null;
    }
  };

  return (
    <Card className={cn("border-border/50", className)}>
      <CardHeader className="pb-3">
        <CardTitle className="text-base">同步历史记录</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          {currentVersionQueues.slice(0, maxItems).map((queue, index) => (
            <div
              key={queue.id}
              className={cn(
                "flex items-center justify-between p-3 rounded-lg border transition-colors",
                queue.id === activeQueueId
                  ? "bg-primary/5 border-primary/20"
                  : "bg-muted/30 border-border/50 hover:bg-muted/50",
              )}
            >
              <div className="flex items-center gap-3">
                {getStatusIcon(queue.status)}
                <div className="flex flex-col">
                  <div className="text-sm font-medium">
                    {index === 0 ? "当前同步" : `历史记录 #${index}`}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {dayjs(queue.createdAt).format("YYYY-MM-DD HH:mm:ss")}
                  </div>
                </div>
              </div>
              {getStatusBadge(queue.status)}
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
