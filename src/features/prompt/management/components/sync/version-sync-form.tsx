import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { UseFormReturn } from "react-hook-form";
import { BaseVersionInfo } from "./base-version-info";
import { VersionDataStatistics } from "./version-data-statistics";
import { VersionSyncFormValues } from "./version-sync-form-schema";

interface VersionSyncFormProps {
  /**
   * 版本ID
   */
  versionId: string;
  /**
   * 表单实例（可选，用于外部控制）
   */
  form: UseFormReturn<VersionSyncFormValues>;
  /**
   * 表单提交回调
   */
  onSubmit?: (values: VersionSyncFormValues) => void;
}

export function VersionSyncForm({
  versionId,
  form,
  onSubmit,
}: VersionSyncFormProps) {
  const handleSubmit = (values: VersionSyncFormValues) => {
    onSubmit?.(values);
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(handleSubmit)}
        className="space-y-6 p-4"
      >
        {/* 基准版本信息 */}
        <BaseVersionInfo versionId={versionId} />

        <div className="space-y-4">
          <FormField
            control={form.control}
            name="versionName"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm font-medium">版本名称</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    placeholder="如：新功能发布版本"
                    className="h-11"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="remark"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm font-medium">版本备注</FormLabel>
                <FormControl>
                  <Textarea
                    {...field}
                    placeholder="请描述此版本的主要变更内容..."
                    rows={4}
                    className="resize-none"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <VersionDataStatistics draftVersionId={versionId} />
      </form>
    </Form>
  );
}
