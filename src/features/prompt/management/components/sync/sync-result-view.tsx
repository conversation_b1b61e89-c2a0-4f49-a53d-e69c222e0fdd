import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { useAtomValue } from "jotai";
import { AlertCircleIcon, CheckCircle2Icon } from "lucide-react";
import {
  activeQueueIdAtom,
  canRetryAtom,
  syncResultAtom,
} from "../../services";

interface SyncResultViewProps {
  /**
   * 自定义样式类名
   */
  className?: string;
}

export function SyncResultView({ className }: SyncResultViewProps) {
  const syncResult = useAtomValue(syncResultAtom);
  const activeQueueId = useAtomValue(activeQueueIdAtom);
  const canRetryFn = useAtomValue(canRetryAtom);
  const canRetry = activeQueueId ? canRetryFn(activeQueueId) : false;

  if (!syncResult) {
    return null;
  }

  return (
    <Card className={cn("border-border/50", className)}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-base">
          {syncResult.success ? (
            <CheckCircle2Icon className="h-4 w-4 text-green-600" />
          ) : (
            <AlertCircleIcon className="h-4 w-4 text-destructive" />
          )}
          <span>{syncResult.success ? "同步完成" : "同步失败"}</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {syncResult.success ? (
          <Alert className="border-green-200 bg-green-50/50">
            <CheckCircle2Icon className="h-4 w-4 text-green-600" />
            <AlertTitle className="text-green-800">同步成功！</AlertTitle>
            <AlertDescription className="text-green-700">
              数据已成功同步到服务端
            </AlertDescription>
          </Alert>
        ) : (
          <Alert variant="destructive" className="border-red-200 bg-red-50/50">
            <AlertCircleIcon className="h-4 w-4" />
            <AlertTitle>同步失败</AlertTitle>
            <AlertDescription>
              <div className="space-y-3">
                <div>
                  <p className="font-medium mb-2">错误详情：</p>
                  <div className="space-y-1">
                    {syncResult.errors?.map((error, index) => (
                      <div
                        key={`${error.dataId}-${error.taskId}-${index}`}
                        className="text-sm bg-red-100/50 rounded px-2 py-1 border border-red-200"
                      >
                        {error.message}
                      </div>
                    ))}
                  </div>
                </div>

                {!canRetry && (
                  <div className="mt-3 p-3 bg-muted/50 rounded-md border border-border/50">
                    <p className="font-medium text-sm mb-1">无法重试原因：</p>
                    <p className="text-sm text-muted-foreground">
                      数据已发生关键变更（如依赖关系、引用名称等），需要重新同步。
                    </p>
                  </div>
                )}
              </div>
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  );
}
