import { cn } from "@/lib/utils";
import { FileText, GitCompare, Loader2 } from "lucide-react";

interface VersionComparisonEmptyStateProps {
  className?: string;
  type?: "no-selection" | "no-changes" | "loading" | "error";
  title?: string;
  description?: string;
}

export function VersionComparisonEmptyState({
  className,
  type = "loading",
  title,
  description,
}: VersionComparisonEmptyStateProps) {
  const getEmptyStateConfig = () => {
    switch (type) {
      case "no-selection":
        return {
          icon: GitCompare,
          title: title || "请选择版本进行对比",
          description:
            description ||
            "选择两个不同的版本来查看它们之间的差异，包括能力、模块、标签和共享变量的变更。",
          iconColor: "text-blue-400",
          bgColor: "bg-blue-50",
          showLegend: true,
        };
      case "no-changes":
        return {
          icon: FileText,
          title: title || "版本内容完全相同",
          description:
            description ||
            "所选的两个版本在能力、模块、模块标签和共享变量方面没有任何差异。",
          iconColor: "text-green-400",
          bgColor: "bg-green-50",
          showLegend: false,
        };
      case "loading":
        return {
          icon: Loader2,
          title: title || "正在加载版本对比数据",
          description:
            description || "请稍候，正在获取版本数据并进行对比分析...",
          iconColor: "text-gray-400",
          bgColor: "bg-gray-50",
          showLegend: false,
          animate: true,
        };
      case "error":
        return {
          icon: FileText,
          title: title || "加载失败",
          description:
            description || "无法加载版本对比数据，请检查网络连接或稍后重试。",
          iconColor: "text-red-400",
          bgColor: "bg-red-50",
          showLegend: false,
        };
      default:
        return {
          icon: GitCompare,
          title: title || "暂无数据",
          description: description || "暂时没有可显示的内容。",
          iconColor: "text-gray-400",
          bgColor: "bg-gray-50",
          showLegend: false,
        };
    }
  };

  const config = getEmptyStateConfig();
  const IconComponent = config.icon;

  return (
    <div
      className={cn(
        "flex flex-col items-center justify-center p-8 text-center min-h-64",
        className,
      )}
    >
      <div
        className={cn(
          "w-16 h-16 rounded-full flex items-center justify-center mb-4",
          config.bgColor,
        )}
      >
        <IconComponent
          className={cn(
            "w-8 h-8",
            config.iconColor,
            config.animate && "animate-spin",
          )}
        />
      </div>

      <h3 className="text-lg font-medium text-gray-900 mb-2">{config.title}</h3>

      <p className="text-sm text-gray-500 max-w-md leading-relaxed">
        {config.description}
      </p>

      {config.showLegend && (
        <div className="mt-6 text-xs text-gray-400">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 bg-green-400 rounded-full"></div>
              <span>新增</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 bg-red-400 rounded-full"></div>
              <span>删除</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
              <span>修改</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
