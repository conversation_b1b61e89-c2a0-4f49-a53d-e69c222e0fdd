/**
 * 基准版本信息展示组件
 */

import { getPromptVersionName } from "@/components/common/version-select/prompt-version/helper";
import { PromptVersionStatusView } from "@/components/common/version-select/prompt-version/prompt-version-status-view";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { PromptVersionService } from "@/services/prompt-version-service";
import { PromptVersionStatus } from "@/types/api/prompt-version";
import { useQuery } from "@tanstack/react-query";
import { useAtomValue } from "jotai";
import { FileText, Info } from "lucide-react";
import { draftVersionsAtom } from "../../atoms";

export function BaseVersionInfo({ versionId }: { versionId: string }) {
  // 获取草稿数据
  const draftVersions = useAtomValue(draftVersionsAtom);
  const draftVersion = draftVersions.find((v) => v.id === versionId);

  // 获取基准版本详情
  const { data: baseVersionData, isLoading } = useQuery({
    queryKey: ["getPromptVersionDetail", draftVersion?.baseVersionId],
    queryFn: () => {
      if (!draftVersion?.baseVersionId) {
        return null;
      }

      return PromptVersionService.getPromptVersionDetail({
        promptVersionId: draftVersion.baseVersionId,
      });
    },
    enabled: draftVersion?.baseVersionId != null,
  });

  // 判断操作类型
  const isCreateNewVersion =
    !baseVersionData || baseVersionData.status === PromptVersionStatus.ONLINE;

  // 加载状态
  if (isLoading && draftVersion?.baseVersionId) {
    return (
      <Card className="border-muted">
        <CardContent className="p-4">
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Info className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium text-muted-foreground">
                基准版本信息
              </span>
            </div>
            <div className="space-y-2">
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-28" />
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // 无基准版本（全新创建）
  if (!draftVersion?.baseVersionId || !baseVersionData) {
    return (
      <Card>
        <CardContent className="p-4">
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Info className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium text-muted-foreground">
                基准版本信息
              </span>
            </div>
            <Alert className="bg-muted/50 border-muted">
              <FileText className="h-4 w-4" />
              <AlertDescription>将新建一个版本（无基准版本）</AlertDescription>
            </Alert>
          </div>
        </CardContent>
      </Card>
    );
  }

  // 有基准版本
  return (
    <Card>
      <CardContent className="p-4">
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <Info className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm font-medium text-muted-foreground">
              基准版本信息
            </span>
          </div>

          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <div className="flex-1 space-y-1">
                <div className="text-sm font-medium">
                  {getPromptVersionName(baseVersionData)}
                </div>
                <div className="text-xs text-muted-foreground">
                  版本号: {baseVersionData.version}
                </div>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <div className="flex items-center gap-2">
                <span className="text-xs text-muted-foreground">状态:</span>
                <PromptVersionStatusView version={baseVersionData} />
              </div>
            </div>

            {baseVersionData.remark && (
              <div className="flex items-start gap-2">
                <div className="flex-1 space-y-1">
                  <div className="text-xs text-muted-foreground mb-1">
                    备注:
                  </div>
                  <div className="text-xs text-foreground bg-muted/50 rounded px-3 py-2">
                    {baseVersionData.remark}
                  </div>
                </div>
              </div>
            )}
          </div>

          <Alert className="bg-muted/50 border-muted">
            <Info className="h-4 w-4" />
            <AlertDescription>
              {isCreateNewVersion ? (
                <span>基准版本为线上版本，将创建新的版本</span>
              ) : (
                <span>基准版本为非线上版本，将更新现有版本</span>
              )}
            </AlertDescription>
          </Alert>
        </div>
      </CardContent>
    </Card>
  );
}
