import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { cn } from "@/lib/utils";
import dayjs from "dayjs";
import { ChevronDown, ChevronRight, Clock } from "lucide-react";
import { useState } from "react";
import {
  ChangeItem,
  generateTabConfigs,
  PatchGroupComparison,
  VersionHistoryComparison,
} from "../../utils";
import { DiffViewerFactory } from "./diff-viewers";
import { ChangeItemView } from "./shared";

interface VersionHistoryViewProps {
  /**
   * 版本历史对比结果
   */
  historyComparison: VersionHistoryComparison;
  /**
   * 自定义样式类名
   */
  className?: string;
}

interface PatchGroupCardProps {
  /**
   * 补丁组对比结果
   */
  patchGroup: PatchGroupComparison;
  /**
   * 是否默认展开
   */
  defaultOpen?: boolean;
}

/**
 * 单个补丁组卡片组件
 */
function PatchGroupCard({
  patchGroup,
  defaultOpen = false,
}: PatchGroupCardProps) {
  const [isOpen, setIsOpen] = useState(defaultOpen);

  // 如果没有变更，不显示
  if (!patchGroup.hasChanges) {
    return null;
  }

  // 生成标签页配置
  const availableTabs = generateTabConfigs(patchGroup.comparison);

  // 渲染变更项列表
  const renderChangeItems = (items: ChangeItem[]) => (
    <div className="divide-y border border-border rounded-lg overflow-hidden">
      {items.map((change) => (
        <ChangeItemView key={change.id} change={change}>
          <DiffViewerFactory change={change} />
        </ChangeItemView>
      ))}
    </div>
  );

  return (
    <div className="bg-card rounded-lg border border-border/50">
      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <CollapsibleTrigger asChild>
          <div className="cursor-pointer hover:bg-muted/30 transition-colors py-2 px-3 rounded-t-lg">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Button variant="ghost" size="sm" className="h-4 w-4 p-0">
                  {isOpen ? (
                    <ChevronDown className="h-3 w-3" />
                  ) : (
                    <ChevronRight className="h-3 w-3" />
                  )}
                </Button>
                <span className="text-sm font-medium text-foreground">
                  {dayjs(patchGroup.timestamp).format("YYYY-MM-DD HH:mm:ss")}
                </span>
              </div>
              <Badge variant="secondary" className="text-xs px-1.5 py-0.5 h-5">
                {patchGroup.comparison.summary.total} 项变更
              </Badge>
            </div>
          </div>
        </CollapsibleTrigger>

        <CollapsibleContent>
          <div className="px-3 pb-2 border-t border-border/30">
            {availableTabs.length > 0 ? (
              <Tabs className="w-full" defaultValue={availableTabs[0]?.key}>
                <TabsList className="w-full justify-start h-8 mt-2">
                  {availableTabs.map((tab) => (
                    <TabsTrigger
                      key={tab.key}
                      value={tab.key}
                      className="text-xs h-6 px-2"
                    >
                      {tab.label}
                      <Label
                        className="px-1 py-0.5 ml-1 text-xs"
                        variant="secondary"
                      >
                        {tab.count}
                      </Label>
                    </TabsTrigger>
                  ))}
                </TabsList>

                {availableTabs.map((tab) => (
                  <TabsContent
                    key={tab.key}
                    value={tab.key}
                    className="mt-2 min-h-20 overflow-auto"
                  >
                    {renderChangeItems(tab.items)}
                  </TabsContent>
                ))}
              </Tabs>
            ) : (
              <div className="text-center text-muted-foreground py-4 text-sm">
                此版本无变更
              </div>
            )}
          </div>
        </CollapsibleContent>
      </Collapsible>
    </div>
  );
}

/**
 * 版本历史展示组件
 */
export function VersionHistoryView({
  historyComparison,
  className,
}: VersionHistoryViewProps) {
  // 过滤出有变更的补丁组
  const patchGroupsWithChanges = historyComparison.patchGroups.filter(
    (group) => group.hasChanges,
  );

  if (patchGroupsWithChanges.length === 0) {
    return (
      <div
        className={cn(
          "flex flex-col items-center justify-center py-8",
          className,
        )}
      >
        <div className="text-center text-muted-foreground">
          <Clock className="h-8 w-8 mx-auto mb-3 opacity-50" />
          <p className="text-base font-medium mb-1">暂无版本变更记录</p>
          <p className="text-sm">当前版本尚未进行任何修改</p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("flex flex-col w-full space-y-3", className)}>
      {/* 版本变更列表 - 时间轴样式 */}
      <div className="relative">
        {/* 时间轴线 */}
        <div className="absolute left-6 top-0 bottom-0 w-px bg-border"></div>

        <div className="space-y-3">
          {patchGroupsWithChanges.map((patchGroup, index) => (
            <div key={patchGroup.index} className="relative">
              {/* 时间轴节点 */}
              <div className="absolute left-5 top-3 w-2 h-2 bg-primary rounded-full border-2 border-background z-10"></div>

              {/* 内容区域 */}
              <div className="ml-12">
                <PatchGroupCard
                  patchGroup={patchGroup}
                  defaultOpen={index === 0} // 默认展开第一个版本
                />
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
