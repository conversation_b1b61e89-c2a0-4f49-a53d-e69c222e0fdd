import { ExtendedDialog } from "@/components/common/extended-dialog";
import { But<PERSON> } from "@/components/ui/button";
import { zodResolver } from "@hookform/resolvers/zod";
import { useAtomValue, useSetAtom } from "jotai";
import { Database, Upload } from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { draftVersionsAtom } from "../../atoms";
import {
  activeQueueIdAtom,
  canRetryAtom,
  hasDataToSyncAtom,
  queueProgressAtom,
  QueueStatus,
  quickSyncAtom,
  retryFailedTasksAtom,
  syncResultAtom,
} from "../../services";
import { SyncHistoryView } from "./sync-history-view";
import { SyncProgressView } from "./sync-progress-view";
import { SyncResultView } from "./sync-result-view";
import { VersionSyncDialogProps } from "./version-sync-dialog-types";
import { VersionSyncForm } from "./version-sync-form";
import {
  VersionSyncFormValues,
  versionSyncSchema,
} from "./version-sync-form-schema";

export function VersionSyncDialog({
  open,
  versionId,
  syncOptions = {},
  onOpenChange,
  onSyncComplete,
}: VersionSyncDialogProps) {
  const [showForm, setShowForm] = useState(true);

  // 同步状态
  const activeQueueId = useAtomValue(activeQueueIdAtom);
  const queueProgress = useAtomValue(queueProgressAtom);
  const syncResult = useAtomValue(syncResultAtom);
  const draftVersions = useAtomValue(draftVersionsAtom);
  const draftVersion = draftVersions.find(
    (version) => version.id === versionId,
  );

  // 数据检查
  const hasDataToSync = useAtomValue(hasDataToSyncAtom);

  // 操作
  const executeQuickSync = useSetAtom(quickSyncAtom);
  const retryFailedTasks = useSetAtom(retryFailedTasksAtom);

  // 计算同步状态
  const isSyncing = queueProgress?.status === QueueStatus.RUNNING;

  // 获取重试检查函数
  const canRetryFn = useAtomValue(canRetryAtom);
  const canRetry = activeQueueId ? canRetryFn(activeQueueId) : false;

  // 检查是否有现有的队列状态
  const hasExistingQueue = activeQueueId !== null;
  const hasFailedQueue = syncResult && !syncResult.success;
  const hasRunningQueue = queueProgress?.status === QueueStatus.RUNNING;
  const hasCompletedQueue = syncResult?.success;

  // 初始显示状态
  const shouldShowProgress =
    hasExistingQueue || hasRunningQueue || hasFailedQueue || hasCompletedQueue;

  // 表单
  const form = useForm<VersionSyncFormValues>({
    resolver: zodResolver(versionSyncSchema),
    defaultValues: {
      versionName: draftVersion?.versionName ?? "",
      remark: "",
    },
  });

  // 检查是否有数据可同步
  const hasSyncData = hasDataToSync(versionId);

  // 关闭对话框
  const handleClose = useCallback(() => {
    if (!isSyncing) {
      onOpenChange(false);
      setShowForm(true);
      form.reset();
    }
  }, [isSyncing, onOpenChange, form]);

  // 开始同步
  const handleStartSync = useCallback(async () => {
    const isValid = await form.trigger();

    if (!isValid) {
      return;
    }

    const formValues = form.getValues();

    setShowForm(false);

    try {
      const result = await executeQuickSync(versionId, syncOptions, {
        remark: formValues.remark,
        versionName: formValues.versionName,
      });

      if (result.success && result.syncedVersionId) {
        onSyncComplete?.(true, result.syncedVersionId);
        // 关闭对话框
        handleClose();
      } else {
        onSyncComplete?.(false);
      }
    } catch (error) {
      console.error("同步失败:", error);
      onSyncComplete?.(false);
    }
  }, [
    form,
    executeQuickSync,
    versionId,
    syncOptions,
    onSyncComplete,
    handleClose,
  ]);

  // 重试失败的任务
  const handleRetryTasks = useCallback(async () => {
    if (activeQueueId) {
      try {
        const result = await retryFailedTasks(activeQueueId);
        if (result.success && result.syncedVersionId) {
          onSyncComplete?.(true, result.syncedVersionId);
        } else {
          onSyncComplete?.(false);
        }

        onOpenChange(false);
      } catch (error) {
        console.error("重试失败:", error);
        onSyncComplete?.(false);
      }
    }
  }, [activeQueueId, retryFailedTasks, onOpenChange, onSyncComplete]);

  // 重置表单和智能状态管理
  useEffect(() => {
    if (open) {
      form.reset({
        versionName: draftVersion?.versionName ?? "",
        remark: "",
      });

      if (shouldShowProgress) {
        setShowForm(false);
      } else {
        setShowForm(true);
      }
    }
  }, [open, form, shouldShowProgress, draftVersion?.versionName]);

  return (
    <ExtendedDialog
      open={open}
      onClose={handleClose}
      title="保存版本"
      description="保存版本"
      width="60%"
      fullHeight={false}
      footer={
        showForm ? (
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Database className="h-4 w-4" />
              <span>{hasSyncData ? "有数据可同步" : "无数据可同步"}</span>
            </div>
            <div className="flex gap-3">
              <Button variant="outline" onClick={handleClose}>
                取消
              </Button>
              <Button
                onClick={handleStartSync}
                disabled={!hasSyncData}
                className="min-w-[100px]"
              >
                <Upload className="mr-2 h-4 w-4" />
                开始同步
              </Button>
            </div>
          </div>
        ) : (
          <div className="flex gap-3">
            {syncResult && !syncResult.success && (
              <>
                {canRetry ? (
                  <Button variant="outline" onClick={handleRetryTasks}>
                    重试失败任务
                  </Button>
                ) : (
                  <Button variant="outline" onClick={() => setShowForm(true)}>
                    重新同步
                  </Button>
                )}
              </>
            )}
            <Button onClick={handleClose} disabled={isSyncing}>
              {isSyncing ? "同步中..." : "关闭"}
            </Button>
          </div>
        )
      }
    >
      <div className="flex flex-col h-full overflow-auto">
        {showForm ? (
          <VersionSyncForm versionId={versionId} form={form} />
        ) : (
          <div className="space-y-4 p-4">
            <SyncProgressView />
            <SyncResultView />
            <SyncHistoryView />
          </div>
        )}
      </div>
    </ExtendedDialog>
  );
}
