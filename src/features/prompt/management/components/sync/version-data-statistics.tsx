import { Label } from "@/components/ui/label";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { cn } from "@/lib/utils";
import { useAtomValue } from "jotai";
import { useEffect, useState } from "react";
import { getDraftDataByVersionAtom } from "../../services";
import {
  buildComparisonFromChangeContent,
  buildHistoryComparisonFromChangeContent,
  ChangeItem,
  compareDraftWithPublished,
  generateTabConfigs,
  VersionChangeContent,
  VersionDataComparison,
  VersionHistoryComparison,
} from "../../utils";
import { DiffViewerFactory } from "./diff-viewers";
import { ChangeItemView, StatisticsHeader } from "./shared";
import { VersionComparisonEmptyState } from "./version-comparison-empty-state";
import { VersionHistoryView } from "./version-history-view";

interface VersionDataStatisticsProps {
  /**
   * 草稿版本ID（当使用草稿数据时必填）
   */
  draftVersionId?: string;
  /**
   * 自定义样式类名
   */
  className?: string;
  /**
   * 变更内容
   */
  changeContent?: VersionChangeContent;
  /**
   * 直接传入的对比数据（用于版本对比场景）
   */
  comparison?: VersionDataComparison | null;
  /**
   * 是否正在加载对比数据（用于区分加载中和未选择状态）
   */
  isLoadingComparison?: boolean;
  /**
   * 展示模式
   * - "summary": 汇总模式，显示最终的变更结果（默认）
   * - "history": 历史模式，显示每个版本的变更记录
   */
  mode?: "summary" | "history";
}

export function VersionDataStatistics({
  draftVersionId,
  className,
  changeContent,
  comparison: externalComparison,
  isLoadingComparison = false,
  mode = "summary",
}: VersionDataStatisticsProps) {
  const [comparison, setComparison] = useState<VersionDataComparison | null>(
    null,
  );
  const [historyComparison, setHistoryComparison] =
    useState<VersionHistoryComparison | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 获取草稿数据
  const getDraftData = useAtomValue(getDraftDataByVersionAtom);

  // 加载数据并进行对比
  useEffect(() => {
    const loadAndCompare = async () => {
      try {
        setLoading(true);
        setError(null);

        // 如果直接提供了对比数据，直接使用
        if (externalComparison !== undefined) {
          setComparison(externalComparison);
          setLoading(false);
          return;
        }

        // 如果提供了 changeContent，根据模式构建对比结果
        if (changeContent) {
          try {
            if (mode === "history") {
              // 历史模式：构建版本历史对比结果
              const historyResult =
                buildHistoryComparisonFromChangeContent(changeContent);

              setHistoryComparison(historyResult);
              // 清空汇总对比结果
              setComparison(null);
            } else {
              // 汇总模式：构建汇总对比结果
              const comparisonResult =
                buildComparisonFromChangeContent(changeContent);

              setComparison(comparisonResult);
              // 清空历史对比结果
              setHistoryComparison(null);
            }

            return;
          } catch (err) {
            console.error("从变更内容构建对比结果失败:", err);
            setError("从变更内容构建对比结果失败");
            return;
          }
        }

        // 如果没有提供 draftVersionId，无法进行对比
        if (!draftVersionId) {
          return;
        }

        // 获取草稿数据
        const rawDraftData = await getDraftData(draftVersionId);

        if (!rawDraftData) {
          setError("草稿数据不存在");

          return;
        }

        // 使用公共函数进行数据对比
        const comparisonResult = await compareDraftWithPublished(
          rawDraftData,
          rawDraftData.draftVersion.baseVersionId,
        );

        setComparison(comparisonResult);
      } catch (err) {
        console.error("加载版本数据失败:", err);
        setError("加载版本数据失败");
      } finally {
        setLoading(false);
      }
    };

    loadAndCompare();
  }, [draftVersionId, getDraftData, changeContent, externalComparison, mode]);

  if (loading) {
    return (
      <div className={cn("w-full", className)}>
        <VersionComparisonEmptyState type="loading" />
      </div>
    );
  }

  if (error) {
    return (
      <div className={cn("w-full", className)}>
        <VersionComparisonEmptyState type="error" description={error} />
      </div>
    );
  }

  // 根据模式检查数据是否存在
  const hasData = mode === "history" ? historyComparison : comparison;

  if (!hasData) {
    // 如果外部传入了 isLoadingComparison=true，说明正在异步加载，显示加载状态
    if (isLoadingComparison) {
      return (
        <div className={cn("w-full", className)}>
          <VersionComparisonEmptyState type="loading" />
        </div>
      );
    }

    // 否则显示未选择状态
    return (
      <div className={cn("w-full", className)}>
        <VersionComparisonEmptyState type="no-selection" />
      </div>
    );
  }

  // 根据模式渲染不同的内容
  if (mode === "history") {
    // 历史模式：显示版本历史
    if (!historyComparison) {
      return (
        <div className={cn("w-full", className)}>
          <VersionComparisonEmptyState type="no-changes" />
        </div>
      );
    }

    return (
      <div className={cn("flex flex-col w-full overflow-hidden", className)}>
        <StatisticsHeader
          title="版本变更历史"
          totalChanges={historyComparison.totalSummary.total}
        />
        <div className="flex-1 min-h-0 overflow-auto">
          <VersionHistoryView
            historyComparison={historyComparison}
            className="p-3"
          />
        </div>
      </div>
    );
  } else {
    // 汇总模式：显示汇总对比结果
    if (!comparison) {
      return (
        <div className={cn("w-full", className)}>
          <VersionComparisonEmptyState type="no-changes" />
        </div>
      );
    }

    // 生成标签页配置
    const availableTabs = generateTabConfigs(comparison);

    // 如果没有变更，显示空状态
    if (availableTabs.length === 0) {
      return (
        <div className={cn("w-full", className)}>
          <VersionComparisonEmptyState type="no-changes" />
        </div>
      );
    }

    // 渲染变更项列表
    const renderChangeItems = (items: ChangeItem[]) => (
      <div className="divide-y border border-border rounded-lg overflow-hidden">
        {items.map((change) => {
          return (
            <ChangeItemView key={change.id} change={change}>
              <DiffViewerFactory change={change} />
            </ChangeItemView>
          );
        })}
      </div>
    );

    return (
      <div className={cn("flex flex-col w-full overflow-hidden", className)}>
        <StatisticsHeader
          title="版本变更统计"
          totalChanges={comparison.summary.total}
        />

        <Tabs
          className="w-full flex flex-col flex-1 min-h-0"
          defaultValue={availableTabs[0]?.key}
        >
          <TabsList className="w-full justify-start">
            {availableTabs.map((tab) => (
              <TabsTrigger key={tab.key} value={tab.key} className="text-sm">
                {tab.label}
                <Label className="px-1.5 py-0.5 ml-1" variant="secondary">
                  {tab.count}
                </Label>
              </TabsTrigger>
            ))}
          </TabsList>

          {availableTabs.map((tab) => (
            <TabsContent
              key={tab.key}
              value={tab.key}
              className="px-3 py-4 flex-1 min-h-64 overflow-auto"
            >
              {renderChangeItems(tab.items)}
            </TabsContent>
          ))}
        </Tabs>
      </div>
    );
  }
}
