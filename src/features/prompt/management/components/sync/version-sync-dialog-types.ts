import { SyncOptions } from "../../services/sync/types";

/**
 * 版本同步对话框属性
 */
export interface VersionSyncDialogProps {
  /**
   * 是否显示对话框
   */
  open: boolean;
  /**
   * 要同步的版本ID
   */
  versionId: string;
  /**
   * 同步选项
   */
  syncOptions?: SyncOptions;
  /**
   * 关闭对话框回调
   */
  onOpenChange: (open: boolean) => void;
  /**
   * 同步完成回调
   */
  onSyncComplete?: (success: boolean, syncedVersionId?: string) => void;
}
