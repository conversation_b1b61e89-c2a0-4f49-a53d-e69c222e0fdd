import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { DraftVersion } from "../../types";
import dayjs from "dayjs";

export interface DraftDeleteDialogProps {
  open: boolean;
  draftVersion: DraftVersion | null | undefined;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void;
}

export const DraftDeleteDialog: React.FC<DraftDeleteDialogProps> = ({
  open,
  draftVersion,
  onOpenChange,
  onConfirm,
}) => {
  const handleConfirm = () => {
    onConfirm();
    onOpenChange(false);
  };

  const formatDate = (timestamp: string) => {
    return dayjs.unix(+timestamp).format("YYYY-MM-DD HH:mm:ss");
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>确认删除草稿</AlertDialogTitle>
          <AlertDialogDescription asChild>
            <div className="space-y-2">
              <p>确定要删除以下草稿版本吗？此操作不可撤销。</p>
              {draftVersion && (
                <div className="bg-muted p-3 rounded-md space-y-1">
                  <div className="font-medium text-foreground">
                    {draftVersion.versionName}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    创建时间：{formatDate(draftVersion.createdAt)}
                  </div>
                  {draftVersion.draftName && (
                    <div className="text-sm text-muted-foreground">
                      草稿名称：{draftVersion.draftName}
                    </div>
                  )}
                </div>
              )}
              <p className="text-sm text-destructive">
                删除后，该草稿版本的所有数据（包括能力、模块、共享变量等）都将被永久删除。
              </p>
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>取消</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            确认删除
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};
