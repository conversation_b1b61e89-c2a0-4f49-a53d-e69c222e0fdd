import { But<PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useAtomValue, useSetAtom } from "jotai";
import { useCallback, useState } from "react";
import {
  hasVersionsAtom,
  selectedAbility<PERSON>tom,
  selectedVersionIdAtom,
  updateAbilityContentAtom,
} from "../../atoms";
import { useEditPermission } from "../../hooks";
import { UniversalModule } from "../../types";
import {
  addModuleToAbilityItem,
  reorderModulesInAbilityItem,
} from "../../utils";
import { AbilityEditor } from "../ability-editor";
import { ModuleForm } from "../module-form";
import { ReadOnlyNotice } from "../read-only-notice";
import { VersionEmpty } from "../version-empty";

export const AbilityContent = () => {
  const selectedVersionId = useAtomValue(selectedVersionIdAtom);
  const selectedAbilityId = useAtomValue(selectedAbilityAtom);
  const hasVersions = useAtomValue(hasVersionsAtom);
  const updateAbilityContent = useSetAtom(updateAbilityContentAtom);
  const { canEdit } = useEditPermission();

  const [isFormDrawerOpen, setIsFormDrawerOpen] = useState(false);

  // 处理模块选择
  const handleModuleSelect = useCallback(
    (abilityItemType: string, module: UniversalModule) => {
      if (!selectedAbilityId) {
        return;
      }

      try {
        // 向能力项中添加模块引用
        const updatedContent = addModuleToAbilityItem(
          selectedAbilityId.content,
          abilityItemType,
          module,
        );

        // 更新能力数据
        updateAbilityContent(selectedAbilityId.id, updatedContent);
      } catch (error) {
        console.error("添加模块失败:", error);
      }
    },
    [selectedAbilityId, updateAbilityContent],
  );

  // 处理模块排序
  const handleModuleReorder = useCallback(
    (abilityItemType: string, activeIndex: number, overIndex: number) => {
      if (!selectedAbilityId) {
        return;
      }

      try {
        // 更新能力数据
        // @see https://github.com/ueberdosis/tiptap/issues/3764
        setTimeout(() => {
          // 重新排序能力项中的模块
          const updatedContent = reorderModulesInAbilityItem(
            selectedAbilityId.content,
            abilityItemType,
            activeIndex,
            overIndex,
          );
          updateAbilityContent(selectedAbilityId.id, updatedContent);
        });
      } catch (error) {
        console.error("模块排序失败:", error);
      }
    },
    [selectedAbilityId, updateAbilityContent],
  );

  // 当有选中的版本和能力时显示编辑器
  const shouldShowEditor =
    hasVersions && selectedVersionId && selectedAbilityId;

  return (
    <>
      <div className="h-full overflow-hidden flex flex-col bg-white">
        {shouldShowEditor ? (
          <ScrollArea className="h-full">
            <div className="flex flex-col p-4 space-y-4">
              {/* 只读状态提示 */}
              {!canEdit && <ReadOnlyNotice />}

              <AbilityEditor
                promptVersionId={selectedVersionId}
                content={selectedAbilityId.content}
                editable={canEdit}
                abilityId={selectedAbilityId.id}
                extra={
                  <div className="flex justify-end">
                    <Button
                      size="sm"
                      variant="outline"
                      disabled={!canEdit}
                      onClick={() => setIsFormDrawerOpen(true)}
                    >
                      创建模块
                    </Button>
                  </div>
                }
                onModuleSelect={handleModuleSelect}
                onModuleReorder={handleModuleReorder}
              />
            </div>
          </ScrollArea>
        ) : (
          <div className="p-4">
            <VersionEmpty
              className="mt-[10%]"
              hasVersions={hasVersions}
              hasSelectedVersion={!!selectedVersionId}
              hasSelectedAbility={!!selectedAbilityId}
              title={{
                noVersions: "创建第一个版本",
                noSelection: "选择一个版本开始编辑",
                noAbility: "选择一个能力开始编辑",
              }}
              description={{
                noVersions: "您需要先创建一个提示版本才能开始编辑。",
                noSelection: "请从左侧面板选择一个版本。",
                noAbility: "请从左侧面板选择一个能力进行编辑。",
              }}
            />
          </div>
        )}
      </div>

      <ModuleForm
        open={isFormDrawerOpen}
        module={null}
        onOpenChange={setIsFormDrawerOpen}
      />
    </>
  );
};
