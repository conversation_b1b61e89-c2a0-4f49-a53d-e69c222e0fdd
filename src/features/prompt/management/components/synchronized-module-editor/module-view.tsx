import { ScopeBadge } from "@/components/common/prompt";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { SortableItemHandle } from "@/components/ui/sortable";
import { cn } from "@/lib/utils";
import { Level } from "@/types/api/prompt";
import { Edit, GripVertical, MoreHorizontal, Trash2 } from "lucide-react";
import { useState } from "react";
import { UniversalModuleLabel } from "../../types";
import { ModuleLabelsView } from "../module-labels-view";

export interface ModuleViewProps {
  name: string;
  editable: boolean;
  level?: Level | null;
  scopeDetail?: string | null;
  moduleLabelIds?: string | null;
  moduleLabels?: UniversalModuleLabel[];
  children: React.ReactNode;
  onEditName?: (newName: string) => void;
  onRemove?: () => void;
}

export const ModuleView = ({
  name,
  editable,
  level,
  scopeDetail,
  moduleLabelIds,
  moduleLabels,
  children,
  onEditName,
  onRemove,
}: ModuleViewProps) => {
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [editingName, setEditingName] = useState(name);

  const handleEditSubmit = () => {
    if (editingName.trim() && onEditName) {
      onEditName(editingName.trim());
      setIsEditDialogOpen(false);
    }
  };

  const handleDeleteConfirm = () => {
    if (onRemove) {
      onRemove();
      setIsDeleteDialogOpen(false);
    }
  };

  return (
    <>
      <div className="p-1 bg-gray-100 rounded-lg">
        <div
          className={cn(
            "border border-gray-200 rounded-lg bg-background shadow-xs",
            "focus-within:border-ring focus-within:ring-ring/50 focus-within:ring-[3px]",
          )}
        >
          <div className="flex items-center justify-between px-3 py-2 border-b border-border">
            <div className="flex items-center gap-2">
              {editable && (
                <SortableItemHandle>
                  <GripVertical className="h-4 w-4 text-gray-400" />
                </SortableItemHandle>
              )}
              <span className="text-sm font-normal">{name}</span>
              {level && <ScopeBadge level={level} scopeDetail={scopeDetail} />}
              {moduleLabelIds && (
                <ModuleLabelsView
                  moduleLabelIds={moduleLabelIds}
                  moduleLabels={moduleLabels}
                  showPlaceholder={false}
                />
              )}
            </div>

            <div className="flex items-center gap-2">
              {editable && (onEditName || onRemove) && (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="icon"
                      size="sm"
                      icon={<MoreHorizontal />}
                    />
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    {onEditName && (
                      <DropdownMenuItem
                        onClick={() => {
                          setEditingName(name);
                          setIsEditDialogOpen(true);
                        }}
                      >
                        <Edit className="h-4 w-4 mr-2" />
                        修改名称
                      </DropdownMenuItem>
                    )}
                    {onRemove && (
                      <DropdownMenuItem
                        onClick={() => setIsDeleteDialogOpen(true)}
                        className="text-destructive focus:text-destructive"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        移除模块
                      </DropdownMenuItem>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              )}
            </div>
          </div>

          {children}
        </div>
      </div>

      {/* 编辑名称对话框 */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>修改模块名称</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <Input
              value={editingName}
              onChange={(e) => setEditingName(e.target.value)}
              placeholder="请输入模块名称"
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  handleEditSubmit();
                }
              }}
            />
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsEditDialogOpen(false)}
            >
              取消
            </Button>
            <Button onClick={handleEditSubmit} disabled={!editingName.trim()}>
              确认
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 删除确认对话框 */}
      <AlertDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认移除</AlertDialogTitle>
            <AlertDialogDescription>
              确定要移除模块 <strong>"{name}"</strong>{" "}
              吗？移除后可以通过添加模块重新添加。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              确认移除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};
