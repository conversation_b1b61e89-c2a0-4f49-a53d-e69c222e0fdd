import { AbilityItemType } from "@/types/api/prompt";
import { Editor, JSONContent } from "@tiptap/core";
import { useAtomValue, useSet<PERSON>tom } from "jotai";
import { nanoid } from "nanoid";
import { use, useCallback, useMemo, useRef } from "react";
import { toast } from "sonner";
import {
  moduleLabelsAtom,
  modulesAtom,
  removeModuleFromAbilityItemAtom,
  updateModuleAtom,
  updateModulePromptAtom,
} from "../../atoms";
import { useModuleContentSync } from "../../hooks";
import { PromptNodeType } from "../../schemas/node";
import { createEditorModuleContent } from "../../utils";
import { ModuleEditorWithVariables } from "../editors";
import { ModuleView } from "./module-view";

export interface SynchronizedModuleEditorProps {
  /** 版本ID */
  promptVersionId: string;
  /** 能力ID */
  abilityId: string;
  /** 能力项类型 */
  abilityItemType: AbilityItemType;
  /** 模块ID */
  moduleId: string;
  /** 是否可编辑 */
  editable?: boolean;
}

export const SynchronizedModuleEditor = ({
  promptVersionId,
  abilityId,
  abilityItemType,
  moduleId,
  editable = true,
}: SynchronizedModuleEditorProps) => {
  // 获取模块数据
  const modulesQuery = useAtomValue(modulesAtom);
  const moduleLabelsQuery = useAtomValue(moduleLabelsAtom);

  const editorRef = useRef<Editor>(null);
  const componentIdRef = useRef(
    `synchronized-module-editor-${moduleId}-${nanoid()}`,
  );

  const updateModulePrompt = useSetAtom(updateModulePromptAtom);
  const updateModule = useSetAtom(updateModuleAtom);
  const removeModuleFromAbility = useSetAtom(removeModuleFromAbilityItemAtom);

  const modules = use(modulesQuery.promise);
  const module = modules?.find((module) => module.id === moduleId);
  const moduleLabels = use(moduleLabelsQuery.promise);

  // 用于标识是否正在处理同步事件，避免循环更新
  const isProcessingSyncEvent = useRef(false);

  // 解析模块内容为编辑器格式
  const editorContent = useMemo(
    () => createEditorModuleContent(module?.prompt || ""),
    [module?.prompt],
  );

  // 处理同步事件接收
  const handleSyncContentChange = useCallback(
    (content: PromptNodeType[]) => {
      if (!module || isProcessingSyncEvent.current) {
        return;
      }

      // 标记正在处理同步事件
      isProcessingSyncEvent.current = true;

      try {
        setTimeout(() => {
          editorRef.current?.commands?.setContent?.(content);
        });
      } finally {
        isProcessingSyncEvent.current = false;
      }
    },
    [module],
  );

  // 初始化内容同步
  const { publishContentChange } = useModuleContentSync({
    moduleId,
    componentId: componentIdRef.current,
    onContentChange: handleSyncContentChange,
  });

  // 处理内容变化
  const handleContentChange = useCallback(
    (_content: JSONContent) => {
      if (!module || isProcessingSyncEvent.current) {
        return;
      }

      const { content } = _content as {
        type: "doc";
        content: PromptNodeType[];
      };

      // 更新本地状态
      updateModulePrompt(module.id, content);

      // 发布同步事件
      publishContentChange(content);
    },
    [module, updateModulePrompt, publishContentChange],
  );

  // 处理编辑模块名称
  const handleEditName = useCallback(
    (newName: string) => {
      if (!module) {
        return;
      }

      updateModule(module.id, { name: newName });
      toast.success("模块名称修改成功");
    },
    [module, updateModule],
  );

  // 处理移除模块
  const handleRemove = useCallback(() => {
    if (!module) {
      return;
    }

    removeModuleFromAbility(abilityId, abilityItemType, module.id);
  }, [abilityId, abilityItemType, module, removeModuleFromAbility]);

  // 如果模块不存在，显示占位符
  if (!module) {
    return (
      <div className={"border border-dashed border-gray-300 rounded-lg p-4"}>
        <div className="text-center text-gray-500">
          模块不存在 (ID: {moduleId})
        </div>
      </div>
    );
  }

  return (
    <div id={`module-${moduleId}`}>
      <ModuleView
        editable={editable}
        name={module.name}
        level={module.level}
        scopeDetail={module.scopeDetail}
        moduleLabelIds={module.moduleLabelIds}
        moduleLabels={moduleLabels}
        onEditName={editable ? handleEditName : undefined}
        onRemove={editable ? handleRemove : undefined}
      >
        <ModuleEditorWithVariables
          key={`${promptVersionId}-${abilityId}-${moduleId}-editor`}
          editable={editable}
          level={module.level}
          scopeDetail={module.scopeDetail}
          className="border-0 bg-transparent focus-within:ring-0 pointer-events-auto"
          content={editorContent}
          ref={editorRef}
          onChange={handleContentChange}
        />
      </ModuleView>
    </div>
  );
};
