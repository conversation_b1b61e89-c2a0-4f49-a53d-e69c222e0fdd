import { PromptVersionSelect } from "@/components/common/version-select/prompt-version/prompt-version-select";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";
import { useAtomValue, useSetAtom } from "jotai";
import { useCallback, useEffect } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";
import { selectedVersionIdAtom } from "../../atoms";
import { createDraftFromVersionAtom } from "../../atoms/mutations";
import { draftVersionsAtom, versionsAtom } from "../../atoms/queries";
import { isDraftId } from "../../utils";

// 定义表单验证模式
const draftCreateSchema = z.object({
  sourceVersionId: z.string().min(1, { message: "请选择源版本" }),
  draftName: z
    .string()
    .min(1, { message: "草稿名称不能为空" })
    .max(50, { message: "草稿名称不能超过50个字符" })
    .regex(/^[a-zA-Z0-9\u4e00-\u9fa5\s\-_.]+$/, {
      message: "草稿名称只能包含中英文、数字、空格和常用符号（-_.）",
    }),
});

type DraftCreateFormValues = z.infer<typeof draftCreateSchema>;

/**
 * 检查草稿名称是否重复
 */
function checkDraftNameDuplication(
  draftName: string,
  draftVersions: Array<{ draftName?: string; id: string }>,
  excludeId?: string,
): boolean {
  return draftVersions.some(
    (draft) => draft.draftName === draftName && draft.id !== excludeId,
  );
}

export interface DraftCreateDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const DraftCreateDialog: React.FC<DraftCreateDialogProps> = ({
  open,
  onOpenChange,
}) => {
  const defaultSelectedVersionId = useAtomValue(selectedVersionIdAtom);
  const versionList = useAtomValue(versionsAtom);
  const draftVersions = useAtomValue(draftVersionsAtom);
  const createDraftFromVersion = useSetAtom(createDraftFromVersionAtom);

  // 创建带有草稿名称重复检查的验证模式
  const draftCreateSchemaWithDuplicationCheck = draftCreateSchema.refine(
    (data) => {
      return !checkDraftNameDuplication(data.draftName, draftVersions);
    },
    {
      message: "草稿名称已存在",
      path: ["draftName"],
    },
  );

  // 初始化表单
  const form = useForm<DraftCreateFormValues>({
    resolver: zodResolver(draftCreateSchemaWithDuplicationCheck),
    mode: "onChange",
    defaultValues: {
      sourceVersionId: "",
      draftName: "",
    },
  });

  const { mutateAsync: createDraft, isPending: isCreating } = useMutation({
    mutationFn: async (values: DraftCreateFormValues) => {
      return await createDraftFromVersion(
        values.sourceVersionId,
        values.draftName.trim(),
      );
    },
    onSuccess: () => {
      toast.success("草稿版本创建成功");
      onOpenChange(false);
      form.reset();
    },
  });

  // 提交表单
  const handleSubmit = useCallback(async () => {
    const isValid = await form.trigger();
    if (!isValid) {
      return;
    }

    const values = form.getValues();
    await createDraft(values);
  }, [form, createDraft]);

  // 关闭对话框
  const handleClose = useCallback(() => {
    onOpenChange(false);
  }, [onOpenChange]);

  // 取消操作
  const handleCancel = useCallback(() => {
    handleClose();
  }, [handleClose]);

  useEffect(() => {
    if (open) {
      // 默认选中第一个版本
      const defaultVersionId =
        defaultSelectedVersionId && !isDraftId(defaultSelectedVersionId)
          ? defaultSelectedVersionId
          : versionList.data?.[0]?.promptVersionId;

      form.reset({
        sourceVersionId: defaultVersionId,
        draftName: "",
      });
    }
  }, [open, form, defaultSelectedVersionId, versionList.data]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>创建草稿版本</DialogTitle>
          <DialogDescription>
            基于现有版本创建一个新的草稿版本。
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <div className="space-y-4">
            <FormField
              control={form.control}
              name="sourceVersionId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>源版本</FormLabel>
                  <FormControl>
                    <PromptVersionSelect
                      value={field.value}
                      versions={versionList.data}
                      draftVersions={[]} // 过滤掉草稿版本
                      onChange={field.onChange}
                      className="w-full"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="draftName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>草稿名称</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="输入草稿名称"
                      {...field}
                      onKeyDown={(e) => {
                        if (e.key === "Enter" && !isCreating) {
                          handleSubmit();
                        }
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                variant="outline"
                onClick={handleCancel}
                disabled={isCreating}
              >
                取消
              </Button>
              <Button onClick={handleSubmit} disabled={isCreating}>
                {isCreating ? "创建中..." : "创建草稿"}
              </Button>
            </DialogFooter>
          </div>
        </Form>
      </DialogContent>
    </Dialog>
  );
};
