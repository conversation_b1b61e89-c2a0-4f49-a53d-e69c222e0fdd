import { PromptEditorSkeleton } from "@/components/common/prompt-editor";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  Sortable,
  SortableContent,
  SortableItem,
  SortableOverlay,
} from "@/components/ui/sortable";
import { AbilityItemTypeLabels } from "@/constants/prompt";
import { cn } from "@/lib/utils";
import { AbilityItemType } from "@/types/api/prompt";
import { ChevronRight } from "lucide-react";
import { Suspense, useMemo, useState } from "react";
import { AbilityItemNodeType } from "../../schemas/node";
import { UniversalModule } from "../../types";
import { SynchronizedModuleEditor } from "../synchronized-module-editor";
import { ModuleSelector } from "./module-selector";

export interface AbilityItemViewProps {
  type: AbilityItemType;
  editable: boolean;
  promptVersionId: string;
  abilityItem: AbilityItemNodeType;
  abilityId: string;
  onModuleSelect: (abilityItemType: string, module: UniversalModule) => void;
  onModuleReorder: (
    abilityItemType: string,
    activeIndex: number,
    overIndex: number,
  ) => void;
}

export const AbilityItemView: React.FC<AbilityItemViewProps> = ({
  type,
  promptVersionId,
  editable,
  abilityItem,
  abilityId,
  onModuleSelect,
  onModuleReorder,
}) => {
  const [isExpanded, setIsExpanded] = useState(true);

  // 计算当前能力项中已添加的模块ID列表
  const addedModuleIds = useMemo(
    () => abilityItem.content.map((module) => module.attrs.id),
    [abilityItem.content],
  );

  // 处理模块排序
  const handleModuleMove = (event: any) => {
    const { activeIndex, overIndex } = event;

    onModuleReorder(abilityItem.attrs.type, activeIndex, overIndex);
  };

  return (
    <div className="flex flex-col gap-2">
      <div
        className="flex items-center justify-between pb-2 border-b border-border"
        id={abilityId ? `ability-${abilityId}-item-${type}` : undefined}
      >
        <div className="flex items-center gap-2">
          <span className="text-sm text-primary bg-primary/10 px-2 py-1 rounded-lg">
            {AbilityItemTypeLabels[type]}
          </span>
          <Label variant="secondary">{abilityItem.content.length}</Label>
          <Button
            variant="icon"
            icon={
              <ChevronRight
                className={cn(
                  "h-4 w-4 text-gray-500 transition-transform duration-200",
                  {
                    "rotate-90": isExpanded,
                  },
                )}
              />
            }
            onClick={() => setIsExpanded(!isExpanded)}
          />
        </div>

        <ModuleSelector
          disabled={!editable}
          excludedModuleIds={addedModuleIds}
          onSelect={(module) => {
            onModuleSelect(abilityItem.attrs.type, module);
          }}
        />
      </div>

      {isExpanded && (
        <div className="p-1">
          {abilityItem.content.length > 0 ? (
            <Sortable
              value={abilityItem.content}
              getItemValue={(module) => module.attrs.id}
              orientation="vertical"
              onMove={handleModuleMove}
            >
              <SortableContent className="space-y-3">
                {abilityItem.content.map((module) => (
                  <SortableItem
                    key={module.attrs.id}
                    value={module.attrs.id}
                    disabled={!editable}
                    className={cn({
                      "opacity-100": !editable,
                    })}
                  >
                    <Suspense
                      fallback={<PromptEditorSkeleton editable={editable} />}
                    >
                      <SynchronizedModuleEditor
                        promptVersionId={promptVersionId}
                        abilityId={abilityId}
                        abilityItemType={abilityItem.attrs.type}
                        moduleId={module.attrs.id}
                        editable={editable}
                      />
                    </Suspense>
                  </SortableItem>
                ))}
              </SortableContent>
              <SortableOverlay>
                {({ value }) => {
                  const module = abilityItem.content.find(
                    (m) => m.attrs.id === value,
                  );

                  return module ? (
                    <div className="bg-white border border-gray-200 rounded-lg shadow-lg">
                      <Suspense
                        fallback={<PromptEditorSkeleton editable={editable} />}
                      >
                        <SynchronizedModuleEditor
                          promptVersionId={promptVersionId}
                          abilityId={abilityId}
                          abilityItemType={abilityItem.attrs.type}
                          moduleId={module.attrs.id}
                          editable={false}
                        />
                      </Suspense>
                    </div>
                  ) : null;
                }}
              </SortableOverlay>
            </Sortable>
          ) : (
            <div className="border border-dashed border-gray-300 rounded-lg p-4 text-center text-gray-500 text-sm">
              {editable ? "请添加模块" : "暂无模块"}
            </div>
          )}
        </div>
      )}
    </div>
  );
};
