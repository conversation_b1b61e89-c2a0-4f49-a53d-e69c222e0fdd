import { useMemo } from "react";
import { parseAbilityContent } from "../../schemas/node";
import { UniversalModule } from "../../types";
import { AbilityItemView } from "./ability-item-view";

export interface AbilityEditorProps {
  promptVersionId: string;
  abilityId: string;
  content: string;
  editable?: boolean;
  className?: string;
  extra?: React.ReactNode;
  onModuleSelect: (abilityItemType: string, module: UniversalModule) => void;
  onModuleReorder: (
    abilityItemType: string,
    activeIndex: number,
    overIndex: number,
  ) => void;
}

export const AbilityEditor: React.FC<AbilityEditorProps> = ({
  promptVersionId,
  abilityId,
  content,
  editable = true,
  extra,
  onModuleSelect,
  onModuleReorder,
}) => {
  const abilityData = useMemo(() => {
    const parseResult = parseAbilityContent(content);

    if (!parseResult.success) {
      console.error("Failed to parse ability content:", parseResult.error);

      return null;
    }

    return parseResult.data;
  }, [content]);

  if (!abilityData) {
    return (
      <div className="border border-red-200 rounded-lg p-4">
        <div className="text-center text-red-600">无法解析能力内容</div>
      </div>
    );
  }

  const name = abilityData.attrs.name;

  return (
    <div className="flex flex-col gap-4 flex-1 min-h-0">
      <div
        className="flex items-center justify-between"
        id={abilityId ? `ability-${abilityId}` : undefined}
      >
        <h1 className="m-0 text-lg font-normal">{name}</h1>

        {extra}
      </div>

      <div className="space-y-4">
        {abilityData.content.map((abilityItem, itemIndex) => (
          <AbilityItemView
            promptVersionId={promptVersionId}
            key={`${abilityItem.attrs.type}-${itemIndex}`}
            type={abilityItem.attrs.type}
            abilityItem={abilityItem}
            editable={editable}
            abilityId={abilityId}
            onModuleSelect={onModuleSelect}
            onModuleReorder={onModuleReorder}
          />
        ))}
      </div>
    </div>
  );
};
