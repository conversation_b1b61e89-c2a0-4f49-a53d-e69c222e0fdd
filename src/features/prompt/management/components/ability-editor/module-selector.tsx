import { LevelBadge } from "@/components/common/prompt";
import { Tooltip } from "@/components/common/tooltip";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { useAtomValue } from "jotai";
import { Plus } from "lucide-react";
import { useCallback, useMemo, useState } from "react";
import { moduleLabelsAtom, modulesAtom } from "../../atoms";
import { UniversalModule } from "../../types";
import { parseModuleLabelIds } from "../../utils";
import { ModuleLabelsView } from "../module-labels-view";

export interface ModuleSelectorProps {
  disabled?: boolean;
  excludedModuleIds?: string[];
  onSelect: (module: UniversalModule) => void;
}

export const ModuleSelector: React.FC<ModuleSelectorProps> = ({
  disabled = false,
  excludedModuleIds = [],
  onSelect,
}) => {
  const [open, setOpen] = useState(false);
  const modulesQuery = useAtomValue(modulesAtom);
  const moduleLabelsQuery = useAtomValue(moduleLabelsAtom);

  const modules = useMemo(() => modulesQuery.data || [], [modulesQuery.data]);
  const moduleLabels = useMemo(
    () => moduleLabelsQuery.data || [],
    [moduleLabelsQuery.data],
  );

  // 过滤掉已排除的模块
  const filteredModules = useMemo(
    () => modules.filter((module) => !excludedModuleIds.includes(module.id)),
    [modules, excludedModuleIds],
  );

  // 为每个模块生成搜索关键词，包括模块名称和标签名称
  const modulesWithSearchKeywords = useMemo(
    () =>
      filteredModules.map((module) => {
        const keywords = [module.name];

        if (module.moduleLabelIds) {
          const labelIds = parseModuleLabelIds(module.moduleLabelIds);
          const moduleLabelsNames = labelIds
            .map((id) => moduleLabels.find((label) => label.id === id)?.name)
            .filter(Boolean) as string[];

          keywords.push(...moduleLabelsNames);
        }

        return {
          ...module,
          searchKeywords: keywords.join(" "),
        };
      }),
    [filteredModules, moduleLabels],
  );

  const handleSelect = useCallback(
    (module: UniversalModule) => {
      onSelect(module);
      setOpen(false);
    },
    [onSelect],
  );

  const tooltipContent = disabled
    ? "当前不可编辑"
    : filteredModules.length === 0
      ? "暂无可用模块"
      : undefined;

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Tooltip content={tooltipContent} disabled={!tooltipContent}>
          <Button
            size="sm"
            variant="ghost"
            disabled={disabled}
            icon={<Plus />}
            onClick={(event) => {
              event.stopPropagation();
            }}
          >
            添加模块
          </Button>
        </Tooltip>
      </PopoverTrigger>
      <PopoverContent
        className="w-[460px] p-0"
        align="start"
        side="left"
        onClick={(event) => {
          event.stopPropagation();
        }}
      >
        <Command>
          <CommandInput placeholder="搜索模块..." />
          <CommandList className="max-h-[400px]">
            <CommandEmpty>
              {modulesQuery.isLoading
                ? "加载中..."
                : filteredModules.length === 0
                  ? "暂无可用模块"
                  : "未找到匹配的模块"}
            </CommandEmpty>
            <CommandGroup>
              {modulesWithSearchKeywords.map((module) => (
                <CommandItem
                  key={module.id}
                  value={module.searchKeywords}
                  onSelect={() => handleSelect(module)}
                >
                  <div className="flex flex-col w-full">
                    <div
                      className="flex w-full items-center justify-between gap-2 text-sm truncate"
                      title={module.name}
                    >
                      <Tooltip autoDetectOverflow={true} content={module.name}>
                        <span className="flex-1 min-w-0 truncate">
                          {module.name}
                        </span>
                      </Tooltip>

                      <div className="flex items-center gap-2">
                        <LevelBadge level={module.level} />
                      </div>
                    </div>
                    <ModuleLabelsView
                      moduleLabelIds={module.moduleLabelIds}
                      moduleLabels={moduleLabelsQuery.data}
                    />
                  </div>
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
};
