import { CategoryDropdownSelector } from "@/components/common/category-dropdown-selector";
import { ExtendedDialog } from "@/components/common/extended-dialog";
import { ShopMultiSelector } from "@/components/common/shop-multi-selector";
import { Button } from "@/components/ui/button";
import { DialogClose } from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { getLevelOptions } from "@/constants/prompt";
import { Level } from "@/types/api/prompt";
import { zodResolver } from "@hookform/resolvers/zod";
import { useAtomValue, useSetAtom } from "jotai";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useForm, useWatch } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";
import {
  createSharedVariableAtom,
  selectedVersionIdAtom,
  sharedVariablesAtom,
  shopVariablesAtom,
  systemVariablesAtom,
  updateSharedVariableAtom,
} from "../../atoms";
import {
  SharedVariableFormData,
  createSharedVariableFormSchema,
} from "../../schemas/shared-variable";
import { UniversalSharedVariable } from "../../types";

export interface SharedVariableFormDrawerProps {
  /** 要编辑的共享变量，null 表示创建新变量 */
  sharedVariable: UniversalSharedVariable | null;
  /** 抽屉是否打开 */
  open: boolean;
  /** 抽屉打开状态变化回调 */
  onOpenChange: (open: boolean) => void;
  /** 保存成功回调 */
  onSave?: () => void;
}

export const SharedVariableForm = ({
  sharedVariable,
  open,
  onOpenChange,
  onSave,
}: SharedVariableFormDrawerProps) => {
  const { t } = useTranslation();
  const selectedVersionId = useAtomValue(selectedVersionIdAtom);
  const createSharedVariable = useSetAtom(createSharedVariableAtom);
  const updateSharedVariable = useSetAtom(updateSharedVariableAtom);

  // 获取所有变量数据用于重复检查
  const { data: systemVariables = [] } = useAtomValue(systemVariablesAtom);
  const { data: shopVariables = [] } = useAtomValue(shopVariablesAtom);
  const { data: sharedVariables = [] } = useAtomValue(sharedVariablesAtom);

  // 创建带有重复检查的schema
  const validationSchema = useMemo(
    () =>
      createSharedVariableFormSchema(
        systemVariables,
        shopVariables,
        sharedVariables,
        sharedVariable?.id,
      ),
    [systemVariables, shopVariables, sharedVariables, sharedVariable?.id],
  );

  // 表单配置
  const form = useForm<SharedVariableFormData>({
    resolver: zodResolver(validationSchema),
    mode: "onChange",
    defaultValues: {
      name: "",
      promptName: "",
      level: Level.Product,
      definition: "",
      scopeDetail: [],
    },
  });

  // 是否为创建模式
  const isCreateMode = !sharedVariable;

  // 保存加载状态
  const [isSaving, setIsSaving] = useState(false);

  // 监听 level 字段变化
  const currentLevel = useWatch({
    control: form.control,
    name: "level",
  });

  // 处理保存
  const handleSave = useCallback(async () => {
    const isValid = await form.trigger();

    if (!isValid) {
      return;
    }

    const data = form.getValues();

    setIsSaving(true);

    if (isCreateMode) {
      // 创建新共享变量
      if (!selectedVersionId) {
        toast.error("请先选择一个版本");
        return;
      }

      const result = createSharedVariable(selectedVersionId, data);

      if (result) {
        toast.success("共享变量创建成功");
      }
    } else {
      // 更新现有共享变量
      const result = updateSharedVariable(sharedVariable!.id, data);

      if (result) {
        toast.success("共享变量保存成功");
      }
    }

    onSave?.();
    onOpenChange(false);
    setIsSaving(false);
  }, [
    form,
    isCreateMode,
    selectedVersionId,
    onSave,
    onOpenChange,
    createSharedVariable,
    updateSharedVariable,
    sharedVariable,
  ]);

  // 处理关闭
  const handleClose = useCallback(() => {
    onOpenChange(false);
  }, [onOpenChange]);

  // 解析 scopeDetail JSON 字符串为数组
  const parseScopeDetail = (scopeDetail?: string): string[] => {
    if (!scopeDetail) return [];

    try {
      const parsed = JSON.parse(scopeDetail);

      return Array.isArray(parsed) ? parsed : [];
    } catch {
      return [];
    }
  };

  // 当共享变量变化时更新表单数据
  useEffect(() => {
    if (sharedVariable) {
      form.reset({
        name: sharedVariable.name,
        promptName: sharedVariable.promptName,
        level: sharedVariable.level,
        definition: sharedVariable.definition || "",
        scopeDetail: parseScopeDetail(sharedVariable.scopeDetail),
      });
    } else {
      form.reset({
        name: "",
        promptName: "",
        level: Level.Product,
        definition: "",
        scopeDetail: [],
      });
    }
  }, [sharedVariable, form]);

  useEffect(() => {
    if (!open) {
      form.reset();
    }
  }, [form, open]);

  return (
    <ExtendedDialog
      title={isCreateMode ? "创建共享变量" : "编辑共享变量"}
      open={open}
      width="60%"
      modal={false}
      fullHeight={false}
      footer={
        <>
          <DialogClose asChild>
            <Button variant="outline" onClick={handleClose}>
              {t("common.cancel")}
            </Button>
          </DialogClose>
          <Button type="submit" onClick={handleSave} disabled={isSaving}>
            {isSaving
              ? "保存中..."
              : isCreateMode
                ? t("common.create")
                : t("common.save")}
          </Button>
        </>
      }
      onOpenChange={onOpenChange}
    >
      <Form {...form}>
        <div className="flex flex-col h-full p-6 overflow-auto">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem className="mb-4">
                <FormLabel>
                  变量名称
                  <span className="text-red-500 ml-1">*</span>
                </FormLabel>
                <FormControl>
                  <Input placeholder="请输入变量名称" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="promptName"
            render={({ field }) => (
              <FormItem className="mb-4">
                <FormLabel>
                  提示词变量名称
                  <span className="text-red-500 ml-1">*</span>
                </FormLabel>
                <FormControl>
                  <Input placeholder="请输入提示词变量名称" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="grid grid-cols-2 gap-4 mb-4">
            <FormField
              control={form.control}
              name="level"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    等级
                    <span className="text-red-500 ml-1">*</span>
                  </FormLabel>
                  <Select
                    disabled={!isCreateMode}
                    value={field.value}
                    onValueChange={(value) => {
                      field.onChange(value);
                      form.setValue("scopeDetail", []);
                    }}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="请选择等级" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {getLevelOptions().map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* 动态范围选择器 */}
            {(currentLevel === Level.Shop ||
              currentLevel === Level.Category) && (
              <FormField
                control={form.control}
                name="scopeDetail"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {currentLevel === Level.Shop ? "选择店铺" : "选择类目"}
                      <span className="text-red-500 ml-1">*</span>
                    </FormLabel>
                    <FormControl>
                      {currentLevel === Level.Shop ? (
                        <ShopMultiSelector
                          value={field.value || []}
                          disabled={!isCreateMode}
                          placeholder="请选择店铺"
                          triggerClassName="w-full"
                          onChange={field.onChange}
                        />
                      ) : (
                        <CategoryDropdownSelector
                          value={field.value || []}
                          disabled={!isCreateMode}
                          placeholder="请选择类目"
                          triggerClassName="w-full"
                          onChange={field.onChange}
                        />
                      )}
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}
          </div>

          <FormField
            control={form.control}
            name="definition"
            render={({ field }) => (
              <FormItem className="flex-1 flex flex-col">
                <FormLabel>变量定义</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="请输入变量定义"
                    className="flex-1 resize-none min-h-64"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </Form>
    </ExtendedDialog>
  );
};
