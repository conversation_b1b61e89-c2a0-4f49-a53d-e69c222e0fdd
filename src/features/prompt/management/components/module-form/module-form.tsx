import { CategoryDropdownSelector } from "@/components/common/category-dropdown-selector";
import { ExtendedDialog } from "@/components/common/extended-dialog";
import { LevelTip, ScopeTip } from "@/components/common/prompt";
import { SelectSearch } from "@/components/common/select-search";
import { ShopMultiSelector } from "@/components/common/shop-multi-selector";
import { Button } from "@/components/ui/button";
import { DialogClose } from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { getLevelOptions } from "@/constants/prompt";
import { Level } from "@/types/api/prompt";
import { safeParseJson } from "@/utils";
import { zodResolver } from "@hookform/resolvers/zod";
import { JSONContent } from "@tiptap/react";
import { useAtomValue, useSet<PERSON>tom } from "jotai";
import { use<PERSON>allback, useEffect, useMemo, useRef, useState } from "react";
import { useForm, useWatch } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";
import {
  createModuleAtom,
  moduleLabelsAtom,
  selectedModuleLabelIdAtom,
  selectedVersionIdAtom,
  updateModuleAtom,
} from "../../atoms";
import {
  ModuleFormData,
  moduleFormSchema,
  moduleLabelIdsSchema,
} from "../../schemas/module";
import {
  parseModuleNode,
  PromptNodeType,
  stringifyModuleNode,
} from "../../schemas/node";
import { UniversalModule } from "../../types";
import { createDefaultDoc, createDefaultPromptNode } from "../../utils";
import { ModuleEditorWithVariables } from "../editors";

export interface ModuleFormDrawerProps {
  /** 要编辑的模块，null 表示创建新模块 */
  module: UniversalModule | null;
  /** 抽屉是否打开 */
  open: boolean;
  /** 抽屉打开状态变化回调 */
  onOpenChange: (open: boolean) => void;
  /** 保存成功回调 */
  onSave?: () => void;
}

export const ModuleForm = ({
  module,
  open,
  onOpenChange,
  onSave,
}: ModuleFormDrawerProps) => {
  const { t } = useTranslation();
  const containerRef = useRef<HTMLDivElement>(null);

  const selectedVersionId = useAtomValue(selectedVersionIdAtom);
  const selectedModuleLabelId = useAtomValue(selectedModuleLabelIdAtom);
  const createModule = useSetAtom(createModuleAtom);
  const updateModule = useSetAtom(updateModuleAtom);
  const { data: moduleLabels } = useAtomValue(moduleLabelsAtom);

  // ModuleEditor 内容状态
  const [editorContent, setEditorContent] = useState<JSONContent>({
    type: "doc",
    content: [],
  });

  // 表单配置
  const form = useForm<ModuleFormData>({
    resolver: zodResolver(moduleFormSchema),
    defaultValues: {
      name: "",
      content: [],
      level: Level.Product,
      moduleLabelIds: [],
      scopeDetail: [],
    },
  });

  // 是否为创建模式
  const isCreateMode = !module;

  // 监听字段
  const currentLevel = useWatch({
    control: form.control,
    name: "level",
  });
  const currentScopeDetail = useWatch({
    control: form.control,
    name: "scopeDetail",
  });

  // 将模块标签转换为 SelectSearch 选项
  const labelOptions = useMemo(
    () =>
      (moduleLabels ?? []).map((label) => ({
        label: label.name,
        value: label.id,
      })),
    [moduleLabels],
  );

  // 解析 scopeDetail JSON 字符串为数组
  const parseScopeDetail = useCallback((scopeDetail?: string): string[] => {
    if (!scopeDetail) return [];

    try {
      const parsed = JSON.parse(scopeDetail);

      return Array.isArray(parsed) ? parsed : [];
    } catch {
      return [];
    }
  }, []);

  // 处理保存
  const handleSave = useCallback(async () => {
    const isValid = await form.trigger();

    if (!isValid) {
      return;
    }

    const data = form.getValues();

    try {
      // 将编辑器内容序列化为字符串
      const promptContent = stringifyModuleNode({
        type: "module",
        attrs: {
          name: data.name,
          level: data.level,
        },
        content: data.content,
      });
      // 将模块标签 ID 序列化为字符串
      const moduleLabelIdsString = JSON.stringify(data.moduleLabelIds);
      // 将 scopeDetail 数组转换为 JSON 字符串
      const scopeDetailString =
        data.scopeDetail && data.scopeDetail.length > 0
          ? JSON.stringify(data.scopeDetail)
          : undefined;

      if (isCreateMode) {
        // 创建新模块
        if (!selectedVersionId) {
          toast.error("请先选择一个版本");
          return;
        }

        const result = await createModule(selectedVersionId, {
          name: data.name.trim(),
          prompt: promptContent,
          level: data.level,
          moduleLabelIds: moduleLabelIdsString,
          scopeDetail: scopeDetailString,
        });

        if (result) {
          toast.success("模块创建成功");
        }
      } else {
        // 更新现有模块
        const result = await updateModule(module!.id, {
          name: data.name.trim(),
          prompt: promptContent,
          level: data.level,
          moduleLabelIds: moduleLabelIdsString,
          scopeDetail: scopeDetailString,
        });

        if (result) {
          toast.success("模块保存成功");
        }
      }

      onSave?.();
      onOpenChange(false);
    } catch (error) {
      console.error("保存模块失败:", error);
    }
  }, [
    form,
    isCreateMode,
    selectedVersionId,
    onSave,
    onOpenChange,
    createModule,
    updateModule,
    module,
  ]);

  // 处理关闭
  const handleClose = useCallback(() => {
    onOpenChange(false);
  }, [onOpenChange]);

  const containerDomGetter = useCallback(() => containerRef.current, []);

  // 当模块变化时更新表单数据
  useEffect(() => {
    const defaultContent = createDefaultDoc([createDefaultPromptNode()]);

    if (module) {
      // 解析模块标签 ID
      let parsedLabelIds: string[] = [];

      try {
        const { success, data } = moduleLabelIdsSchema.safeParse(
          safeParseJson(module.moduleLabelIds, null),
        );

        if (success) {
          parsedLabelIds = data;
        }
      } catch (error) {
        parsedLabelIds = [];
      }

      // 解析模块的 prompt 内容
      const { success, data } = parseModuleNode(
        safeParseJson(module.prompt, null),
      );

      if (success) {
        setEditorContent(createDefaultDoc(data.content));
      } else {
        setEditorContent(defaultContent);
      }

      form.reset({
        name: module.name,
        content: data?.content ?? [],
        level: module.level,
        moduleLabelIds: parsedLabelIds,
        scopeDetail: parseScopeDetail(module.scopeDetail),
      });
    } else {
      // 创建模式：如果有选中的标签，设为默认值
      const defaultLabelIds = selectedModuleLabelId
        ? [selectedModuleLabelId]
        : [];

      form.reset({
        name: "",
        content: [],
        level: Level.Product,
        moduleLabelIds: defaultLabelIds,
        scopeDetail: [],
      });

      setEditorContent(defaultContent);
    }
  }, [module, selectedModuleLabelId, form, parseScopeDetail]);

  useEffect(() => {
    if (!open) {
      form.reset();
    }
  }, [form, open]);

  return (
    <ExtendedDialog
      title={isCreateMode ? "创建模块" : "编辑模块"}
      open={open}
      modal={false}
      footer={
        <>
          <DialogClose asChild>
            <Button variant="outline" onClick={handleClose}>
              {t("common.cancel")}
            </Button>
          </DialogClose>
          <Button type="submit" onClick={handleSave}>
            {isCreateMode ? t("common.create") : t("common.save")}
          </Button>
        </>
      }
      onOpenChange={onOpenChange}
    >
      <Form {...form}>
        <div
          className="flex flex-col h-full p-6 overflow-auto"
          ref={containerRef}
        >
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem className="mb-4">
                <FormLabel>
                  模块名称
                  <span className="text-red-500 ml-1">*</span>
                </FormLabel>
                <FormControl>
                  <Input placeholder="请输入模块名称" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="moduleLabelIds"
            render={({ field }) => (
              <FormItem className="mb-4">
                <FormLabel>模块标签</FormLabel>
                <FormControl>
                  <SelectSearch
                    options={labelOptions}
                    placeholder="请选择模块标签"
                    multiple={true}
                    modal={false}
                    value={field.value}
                    onChange={field.onChange}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="grid grid-cols-2 gap-4 mb-4">
            <FormField
              control={form.control}
              name="level"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    <span>等级</span>
                    <LevelTip />
                    <span className="text-red-500 ml-1">*</span>
                  </FormLabel>
                  <Select
                    onValueChange={(value) => {
                      field.onChange(value);
                      form.setValue("scopeDetail", []);
                    }}
                    value={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="请选择等级" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {getLevelOptions().map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* 动态范围选择器 */}
            {(currentLevel === Level.Shop ||
              currentLevel === Level.Category) && (
              <FormField
                control={form.control}
                name="scopeDetail"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {currentLevel === Level.Shop ? "选择店铺" : "选择类目"}
                      <ScopeTip />
                      <span className="text-red-500 ml-1">*</span>
                    </FormLabel>
                    <FormControl>
                      {currentLevel === Level.Shop ? (
                        <ShopMultiSelector
                          value={field.value || []}
                          onChange={field.onChange}
                          placeholder="请选择店铺"
                          triggerClassName="w-full"
                        />
                      ) : (
                        <CategoryDropdownSelector
                          value={field.value || []}
                          onChange={field.onChange}
                          placeholder="请选择类目"
                          triggerClassName="w-full"
                        />
                      )}
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}
          </div>

          <div className="flex-1 flex flex-col min-h-72">
            <label className="text-sm font-medium mb-2">提示词</label>
            <div className="flex-1 min-h-0">
              <ModuleEditorWithVariables
                content={editorContent}
                className="h-full"
                level={currentLevel}
                scopeDetail={JSON.stringify(currentScopeDetail)}
                containerDomGetter={containerDomGetter}
                onChange={(content) => {
                  form.setValue("content", content.content as PromptNodeType[]);
                }}
              />
            </div>
          </div>
        </div>
      </Form>
    </ExtendedDialog>
  );
};
