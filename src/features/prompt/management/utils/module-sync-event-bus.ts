import { PromptNodeType } from "../schemas/node";

/**
 * 模块内容变更事件数据
 */
export interface ModuleContentChangeEvent {
  moduleId: string;
  content: PromptNodeType[];
  timestamp: number;
  source?: string; // 用于标识事件来源，避免循环更新
}

/**
 * 事件监听器类型
 */
export type ModuleContentChangeListener = (
  event: ModuleContentChangeEvent,
) => void;

/**
 * 模块同步事件总线
 * 用于在相同 moduleId 的组件之间同步内容变更
 */
class ModuleSyncEventBus {
  private listeners: Map<string, Set<ModuleContentChangeListener>> = new Map();
  private static instance: ModuleSyncEventBus;

  private constructor() {}

  /**
   * 获取单例实例
   */
  static getInstance(): ModuleSyncEventBus {
    if (!ModuleSyncEventBus.instance) {
      ModuleSyncEventBus.instance = new ModuleSyncEventBus();
    }
    return ModuleSyncEventBus.instance;
  }

  /**
   * 订阅模块内容变更事件
   * @param moduleId 模块ID
   * @param listener 监听器函数
   * @returns 取消订阅的函数
   */
  subscribe(
    moduleId: string,
    listener: ModuleContentChangeListener,
  ): () => void {
    if (!this.listeners.has(moduleId)) {
      this.listeners.set(moduleId, new Set());
    }

    const moduleListeners = this.listeners.get(moduleId)!;
    moduleListeners.add(listener);

    // 返回取消订阅函数
    return () => {
      moduleListeners.delete(listener);

      if (moduleListeners.size === 0) {
        this.listeners.delete(moduleId);
      }
    };
  }

  /**
   * 发布模块内容变更事件
   * @param event 事件数据
   */
  publish(event: ModuleContentChangeEvent): void {
    const moduleListeners = this.listeners.get(event.moduleId);

    if (!moduleListeners) {
      return;
    }

    moduleListeners.forEach((listener) => {
      try {
        listener(event);
      } catch (error) {
        console.error(
          `模块内容同步事件处理失败 (moduleId: ${event.moduleId}):`,
          error,
        );
      }
    });
  }

  /**
   * 获取指定模块的监听器数量
   * @param moduleId 模块ID
   */
  getListenerCount(moduleId: string): number {
    return this.listeners.get(moduleId)?.size || 0;
  }

  /**
   * 清除所有监听器（主要用于测试）
   */
  clear(): void {
    this.listeners.clear();
  }

  /**
   * 获取所有正在监听的模块ID列表
   */
  getActiveModuleIds(): string[] {
    return Array.from(this.listeners.keys());
  }
}

/**
 * 导出单例实例
 */
export const moduleSyncEventBus = ModuleSyncEventBus.getInstance();
