import { type Ability, type TypedAbility } from "@/types/api/ability";
import { AbilityItemType } from "@/types/api/prompt";
import {
  AbilityItemNodeType,
  ModuleNodeType,
  parseAbilityContent,
  parseModuleNode,
  PromptNodeType,
  sharedVariableNodeSchema,
  SharedVariableNodeType,
  stringifyAbilityNode,
  type AbilityNodeType,
} from "../schemas/node";

/**
 * 将 Ability 转换为 TypedAbility
 * @param ability 原始能力对象
 * @returns 类型安全的能力对象
 */
export const convertToTypedAbility = (ability: Ability): TypedAbility => ({
  ...ability,
  content: parseAbilityContent(ability.content).data,
});

/**
 * 将 TypedAbility 转换为 Ability
 * @param typedAbility 类型安全的能力对象
 * @returns 原始能力对象
 */
export const convertToAbility = (typedAbility: TypedAbility): Ability => ({
  ...typedAbility,
  content: stringifyAbilityNode(typedAbility.content),
});

/**
 * 创建默认文档
 *
 * @returns
 */
export const createDefaultDoc = (
  content: AbilityNodeType[] | PromptNodeType[] | SharedVariableNodeType[] = [],
) => ({
  type: "doc",
  content,
});

/**
 * 创建一个以能力为编辑项的文档
 *
 * @param abilityContentString 能力内容字符串
 * @returns 文档
 */
export const createAbilityDoc = (abilityContentString: string) => {
  const result = parseAbilityContent(abilityContentString);

  if (result.error) {
    console.error(
      "Failed to parse ability content:",
      result.error,
      abilityContentString,
    );

    return {
      type: "doc",
      content: [],
    };
  }

  return {
    type: "doc",
    content: [result.data],
  };
};

/**
 * 创建默认能力项节点
 * @returns 能力项节点数组
 */
export const createDefaultAbilityItemNodes = (): AbilityItemNodeType[] =>
  [
    AbilityItemType.Role,
    AbilityItemType.Background,
    AbilityItemType.Goals,
    AbilityItemType.Profile,
    AbilityItemType.Skills,
    AbilityItemType.Workflow,
    AbilityItemType.Examples,
    AbilityItemType.OutputFormat,
  ].map((type) => ({
    type: "abilityItem",
    attrs: {
      type,
    },
    content: [],
  }));

/**
 * 创建默认能力内容
 */
export const createDefaultAbilityNode = (
  name = "未命名能力",
): AbilityNodeType => ({
  type: "ability",
  attrs: {
    name,
  },
  content: createDefaultAbilityItemNodes(),
});

/**
 * 创建默认模块内容
 */
export const createDefaultModuleNode = (
  name = "未命名模块",
): ModuleNodeType => ({
  type: "module",
  attrs: {
    name,
  },
  content: [
    {
      type: "prompt",
      content: [],
    },
  ],
});

/**
 * 创建默认提示词内容
 */
export const createDefaultPromptNode = (): PromptNodeType => ({
  type: "prompt",
  content: [],
});

/**
 * 创建编辑器使用的模块内容
 */
export const createEditorModuleContent = (moduleContent: string) => {
  if (!moduleContent || !moduleContent.trim()) {
    return createDefaultDoc();
  }

  const parseResult = parseModuleNode(JSON.parse(moduleContent));

  if (!parseResult.success) {
    return createDefaultDoc();
  }

  return createDefaultDoc(parseResult.data.content);
};

/**
 * 创建默认共享变量内容 (3x3 表格)
 */
export const createDefaultSharedVariableContent = () => {
  return {
    type: "tableVariable",
    content: [
      // 表头行
      {
        type: "tableVariableRow",
        content: [
          {
            type: "tableVariableHeader",
            content: [
              {
                type: "paragraph",
              },
            ],
          },
          {
            type: "tableVariableHeader",
            content: [
              {
                type: "paragraph",
              },
            ],
          },
          {
            type: "tableVariableHeader",
            content: [
              {
                type: "paragraph",
              },
            ],
          },
        ],
      },
      // 数据行1
      {
        type: "tableVariableRow",
        content: [
          {
            type: "tableVariableCell",
            content: [
              {
                type: "paragraph",
              },
            ],
          },
          {
            type: "tableVariableCell",
            content: [
              {
                type: "paragraph",
              },
            ],
          },
          {
            type: "tableVariableCell",
            content: [
              {
                type: "paragraph",
              },
            ],
          },
        ],
      },
      // 数据行2
      {
        type: "tableVariableRow",
        content: [
          {
            type: "tableVariableCell",
            content: [
              {
                type: "paragraph",
              },
            ],
          },
          {
            type: "tableVariableCell",
            content: [
              {
                type: "paragraph",
              },
            ],
          },
          {
            type: "tableVariableCell",
            content: [
              {
                type: "paragraph",
              },
            ],
          },
        ],
      },
    ],
  };
};

/**
 * 创建默认共享变量文档
 */
export const createDefaultSharedVariableDoc = () => {
  return {
    type: "doc",
    content: [createDefaultSharedVariableContent()],
  };
};

/**
 * 创建编辑器使用的共享变量内容
 */
export const createEditorSharedVariableContent = (
  sharedVariableContent: string,
) => {
  if (!sharedVariableContent || !sharedVariableContent.trim()) {
    return createDefaultDoc();
  }

  try {
    const parsedContent = sharedVariableNodeSchema.parse(
      JSON.parse(sharedVariableContent),
    );

    return createDefaultDoc([parsedContent]);
  } catch {
    return createDefaultDoc();
  }
};
