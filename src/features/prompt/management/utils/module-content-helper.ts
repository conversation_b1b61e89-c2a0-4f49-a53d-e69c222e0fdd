import { parseModuleNode, stringifyModuleNode } from "../schemas/node";

/**
 * 递归移除对象中的共享变量引用节点
 */
const removeSharedVariableReferencesFromObject = (
  obj: any,
  sharedVariableId: string,
): any => {
  if (!obj || typeof obj !== "object") {
    return obj;
  }

  if (Array.isArray(obj)) {
    // 过滤掉匹配的共享变量引用节点，并递归处理剩余的节点
    return obj
      .filter((item) => {
        // 如果是共享变量引用节点且ID匹配，则过滤掉
        if (
          item?.type === "sharedVariableReference" &&
          item?.attrs?.sharedVariableId === sharedVariableId
        ) {
          return false;
        }
        return true;
      })
      .map((item) =>
        removeSharedVariableReferencesFromObject(item, sharedVariableId),
      );
  }

  // 递归处理对象的所有属性
  const result: any = {};

  for (const [key, value] of Object.entries(obj)) {
    result[key] = removeSharedVariableReferencesFromObject(
      value,
      sharedVariableId,
    );
  }

  return result;
};

/**
 * 从模块内容中移除对指定共享变量的引用
 *
 * @param moduleContent 模块内容的 JSON 字符串
 * @param sharedVariableId 要移除的共享变量ID
 * @returns 更新后的模块内容 JSON 字符串
 */
export const removeSharedVariableFromModuleContent = (
  moduleContent: string,
  sharedVariableId: string,
): string => {
  if (!moduleContent || !moduleContent.trim()) {
    return moduleContent;
  }

  try {
    // 解析模块内容
    const content = JSON.parse(moduleContent);
    const parseResult = parseModuleNode(content);

    if (!parseResult.success) {
      console.error("Failed to parse module content:", parseResult.error);
      return moduleContent;
    }

    const moduleNode = parseResult.data;

    // 递归移除共享变量引用节点
    const updatedModuleNode = removeSharedVariableReferencesFromObject(
      moduleNode,
      sharedVariableId,
    );

    // 序列化并返回
    return stringifyModuleNode(updatedModuleNode);
  } catch (error) {
    console.error("Failed to process module content:", error);
    // JSON 解析失败，返回原内容
    return moduleContent;
  }
};
