/**
 * 名称重复性验证工具
 */
import { AbilityService } from "@/services/ability-service";
import { ModuleService } from "@/services/module-service";
import { Getter } from "jotai";
import { toast } from "sonner";
import { abilityDb, moduleDb, moduleLabelDb, versionDb } from "../atoms/core";

/**
 * 验证类型枚举
 */
export enum ValidationType {
  ABILITY = "ability",
  MODULE = "module",
  MODULE_LABEL = "moduleLabel",
}

/**
 * 验证配置接口
 */
export interface ValidationConfig {
  type: ValidationType;
  name: string;
  promptVersionId: string;
  /** 排除的实体名称（用于编辑时排除当前实体） */
  excludeName?: string;
  /** Jotai getter 函数，用于获取草稿数据 */
  get: Getter;
}

/**
 * 获取当前版本的基准版本ID
 */
async function getBaseVersionId(
  promptVersionId: string,
  get: Getter,
): Promise<string | null> {
  await get(versionDb.suspendBeforeInit);

  const versions = get(versionDb.values);
  const currentVersion = Array.from(versions).find(
    (v) => v.id === promptVersionId,
  );
  return currentVersion?.baseVersionId || null;
}

/**
 * 检查能力名称是否重复（基准版本）
 */
async function checkAbilityNameDuplicateInBase(
  name: string,
  baseVersionId: string,
): Promise<boolean> {
  try {
    const nameList = await AbilityService.selectAbilityNameList({
      promptVersionId: baseVersionId,
    });
    return (nameList || []).includes(name);
  } catch (error) {
    console.error("检查能力名称重复失败:", error);
    return false;
  }
}

/**
 * 检查能力名称是否在草稿中重复
 */
function checkAbilityNameDuplicateInDraft(
  name: string,
  promptVersionId: string,
  excludeName: string | undefined,
  get: Getter,
): boolean {
  // 获取当前版本的所有草稿能力
  const abilities = get(abilityDb.values);
  const versionAbilities = abilities.filter(
    (ability) => ability.promptVersionId === promptVersionId,
  );

  // 检查名称是否重复（排除自己）
  return versionAbilities.some(
    (ability) => ability.name === name && ability.name !== excludeName,
  );
}

/**
 * 检查模块名称是否重复（基准版本）
 */
async function checkModuleNameDuplicateInBase(
  name: string,
  baseVersionId: string,
): Promise<boolean> {
  try {
    const nameList = await ModuleService.selectModuleNameList({
      promptVersionId: baseVersionId,
    });
    return (nameList || []).includes(name);
  } catch (error) {
    console.error("检查模块名称重复失败:", error);
    return false;
  }
}

/**
 * 检查模块名称是否在草稿中重复
 */
function checkModuleNameDuplicateInDraft(
  name: string,
  promptVersionId: string,
  excludeName: string | undefined,
  get: Getter,
): boolean {
  // 获取当前版本的所有草稿模块
  const modules = get(moduleDb.values);
  const versionModules = modules.filter(
    (module) => module.promptVersionId === promptVersionId,
  );

  // 检查名称是否重复（排除自己）
  return versionModules.some(
    (module) => module.name === name && module.name !== excludeName,
  );
}

/**
 * 检查模块标签名称是否重复（基准版本）
 */
async function checkModuleLabelNameDuplicateInBase(
  name: string,
  baseVersionId: string,
): Promise<boolean> {
  try {
    const nameList = await ModuleService.selectModuleLabelNameList({
      promptVersionId: baseVersionId,
    });
    return (nameList || []).includes(name);
  } catch (error) {
    console.error("检查模块标签名称重复失败:", error);
    return false;
  }
}

/**
 * 检查模块标签名称是否在草稿中重复
 */
function checkModuleLabelNameDuplicateInDraft(
  name: string,
  promptVersionId: string,
  excludeName: string | undefined,
  get: Getter,
): boolean {
  // 获取当前版本的所有草稿模块标签
  const labels = get(moduleLabelDb.values);
  const versionLabels = labels.filter(
    (label) => label.promptVersionId === promptVersionId,
  );

  // 检查名称是否重复（排除自己）
  return versionLabels.some(
    (label) => label.name === name && label.name !== excludeName,
  );
}

/**
 * 统一的名称重复检查函数
 */
async function checkNameDuplicate(
  type: ValidationType,
  name: string,
  promptVersionId: string,
  excludeName: string | undefined,
  get: Getter,
): Promise<boolean> {
  // 1. 检查草稿中是否重复
  let isDuplicateInDraft = false;

  switch (type) {
    case ValidationType.ABILITY:
      isDuplicateInDraft = checkAbilityNameDuplicateInDraft(
        name,
        promptVersionId,
        excludeName,
        get,
      );
      break;
    case ValidationType.MODULE:
      isDuplicateInDraft = checkModuleNameDuplicateInDraft(
        name,
        promptVersionId,
        excludeName,
        get,
      );
      break;
    case ValidationType.MODULE_LABEL:
      isDuplicateInDraft = checkModuleLabelNameDuplicateInDraft(
        name,
        promptVersionId,
        excludeName,
        get,
      );
      break;
  }

  if (isDuplicateInDraft) {
    return true;
  }

  // 2. 检查基准版本中是否重复（如果有基准版本）
  const baseVersionId = await getBaseVersionId(promptVersionId, get);

  if (!baseVersionId) {
    return false; // 没有基准版本，只检查草稿
  }

  // 如果排除名称与当前名称相同，则不算重复
  if (excludeName && excludeName === name) {
    return false;
  }

  switch (type) {
    case ValidationType.ABILITY:
      return await checkAbilityNameDuplicateInBase(name, baseVersionId);
    case ValidationType.MODULE:
      return await checkModuleNameDuplicateInBase(name, baseVersionId);
    case ValidationType.MODULE_LABEL:
      return await checkModuleLabelNameDuplicateInBase(name, baseVersionId);
    default:
      return false;
  }
}

/**
 * 获取错误提示消息映射
 */
const errorMessageMap = {
  [ValidationType.ABILITY]: "能力名称不能重复",
  [ValidationType.MODULE]: "模块名称不能重复",
  [ValidationType.MODULE_LABEL]: "模块标签名称不能重复",
};

/**
 * 验证名称是否重复
 *
 * @param config 验证配置
 * @returns Promise<boolean> true表示验证通过（无重复），false表示验证失败（有重复）
 */
export async function validateNameDuplicate(
  config: ValidationConfig,
): Promise<boolean> {
  const { type, name, promptVersionId, excludeName, get } = config;

  try {
    // 执行重复性检查
    const isDuplicate = await checkNameDuplicate(
      type,
      name,
      promptVersionId,
      excludeName,
      get,
    );

    if (isDuplicate) {
      // 显示错误提示
      const errorMessage = errorMessageMap[type];
      toast.error(errorMessage);
      return false;
    }

    return true;
  } catch (error) {
    console.error(`验证名称重复失败:`, error);
    return true;
  }
}

/**
 * 批量验证多个名称
 *
 * @param configs 验证配置数组
 * @returns Promise<boolean> true表示全部验证通过，false表示至少有一个验证失败
 */
export async function validateMultipleNames(
  configs: ValidationConfig[],
): Promise<boolean> {
  const results = await Promise.all(
    configs.map((config) => validateNameDuplicate(config)),
  );

  return results.every((result) => result === true);
}
