import { PromptVersion } from "@/types/api/prompt-version";
import { DraftVersion } from "../types";

/**
 * 判断数据是否为草稿
 */
export const isDraftId = (id: string) => {
  if (typeof id !== "string") {
    return false;
  }

  return id.startsWith("draft-");
};

/**
 * 判断版本是否为草稿版本
 */
export const isDraftVersion = (
  version: PromptVersion | DraftVersion,
): version is DraftVersion => {
  return isDraftId((version as DraftVersion).id);
};
