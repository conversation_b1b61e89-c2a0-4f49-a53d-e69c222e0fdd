import {
  CompleteVersionData,
  DraftAbility,
  CompleteDraftVersionData,
  DraftModule,
  DraftModuleLabel,
  DraftSharedVariable,
  DraftVersion,
} from "../types";

/**
 * 基于源版本数据创建草稿数据的纯函数
 */
export function createDraftData(
  sourceData: CompleteVersionData,
  sourceVersionId: string,
  draftName: string,
  draftId: string,
  timestamp: string,
): CompleteDraftVersionData {
  // 创建草稿版本
  const draftVersion: DraftVersion = {
    id: draftId,
    draftName,
    baseVersionId: sourceVersionId,
    versionName: draftName,
    createdAt: timestamp,
    updatedAt: timestamp,
  };

  // 复制模块标签
  const draftModuleLabels: DraftModuleLabel[] = sourceData.moduleLabels.map(
    (label) => ({
      ...label,
      id: label.id,
      isDraft: true,
      sourceVersionId: label.promptVersionId,
      draftName,
      promptVersionId: draftId,
      createdAt: timestamp,
      updatedAt: timestamp,
    }),
  );

  // 复制共享变量
  const draftSharedVariables: DraftSharedVariable[] =
    sourceData.sharedVariables.map((variable) => ({
      ...variable,
      id: variable.id,
      isDraft: true,
      sourceVersionId: variable.promptVersionId,
      draftName,
      promptVersionId: draftId,
      createdAt: timestamp,
      updatedAt: timestamp,
    }));

  // 复制模块
  const draftModules: DraftModule[] = sourceData.modules.map((module) => ({
    ...module,
    id: module.id,
    isDraft: true,
    sourceVersionId: module.promptVersionId,
    draftName,
    promptVersionId: draftId,
    createdAt: timestamp,
    updatedAt: timestamp,
  }));

  // 复制能力
  const draftAbilities: DraftAbility[] = sourceData.abilities.map(
    (ability) => ({
      ...ability,
      id: ability.id,
      isDraft: true,
      sourceVersionId: ability.promptVersionId,
      draftName,
      promptVersionId: draftId,
      createdAt: timestamp,
      updatedAt: timestamp,
    }),
  );

  return {
    draftVersion,
    draftModuleLabels,
    draftSharedVariables,
    draftModules,
    draftAbilities,
  };
}
