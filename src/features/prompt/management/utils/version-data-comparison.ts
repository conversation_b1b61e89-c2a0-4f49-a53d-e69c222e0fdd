/**
 * 版本数据对比工具函数
 * 用于对比草稿数据和远程数据，计算增加、减少、变更的项目
 */

import { AbilityItemTypeLabels } from "@/constants/prompt";
import { Level } from "@/types/api/prompt";
import { parseAbilityContent } from "../schemas/node";
import {
  ChangeType,
  CompleteDraftVersionData,
  CompleteVersionData,
  UniversalModule,
} from "../types";

/**
 * 基础变更项
 */
export interface BaseChangeItem {
  id: string;
  name: string;
  changeType: ChangeType;
}

/**
 * 能力项模块变更详情
 */
export interface AbilityItemModuleChange {
  /** 模块ID */
  moduleId: string;
  /** 模块名称 */
  moduleName: string;
  /** 模块级别 */
  level: Level;
  /** 变更类型 */
  changeType: ChangeType;
  /** 原始提示词内容（用于 diff 展示） */
  oldPrompt?: string;
  /** 新提示词内容（用于 diff 展示） */
  newPrompt?: string;
}

/**
 * 能力项变更详情
 */
export interface AbilityItemChange {
  /** 能力项类型 */
  type: string;
  /** 能力项名称 */
  name: string;
  /** 模块变更列表 */
  moduleChanges: AbilityItemModuleChange[];
  /** 变更统计 */
  summary: {
    added: number;
    removed: number;
    modified: number;
  };
}

/**
 * 能力变更项
 */
export interface AbilityChangeItem extends BaseChangeItem {
  type: "ability";
  /** 下属模块的变更统计 */
  moduleChanges: {
    added: number;
    removed: number;
    modified: number;
  };
  /** 按能力项分组的详细变更信息 */
  abilityItemChanges: AbilityItemChange[];
  /** 原始内容（用于 diff 展示） */
  oldContent?: string;
  /** 新内容（用于 diff 展示） */
  newContent?: string;
}

/**
 * 模块变更项
 */
export interface ModuleChangeItem extends BaseChangeItem {
  type: "module";
  level: Level;
  /** 原始提示词内容 */
  oldPrompt?: string;
  /** 新提示词内容 */
  newPrompt?: string;
}

/**
 * 模块标签变更项
 */
export interface ModuleLabelChangeItem extends BaseChangeItem {
  type: "moduleLabel";
}

/**
 * 共享变量变更项
 */
export interface SharedVariableChangeItem extends BaseChangeItem {
  type: "sharedVariable";
  /** 原始内容 */
  oldContent?: string;
  /** 新内容 */
  newContent?: string;
}

/**
 * 所有变更项的联合类型
 */
export type ChangeItem =
  | AbilityChangeItem
  | ModuleChangeItem
  | ModuleLabelChangeItem
  | SharedVariableChangeItem;

/**
 * 版本数据对比结果
 */
export interface VersionDataComparison {
  /** 能力变更 */
  abilities: AbilityChangeItem[];
  /** 模块变更 */
  modules: ModuleChangeItem[];
  /** 模块标签变更 */
  moduleLabels: ModuleLabelChangeItem[];
  /** 共享变量变更 */
  sharedVariables: SharedVariableChangeItem[];
  /** 统计信息 */
  summary: {
    abilities: { added: number; removed: number; modified: number };
    modules: { added: number; removed: number; modified: number };
    moduleLabels: { added: number; removed: number; modified: number };
    sharedVariables: { added: number; removed: number; modified: number };
    total: number;
  };
}

/**
 * 单个补丁组的对比结果
 */
export interface PatchGroupComparison {
  /** 补丁组索引 */
  index: number;
  /** 变更时间戳 */
  timestamp: string;
  /** 变更描述 */
  description?: string;
  /** 对比结果 */
  comparison: VersionDataComparison;
  /** 是否有变更 */
  hasChanges: boolean;
}

/**
 * 版本历史对比结果
 */
export interface VersionHistoryComparison {
  /** 补丁组对比结果列表（按时间正序） */
  patchGroups: PatchGroupComparison[];
  /** 总体统计信息 */
  totalSummary: {
    abilities: { added: number; removed: number; modified: number };
    modules: { added: number; removed: number; modified: number };
    moduleLabels: { added: number; removed: number; modified: number };
    sharedVariables: { added: number; removed: number; modified: number };
    total: number;
  };
}

/**
 * 提取能力中引用的模块ID列表
 */
function extractModuleIdsFromAbility(content: string): string[] {
  const parseResult = parseAbilityContent(content);
  if (!parseResult.success) {
    return [];
  }

  const moduleIds: string[] = [];
  const ability = parseResult.data;

  ability.content?.forEach((abilityItem) => {
    abilityItem.content?.forEach((moduleRef) => {
      if (moduleRef.attrs?.id) {
        moduleIds.push(moduleRef.attrs.id);
      }
    });
  });

  return moduleIds;
}

/**
 * 计算能力项的详细变更信息
 */
function calculateAbilityItemChanges(
  oldContent: string | undefined,
  newContent: string | undefined,
  draftModules: UniversalModule[],
  remoteModules: UniversalModule[],
): AbilityItemChange[] {
  const changes: AbilityItemChange[] = [];

  // 创建模块映射以便快速查找
  const draftModuleMap = new Map(draftModules.map((m) => [m.id, m]));
  const remoteModuleMap = new Map(remoteModules.map((m) => [m.id, m]));

  // 解析能力内容
  const oldAbility = oldContent ? parseAbilityContent(oldContent) : null;
  const newAbility = newContent ? parseAbilityContent(newContent) : null;

  // 获取所有能力项类型
  const allAbilityItemTypes = new Set<string>();

  if (oldAbility?.success) {
    oldAbility.data.content?.forEach((item) =>
      allAbilityItemTypes.add(item.attrs.type),
    );
  }

  if (newAbility?.success) {
    newAbility.data.content?.forEach((item) =>
      allAbilityItemTypes.add(item.attrs.type),
    );
  }

  // 为每个能力项类型计算变更
  for (const abilityItemType of allAbilityItemTypes) {
    const oldItem = oldAbility?.success
      ? oldAbility.data.content?.find(
          (item) => item.attrs.type === abilityItemType,
        )
      : undefined;
    const newItem = newAbility?.success
      ? newAbility.data.content?.find(
          (item) => item.attrs.type === abilityItemType,
        )
      : undefined;

    const oldModuleIds = oldItem?.content?.map((ref) => ref.attrs.id) || [];
    const newModuleIds = newItem?.content?.map((ref) => ref.attrs.id) || [];

    const moduleChanges: AbilityItemModuleChange[] = [];

    // 计算新增的模块
    const addedModuleIds = newModuleIds.filter(
      (id) => !oldModuleIds.includes(id),
    );
    addedModuleIds.forEach((moduleId) => {
      const module = draftModuleMap.get(moduleId);
      if (module) {
        moduleChanges.push({
          moduleId,
          moduleName: module.name,
          level: module.level,
          changeType: ChangeType.ADDED,
          newPrompt: module.prompt,
        });
      }
    });

    // 计算删除的模块
    const removedModuleIds = oldModuleIds.filter(
      (id) => !newModuleIds.includes(id),
    );
    removedModuleIds.forEach((moduleId) => {
      const module = remoteModuleMap.get(moduleId);
      if (module) {
        moduleChanges.push({
          moduleId,
          moduleName: module.name,
          level: module.level,
          changeType: ChangeType.REMOVED,
          oldPrompt: module.prompt,
        });
      }
    });

    // 计算修改的模块
    const commonModuleIds = newModuleIds.filter((id) =>
      oldModuleIds.includes(id),
    );
    commonModuleIds.forEach((moduleId) => {
      const draftModule = draftModuleMap.get(moduleId);
      const remoteModule = remoteModuleMap.get(moduleId);

      if (
        draftModule &&
        remoteModule &&
        draftModule.prompt !== remoteModule.prompt
      ) {
        moduleChanges.push({
          moduleId,
          moduleName: draftModule.name,
          level: draftModule.level,
          changeType: ChangeType.MODIFIED,
          oldPrompt: remoteModule.prompt,
          newPrompt: draftModule.prompt,
        });
      }
    });

    // 只有当有变更时才添加到结果中
    if (moduleChanges.length > 0) {
      changes.push({
        type: abilityItemType,
        name:
          AbilityItemTypeLabels[
            abilityItemType as keyof typeof AbilityItemTypeLabels
          ] || abilityItemType,
        moduleChanges,
        summary: {
          added: addedModuleIds.length,
          removed: removedModuleIds.length,
          modified: commonModuleIds.filter((id) => {
            const draftModule = draftModuleMap.get(id);
            const remoteModule = remoteModuleMap.get(id);
            return (
              draftModule &&
              remoteModule &&
              draftModule.prompt !== remoteModule.prompt
            );
          }).length,
        },
      });
    }
  }

  return changes;
}

/**
 * 对比能力数据
 */
function compareAbilities(
  draftAbilities: CompleteDraftVersionData["draftAbilities"],
  remoteAbilities: CompleteVersionData["abilities"],
  draftModules: CompleteDraftVersionData["draftModules"],
  remoteModules: CompleteVersionData["modules"],
): AbilityChangeItem[] {
  const changes: AbilityChangeItem[] = [];
  const remoteMap = new Map(remoteAbilities.map((item) => [item.id, item]));
  const draftMap = new Map(draftAbilities.map((item) => [item.id, item]));

  // 检查新增和修改的能力
  for (const draftAbility of draftAbilities) {
    const remoteAbility = remoteMap.get(draftAbility.id);

    if (!remoteAbility) {
      // 新增的能力
      const moduleIds = extractModuleIdsFromAbility(draftAbility.content);
      const abilityItemChanges = calculateAbilityItemChanges(
        undefined,
        draftAbility.content,
        draftModules,
        remoteModules,
      );

      changes.push({
        id: draftAbility.id,
        name: draftAbility.name,
        type: "ability",
        changeType: ChangeType.ADDED,
        moduleChanges: {
          added: moduleIds.length,
          removed: 0,
          modified: 0,
        },
        abilityItemChanges,
        newContent: draftAbility.content,
      });
    } else if (draftAbility.content !== remoteAbility.content) {
      // 修改的能力
      const oldModuleIds = extractModuleIdsFromAbility(remoteAbility.content);
      const newModuleIds = extractModuleIdsFromAbility(draftAbility.content);
      const addedModules = newModuleIds.filter(
        (id) => !oldModuleIds.includes(id),
      );
      const removedModules = oldModuleIds.filter(
        (id) => !newModuleIds.includes(id),
      );
      const commonModules = newModuleIds.filter((id) =>
        oldModuleIds.includes(id),
      );
      const abilityItemChanges = calculateAbilityItemChanges(
        remoteAbility.content,
        draftAbility.content,
        draftModules,
        remoteModules,
      );

      changes.push({
        id: draftAbility.id,
        name: draftAbility.name,
        type: "ability",
        changeType: ChangeType.MODIFIED,
        moduleChanges: {
          added: addedModules.length,
          removed: removedModules.length,
          modified: commonModules.length,
        },
        abilityItemChanges,
        oldContent: remoteAbility.content,
        newContent: draftAbility.content,
      });
    }
  }

  // 检查删除的能力
  for (const remoteAbility of remoteAbilities) {
    if (!draftMap.has(remoteAbility.id)) {
      const moduleIds = extractModuleIdsFromAbility(remoteAbility.content);
      const abilityItemChanges = calculateAbilityItemChanges(
        remoteAbility.content,
        undefined,
        draftModules,
        remoteModules,
      );

      changes.push({
        id: remoteAbility.id,
        name: remoteAbility.name,
        type: "ability",
        changeType: ChangeType.REMOVED,
        moduleChanges: {
          added: 0,
          removed: moduleIds.length,
          modified: 0,
        },
        abilityItemChanges,
        oldContent: remoteAbility.content,
      });
    }
  }

  return changes;
}

/**
 * 对比模块数据
 */
function compareModules(
  draftModules: CompleteDraftVersionData["draftModules"],
  remoteModules: CompleteVersionData["modules"],
): ModuleChangeItem[] {
  const changes: ModuleChangeItem[] = [];
  const remoteMap = new Map(remoteModules.map((item) => [item.id, item]));
  const draftMap = new Map(draftModules.map((item) => [item.id, item]));

  // 检查新增和修改的模块
  for (const draftModule of draftModules) {
    const remoteModule = remoteMap.get(draftModule.id);

    if (!remoteModule) {
      // 新增的模块
      changes.push({
        id: draftModule.id,
        name: draftModule.name,
        type: "module",
        level: draftModule.level,
        changeType: ChangeType.ADDED,
        newPrompt: draftModule.prompt,
      });
    } else if (draftModule.prompt !== remoteModule.prompt) {
      // 修改的模块
      changes.push({
        id: draftModule.id,
        name: draftModule.name,
        level: draftModule.level,
        type: "module",
        changeType: ChangeType.MODIFIED,
        oldPrompt: remoteModule.prompt,
        newPrompt: draftModule.prompt,
      });
    }
  }

  // 检查删除的模块
  for (const remoteModule of remoteModules) {
    if (!draftMap.has(remoteModule.id)) {
      changes.push({
        id: remoteModule.id,
        name: remoteModule.name,
        level: remoteModule.level,
        type: "module",
        changeType: ChangeType.REMOVED,
        oldPrompt: remoteModule.prompt,
      });
    }
  }

  return changes;
}

/**
 * 对比模块标签数据
 */
function compareModuleLabels(
  draftModuleLabels: CompleteDraftVersionData["draftModuleLabels"],
  remoteModuleLabels: CompleteVersionData["moduleLabels"],
): ModuleLabelChangeItem[] {
  const changes: ModuleLabelChangeItem[] = [];
  const remoteMap = new Map(remoteModuleLabels.map((item) => [item.id, item]));
  const draftMap = new Map(draftModuleLabels.map((item) => [item.id, item]));

  // 检查新增的模块标签
  for (const draftLabel of draftModuleLabels) {
    if (!remoteMap.has(draftLabel.id)) {
      changes.push({
        id: draftLabel.id,
        name: draftLabel.name,
        type: "moduleLabel",
        changeType: ChangeType.ADDED,
      });
    }
  }

  // 检查删除的模块标签
  for (const remoteLabel of remoteModuleLabels) {
    if (!draftMap.has(remoteLabel.id)) {
      changes.push({
        id: remoteLabel.id,
        name: remoteLabel.name,
        type: "moduleLabel",
        changeType: ChangeType.REMOVED,
      });
    }
  }

  return changes;
}

/**
 * 对比共享变量数据
 */
function compareSharedVariables(
  draftSharedVariables: CompleteDraftVersionData["draftSharedVariables"],
  remoteSharedVariables: CompleteVersionData["sharedVariables"],
): SharedVariableChangeItem[] {
  const changes: SharedVariableChangeItem[] = [];
  const remoteMap = new Map(
    remoteSharedVariables.map((item) => [item.id, item]),
  );
  const draftMap = new Map(draftSharedVariables.map((item) => [item.id, item]));

  // 检查新增和修改的共享变量
  for (const draftVariable of draftSharedVariables) {
    const remoteVariable = remoteMap.get(draftVariable.id);

    if (!remoteVariable) {
      // 新增的共享变量
      changes.push({
        id: draftVariable.id,
        name: draftVariable.name,
        type: "sharedVariable",
        changeType: ChangeType.ADDED,
        newContent: draftVariable.content,
      });
    } else if (draftVariable.content !== remoteVariable.content) {
      // 修改的共享变量
      changes.push({
        id: draftVariable.id,
        name: draftVariable.name,
        type: "sharedVariable",
        changeType: ChangeType.MODIFIED,
        oldContent: remoteVariable.content,
        newContent: draftVariable.content,
      });
    }
  }

  // 检查删除的共享变量
  for (const remoteVariable of remoteSharedVariables) {
    if (!draftMap.has(remoteVariable.id)) {
      changes.push({
        id: remoteVariable.id,
        name: remoteVariable.name,
        type: "sharedVariable",
        changeType: ChangeType.REMOVED,
        oldContent: remoteVariable.content,
      });
    }
  }

  return changes;
}

/**
 * 计算统计信息
 */
function calculateSummary(
  abilities: AbilityChangeItem[],
  modules: ModuleChangeItem[],
  moduleLabels: ModuleLabelChangeItem[],
  sharedVariables: SharedVariableChangeItem[],
): VersionDataComparison["summary"] {
  const countByType = (items: ChangeItem[]) => ({
    added: items.filter((item) => item.changeType === ChangeType.ADDED).length,
    removed: items.filter((item) => item.changeType === ChangeType.REMOVED)
      .length,
    modified: items.filter((item) => item.changeType === ChangeType.MODIFIED)
      .length,
  });

  const abilitySummary = countByType(abilities);
  const moduleSummary = countByType(modules);
  const moduleLabelSummary = countByType(moduleLabels);
  const sharedVariableSummary = countByType(sharedVariables);

  const total =
    abilitySummary.added +
    abilitySummary.removed +
    abilitySummary.modified +
    moduleSummary.added +
    moduleSummary.removed +
    moduleSummary.modified +
    moduleLabelSummary.added +
    moduleLabelSummary.removed +
    moduleLabelSummary.modified +
    sharedVariableSummary.added +
    sharedVariableSummary.removed +
    sharedVariableSummary.modified;

  return {
    abilities: abilitySummary,
    modules: moduleSummary,
    moduleLabels: moduleLabelSummary,
    sharedVariables: sharedVariableSummary,
    total,
  };
}

/**
 * 对比版本数据
 * @param draftData 草稿数据
 * @param remoteData 远程数据
 * @returns 对比结果
 */
export function compareVersionData(
  draftData: CompleteDraftVersionData,
  remoteData: CompleteVersionData,
): VersionDataComparison {
  const abilities = compareAbilities(
    draftData.draftAbilities,
    remoteData.abilities,
    draftData.draftModules,
    remoteData.modules,
  );

  const modules = compareModules(draftData.draftModules, remoteData.modules);

  const moduleLabels = compareModuleLabels(
    draftData.draftModuleLabels,
    remoteData.moduleLabels,
  );

  const sharedVariables = compareSharedVariables(
    draftData.draftSharedVariables,
    remoteData.sharedVariables,
  );

  const summary = calculateSummary(
    abilities,
    modules,
    moduleLabels,
    sharedVariables,
  );

  return {
    abilities,
    modules,
    moduleLabels,
    sharedVariables,
    summary,
  };
}
