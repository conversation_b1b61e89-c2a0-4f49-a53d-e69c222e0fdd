import { PromptVersionStatus } from "@/types/api/prompt-version";
import dayjs from "dayjs";
import {
  CompleteDraftVersionData,
  CompleteVersionData,
  DraftAbility,
  DraftModule,
  DraftModuleLabel,
  DraftSharedVariable,
  UniversalAbility,
  UniversalModule,
  UniversalModuleLabel,
  UniversalSharedVariable,
} from "../types";
import {
  VersionChangeContent,
  VersionChangeTracker,
} from "./version-change-tracker";
import {
  compareVersionData,
  PatchGroupComparison,
  VersionDataComparison,
  VersionHistoryComparison,
} from "./version-data-comparison";
import { getCompleteVersionData } from "./version-data-helper";

/**
 * 从变更内容构建对比结果
 */
export function buildComparisonFromChangeContent(
  changeContent: VersionChangeContent,
): VersionDataComparison {
  const tracker = new VersionChangeTracker();

  // 通过应用所有 patches 获取最终状态
  const finalStateResult = tracker.applyAllPatches(
    changeContent.baseSnapshot,
    changeContent.patches,
  );

  if (!finalStateResult.success) {
    throw new Error(`重建版本数据失败: ${finalStateResult.error}`);
  }

  const finalState = finalStateResult.data!;

  // 将快照数据转换为 CompleteVersionData 格式
  const baseData: CompleteVersionData = {
    version: {
      promptVersionId: "",
      versionName: "",
      version: "",
      status: PromptVersionStatus.SUBMIT,
      remark: "",
    },
    abilities: changeContent.baseSnapshot.abilities as UniversalAbility[],
    modules: changeContent.baseSnapshot.modules as UniversalModule[],
    moduleLabels: changeContent.baseSnapshot
      .moduleLabels as UniversalModuleLabel[],
    sharedVariables: changeContent.baseSnapshot
      .sharedVariables as UniversalSharedVariable[],
  };

  // 将数据转换为 DraftCreationResult 格式
  const timestamp = dayjs().unix().toString();
  const draftCreationResult: CompleteDraftVersionData = {
    draftVersion: {
      id: "temp-id",
      createdAt: timestamp,
      updatedAt: timestamp,
      versionName: "",
    },
    draftAbilities: finalState.abilities as DraftAbility[],
    draftModules: finalState.modules as DraftModule[],
    draftModuleLabels: finalState.moduleLabels as DraftModuleLabel[],
    draftSharedVariables: finalState.sharedVariables as DraftSharedVariable[],
  };

  // 使用现有的 compareVersionData 函数
  return compareVersionData(draftCreationResult, baseData);
}

/**
 * 从变更内容构建版本历史对比结果
 */
export function buildHistoryComparisonFromChangeContent(
  changeContent: VersionChangeContent,
): VersionHistoryComparison {
  const tracker = new VersionChangeTracker();
  const patchGroups: PatchGroupComparison[] = [];

  // 逐步应用每个补丁组，生成每个版本的对比结果
  let currentState = changeContent.baseSnapshot;

  for (let i = 0; i < changeContent.patches.length; i++) {
    const patchGroup = changeContent.patches[i];

    // 保存应用补丁前的状态用于对比（深拷贝）
    const beforeState = JSON.parse(JSON.stringify(currentState));

    // 应用当前补丁组到当前状态
    const applyResult = tracker.applyAllPatches(currentState, [patchGroup]);

    if (!applyResult.success) {
      console.error(`应用第 ${i + 1} 个补丁组失败:`, applyResult.error);
      continue;
    }

    const newState = applyResult.data!;

    // 将新状态转换为 DraftCreationResult 格式
    const timestamp = dayjs().unix().toString();
    const draftCreationResult: CompleteDraftVersionData = {
      draftVersion: {
        id: `temp-id-${i}`,
        createdAt: timestamp,
        updatedAt: timestamp,
        versionName: "",
      },
      draftAbilities: newState.abilities.map((ability: any) => ({
        ...ability,
        promptVersionId: `temp-id-${i}`,
        createdAt: timestamp,
        updatedAt: timestamp,
      })) as DraftAbility[],
      draftModules: newState.modules.map((module: any) => ({
        ...module,
        promptVersionId: `temp-id-${i}`,
        createdAt: timestamp,
        updatedAt: timestamp,
      })) as DraftModule[],
      draftModuleLabels: newState.moduleLabels.map((label: any) => ({
        ...label,
        promptVersionId: `temp-id-${i}`,
        createdAt: timestamp,
        updatedAt: timestamp,
      })) as DraftModuleLabel[],
      draftSharedVariables: newState.sharedVariables.map((variable: any) => ({
        ...variable,
        promptVersionId: `temp-id-${i}`,
        createdAt: timestamp,
        updatedAt: timestamp,
      })) as DraftSharedVariable[],
    };

    // 将应用补丁前的状态转换为 CompleteVersionData 格式进行对比
    const currentData: CompleteVersionData = {
      version: {
        promptVersionId: "",
        versionName: "",
        version: "",
        status: PromptVersionStatus.SUBMIT,
        remark: "",
      },
      abilities: beforeState.abilities.map((ability: any) => ({
        ...ability,
        promptVersionId: `current-${i}`,
      })) as UniversalAbility[],
      modules: beforeState.modules.map((module: any) => ({
        ...module,
        promptVersionId: `current-${i}`,
      })) as UniversalModule[],
      moduleLabels: beforeState.moduleLabels.map((label: any) => ({
        ...label,
        promptVersionId: `current-${i}`,
      })) as UniversalModuleLabel[],
      sharedVariables: beforeState.sharedVariables.map((variable: any) => ({
        ...variable,
        promptVersionId: `current-${i}`,
      })) as UniversalSharedVariable[],
    };

    // 对比当前状态和新状态
    const comparison = compareVersionData(draftCreationResult, currentData);

    // 检查是否有变更
    const hasChanges = comparison.summary.total > 0;

    patchGroups.push({
      index: i,
      timestamp: patchGroup.timestamp,
      description: patchGroup.description,
      comparison,
      hasChanges,
    });

    // 更新当前状态为新状态
    currentState = newState;
  }

  // 计算总体统计信息
  const totalSummary = patchGroups.reduce(
    (acc, group) => {
      if (group.hasChanges) {
        acc.abilities.added += group.comparison.summary.abilities.added;
        acc.abilities.removed += group.comparison.summary.abilities.removed;
        acc.abilities.modified += group.comparison.summary.abilities.modified;

        acc.modules.added += group.comparison.summary.modules.added;
        acc.modules.removed += group.comparison.summary.modules.removed;
        acc.modules.modified += group.comparison.summary.modules.modified;

        acc.moduleLabels.added += group.comparison.summary.moduleLabels.added;
        acc.moduleLabels.removed +=
          group.comparison.summary.moduleLabels.removed;
        acc.moduleLabels.modified +=
          group.comparison.summary.moduleLabels.modified;

        acc.sharedVariables.added +=
          group.comparison.summary.sharedVariables.added;
        acc.sharedVariables.removed +=
          group.comparison.summary.sharedVariables.removed;
        acc.sharedVariables.modified +=
          group.comparison.summary.sharedVariables.modified;

        acc.total += group.comparison.summary.total;
      }
      return acc;
    },
    {
      abilities: { added: 0, removed: 0, modified: 0 },
      modules: { added: 0, removed: 0, modified: 0 },
      moduleLabels: { added: 0, removed: 0, modified: 0 },
      sharedVariables: { added: 0, removed: 0, modified: 0 },
      total: 0,
    },
  );

  return {
    patchGroups,
    totalSummary,
  };
}

/**
 * 对比两个已发布版本的数据
 * @param currentVersionId 当前版本ID
 * @param baseVersionId 基准版本ID
 * @returns 对比结果
 */
export async function comparePublishedVersions(
  currentVersionId: string,
  baseVersionId: string,
): Promise<VersionDataComparison> {
  // 并行获取两个版本的完整数据
  const [currentVersionData, baseVersionData] = await Promise.all([
    getCompleteVersionData(currentVersionId),
    getCompleteVersionData(baseVersionId),
  ]);

  // 将已发布版本数据转换为草稿格式以便使用现有的对比函数
  const timestamp = dayjs().unix().toString();
  const currentAsDraft: CompleteDraftVersionData = {
    draftVersion: {
      id: "temp-current",
      createdAt: timestamp,
      updatedAt: timestamp,
      versionName: currentVersionData.version.versionName || "",
    },
    draftAbilities: currentVersionData.abilities.map((ability) => ({
      ...ability,
      draftVersionId: "temp-current",
    })) as unknown as DraftAbility[],
    draftModules: currentVersionData.modules.map((module) => ({
      ...module,
      draftVersionId: "temp-current",
    })) as unknown as DraftModule[],
    draftModuleLabels: currentVersionData.moduleLabels.map((label) => ({
      ...label,
      draftVersionId: "temp-current",
    })) as unknown as DraftModuleLabel[],
    draftSharedVariables: currentVersionData.sharedVariables.map(
      (variable) => ({
        ...variable,
        draftVersionId: "temp-current",
      }),
    ) as unknown as DraftSharedVariable[],
  };

  return compareVersionData(currentAsDraft, baseVersionData);
}

/**
 * 对比草稿版本与已发布版本的数据
 * @param draftData 草稿数据
 * @param baseVersionId 基准版本ID（可选）
 * @returns 对比结果
 */
export async function compareDraftWithPublished(
  draftData: CompleteDraftVersionData,
  baseVersionId?: string,
): Promise<VersionDataComparison> {
  // 获取远程数据（基准版本数据）
  let remoteData: CompleteVersionData = {
    version: {
      promptVersionId: "",
      versionName: "",
      version: "",
      status: PromptVersionStatus.SUBMIT,
      remark: "",
    },
    abilities: [],
    modules: [],
    moduleLabels: [],
    sharedVariables: [],
  };

  if (baseVersionId) {
    try {
      remoteData = await getCompleteVersionData(baseVersionId);
    } catch (err) {
      console.log("获取基准版本数据失败:", err);
    }
  }

  return compareVersionData(draftData, remoteData);
}

/**
 * 生成标签页配置
 * @param comparison 对比结果
 * @returns 标签页配置数组
 */
export function generateTabConfigs(comparison: VersionDataComparison) {
  const tabConfigs = [
    {
      key: "abilities",
      label: "能力",
      items: comparison.abilities,
      count:
        comparison.summary.abilities.added +
        comparison.summary.abilities.removed +
        comparison.summary.abilities.modified,
    },
    {
      key: "modules",
      label: "模块",
      items: comparison.modules,
      count:
        comparison.summary.modules.added +
        comparison.summary.modules.removed +
        comparison.summary.modules.modified,
    },
    {
      key: "moduleLabels",
      label: "模块标签",
      items: comparison.moduleLabels,
      count:
        comparison.summary.moduleLabels.added +
        comparison.summary.moduleLabels.removed +
        comparison.summary.moduleLabels.modified,
    },
    {
      key: "sharedVariables",
      label: "共享变量",
      items: comparison.sharedVariables,
      count:
        comparison.summary.sharedVariables.added +
        comparison.summary.sharedVariables.removed +
        comparison.summary.sharedVariables.modified,
    },
  ];

  // 过滤出有变更的标签页
  return tabConfigs.filter((tab) => tab.count > 0);
}

/**
 * 检查是否有任何变更
 * @param comparison 对比结果
 * @returns 是否有变更
 */
export function hasAnyChanges(comparison: VersionDataComparison): boolean {
  return comparison.summary.total > 0;
}
