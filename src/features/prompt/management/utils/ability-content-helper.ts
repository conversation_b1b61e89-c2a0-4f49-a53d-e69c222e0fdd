import { arrayMove } from "@dnd-kit/sortable";
import {
  AbilityReferenceModuleNodeType,
  parseAbilityContent,
  stringifyAbilityNode,
} from "../schemas/node";
import { UniversalModule } from "../types";

/**
 * 向能力项中添加模块引用
 * @param abilityContentString 能力内容的 JSON 字符串
 * @param abilityItemType 能力项类型
 * @param module 要添加的模块
 * @returns 更新后的能力内容 JSON 字符串
 */
export const addModuleToAbilityItem = (
  abilityContentString: string,
  abilityItemType: string,
  module: UniversalModule,
): string => {
  // 解析能力内容
  const parseResult = parseAbilityContent(abilityContentString);

  if (!parseResult.success) {
    console.error("Failed to parse ability content:", parseResult.error);
    return abilityContentString;
  }

  const abilityNode = parseResult.data;

  // 查找对应的能力项
  const abilityItem = abilityNode.content.find(
    (item) => item.attrs.type === abilityItemType,
  );

  if (!abilityItem) {
    console.error(`Ability item type "${abilityItemType}" not found`);
    return abilityContentString;
  }

  // 创建模块引用节点
  const moduleReferenceNode: AbilityReferenceModuleNodeType = {
    type: "module" as const,
    attrs: {
      id: module.id,
    },
  };

  // 添加模块引用到能力项
  abilityItem.content.push(moduleReferenceNode);

  // 序列化并返回
  return stringifyAbilityNode(abilityNode);
};

/**
 * 从能力项中移除模块引用
 * @param abilityContentString 能力内容的 JSON 字符串
 * @param abilityItemType 能力项类型
 * @param moduleId 要移除的模块ID
 * @returns 更新后的能力内容 JSON 字符串
 */
export const removeModuleFromAbilityItem = (
  abilityContentString: string,
  abilityItemType: string,
  moduleId: string,
): string => {
  // 解析能力内容
  const parseResult = parseAbilityContent(abilityContentString);

  if (!parseResult.success) {
    console.error("Failed to parse ability content:", parseResult.error);
    return abilityContentString;
  }

  const abilityNode = parseResult.data;

  // 查找对应的能力项
  const abilityItem = abilityNode.content.find(
    (item) => item.attrs.type === abilityItemType,
  );

  if (!abilityItem) {
    console.error(`Ability item type "${abilityItemType}" not found`);
    return abilityContentString;
  }

  // 移除指定的模块引用
  abilityItem.content = abilityItem.content.filter(
    (module) => module.attrs.id !== moduleId,
  );

  // 序列化并返回
  return stringifyAbilityNode(abilityNode);
};

/**
 * 从能力内容中移除对指定模块的引用
 *
 * @param abilityContent
 * @param moduleId
 * @returns
 */
export const removeModuleFromAbilityContent = (
  abilityContent: string,
  moduleId: string,
) => {
  // 解析能力内容
  const parseResult = parseAbilityContent(abilityContent);

  if (!parseResult.success) {
    console.error("Failed to parse ability content:", parseResult.error);
    return abilityContent;
  }

  const abilityNode = parseResult.data;

  // 遍历所有能力项
  abilityNode.content.forEach((abilityItem) => {
    // 移除指定的模块引用
    abilityItem.content = abilityItem.content.filter(
      (module) => module.attrs.id !== moduleId,
    );
  });

  // 序列化并返回
  return stringifyAbilityNode(abilityNode);
};

/**
 * 重新排序能力项中的模块
 * @param abilityContentString 能力内容的 JSON 字符串
 * @param abilityItemType 能力项类型
 * @param activeIndex 被拖拽模块的原始索引
 * @param overIndex 目标位置的索引
 * @returns 更新后的能力内容 JSON 字符串
 */
export const reorderModulesInAbilityItem = (
  abilityContentString: string,
  abilityItemType: string,
  activeIndex: number,
  overIndex: number,
): string => {
  // 解析能力内容
  const parseResult = parseAbilityContent(abilityContentString);

  if (!parseResult.success) {
    console.error("Failed to parse ability content:", parseResult.error);
    return abilityContentString;
  }

  const abilityNode = parseResult.data;

  // 查找对应的能力项
  const abilityItem = abilityNode.content.find(
    (item) => item.attrs.type === abilityItemType,
  );

  if (!abilityItem) {
    console.error(`Ability item type "${abilityItemType}" not found`);
    return abilityContentString;
  }

  // 使用 arrayMove 进行排序
  abilityItem.content = arrayMove(abilityItem.content, activeIndex, overIndex);

  // 序列化并返回
  return stringifyAbilityNode(abilityNode);
};
