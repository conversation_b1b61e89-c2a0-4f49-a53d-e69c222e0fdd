import { applyPatch, compare } from "fast-json-patch";
import {
  ChangePatchGroup,
  ChangeStatistics,
  ChangeTrackerConfig,
  ChangeTrackingResult,
  PatchApplicationResult,
  VersionChangeContent,
  VersionDataSnapshot,
} from "./types";

/**
 * 版本变更跟踪器
 * 使用 JSON Patch 记录和管理版本数据的变更历史
 */
export class VersionChangeTracker {
  private config: ChangeTrackerConfig;

  constructor(config: ChangeTrackerConfig = {}) {
    this.config = {
      debug: false,
      timestampGenerator: () => new Date().toISOString(),
      ...config,
    };
  }

  /**
   * 创建初始变更内容
   * @param baseSnapshot 基准版本快照
   * @param currentSnapshot 当前版本快照
   * @param metadata 元数据
   * @returns 变更跟踪结果
   */
  createInitialChangeContent(
    baseSnapshot: VersionDataSnapshot,
    currentSnapshot: VersionDataSnapshot,
    metadata: {
      baseVersionId?: string;
      baseVersion?: string;
    } = {},
  ): ChangeTrackingResult {
    try {
      const normalizedBase = this.normalizeSnapshot(baseSnapshot);
      const normalizedCurrent = this.normalizeSnapshot(currentSnapshot);

      const operations = compare(normalizedBase, normalizedCurrent);
      const timestamp = this.config.timestampGenerator!();

      const changeContent: VersionChangeContent = {
        baseSnapshot: { ...baseSnapshot },
        patches:
          operations.length > 0
            ? [
                {
                  operations,
                  timestamp,
                  description: "初始变更",
                },
              ]
            : [],
        metadata: {
          baseVersionId: metadata.baseVersionId,
          baseVersion: metadata.baseVersion,
          createdAt: timestamp,
          lastModified: timestamp,
        },
      };

      this.log("创建初始变更内容", { operationsCount: operations.length });

      return {
        success: true,
        changeContent,
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "未知错误";
      this.log("创建初始变更内容失败", { error: errorMessage });

      return {
        success: false,
        error: `创建初始变更内容失败: ${errorMessage}`,
      };
    }
  }

  /**
   * 更新变更内容
   * @param existingChangeContent 现有变更内容
   * @param currentSnapshot 当前版本快照
   * @param description 变更描述
   * @returns 变更跟踪结果
   */
  updateChangeContent(
    existingChangeContent: VersionChangeContent,
    currentSnapshot: VersionDataSnapshot,
    description?: string,
  ): ChangeTrackingResult {
    try {
      // 重建上次的状态
      const lastStateResult = this.applyAllPatches(
        existingChangeContent.baseSnapshot,
        existingChangeContent.patches,
      );

      if (!lastStateResult.success) {
        return {
          success: false,
          error: `重建上次状态失败: ${lastStateResult.error}`,
        };
      }

      const normalizedLastState = this.normalizeSnapshot(lastStateResult.data!);
      const normalizedCurrentState = this.normalizeSnapshot(currentSnapshot);

      const operations = compare(normalizedLastState, normalizedCurrentState);

      // 如果没有变更，返回原有内容
      if (operations.length === 0) {
        this.log("没有检测到变更");
        return {
          success: true,
          changeContent: existingChangeContent,
        };
      }

      const timestamp = this.config.timestampGenerator!();
      const patchGroup: ChangePatchGroup = {
        operations,
        timestamp,
        description:
          description || `变更 ${existingChangeContent.patches.length + 1}`,
      };

      const updatedChangeContent: VersionChangeContent = {
        ...existingChangeContent,
        patches: [...existingChangeContent.patches, patchGroup],
        metadata: {
          ...existingChangeContent.metadata,
          lastModified: timestamp,
        },
      };

      this.log("更新变更内容", {
        operationsCount: operations.length,
        totalPatchGroups: updatedChangeContent.patches.length,
      });

      return {
        success: true,
        changeContent: updatedChangeContent,
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "未知错误";
      this.log("更新变更内容失败", { error: errorMessage });

      return {
        success: false,
        error: `更新变更内容失败: ${errorMessage}`,
      };
    }
  }

  /**
   * 应用所有补丁
   * @param baseSnapshot 基准快照
   * @param patches 补丁组列表
   * @returns 补丁应用结果
   */
  applyAllPatches(
    baseSnapshot: VersionDataSnapshot,
    patches: ChangePatchGroup[],
  ): PatchApplicationResult {
    try {
      let currentState = { ...baseSnapshot };

      for (let i = 0; i < patches.length; i++) {
        const patchGroup = patches[i];

        try {
          const result = applyPatch(currentState, patchGroup.operations);
          currentState = result.newDocument;
        } catch (error) {
          return {
            success: false,
            error: `应用第 ${i + 1} 个补丁组失败: ${error instanceof Error ? error.message : "未知错误"}`,
          };
        }
      }

      this.log("成功应用所有补丁", { patchGroupsCount: patches.length });

      return {
        success: true,
        data: currentState,
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "未知错误";
      this.log("应用补丁失败", { error: errorMessage });

      return {
        success: false,
        error: `应用补丁失败: ${errorMessage}`,
      };
    }
  }

  /**
   * 获取变更统计信息
   * @param changeContent 变更内容
   * @returns 变更统计信息
   */
  getChangeStatistics(changeContent: VersionChangeContent): ChangeStatistics {
    const operationsByType: Record<string, number> = {};
    const changesByDataType = {
      abilities: 0,
      modules: 0,
      moduleLabels: 0,
      sharedVariables: 0,
    };

    let totalOperations = 0;

    for (const patchGroup of changeContent.patches) {
      totalOperations += patchGroup.operations.length;

      for (const operation of patchGroup.operations) {
        // 统计操作类型
        operationsByType[operation.op] =
          (operationsByType[operation.op] || 0) + 1;

        // 统计数据类型
        const path = operation.path;
        if (path.startsWith("/abilities")) {
          changesByDataType.abilities++;
        } else if (path.startsWith("/modules")) {
          changesByDataType.modules++;
        } else if (path.startsWith("/moduleLabels")) {
          changesByDataType.moduleLabels++;
        } else if (path.startsWith("/sharedVariables")) {
          changesByDataType.sharedVariables++;
        }
      }
    }

    return {
      totalPatchGroups: changeContent.patches.length,
      totalOperations,
      operationsByType,
      changesByDataType,
    };
  }

  /**
   * 标准化快照数据
   * @param snapshot 原始快照
   * @returns 标准化后的快照
   */
  private normalizeSnapshot(
    snapshot: VersionDataSnapshot,
  ): VersionDataSnapshot {
    return {
      abilities: [...snapshot.abilities].sort((a, b) =>
        a.id.localeCompare(b.id),
      ),
      modules: [...snapshot.modules].sort((a, b) => a.id.localeCompare(b.id)),
      moduleLabels: [...snapshot.moduleLabels].sort((a, b) =>
        a.id.localeCompare(b.id),
      ),
      sharedVariables: [...snapshot.sharedVariables].sort((a, b) =>
        a.id.localeCompare(b.id),
      ),
    };
  }

  /**
   * 调试日志
   * @param message 消息
   * @param data 数据
   */
  private log(message: string, data?: any): void {
    if (this.config.debug) {
      console.log(`[VersionChangeTracker] ${message}`, data);
    }
  }
}
