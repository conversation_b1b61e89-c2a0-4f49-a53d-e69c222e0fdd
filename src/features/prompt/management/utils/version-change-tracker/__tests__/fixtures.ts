import { Level } from "@/types/api/prompt";
import { VersionDataSnapshot } from "../types";

/**
 * 创建空的版本数据快照
 */
export function createEmptySnapshot(): VersionDataSnapshot {
  return {
    abilities: [],
    modules: [],
    moduleLabels: [],
    sharedVariables: [],
  };
}

/**
 * 创建基础版本数据快照
 */
export function createBaseSnapshot(): VersionDataSnapshot {
  return {
    abilities: [
      {
        id: "ability-1",
        name: "基础能力",
        content: '{"type":"doc","content":[]}',
      },
    ],
    modules: [
      {
        id: "module-1",
        name: "基础模块",
        level: Level.Product,
        prompt: "这是基础模块的提示词",
        moduleLabelIds: null,
      },
    ],
    moduleLabels: [
      {
        id: "label-1",
        name: "基础标签",
      },
    ],
    sharedVariables: [
      {
        id: "var-1",
        name: "基础变量",
        content: "基础变量内容",
        definition: "基础变量定义",
        level: Level.Product,
        promptName: "测试提示",
      },
    ],
  };
}

/**
 * 创建修改后的版本数据快照
 */
export function createModifiedSnapshot(): VersionDataSnapshot {
  return {
    abilities: [
      {
        id: "ability-1",
        name: "修改后的能力",
        content:
          '{"type":"doc","content":[{"type":"paragraph","content":[{"type":"text","text":"修改后的内容"}]}]}',
      },
      {
        id: "ability-2",
        name: "新增能力",
        content: '{"type":"doc","content":[]}',
      },
    ],
    modules: [
      {
        id: "module-1",
        name: "基础模块",
        level: Level.Product,
        prompt: "这是修改后的基础模块提示词",
        moduleLabelIds: "label-1",
      },
      {
        id: "module-2",
        name: "新增模块",
        level: Level.Shop,
        prompt: "这是新增模块的提示词",
        moduleLabelIds: null,
        scopeDetail: "店铺详情",
      },
    ],
    moduleLabels: [
      {
        id: "label-1",
        name: "基础标签",
      },
      {
        id: "label-2",
        name: "新增标签",
      },
    ],
    sharedVariables: [
      {
        id: "var-1",
        name: "基础变量",
        content: "修改后的基础变量内容",
        definition: "修改后的基础变量定义",
        level: Level.Product,
        promptName: "测试提示",
      },
    ],
  };
}

/**
 * 创建进一步修改的版本数据快照
 */
export function createFurtherModifiedSnapshot(): VersionDataSnapshot {
  return {
    abilities: [
      {
        id: "ability-3",
        name: "最终保留的能力",
        content:
          '{"type":"doc","content":[{"type":"paragraph","content":[{"type":"text","text":"最终的内容"}]}]}',
      },
    ],
    modules: [
      {
        id: "module-1",
        name: "重命名的基础模块",
        level: Level.Category,
        prompt: "这是再次修改后的基础模块提示词",
        moduleLabelIds: "label-1,label-2",
      },
      {
        id: "module-2",
        name: "新增模块",
        level: Level.Shop,
        prompt: "这是新增模块的提示词",
        moduleLabelIds: null,
        scopeDetail: "店铺详情",
      },
    ],
    moduleLabels: [
      {
        id: "label-1",
        name: "基础标签",
      },
      {
        id: "label-2",
        name: "新增标签",
      },
      {
        id: "label-3",
        name: "第三个标签",
      },
    ],
    sharedVariables: [
      {
        id: "var-1",
        name: "基础变量",
        content: "再次修改后的基础变量内容",
        definition: "再次修改后的基础变量定义",
        level: Level.Category,
        promptName: "测试提示",
        scopeDetail: "类目详情",
      },
      {
        id: "var-2",
        name: "新增共享变量",
        content: "新增共享变量内容",
        definition: "新增共享变量定义",
        level: Level.Product,
        promptName: "测试提示",
      },
    ],
  };
}

/**
 * 固定时间戳生成器（用于测试）
 */
export function createFixedTimestampGenerator(timestamps: string[]) {
  let index = 0;
  return () => {
    if (index >= timestamps.length) {
      throw new Error("时间戳用尽");
    }
    return timestamps[index++];
  };
}
