import { describe, expect, it } from "vitest";
import { VersionChangeTracker } from "../tracker";
import {
  createBaseSnapshot,
  createEmptySnapshot,
  createFixedTimestampGenerator,
  createFurtherModifiedSnapshot,
  createModifiedSnapshot,
} from "./fixtures";

describe("VersionChangeTracker", () => {
  const fixedTimestamps = [
    "2024-01-01T00:00:00.000Z",
    "2024-01-01T01:00:00.000Z",
    "2024-01-01T02:00:00.000Z",
  ];

  const createTracker = () =>
    new VersionChangeTracker({
      debug: false,
      timestampGenerator: createFixedTimestampGenerator([...fixedTimestamps]),
    });

  describe("createInitialChangeContent", () => {
    it("应该创建空变更内容（当基准和当前数据相同时）", () => {
      const tracker = createTracker();
      const baseSnapshot = createBaseSnapshot();
      const currentSnapshot = createBaseSnapshot();

      const result = tracker.createInitialChangeContent(
        baseSnapshot,
        currentSnapshot,
        { baseVersionId: "base-1", baseVersion: "1.0.0" },
      );

      expect(result.success).toBe(true);
      expect(result.changeContent).toBeDefined();
      expect(result.changeContent!.patches).toHaveLength(0);
      expect(result.changeContent!.metadata.baseVersionId).toBe("base-1");
      expect(result.changeContent!.metadata.baseVersion).toBe("1.0.0");
      expect(result.changeContent!.metadata.createdAt).toBe(fixedTimestamps[0]);
    });

    it("应该创建初始变更内容（从空到有数据）", () => {
      const tracker = createTracker();
      const baseSnapshot = createEmptySnapshot();
      const currentSnapshot = createBaseSnapshot();

      const result = tracker.createInitialChangeContent(
        baseSnapshot,
        currentSnapshot,
      );

      expect(result.success).toBe(true);
      expect(result.changeContent).toBeDefined();
      expect(result.changeContent!.patches).toHaveLength(1);

      const patchGroup = result.changeContent!.patches[0];
      expect(patchGroup.operations.length).toBeGreaterThan(0);
      expect(patchGroup.description).toBe("初始变更");
      expect(patchGroup.timestamp).toBe(fixedTimestamps[0]);
    });

    it("应该创建初始变更内容（有数据变更）", () => {
      const tracker = createTracker();
      const baseSnapshot = createBaseSnapshot();
      const currentSnapshot = createModifiedSnapshot();

      const result = tracker.createInitialChangeContent(
        baseSnapshot,
        currentSnapshot,
      );

      expect(result.success).toBe(true);
      expect(result.changeContent).toBeDefined();
      expect(result.changeContent!.patches).toHaveLength(1);

      const patchGroup = result.changeContent!.patches[0];
      expect(patchGroup.operations.length).toBeGreaterThan(0);

      // 验证包含各种类型的操作
      const operations = patchGroup.operations;
      const hasAdd = operations.some((op) => op.op === "add");
      const hasReplace = operations.some((op) => op.op === "replace");
      expect(hasAdd || hasReplace).toBe(true);
    });
  });

  describe("updateChangeContent", () => {
    it("应该检测到无变更并返回原有内容", () => {
      const tracker = createTracker();
      const baseSnapshot = createBaseSnapshot();
      const currentSnapshot = createModifiedSnapshot();

      // 创建初始变更内容
      const initialResult = tracker.createInitialChangeContent(
        baseSnapshot,
        currentSnapshot,
      );
      expect(initialResult.success).toBe(true);

      // 使用相同的当前快照更新（无变更）
      const updateResult = tracker.updateChangeContent(
        initialResult.changeContent!,
        currentSnapshot,
      );

      expect(updateResult.success).toBe(true);
      expect(updateResult.changeContent).toEqual(initialResult.changeContent);
    });

    it("应该添加新的变更补丁", () => {
      const tracker = createTracker();
      const baseSnapshot = createBaseSnapshot();
      const firstSnapshot = createModifiedSnapshot();
      const secondSnapshot = createFurtherModifiedSnapshot();

      // 创建初始变更内容
      const initialResult = tracker.createInitialChangeContent(
        baseSnapshot,
        firstSnapshot,
      );
      expect(initialResult.success).toBe(true);

      // 添加新的变更
      const updateResult = tracker.updateChangeContent(
        initialResult.changeContent!,
        secondSnapshot,
        "第二次变更",
      );

      expect(updateResult.success).toBe(true);
      expect(updateResult.changeContent!.patches).toHaveLength(2);

      const secondPatch = updateResult.changeContent!.patches[1];
      expect(secondPatch.description).toBe("第二次变更");
      expect(secondPatch.timestamp).toBe(fixedTimestamps[1]);
      expect(secondPatch.operations.length).toBeGreaterThan(0);
    });

    it("应该正确更新元数据", () => {
      const tracker = createTracker();
      const baseSnapshot = createBaseSnapshot();
      const firstSnapshot = createModifiedSnapshot();
      const secondSnapshot = createFurtherModifiedSnapshot();

      const initialResult = tracker.createInitialChangeContent(
        baseSnapshot,
        firstSnapshot,
      );
      const updateResult = tracker.updateChangeContent(
        initialResult.changeContent!,
        secondSnapshot,
      );

      expect(updateResult.success).toBe(true);
      expect(updateResult.changeContent!.metadata.createdAt).toBe(
        fixedTimestamps[0],
      );
      expect(updateResult.changeContent!.metadata.lastModified).toBe(
        fixedTimestamps[1],
      );
    });
  });

  describe("applyAllPatches", () => {
    it("应该正确应用所有补丁", () => {
      const tracker = createTracker();

      // 使用简单的测试数据
      const baseSnapshot = createEmptySnapshot();
      const firstSnapshot = createBaseSnapshot();

      // 创建变更内容（从空到基础数据）
      const initialResult = tracker.createInitialChangeContent(
        baseSnapshot,
        firstSnapshot,
      );

      // 应用补丁
      const applyResult = tracker.applyAllPatches(
        initialResult.changeContent!.baseSnapshot,
        initialResult.changeContent!.patches,
      );

      expect(applyResult.success).toBe(true);
      expect(applyResult.data).toBeDefined();

      // 验证最终结果与预期一致
      const finalData = applyResult.data!;
      expect(finalData.abilities).toHaveLength(firstSnapshot.abilities.length);
      expect(finalData.modules).toHaveLength(firstSnapshot.modules.length);
      expect(finalData.moduleLabels).toHaveLength(
        firstSnapshot.moduleLabels.length,
      );
      expect(finalData.sharedVariables).toHaveLength(
        firstSnapshot.sharedVariables.length,
      );

      // 验证具体内容
      expect(finalData.abilities[0].name).toBe(firstSnapshot.abilities[0].name);
      expect(finalData.modules[0].name).toBe(firstSnapshot.modules[0].name);
    });

    it("应该处理空补丁列表", () => {
      const tracker = createTracker();
      const baseSnapshot = createBaseSnapshot();

      const result = tracker.applyAllPatches(baseSnapshot, []);

      expect(result.success).toBe(true);
      expect(result.data).toEqual(baseSnapshot);
    });
  });

  describe("getChangeStatistics", () => {
    it("应该正确计算变更统计信息", () => {
      const tracker = createTracker();
      const baseSnapshot = createBaseSnapshot();
      const firstSnapshot = createModifiedSnapshot();
      const secondSnapshot = createFurtherModifiedSnapshot();

      // 创建包含多个变更的内容
      const initialResult = tracker.createInitialChangeContent(
        baseSnapshot,
        firstSnapshot,
      );
      const updateResult = tracker.updateChangeContent(
        initialResult.changeContent!,
        secondSnapshot,
      );

      const statistics = tracker.getChangeStatistics(
        updateResult.changeContent!,
      );

      expect(statistics.totalPatchGroups).toBe(2);
      expect(statistics.totalOperations).toBeGreaterThan(0);
      expect(statistics.operationsByType).toBeDefined();
      expect(statistics.changesByDataType).toBeDefined();

      // 验证数据类型统计
      const { changesByDataType } = statistics;
      expect(changesByDataType.abilities).toBeGreaterThanOrEqual(0);
      expect(changesByDataType.modules).toBeGreaterThanOrEqual(0);
      expect(changesByDataType.moduleLabels).toBeGreaterThanOrEqual(0);
      expect(changesByDataType.sharedVariables).toBeGreaterThanOrEqual(0);
    });

    it("应该处理空变更内容", () => {
      const tracker = createTracker();
      const baseSnapshot = createBaseSnapshot();

      const result = tracker.createInitialChangeContent(
        baseSnapshot,
        baseSnapshot,
      );
      const statistics = tracker.getChangeStatistics(result.changeContent!);

      expect(statistics.totalPatchGroups).toBe(0);
      expect(statistics.totalOperations).toBe(0);
      expect(Object.keys(statistics.operationsByType)).toHaveLength(0);
    });
  });

  describe("错误处理", () => {
    it("应该处理无效的补丁应用", () => {
      const tracker = createTracker();
      const baseSnapshot = createBaseSnapshot();

      // 创建一个无效的补丁
      const invalidPatches = [
        {
          operations: [
            { op: "replace" as const, path: "/invalid/path", value: "test" },
          ],
          timestamp: "2024-01-01T00:00:00.000Z",
          description: "无效补丁",
        },
      ];

      const result = tracker.applyAllPatches(baseSnapshot, invalidPatches);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });

    it("应该处理updateChangeContent中的补丁应用失败", () => {
      const tracker = createTracker();
      const baseSnapshot = createBaseSnapshot();

      // 创建一个包含无效补丁的变更内容
      const invalidChangeContent = {
        baseSnapshot,
        patches: [
          {
            operations: [
              { op: "replace" as const, path: "/invalid/path", value: "test" },
            ],
            timestamp: "2024-01-01T00:00:00.000Z",
            description: "无效补丁",
          },
        ],
        metadata: {
          createdAt: "2024-01-01T00:00:00.000Z",
          lastModified: "2024-01-01T00:00:00.000Z",
        },
      };

      const result = tracker.updateChangeContent(
        invalidChangeContent,
        createModifiedSnapshot(),
      );

      expect(result.success).toBe(false);
      expect(result.error).toContain("重建上次状态失败");
    });
  });

  describe("数据标准化", () => {
    it("应该正确处理数据排序", () => {
      const tracker = createTracker();

      // 创建顺序不同的快照
      const snapshot1 = {
        abilities: [
          { id: "b", name: "B", content: "", promptVersionId: "v1" },
          { id: "a", name: "A", content: "", promptVersionId: "v1" },
        ],
        modules: [],
        moduleLabels: [],
        sharedVariables: [],
      };

      const snapshot2 = {
        abilities: [
          { id: "a", name: "A", content: "", promptVersionId: "v1" },
          { id: "b", name: "B", content: "", promptVersionId: "v1" },
        ],
        modules: [],
        moduleLabels: [],
        sharedVariables: [],
      };

      const result = tracker.createInitialChangeContent(snapshot1, snapshot2);

      expect(result.success).toBe(true);
      // 由于数据标准化，相同内容不同顺序应该被认为是相同的
      expect(result.changeContent!.patches).toHaveLength(0);
    });
  });
});
