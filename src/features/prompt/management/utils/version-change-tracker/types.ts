import { Operation } from "fast-json-patch";
import { Level } from "@/types/api/prompt";

/**
 * 精简版能力数据（只包含用户关心的字段）
 */
export interface SlimAbility {
  id: string;
  name: string;
  content: string;
}

/**
 * 精简版模块数据（只包含用户关心的字段）
 */
export interface SlimModule {
  id: string;
  name: string;
  level: Level;
  prompt: string;
  moduleLabelIds?: string | null;
  scopeDetail?: string;
}

/**
 * 精简版模块标签数据（只包含用户关心的字段）
 */
export interface SlimModuleLabel {
  id: string;
  name: string;
}

/**
 * 精简版共享变量数据（只包含用户关心的字段）
 */
export interface SlimSharedVariable {
  id: string;
  name: string;
  content: string;
  definition: string;
  level: Level;
  scopeDetail?: string;
  promptName: string;
}

/**
 * 版本数据快照
 */
export interface VersionDataSnapshot {
  abilities: SlimAbility[];
  modules: SlimModule[];
  moduleLabels: SlimModuleLabel[];
  sharedVariables: SlimSharedVariable[];
}

/**
 * 变更补丁组
 */
export interface ChangePatchGroup {
  /** 补丁操作列表 */
  operations: Operation[];
  /** 变更时间戳 */
  timestamp: string;
  /** 变更描述 */
  description?: string;
}

/**
 * 版本变更内容
 */
export interface VersionChangeContent {
  /** 基准版本的完整数据快照 */
  baseSnapshot: VersionDataSnapshot;

  /** 变更补丁数组，每个元素代表一次保存操作 */
  patches: ChangePatchGroup[];

  /** 元数据 */
  metadata: {
    baseVersionId?: string;
    baseVersion?: string;
    createdAt: string;
    lastModified: string;
  };
}

/**
 * 变更跟踪器配置
 */
export interface ChangeTrackerConfig {
  /** 是否启用调试日志 */
  debug?: boolean;
  /** 自定义时间戳生成函数 */
  timestampGenerator?: () => string;
}

/**
 * 变更跟踪结果
 */
export interface ChangeTrackingResult {
  /** 是否成功 */
  success: boolean;
  /** 变更内容（成功时） */
  changeContent?: VersionChangeContent;
  /** 错误信息（失败时） */
  error?: string;
}

/**
 * 补丁应用结果
 */
export interface PatchApplicationResult {
  /** 是否成功 */
  success: boolean;
  /** 应用后的数据（成功时） */
  data?: VersionDataSnapshot;
  /** 错误信息（失败时） */
  error?: string;
}

/**
 * 变更统计信息
 */
export interface ChangeStatistics {
  /** 总补丁组数量 */
  totalPatchGroups: number;
  /** 总操作数量 */
  totalOperations: number;
  /** 按操作类型统计 */
  operationsByType: Record<string, number>;
  /** 按数据类型统计 */
  changesByDataType: {
    abilities: number;
    modules: number;
    moduleLabels: number;
    sharedVariables: number;
  };
}
