/**
 * 模块内容处理工具函数单元测试
 */

import { describe, expect, it } from "vitest";
import { removeSharedVariableFromModuleContent } from "../module-content-helper";

describe("module-content-helper", () => {
  describe("removeSharedVariableFromModuleContent", () => {
    it("应该从模块内容中移除指定的共享变量引用节点", () => {
      const sharedVariableId = "shared-var-1";
      const moduleContent = JSON.stringify({
        type: "module",
        attrs: { name: "测试模块" },
        content: [
          {
            type: "prompt",
            content: [
              {
                type: "text",
                text: "这是文本内容",
              },
              {
                type: "sharedVariableReference",
                attrs: { sharedVariableId: sharedVariableId },
              },
              {
                type: "text",
                text: "这是更多文本内容",
              },
            ],
          },
        ],
      });

      const result = removeSharedVariableFromModuleContent(
        moduleContent,
        sharedVariableId,
      );

      const parsedResult = JSON.parse(result);
      expect(parsedResult.content[0].content).toHaveLength(2);
      expect(parsedResult.content[0].content[0].type).toBe("text");
      expect(parsedResult.content[0].content[1].type).toBe("text");

      // 确保没有共享变量引用节点
      const hasSharedVariableReference = parsedResult.content[0].content.some(
        (item: any) => item.type === "sharedVariableReference",
      );
      expect(hasSharedVariableReference).toBe(false);
    });

    it("应该移除多个相同的共享变量引用节点", () => {
      const sharedVariableId = "shared-var-1";
      const moduleContent = JSON.stringify({
        type: "module",
        attrs: { name: "测试模块" },
        content: [
          {
            type: "prompt",
            content: [
              {
                type: "sharedVariableReference",
                attrs: { sharedVariableId: sharedVariableId },
              },
              {
                type: "text",
                text: "中间文本",
              },
              {
                type: "sharedVariableReference",
                attrs: { sharedVariableId: sharedVariableId },
              },
            ],
          },
        ],
      });

      const result = removeSharedVariableFromModuleContent(
        moduleContent,
        sharedVariableId,
      );

      const parsedResult = JSON.parse(result);
      expect(parsedResult.content[0].content).toHaveLength(1);
      expect(parsedResult.content[0].content[0].type).toBe("text");
      expect(parsedResult.content[0].content[0].text).toBe("中间文本");
    });

    it("应该保留其他共享变量的引用节点", () => {
      const targetVariableId = "shared-var-1";
      const otherVariableId = "shared-var-2";
      const moduleContent = JSON.stringify({
        type: "module",
        attrs: { name: "测试模块" },
        content: [
          {
            type: "prompt",
            content: [
              {
                type: "sharedVariableReference",
                attrs: { sharedVariableId: targetVariableId },
              },
              {
                type: "sharedVariableReference",
                attrs: { sharedVariableId: otherVariableId },
              },
            ],
          },
        ],
      });

      const result = removeSharedVariableFromModuleContent(
        moduleContent,
        targetVariableId,
      );

      const parsedResult = JSON.parse(result);
      expect(parsedResult.content[0].content).toHaveLength(1);
      expect(parsedResult.content[0].content[0].type).toBe(
        "sharedVariableReference",
      );
      expect(parsedResult.content[0].content[0].attrs.sharedVariableId).toBe(
        otherVariableId,
      );
    });

    it("当模块内容不包含指定共享变量引用时应该返回原内容", () => {
      const sharedVariableId = "shared-var-1";
      const moduleContent = JSON.stringify({
        type: "module",
        attrs: { name: "测试模块" },
        content: [
          {
            type: "prompt",
            content: [
              {
                type: "text",
                text: "这是文本内容",
              },
              {
                type: "sharedVariableReference",
                attrs: { sharedVariableId: "other-var" },
              },
            ],
          },
        ],
      });

      const result = removeSharedVariableFromModuleContent(
        moduleContent,
        sharedVariableId,
      );

      expect(result).toBe(moduleContent);
    });

    it("应该处理嵌套结构中的共享变量引用", () => {
      const sharedVariableId = "shared-var-1";
      const moduleContent = JSON.stringify({
        type: "module",
        attrs: { name: "测试模块" },
        content: [
          {
            type: "prompt",
            content: [
              {
                type: "someContainer",
                content: [
                  {
                    type: "sharedVariableReference",
                    attrs: { sharedVariableId: sharedVariableId },
                  },
                  {
                    type: "text",
                    text: "嵌套文本",
                  },
                ],
              },
            ],
          },
        ],
      });

      const result = removeSharedVariableFromModuleContent(
        moduleContent,
        sharedVariableId,
      );

      const parsedResult = JSON.parse(result);
      const containerContent = parsedResult.content[0].content[0].content;
      expect(containerContent).toHaveLength(1);
      expect(containerContent[0].type).toBe("text");
      expect(containerContent[0].text).toBe("嵌套文本");
    });

    it("当输入为空字符串时应该返回原内容", () => {
      const result = removeSharedVariableFromModuleContent("", "shared-var-1");
      expect(result).toBe("");
    });

    it("当输入为无效JSON时应该返回原内容", () => {
      const invalidJson = "{ invalid json }";
      const result = removeSharedVariableFromModuleContent(
        invalidJson,
        "shared-var-1",
      );
      expect(result).toBe(invalidJson);
    });

    it("当模块内容格式不正确时应该返回原内容", () => {
      const invalidModuleContent = JSON.stringify({
        type: "notModule",
        content: "invalid structure",
      });

      const result = removeSharedVariableFromModuleContent(
        invalidModuleContent,
        "shared-var-1",
      );

      expect(result).toBe(invalidModuleContent);
    });
  });
});
