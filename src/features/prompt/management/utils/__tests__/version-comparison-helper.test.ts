import { Level } from "@/types/api/prompt";
import {
  VersionChangeContent,
  VersionDataSnapshot,
} from "../version-change-tracker";
import { buildHistoryComparisonFromChangeContent } from "../version-comparison-helper";

describe("buildHistoryComparisonFromChangeContent", () => {
  // 创建基础快照数据
  const createBaseSnapshot = (): VersionDataSnapshot => ({
    abilities: [
      {
        id: "ability-1",
        name: "能力1",
        content: "原始能力内容1",
      },
    ],
    modules: [
      {
        id: "module-1",
        name: "模块1",
        level: Level.Product,
        prompt: "原始模块内容1",
        moduleLabelIds: null,
        scopeDetail: undefined,
      },
    ],
    moduleLabels: [
      {
        id: "label-1",
        name: "标签1",
      },
    ],
    sharedVariables: [
      {
        id: "var-1",
        name: "变量1",
        content: "原始变量内容1",
        definition: "变量定义1",
        level: Level.Product,
        scopeDetail: undefined,
        promptName: "变量1",
      },
    ],
  });

  // 创建测试用的变更内容
  const createTestChangeContent = (): VersionChangeContent => {
    const baseSnapshot = createBaseSnapshot();

    return {
      baseSnapshot,
      patches: [
        {
          operations: [
            {
              op: "replace",
              path: "/abilities/0/content",
              value: "修改后的能力内容1",
            },
          ],
          timestamp: "2024-01-01T10:00:00.000Z",
          description: "第一次修改",
        },
        {
          operations: [
            {
              op: "add",
              path: "/modules/-",
              value: {
                id: "module-2",
                name: "新模块2",
                level: Level.Shop,
                prompt: "新模块内容2",
                moduleLabelIds: null,
                scopeDetail: undefined,
              },
            },
          ],
          timestamp: "2024-01-01T11:00:00.000Z",
          description: "第二次修改",
        },
        {
          operations: [
            {
              op: "remove",
              path: "/sharedVariables/0",
            },
          ],
          timestamp: "2024-01-01T12:00:00.000Z",
          description: "第三次修改",
        },
      ],
      metadata: {
        baseVersionId: "base-version-1",
        baseVersion: "1.0.0",
        createdAt: "2024-01-01T09:00:00.000Z",
        lastModified: "2024-01-01T12:00:00.000Z",
      },
    };
  };

  it("应该正确构建版本历史对比结果", () => {
    const changeContent = createTestChangeContent();
    const result = buildHistoryComparisonFromChangeContent(changeContent);

    // 验证基本结构
    expect(result).toBeDefined();
    expect(result.patchGroups).toHaveLength(3);
    expect(result.totalSummary).toBeDefined();

    // 验证补丁组的基本信息
    expect(result.patchGroups[0].index).toBe(0);
    expect(result.patchGroups[0].timestamp).toBe("2024-01-01T10:00:00.000Z");
    expect(result.patchGroups[0].description).toBe("第一次修改");
    expect(result.patchGroups[0].hasChanges).toBe(true);

    expect(result.patchGroups[1].index).toBe(1);
    expect(result.patchGroups[1].timestamp).toBe("2024-01-01T11:00:00.000Z");
    expect(result.patchGroups[1].description).toBe("第二次修改");
    expect(result.patchGroups[1].hasChanges).toBe(true);

    expect(result.patchGroups[2].index).toBe(2);
    expect(result.patchGroups[2].timestamp).toBe("2024-01-01T12:00:00.000Z");
    expect(result.patchGroups[2].description).toBe("第三次修改");
    expect(result.patchGroups[2].hasChanges).toBe(true);
  });

  it("应该正确计算每个补丁组的变更内容", () => {
    const changeContent = createTestChangeContent();
    const result = buildHistoryComparisonFromChangeContent(changeContent);

    // 第一个补丁组：修改能力内容
    const firstPatch = result.patchGroups[0];
    expect(firstPatch.comparison.abilities).toHaveLength(1);
    expect(firstPatch.comparison.abilities[0].changeType).toBe("modified");
    expect(firstPatch.comparison.abilities[0].name).toBe("能力1");

    // 第二个补丁组：添加模块
    const secondPatch = result.patchGroups[1];
    expect(secondPatch.comparison.modules).toHaveLength(1);
    expect(secondPatch.comparison.modules[0].changeType).toBe("added");
    expect(secondPatch.comparison.modules[0].name).toBe("新模块2");

    // 第三个补丁组：删除共享变量
    const thirdPatch = result.patchGroups[2];
    expect(thirdPatch.comparison.sharedVariables).toHaveLength(1);
    expect(thirdPatch.comparison.sharedVariables[0].changeType).toBe("removed");
    expect(thirdPatch.comparison.sharedVariables[0].name).toBe("变量1");
  });

  it("应该正确计算总体统计信息", () => {
    const changeContent = createTestChangeContent();
    const result = buildHistoryComparisonFromChangeContent(changeContent);

    // 验证总体统计
    expect(result.totalSummary.abilities.modified).toBe(1);
    expect(result.totalSummary.modules.added).toBe(1);
    expect(result.totalSummary.sharedVariables.removed).toBe(1);
    expect(result.totalSummary.total).toBe(3);
  });

  it("应该处理空的补丁数组", () => {
    const changeContent: VersionChangeContent = {
      baseSnapshot: createBaseSnapshot(),
      patches: [],
      metadata: {
        baseVersionId: "base-version-1",
        baseVersion: "1.0.0",
        createdAt: "2024-01-01T09:00:00.000Z",
        lastModified: "2024-01-01T09:00:00.000Z",
      },
    };

    const result = buildHistoryComparisonFromChangeContent(changeContent);

    expect(result.patchGroups).toHaveLength(0);
    expect(result.totalSummary.total).toBe(0);
  });

  it("应该处理无效的补丁操作", () => {
    const changeContent: VersionChangeContent = {
      baseSnapshot: createBaseSnapshot(),
      patches: [
        {
          operations: [
            {
              op: "replace",
              path: "/invalid/path",
              value: "test",
            },
          ],
          timestamp: "2024-01-01T10:00:00.000Z",
          description: "无效操作",
        },
      ],
      metadata: {
        baseVersionId: "base-version-1",
        baseVersion: "1.0.0",
        createdAt: "2024-01-01T09:00:00.000Z",
        lastModified: "2024-01-01T10:00:00.000Z",
      },
    };

    // 应该跳过无效的补丁，不抛出错误
    const result = buildHistoryComparisonFromChangeContent(changeContent);

    // 由于补丁应用失败，该补丁组应该被跳过
    expect(result.patchGroups).toHaveLength(0);
  });

  it("应该按时间正序排列补丁组", () => {
    const changeContent = createTestChangeContent();
    const result = buildHistoryComparisonFromChangeContent(changeContent);

    // 验证时间戳是按正序排列的
    expect(result.patchGroups[0].timestamp).toBe("2024-01-01T10:00:00.000Z");
    expect(result.patchGroups[1].timestamp).toBe("2024-01-01T11:00:00.000Z");
    expect(result.patchGroups[2].timestamp).toBe("2024-01-01T12:00:00.000Z");
  });
});
