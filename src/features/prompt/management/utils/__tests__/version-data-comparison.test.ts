/**
 * 版本数据对比工具函数单元测试
 */

import { AbilityItemType, Level } from "@/types/api/prompt";
import { PromptVersionStatus } from "@/types/api/prompt-version";
import { describe, expect, it } from "vitest";
import {
  ChangeType,
  CompleteVersionData,
  CompleteDraftVersionData,
} from "../../types";
import { compareVersionData } from "../version-data-comparison";

describe("version-data-comparison", () => {
  // 测试数据
  const mockDraftData: CompleteDraftVersionData = {
    draftVersion: {
      id: "draft-version-1",
      versionName: "草稿版本",
      createdAt: "2024-01-01T00:00:00Z",
      updatedAt: "2024-01-01T00:00:00Z",
    },
    draftAbilities: [
      {
        id: "ability-1",
        name: "能力1",
        content: JSON.stringify({
          type: "ability",
          attrs: { name: "能力1" },
          content: [
            {
              type: "abilityItem",
              attrs: { type: AbilityItemType.Role },
              content: [{ type: "module", attrs: { id: "module-1" } }],
            },
          ],
        }),
        promptVersionId: "draft-version-1",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
      },
      {
        id: "ability-2",
        name: "新增能力",
        content: JSON.stringify({
          type: "ability",
          attrs: { name: "新增能力" },
          content: [
            {
              type: "abilityItem",
              attrs: { type: AbilityItemType.Background },
              content: [{ type: "module", attrs: { id: "module-2" } }],
            },
          ],
        }),
        promptVersionId: "draft-version-1",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
      },
    ],
    draftModules: [
      {
        id: "module-1",
        name: "模块1",
        prompt: JSON.stringify({
          type: "doc",
          content: [
            {
              type: "paragraph",
              content: [{ type: "text", text: "修改后的模块1内容" }],
            },
          ],
        }),
        level: Level.Product,
        promptVersionId: "draft-version-1",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
      },
      {
        id: "module-2",
        name: "新增模块",
        prompt: JSON.stringify({
          type: "doc",
          content: [
            {
              type: "paragraph",
              content: [{ type: "text", text: "新增模块内容" }],
            },
          ],
        }),
        level: Level.Product,
        promptVersionId: "draft-version-1",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
      },
    ],
    draftModuleLabels: [
      {
        id: "label-1",
        name: "标签1",
        promptVersionId: "draft-version-1",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
      },
      {
        id: "label-2",
        name: "新增标签",
        promptVersionId: "draft-version-1",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
      },
    ],
    draftSharedVariables: [
      {
        id: "var-1",
        name: "变量1",
        content: JSON.stringify({
          type: "doc",
          content: [
            {
              type: "paragraph",
              content: [{ type: "text", text: "修改后的变量1内容" }],
            },
          ],
        }),
        definition: "变量1定义",
        level: Level.Product,
        promptVersionId: "draft-version-1",
        promptName: "测试提示词",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
      },
      {
        id: "var-2",
        name: "新增变量",
        content: JSON.stringify({
          type: "doc",
          content: [
            {
              type: "paragraph",
              content: [{ type: "text", text: "新增变量内容" }],
            },
          ],
        }),
        definition: "新增变量定义",
        level: Level.Product,
        promptVersionId: "draft-version-1",
        promptName: "测试提示词",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
      },
    ],
  };

  const mockRemoteData: CompleteVersionData = {
    version: {
      promptVersionId: "remote-version-1",
      versionName: "远程版本",
      version: "1.0.0",
      status: PromptVersionStatus.ONLINE,
      remark: "",
    },
    abilities: [
      {
        id: "ability-1",
        name: "能力1",
        content: JSON.stringify({
          type: "ability",
          attrs: { name: "能力1" },
          content: [
            {
              type: "abilityItem",
              attrs: { type: AbilityItemType.Role },
              content: [
                { type: "module", attrs: { id: "module-1" } },
                { type: "module", attrs: { id: "module-3" } },
              ],
            },
          ],
        }),
        promptVersionId: "remote-version-1",
      },
      {
        id: "ability-3",
        name: "删除的能力",
        content: JSON.stringify({
          type: "ability",
          attrs: { name: "删除的能力" },
          content: [
            {
              type: "abilityItem",
              attrs: { type: AbilityItemType.Goals },
              content: [{ type: "module", attrs: { id: "module-4" } }],
            },
          ],
        }),
        promptVersionId: "remote-version-1",
      },
    ],
    modules: [
      {
        id: "module-1",
        name: "模块1",
        prompt: JSON.stringify({
          type: "doc",
          content: [
            {
              type: "paragraph",
              content: [{ type: "text", text: "原始模块1内容" }],
            },
          ],
        }),
        level: Level.Product,
        promptVersionId: "remote-version-1",
      },
      {
        id: "module-3",
        name: "删除的模块",
        prompt: JSON.stringify({
          type: "doc",
          content: [
            {
              type: "paragraph",
              content: [{ type: "text", text: "删除的模块内容" }],
            },
          ],
        }),
        level: Level.Product,
        promptVersionId: "remote-version-1",
      },
    ],
    moduleLabels: [
      {
        id: "label-1",
        name: "标签1",
        promptVersionId: "remote-version-1",
      },
      {
        id: "label-3",
        name: "删除的标签",
        promptVersionId: "remote-version-1",
      },
    ],
    sharedVariables: [
      {
        id: "var-1",
        name: "变量1",
        content: JSON.stringify({
          type: "doc",
          content: [
            {
              type: "paragraph",
              content: [{ type: "text", text: "原始变量1内容" }],
            },
          ],
        }),
        definition: "变量1定义",
        level: Level.Product,
        promptVersionId: "remote-version-1",
        scopeDetail: "{}",
        promptName: "测试提示词",
      },
      {
        id: "var-3",
        name: "删除的变量",
        content: JSON.stringify({
          type: "doc",
          content: [
            {
              type: "paragraph",
              content: [{ type: "text", text: "删除的变量内容" }],
            },
          ],
        }),
        definition: "删除的变量定义",
        level: Level.Product,
        promptVersionId: "remote-version-1",
        scopeDetail: "{}",
        promptName: "测试提示词",
      },
    ],
  };

  describe("compareVersionData", () => {
    it("应该正确对比版本数据", () => {
      const result = compareVersionData(mockDraftData, mockRemoteData);

      expect(result).toBeDefined();
      expect(result.abilities).toHaveLength(3); // 1个修改，1个新增，1个删除
      expect(result.modules).toHaveLength(3); // 1个修改，1个新增，1个删除
      expect(result.moduleLabels).toHaveLength(2); // 1个新增，1个删除
      expect(result.sharedVariables).toHaveLength(3); // 1个修改，1个新增，1个删除
    });

    it("应该正确识别新增的能力", () => {
      const result = compareVersionData(mockDraftData, mockRemoteData);
      const addedAbility = result.abilities.find(
        (ability) => ability.changeType === ChangeType.ADDED,
      );

      expect(addedAbility).toBeDefined();
      expect(addedAbility?.id).toBe("ability-2");
      expect(addedAbility?.name).toBe("新增能力");
      expect(addedAbility?.moduleChanges.added).toBe(1);
      expect(addedAbility?.newContent).toBeDefined();
      expect(addedAbility?.abilityItemChanges).toHaveLength(1);
      expect(addedAbility?.abilityItemChanges[0].type).toBe(
        AbilityItemType.Background,
      );
      expect(addedAbility?.abilityItemChanges[0].name).toBe("背景说明");
      expect(addedAbility?.abilityItemChanges[0].summary.added).toBe(1);
    });

    it("应该正确识别修改的能力", () => {
      const result = compareVersionData(mockDraftData, mockRemoteData);
      const modifiedAbility = result.abilities.find(
        (ability) => ability.changeType === ChangeType.MODIFIED,
      );

      expect(modifiedAbility).toBeDefined();
      expect(modifiedAbility?.id).toBe("ability-1");
      expect(modifiedAbility?.name).toBe("能力1");
      expect(modifiedAbility?.moduleChanges.added).toBe(0); // 没有新增模块
      expect(modifiedAbility?.moduleChanges.removed).toBe(1); // module-3 被删除
      expect(modifiedAbility?.moduleChanges.modified).toBe(1); // module-1 保持不变
      expect(modifiedAbility?.oldContent).toBeDefined();
      expect(modifiedAbility?.newContent).toBeDefined();
      expect(modifiedAbility?.abilityItemChanges).toHaveLength(1);
      expect(modifiedAbility?.abilityItemChanges[0].type).toBe(
        AbilityItemType.Role,
      );
      expect(modifiedAbility?.abilityItemChanges[0].summary.removed).toBe(1);
    });

    it("应该正确识别删除的能力", () => {
      const result = compareVersionData(mockDraftData, mockRemoteData);
      const removedAbility = result.abilities.find(
        (ability) => ability.changeType === ChangeType.REMOVED,
      );

      expect(removedAbility).toBeDefined();
      expect(removedAbility?.id).toBe("ability-3");
      expect(removedAbility?.name).toBe("删除的能力");
      expect(removedAbility?.moduleChanges.removed).toBe(1);
      expect(removedAbility?.oldContent).toBeDefined();
    });

    it("应该正确识别模块变更", () => {
      const result = compareVersionData(mockDraftData, mockRemoteData);

      const addedModule = result.modules.find(
        (module) => module.changeType === ChangeType.ADDED,
      );
      const modifiedModule = result.modules.find(
        (module) => module.changeType === ChangeType.MODIFIED,
      );
      const removedModule = result.modules.find(
        (module) => module.changeType === ChangeType.REMOVED,
      );

      expect(addedModule?.id).toBe("module-2");
      expect(modifiedModule?.id).toBe("module-1");
      expect(removedModule?.id).toBe("module-3");
    });

    it("应该正确识别模块标签变更", () => {
      const result = compareVersionData(mockDraftData, mockRemoteData);

      const addedLabel = result.moduleLabels.find(
        (label) => label.changeType === ChangeType.ADDED,
      );
      const removedLabel = result.moduleLabels.find(
        (label) => label.changeType === ChangeType.REMOVED,
      );

      expect(addedLabel?.id).toBe("label-2");
      expect(removedLabel?.id).toBe("label-3");
    });

    it("应该正确识别共享变量变更", () => {
      const result = compareVersionData(mockDraftData, mockRemoteData);

      const addedVariable = result.sharedVariables.find(
        (variable) => variable.changeType === ChangeType.ADDED,
      );
      const modifiedVariable = result.sharedVariables.find(
        (variable) => variable.changeType === ChangeType.MODIFIED,
      );
      const removedVariable = result.sharedVariables.find(
        (variable) => variable.changeType === ChangeType.REMOVED,
      );

      expect(addedVariable?.id).toBe("var-2");
      expect(modifiedVariable?.id).toBe("var-1");
      expect(removedVariable?.id).toBe("var-3");
    });

    it("应该正确计算统计信息", () => {
      const result = compareVersionData(mockDraftData, mockRemoteData);

      expect(result.summary.abilities.added).toBe(1);
      expect(result.summary.abilities.modified).toBe(1);
      expect(result.summary.abilities.removed).toBe(1);

      expect(result.summary.modules.added).toBe(1);
      expect(result.summary.modules.modified).toBe(1);
      expect(result.summary.modules.removed).toBe(1);

      expect(result.summary.moduleLabels.added).toBe(1);
      expect(result.summary.moduleLabels.removed).toBe(1);

      expect(result.summary.sharedVariables.added).toBe(1);
      expect(result.summary.sharedVariables.modified).toBe(1);
      expect(result.summary.sharedVariables.removed).toBe(1);

      expect(result.summary.total).toBe(11);
    });

    it("应该处理空数据的情况", () => {
      const emptyDraftData: CompleteDraftVersionData = {
        draftVersion: mockDraftData.draftVersion,
        draftAbilities: [],
        draftModules: [],
        draftModuleLabels: [],
        draftSharedVariables: [],
      };

      const emptyRemoteData: CompleteVersionData = {
        version: mockRemoteData.version,
        abilities: [],
        modules: [],
        moduleLabels: [],
        sharedVariables: [],
      };

      const result = compareVersionData(emptyDraftData, emptyRemoteData);

      expect(result.abilities).toHaveLength(0);
      expect(result.modules).toHaveLength(0);
      expect(result.moduleLabels).toHaveLength(0);
      expect(result.sharedVariables).toHaveLength(0);
      expect(result.summary.total).toBe(0);
    });

    it("应该处理无效的能力内容", () => {
      const invalidDraftData: CompleteDraftVersionData = {
        ...mockDraftData,
        draftAbilities: [
          {
            id: "ability-invalid",
            name: "无效能力",
            content: "invalid json",
            promptVersionId: "draft-version-1",
            createdAt: "2024-01-01T00:00:00Z",
            updatedAt: "2024-01-01T00:00:00Z",
          },
        ],
      };

      const result = compareVersionData(invalidDraftData, mockRemoteData);
      const addedAbility = result.abilities.find(
        (ability) => ability.id === "ability-invalid",
      );

      expect(addedAbility).toBeDefined();
      expect(addedAbility?.moduleChanges.added).toBe(0); // 无法解析，所以没有模块
    });
  });
});
