import { describe, expect, it } from "vitest";
import { isDraftId, isDraftVersion } from "../is-draft-data";
import { DraftVersion } from "../../types";
import { PromptVersion } from "@/types/api/prompt-version";

describe("is-draft-data", () => {
  describe("isDraftId", () => {
    it("应该正确识别草稿数据", () => {
      // 测试草稿数据
      const draftData = "draft-id";
      expect(isDraftId(draftData)).toBe(true);

      // 测试非草稿数据
      const nonDraftData = "123";
      expect(isDraftId(nonDraftData)).toBe(false);
    });

    it("应该处理边界情况", () => {
      // 测试 undefined 的数据
      const undefinedIsDraftData = undefined;
      expect(isDraftId(undefinedIsDraftData as any)).toBe(false);

      // 测试 null 值
      expect(isDraftId(null as any)).toBe(false);

      // 测试字符串值
      expect(isDraftId("true" as any)).toBe(false);

      // 测试数字值
      expect(isDraftId(1 as any)).toBe(false);
      expect(isDraftId(0 as any)).toBe(false);
    });
  });

  describe("isDraftVersion", () => {
    it("应该正确识别草稿版本", () => {
      // 创建草稿版本数据
      const draftVersion: DraftVersion = {
        id: "draft-test-id",
        versionName: "测试草稿版本",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
      };

      expect(isDraftVersion(draftVersion)).toBe(true);
    });

    it("应该正确识别非草稿版本", () => {
      // 创建服务端版本数据
      const serverVersion: PromptVersion = {
        promptVersionId: "server-version-id",
        versionName: "服务端版本",
        version: "1.0.0",
        status: "ONLINE" as any,
        remark: "测试版本",
        evaluationTasks: [],
      };

      expect(isDraftVersion(serverVersion)).toBe(false);
    });

    it("应该处理混合类型的版本数据", () => {
      // 测试具有 isDraft 字段但为 false 的版本
      const mixedVersion = {
        id: "mixed-id",
        isDraft: false,
        versionName: "混合版本",
        promptVersionId: "server-id",
      };

      expect(isDraftVersion(mixedVersion as any)).toBe(false);

      // 测试没有 isDraft 字段的版本
      const noIsDraftVersion = {
        id: "no-draft-field-id",
        versionName: "无草稿字段版本",
      };

      expect(isDraftVersion(noIsDraftVersion as any)).toBe(false);
    });
  });
});
