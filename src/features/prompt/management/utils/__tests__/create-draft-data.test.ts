import { Level } from "@/types/api/prompt";
import { PromptVersionStatus } from "@/types/api/prompt-version";
import { describe, expect, it } from "vitest";
import { CompleteVersionData } from "../../types";
import { createDraftData } from "../create-draft-data";

describe("createDraftData 创建草稿版本", () => {
  const mockSourceData: CompleteVersionData = {
    version: {
      promptVersionId: "source-version-1",
      versionName: "原始版本",
      version: "1.0.0",
      status: PromptVersionStatus.ONLINE,
      remark: "测试版本",
      evaluationTasks: [],
    },
    abilities: [
      {
        id: "ability-1",
        name: "能力1",
        content: "能力内容1",
        promptVersionId: "source-version-1",
      },
    ],
    modules: [
      {
        id: "module-1",
        name: "模块1",
        moduleLabelIds: "label-1,label-2",
        level: Level.Product,
        promptVersionId: "source-version-1",
        prompt: '{"type":"module"}',
        scopeDetail: "{}",
      },
    ],
    moduleLabels: [
      {
        id: "label-1",
        name: "标签1",
        promptVersionId: "source-version-1",
      },
      {
        id: "label-2",
        name: "标签2",
        promptVersionId: "source-version-1",
      },
    ],
    sharedVariables: [
      {
        id: "var-1",
        name: "变量1",
        content: "变量内容1",
        definition: "变量定义1",
        level: Level.Product,
        scopeDetail: "{}",
        promptName: "变量名1",
        promptVersionId: "source-version-1",
      },
    ],
  };

  const sourceVersionId = "source-version-1";
  const draftName = "测试草稿";
  const draftId = "draft-uuid-123";
  const timestamp = "2024-01-01T00:00:00Z";

  it("应该正确创建草稿版本数据", () => {
    const result = createDraftData(
      mockSourceData,
      sourceVersionId,
      draftName,
      draftId,
      timestamp,
    );

    expect(result.draftVersion).toEqual({
      versionName: "测试草稿",
      id: "draft-uuid-123",
      baseVersionId: "source-version-1",
      draftName: "测试草稿",
      createdAt: "2024-01-01T00:00:00Z",
      updatedAt: "2024-01-01T00:00:00Z",
    });
  });

  it("应该正确创建模块标签数据", () => {
    const result = createDraftData(
      mockSourceData,
      sourceVersionId,
      draftName,
      draftId,
      timestamp,
    );

    expect(result.draftModuleLabels).toHaveLength(2);
    expect(result.draftModuleLabels[0]).toEqual({
      id: "label-1",
      name: "标签1",
      promptVersionId: "draft-uuid-123",
      isDraft: true,
      sourceVersionId: "source-version-1",
      draftName: "测试草稿",
      createdAt: "2024-01-01T00:00:00Z",
      updatedAt: "2024-01-01T00:00:00Z",
    });
  });

  it("应该正确创建共享变量数据", () => {
    const result = createDraftData(
      mockSourceData,
      sourceVersionId,
      draftName,
      draftId,
      timestamp,
    );

    expect(result.draftSharedVariables).toHaveLength(1);
    expect(result.draftSharedVariables[0]).toEqual({
      id: "var-1",
      name: "变量1",
      content: "变量内容1",
      definition: "变量定义1",
      level: Level.Product,
      scopeDetail: "{}",
      promptName: "变量名1",
      promptVersionId: "draft-uuid-123",
      isDraft: true,
      sourceVersionId: "source-version-1",
      draftName: "测试草稿",
      createdAt: "2024-01-01T00:00:00Z",
      updatedAt: "2024-01-01T00:00:00Z",
    });
  });

  it("应该正确创建模块数据", () => {
    const result = createDraftData(
      mockSourceData,
      sourceVersionId,
      draftName,
      draftId,
      timestamp,
    );

    expect(result.draftModules).toHaveLength(1);
    expect(result.draftModules[0]).toEqual({
      id: "module-1",
      name: "模块1",
      moduleLabelIds: "label-1,label-2",
      level: Level.Product,
      prompt: '{"type":"module"}',
      scopeDetail: "{}",
      promptVersionId: "draft-uuid-123",
      isDraft: true,
      sourceVersionId: "source-version-1",
      draftName: "测试草稿",
      createdAt: "2024-01-01T00:00:00Z",
      updatedAt: "2024-01-01T00:00:00Z",
    });
  });

  it("应该正确创建能力数据", () => {
    const result = createDraftData(
      mockSourceData,
      sourceVersionId,
      draftName,
      draftId,
      timestamp,
    );

    expect(result.draftAbilities).toHaveLength(1);
    expect(result.draftAbilities[0]).toEqual({
      id: "ability-1",
      name: "能力1",
      content: "能力内容1",
      promptVersionId: "draft-uuid-123",
      isDraft: true,
      sourceVersionId: "source-version-1",
      draftName: "测试草稿",
      createdAt: "2024-01-01T00:00:00Z",
      updatedAt: "2024-01-01T00:00:00Z",
    });
  });

  it("应该正确处理空数据的情况", () => {
    const emptySourceData: CompleteVersionData = {
      version: {
        promptVersionId: "empty-version",
        versionName: "空版本",
        version: "1.0.0",
        status: PromptVersionStatus.ONLINE,
        remark: "空版本测试",
        evaluationTasks: [],
      },
      abilities: [],
      modules: [],
      moduleLabels: [],
      sharedVariables: [],
    };

    const result = createDraftData(
      emptySourceData,
      "empty-version",
      "空数据草稿",
      "empty-draft-id",
      timestamp,
    );

    expect(result.draftVersion.versionName).toBe("空数据草稿");
    expect(result.draftModuleLabels).toHaveLength(0);
    expect(result.draftSharedVariables).toHaveLength(0);
    expect(result.draftModules).toHaveLength(0);
    expect(result.draftAbilities).toHaveLength(0);
  });

  it("应该保持原始数据的ID不变", () => {
    const result = createDraftData(
      mockSourceData,
      sourceVersionId,
      draftName,
      draftId,
      timestamp,
    );

    // 验证所有数据都保持原ID
    expect(result.draftModuleLabels[0].id).toBe("label-1");
    expect(result.draftModuleLabels[1].id).toBe("label-2");
    expect(result.draftSharedVariables[0].id).toBe("var-1");
    expect(result.draftModules[0].id).toBe("module-1");
    expect(result.draftAbilities[0].id).toBe("ability-1");
  });

  it("应该正确设置关联信息", () => {
    const result = createDraftData(
      mockSourceData,
      sourceVersionId,
      draftName,
      draftId,
      timestamp,
    );

    // 验证所有数据都关联到新的草稿版本
    expect(result.draftModuleLabels[0].promptVersionId).toBe(draftId);
    expect(result.draftSharedVariables[0].promptVersionId).toBe(draftId);
    expect(result.draftModules[0].promptVersionId).toBe(draftId);
    expect(result.draftAbilities[0].promptVersionId).toBe(draftId);

    // 验证所有数据都记录了源版本信息
    expect(result.draftModuleLabels[0].sourceVersionId).toBe(sourceVersionId);
    expect(result.draftSharedVariables[0].sourceVersionId).toBe(
      sourceVersionId,
    );
    expect(result.draftModules[0].sourceVersionId).toBe(sourceVersionId);
    expect(result.draftAbilities[0].sourceVersionId).toBe(sourceVersionId);
  });
});
