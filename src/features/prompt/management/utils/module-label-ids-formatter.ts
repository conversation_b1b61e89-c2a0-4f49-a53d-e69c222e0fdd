import { safeParseJson } from "@/utils";

// 解析模块标签 ID 字符串为数组

export const parseModuleLabelIds = (
  moduleLabelIds?: string | null,
): string[] => {
  if (!moduleLabelIds) {
    return [];
  }

  return safeParseJson<string[]>(moduleLabelIds, []).map((label) =>
    label.toString(),
  );
};
// 将模块标签 ID 数组转换为 JSON 字符串

export const stringifyModuleLabelIds = (labelIds: string[]): string => {
  return JSON.stringify(labelIds);
};
