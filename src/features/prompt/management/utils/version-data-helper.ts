import { AbilityService } from "@/services/ability-service";
import { ModuleService } from "@/services/module-service";
import { PromptVersionService } from "@/services/prompt-version-service";
import { SharedVariableService } from "@/services/shared-variable-service";
import { CompleteVersionData } from "../types";

/**
 * 获取版本的完整数据（包括能力、模块、共享变量等）
 */
export const getCompleteVersionData = async (
  versionId: string,
): Promise<CompleteVersionData> => {
  // 并行获取所有数据
  const [version, abilities, modules, moduleLabels, sharedVariables] =
    await Promise.all([
      PromptVersionService.getPromptVersionDetail({
        promptVersionId: versionId,
      }),
      AbilityService.selectAbilityList({ promptVersionId: versionId }),
      ModuleService.selectModuleList({ promptVersionId: versionId }),
      ModuleService.selectModuleLabelList({ promptVersionId: versionId }),
      SharedVariableService.selectSharedVariableList({
        promptVersionId: versionId,
      }),
    ]);

  if (!version) {
    throw new Error(`找不到服务端版本: ${versionId}`);
  }

  return {
    version,
    abilities: (abilities ?? []).map((ability) => ({
      id: ability.abilityId.toString(),
      name: ability.name,
      content: ability.content,
      promptVersionId: versionId,
    })),
    modules: (modules ?? []).map((module) => ({
      id: module.moduleId.toString(),
      name: module.name,
      moduleLabelIds: module.moduleLabelIds,
      level: module.level,
      promptVersionId: versionId,
      prompt: module.prompt,
      scopeDetail: module.scopeDetail,
    })),
    moduleLabels: (moduleLabels ?? []).map((label) => ({
      id: label.moduleLabelId.toString(),
      name: label.name,
      promptVersionId: versionId,
    })),
    sharedVariables: (sharedVariables ?? []).map((variable) => ({
      id: variable.sharedVariableId.toString(),
      name: variable.name,
      content: variable.content,
      definition: variable.definition,
      level: variable.level,
      scopeDetail: variable.scopeDetail,
      promptName: variable.promptName,
      promptVersionId: versionId,
    })),
  };
};
