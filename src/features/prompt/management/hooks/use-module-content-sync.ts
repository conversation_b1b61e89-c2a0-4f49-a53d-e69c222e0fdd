import { useCallback, useEffect, useRef } from "react";
import { PromptNodeType } from "../schemas/node";
import { ModuleContentChangeEvent, moduleSyncEventBus } from "../utils";

/**
 * 模块内容同步 Hook 的配置选项
 */
export interface UseModuleContentSyncOptions {
  /** 模块ID */
  moduleId: string;
  /** 组件唯一标识（用于避免循环更新） */
  componentId: string;
  /** 内容变更回调 */
  onContentChange?: (
    content: PromptNodeType[],
    event: ModuleContentChangeEvent,
  ) => void;
}

/**
 * 模块内容同步 Hook
 * 用于在相同 moduleId 的组件之间同步内容变更
 */
export const useModuleContentSync = ({
  moduleId,
  componentId,
  onContentChange,
}: UseModuleContentSyncOptions) => {
  // 生成组件唯一标识
  const sourceId = useRef(componentId);

  // 存储最后发布的事件时间戳，避免重复处理
  const lastPublishedTimestamp = useRef<number>(0);

  /**
   * 发布内容变更事件
   */
  const publishContentChange = useCallback(
    (content: PromptNodeType[]) => {
      const timestamp = Date.now();
      const event: ModuleContentChangeEvent = {
        moduleId,
        content,
        timestamp,
        source: sourceId.current,
      };

      // 记录发布时间戳
      lastPublishedTimestamp.current = timestamp;

      // 发布事件
      moduleSyncEventBus.publish(event);
    },
    [moduleId],
  );

  /**
   * 处理接收到的内容变更事件
   */
  const handleContentChangeEvent = useCallback(
    (event: ModuleContentChangeEvent) => {
      // 忽略自己发布的事件
      if (event.source === sourceId.current) {
        return;
      }

      // 忽略已处理过的事件（基于时间戳）
      if (event.timestamp <= lastPublishedTimestamp.current) {
        return;
      }

      // 调用外部回调
      onContentChange?.(event.content, event);
    },
    [onContentChange],
  );

  /**
   * 订阅模块内容变更事件
   */
  useEffect(
    () => moduleSyncEventBus.subscribe(moduleId, handleContentChangeEvent),
    [moduleId, handleContentChangeEvent],
  );

  /**
   * 获取当前模块的监听器数量
   */
  const getListenerCount = useCallback(
    () => moduleSyncEventBus.getListenerCount(moduleId),
    [moduleId],
  );

  return {
    /** 发布内容变更事件 */
    publishContentChange,
    /** 获取监听器数量 */
    getListenerCount,
    /** 组件源标识 */
    sourceId: sourceId.current,
  };
};
