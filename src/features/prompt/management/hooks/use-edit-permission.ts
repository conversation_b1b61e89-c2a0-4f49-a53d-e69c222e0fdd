import { useAtomValue } from "jotai";
import { useMemo } from "react";
import { selectedVersionIdAtom } from "../atoms/core";
import { isDraftId } from "../utils";

/**
 * 编辑权限检查 hook
 * 基于当前选中版本是否为草稿版本来判断编辑权限
 */
export const useEditPermission = () => {
  const selectedVersionId = useAtomValue(selectedVersionIdAtom);

  const canEdit = useMemo(() => {
    if (!selectedVersionId) {
      return false;
    }

    // 检查是否为草稿版本
    return isDraftId(selectedVersionId);
  }, [selectedVersionId]);

  return {
    canEdit,
    selectedVersionId,
  };
};
