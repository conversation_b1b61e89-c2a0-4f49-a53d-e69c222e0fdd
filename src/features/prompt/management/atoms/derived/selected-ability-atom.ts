import { atom } from "jotai";
import { selectedAbilityIdAtom } from "../core";
import { abilitiesAtom } from "../queries";
import { UniversalAbility } from "../../types";

export const selectedAbilityAtom = atom(
  (get) => {
    const selectedAbilityId = get(selectedAbilityIdAtom);
    const abilities = get(abilitiesAtom);

    return abilities.data?.find((ability) => ability.id === selectedAbilityId);
  },
  (_, set, ability: UniversalAbility | null) => {
    if (ability) {
      set(selectedAbilityIdAtom, ability.id);
    } else {
      set(selectedAbilityIdAtom, null);
    }
  },
);
