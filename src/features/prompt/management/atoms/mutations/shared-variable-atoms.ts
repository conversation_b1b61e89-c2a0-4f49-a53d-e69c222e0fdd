import { atom } from "jotai";
import { v4 as uuidv4 } from "uuid";
import { SharedVariableFormData } from "../../schemas/shared-variable";
import { DraftSharedVariable } from "../../types";
import {
  createDefaultSharedVariableContent,
  generateTimestamp,
} from "../../utils";
import { sharedVariableDb } from "../core";
import { sharedVariablesAtom } from "../queries";
import { removeSharedVariableFromModulesAtom } from "./module-atoms";

/**
 * 创建草稿共享变量
 */
export const createSharedVariableAtom = atom(
  null,
  (get, set, promptVersionId: string, formData: SharedVariableFormData) => {
    const id = `draft-${uuidv4()}`;
    const newVariable: DraftSharedVariable = {
      id,
      createdAt: generateTimestamp(),
      updatedAt: generateTimestamp(),
      promptVersionId,
      name: formData.name,
      content: JSON.stringify(createDefaultSharedVariableContent()),
      definition: formData.definition || "",
      level: formData.level,
      promptName: formData.promptName,
      // 将 scopeDetail 数组转换为 JSON 字符串存储
      scopeDetail:
        formData.scopeDetail && formData.scopeDetail.length > 0
          ? JSON.stringify(formData.scopeDetail)
          : undefined,
    };

    set(sharedVariableDb.set, id, newVariable);

    // 触发更新
    get(sharedVariablesAtom).refetch();

    return newVariable;
  },
);

/**
 * 更新草稿共享变量
 */
export const updateSharedVariableAtom = atom(
  null,
  (get, set, variableId: string, formData: SharedVariableFormData) => {
    const variables = get(sharedVariableDb.values);
    const existingVariable = variables.find(
      (variable) => variable.id === variableId,
    );

    if (!existingVariable) {
      throw new Error(`共享变量 ${variableId} 不存在`);
    }

    const updatedVariable: DraftSharedVariable = {
      ...existingVariable,
      name: formData.name,
      definition: formData.definition || "",
      level: formData.level,
      promptName: formData.promptName,
      // 将 scopeDetail 数组转换为 JSON 字符串存储
      scopeDetail:
        formData.scopeDetail && formData.scopeDetail.length > 0
          ? JSON.stringify(formData.scopeDetail)
          : undefined,
      updatedAt: generateTimestamp(),
      // 如果等级变更，清除所有内容
      content:
        existingVariable.level !== formData.level
          ? ""
          : existingVariable.content,
    };

    set(sharedVariableDb.set, variableId, updatedVariable);

    // 触发更新
    get(sharedVariablesAtom).refetch();

    return updatedVariable;
  },
);

/**
 * 更新草稿共享变量名称
 */
export const updateSharedVariableNameAtom = atom(
  null,
  (get, set, variableId: string, name: string) => {
    const variables = get(sharedVariableDb.values);
    const existingVariable = variables.find(
      (variable) => variable.id === variableId,
    );

    if (!existingVariable) {
      throw new Error(`共享变量 ${variableId} 不存在`);
    }

    const updatedVariable: DraftSharedVariable = {
      ...existingVariable,
      name,
      updatedAt: generateTimestamp(),
    };

    set(sharedVariableDb.set, variableId, updatedVariable);

    // 触发更新
    get(sharedVariablesAtom).refetch();

    return updatedVariable;
  },
);

/**
 * 更新草稿共享变量提示词
 */
export const updateSharedVariableContentAtom = atom(
  null,
  (get, set, variableId: string, content: string) => {
    const variables = get(sharedVariableDb.values);
    const existingVariable = variables.find(
      (variable) => variable.id === variableId,
    );

    if (!existingVariable) {
      throw new Error(`共享变量 ${variableId} 不存在`);
    }

    const updatedVariable: DraftSharedVariable = {
      ...existingVariable,
      content,
      updatedAt: generateTimestamp(),
    };

    set(sharedVariableDb.set, variableId, updatedVariable);

    // 触发更新
    get(sharedVariablesAtom).refetch();

    return updatedVariable;
  },
);

/**
 * 删除草稿共享变量
 */
export const deleteSharedVariableAtom = atom(
  null,
  (get, set, variableId: string) => {
    // 从所有模块中移除对这个共享变量的引用
    set(removeSharedVariableFromModulesAtom, variableId);

    // 删除共享变量
    set(sharedVariableDb.delete, variableId);

    // 触发更新
    get(sharedVariablesAtom).refetch();

    return true;
  },
);
