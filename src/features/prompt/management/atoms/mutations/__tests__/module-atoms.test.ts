// Mock 工具函数
vi.mock("../../../utils", async (importOriginal) => {
  const actual = (await importOriginal()) as typeof import("../../../utils");

  return {
    ...actual,
    createDefaultModuleNode: vi.fn(() => ({
      type: "module",
      attrs: { name: "未命名模块" },
      content: [{ type: "prompt", content: [] }],
    })),
    generateTimestamp: vi.fn(() => "2024-01-01T00:00:00Z"),
    validateNameDuplicate: vi.fn(() => Promise.resolve(true)),
    removeSharedVariableFromModuleContent: vi.fn(
      (content: string, variableId: string) => {
        // 简单的模拟实现：如果内容包含共享变量引用，则返回修改后的内容
        if (content.includes(`"sharedVariableId":"${variableId}"`)) {
          return content
            .replace(
              new RegExp(
                `\\{"type":"sharedVariableReference","attrs":\\{"sharedVariableId":"${variableId}"\\}\\}`,
                "g",
              ),
              "",
            )
            .replace(/,\s*,/g, ",")
            .replace(/\[\s*,/g, "[")
            .replace(/,\s*\]/g, "]");
        }
        return content;
      },
    ),
  };
});

vi.mock("uuid", () => ({
  v4: vi.fn(() => "test-uuid-123"),
}));

import { Level } from "@/types/api/prompt";
import "fake-indexeddb/auto";
import { createStore } from "jotai";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { DraftAbility, DraftModule } from "../../../types";
import { abilityDb, moduleDb } from "../../core";
import {
  createModuleAtom,
  deleteModuleAtom,
  deleteModuleLabelFromModuleAtom,
  removeSharedVariableFromModulesAtom,
  updateModuleAtom,
  updateModulePromptAtom,
} from "../module-atoms";

import { createDefaultAbilityNode } from "../../../utils";

describe("模块操作原子", () => {
  let store: ReturnType<typeof createStore>;

  beforeEach(async () => {
    store = createStore();

    // 清理数据库
    await store.set(moduleDb.clear);

    // 重置所有 mock
    vi.clearAllMocks();
  });

  describe("createModuleAtom", () => {
    it("应该创建新的草稿模块", async () => {
      const promptVersionId = "version-1";
      const moduleData: Partial<DraftModule> = {
        name: "自定义模块",
        level: Level.Shop,
      };

      const result = await store.set(
        createModuleAtom,
        promptVersionId,
        moduleData,
      );

      expect(result).toEqual({
        id: "draft-test-uuid-123",
        prompt:
          '{"type":"module","attrs":{"name":"未命名模块"},"content":[{"type":"prompt","content":[]}]}',
        level: Level.Shop,
        promptVersionId,
        name: "自定义模块",
        moduleLabelIds: "",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
      });

      // 验证数据已保存到数据库
      const savedModule = store.get(moduleDb.item("draft-test-uuid-123"));
      expect(savedModule).toEqual(result);
    });

    it("应该使用默认值创建模块", async () => {
      const promptVersionId = "version-1";
      const moduleData: Partial<DraftModule> = {};

      const result = await store.set(
        createModuleAtom,
        promptVersionId,
        moduleData,
      );

      expect(result?.level).toBe(Level.Product);
      expect(result?.name).toBe("未命名模块");
      expect(result?.moduleLabelIds).toBe("");
    });
  });

  describe("updateModuleAtom", () => {
    it("应该更新草稿模块", async () => {
      const moduleId = "test-module-1";
      const testModule: DraftModule = {
        id: moduleId,
        name: "原始模块",
        level: Level.Product,
        prompt: "原始提示词",
        moduleLabelIds: "",
        promptVersionId: "version-1",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
      };

      // 先添加数据
      await store.set(moduleDb.set, moduleId, testModule);

      // 更新模块
      const updates = {
        name: "更新后的模块",
        level: Level.Shop,
      };
      const result = await store.set(updateModuleAtom, moduleId, updates);

      expect(result).toEqual({
        ...testModule,
        ...updates,
        updatedAt: "2024-01-01T00:00:00Z",
      });

      // 验证数据已更新
      const updatedModule = store.get(moduleDb.item(moduleId));
      expect(updatedModule).toEqual(result);
    });

    it("当模块不存在时应该抛出错误", async () => {
      const nonExistentId = "non-existent-id";

      await expect(async () => {
        await store.set(updateModuleAtom, nonExistentId, { name: "新名称" });
      }).rejects.toThrow("模块 non-existent-id 不存在");
    });
  });

  describe("updateModulePromptAtom", () => {
    it("应该更新模块提示词", async () => {
      const moduleId = "test-module-1";
      const testModule: DraftModule = {
        id: moduleId,
        name: "测试模块",
        level: Level.Product,
        prompt: '{"type":"module","attrs":{"name":"测试模块"},"content":[]}',
        moduleLabelIds: "",
        promptVersionId: "version-1",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
      };

      // 先添加数据
      await store.set(moduleDb.set, moduleId, testModule);

      // 更新提示词
      const newPrompt = [{ type: "text", content: "新的提示词内容" }];
      const result = store.set(
        updateModulePromptAtom,
        moduleId,
        newPrompt as any,
      );

      expect(result.prompt).toBe(
        '{"type":"module","attrs":{"name":"测试模块"},"content":[{"type":"text","content":"新的提示词内容"}]}',
      );
      expect(result.updatedAt).toBe("2024-01-01T00:00:00Z");

      // 验证数据已更新
      const updatedModule = store.get(moduleDb.item(moduleId));
      expect(updatedModule).toEqual(result);
    });

    it("当模块不存在时应该抛出错误", () => {
      const nonExistentId = "non-existent-id";
      const newPrompt = [{ type: "text", content: "新内容" }];

      expect(() => {
        store.set(updateModulePromptAtom, nonExistentId, newPrompt as any);
      }).toThrow("模块 non-existent-id 不存在");
    });
  });

  describe("deleteModuleAtom", () => {
    it("应该删除指定的草稿模块", async () => {
      const moduleId = "test-module-1";
      const testModule: DraftModule = {
        id: moduleId,
        name: "测试模块",
        level: Level.Product,
        prompt: "测试提示词",
        moduleLabelIds: "",
        promptVersionId: "version-1",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
      };

      // 先添加数据
      await store.set(moduleDb.set, moduleId, testModule);
      expect(store.get(moduleDb.item(moduleId))).toEqual(testModule);

      // 删除数据
      store.set(deleteModuleAtom, moduleId);

      // 验证数据已删除
      expect(store.get(moduleDb.item(moduleId))).toBeUndefined();
    });

    it("删除模块后，应该从所有能力中移除对这个模块的引用", async () => {
      const moduleId = "test-module-1";
      const abilityId = "test-ability-1";
      const testModule: DraftModule = {
        id: moduleId,
        name: "测试模块",
        level: Level.Product,
        prompt: "测试提示词",
        moduleLabelIds: "",
        promptVersionId: "version-1",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
      };

      const testAbilityPrompt = createDefaultAbilityNode();

      testAbilityPrompt.content[0].content.push({
        type: "module",
        attrs: {
          id: moduleId,
        },
      });
      testAbilityPrompt.content[0].content.push({
        type: "module",
        attrs: {
          id: "test-module-2",
        },
      });
      testAbilityPrompt.content[1].content.push({
        type: "module",
        attrs: {
          id: moduleId,
        },
      });

      const testAbility: DraftAbility = {
        id: abilityId,
        name: "测试模块2",
        promptVersionId: "version-1",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
        content: JSON.stringify(testAbilityPrompt),
      };

      // 先添加数据
      await store.set(moduleDb.set, moduleId, testModule);
      await store.set(abilityDb.set, abilityId, testAbility);

      // 删除数据
      store.set(deleteModuleAtom, moduleId);

      // 验证数据已删除
      expect(store.get(moduleDb.item(moduleId))).toBeUndefined();

      const ability = store.get(abilityDb.item(abilityId));
      const newAbilityPrompt = createDefaultAbilityNode();

      newAbilityPrompt.content[0].content.push({
        type: "module",
        attrs: {
          id: "test-module-2",
        },
      });

      expect(JSON.parse(ability?.content || "")).toEqual(newAbilityPrompt);
    });
  });

  describe("removeSharedVariableFromModulesAtom", () => {
    it("应该从所有模块中移除指定共享变量的引用", async () => {
      const sharedVariableId = "shared-var-1";
      const moduleId1 = "module-1";
      const moduleId2 = "module-2";

      // 创建包含共享变量引用的模块内容
      const moduleContentWithReference = JSON.stringify({
        type: "module",
        attrs: { name: "测试模块" },
        content: [
          {
            type: "prompt",
            content: [
              {
                type: "sharedVariableReference",
                attrs: { sharedVariableId: sharedVariableId },
              },
            ],
          },
        ],
      });

      // 创建不包含共享变量引用的模块内容
      const moduleContentWithoutReference = JSON.stringify({
        type: "module",
        attrs: { name: "测试模块2" },
        content: [
          {
            type: "prompt",
            content: [],
          },
        ],
      });

      const testModule1: DraftModule = {
        id: moduleId1,
        name: "测试模块1",
        level: Level.Product,
        prompt: moduleContentWithReference,
        moduleLabelIds: "",
        promptVersionId: "version-1",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
      };

      const testModule2: DraftModule = {
        id: moduleId2,
        name: "测试模块2",
        level: Level.Product,
        prompt: moduleContentWithoutReference,
        moduleLabelIds: "",
        promptVersionId: "version-1",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
      };

      // 添加测试模块
      await store.set(moduleDb.set, moduleId1, testModule1);
      await store.set(moduleDb.set, moduleId2, testModule2);

      // 执行移除操作
      store.set(removeSharedVariableFromModulesAtom, sharedVariableId);

      // 验证包含引用的模块被更新
      const updatedModule1 = store.get(moduleDb.item(moduleId1));
      expect(updatedModule1?.prompt).not.toBe(moduleContentWithReference);
      expect(updatedModule1?.updatedAt).toBe("2024-01-01T00:00:00Z");

      // 验证不包含引用的模块未被更新
      const updatedModule2 = store.get(moduleDb.item(moduleId2));
      expect(updatedModule2?.prompt).toBe(moduleContentWithoutReference);
      expect(updatedModule2?.updatedAt).toBe("2024-01-01T00:00:00Z");
    });

    it("当模块内容没有共享变量引用时不应该更新模块", async () => {
      const sharedVariableId = "shared-var-1";
      const moduleId = "module-1";

      const moduleContentWithoutReference = JSON.stringify({
        type: "module",
        attrs: { name: "测试模块" },
        content: [
          {
            type: "prompt",
            content: [],
          },
        ],
      });

      const testModule: DraftModule = {
        id: moduleId,
        name: "测试模块",
        level: Level.Product,
        prompt: moduleContentWithoutReference,
        moduleLabelIds: "",
        promptVersionId: "version-1",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
      };

      // 添加测试模块
      await store.set(moduleDb.set, moduleId, testModule);

      // 执行移除操作
      store.set(removeSharedVariableFromModulesAtom, sharedVariableId);

      // 验证模块未被更新（内容和时间戳都应该保持不变）
      const updatedModule = store.get(moduleDb.item(moduleId));
      expect(updatedModule).toEqual(testModule);
    });
  });

  describe("deleteModuleLabelFromModuleAtom", () => {
    it("应该从所有模块中删除指定的标签ID", async () => {
      const labelId = "label-1";
      const moduleId1 = "module-1";
      const moduleId2 = "module-2";

      // 创建包含指定标签的模块
      const testModule1: DraftModule = {
        id: moduleId1,
        name: "测试模块1",
        level: Level.Product,
        prompt: "测试提示词1",
        moduleLabelIds: `["${labelId}", "label-2"]`,
        promptVersionId: "version-1",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
      };

      // 创建不包含指定标签的模块
      const testModule2: DraftModule = {
        id: moduleId2,
        name: "测试模块2",
        level: Level.Product,
        prompt: "测试提示词2",
        moduleLabelIds: '["label-2", "label-3"]',
        promptVersionId: "version-1",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
      };

      // 添加测试模块
      await store.set(moduleDb.set, moduleId1, testModule1);
      await store.set(moduleDb.set, moduleId2, testModule2);

      // 执行删除操作
      store.set(deleteModuleLabelFromModuleAtom, labelId);

      // 验证包含标签的模块被更新
      const updatedModule1 = store.get(moduleDb.item(moduleId1));
      expect(updatedModule1?.moduleLabelIds).toBe('["label-2"]');
      expect(updatedModule1?.updatedAt).toBe("2024-01-01T00:00:00Z");

      // 验证不包含标签的模块未被更新
      const updatedModule2 = store.get(moduleDb.item(moduleId2));
      expect(updatedModule2?.moduleLabelIds).toBe('["label-2", "label-3"]');
      expect(updatedModule2?.updatedAt).toBe("2024-01-01T00:00:00Z");
    });

    it("当模块不包含指定标签时不应该更新模块", async () => {
      const labelId = "label-1";
      const moduleId = "module-1";

      const testModule: DraftModule = {
        id: moduleId,
        name: "测试模块",
        level: Level.Product,
        prompt: "测试提示词",
        moduleLabelIds: '["label-2", "label-3"]',
        promptVersionId: "version-1",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
      };

      // 添加测试模块
      await store.set(moduleDb.set, moduleId, testModule);

      // 执行删除操作
      store.set(deleteModuleLabelFromModuleAtom, labelId);

      // 验证模块未被更新（内容和时间戳都应该保持不变）
      const updatedModule = store.get(moduleDb.item(moduleId));
      expect(updatedModule).toEqual(testModule);
    });

    it("应该处理空标签列表的模块", async () => {
      const labelId = "label-1";
      const moduleId = "module-1";

      const testModule: DraftModule = {
        id: moduleId,
        name: "测试模块",
        level: Level.Product,
        prompt: "测试提示词",
        moduleLabelIds: "[]",
        promptVersionId: "version-1",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
      };

      // 添加测试模块
      await store.set(moduleDb.set, moduleId, testModule);

      // 执行删除操作
      store.set(deleteModuleLabelFromModuleAtom, labelId);

      // 验证模块未被更新
      const updatedModule = store.get(moduleDb.item(moduleId));
      expect(updatedModule).toEqual(testModule);
    });

    it("应该处理无效JSON格式的标签列表", async () => {
      const labelId = "label-1";
      const moduleId = "module-1";

      const testModule: DraftModule = {
        id: moduleId,
        name: "测试模块",
        level: Level.Product,
        prompt: "测试提示词",
        moduleLabelIds: "invalid json",
        promptVersionId: "version-1",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
      };

      // 添加测试模块
      await store.set(moduleDb.set, moduleId, testModule);

      // 执行删除操作（应该不会抛出错误）
      expect(() => {
        store.set(deleteModuleLabelFromModuleAtom, labelId);
      }).not.toThrow();

      // 验证模块未被更新
      const updatedModule = store.get(moduleDb.item(moduleId));
      expect(updatedModule).toEqual(testModule);
    });
  });
});
