vi.mock("../../../utils", async (importOriginal) => {
  const actual = (await importOriginal()) as typeof import("../../../utils");

  return {
    ...actual,
    generateTimestamp: vi.fn(() => "2024-01-01T00:00:00Z"),
    validateNameDuplicate: vi.fn(() => Promise.resolve(true)),
  };
});

vi.mock("uuid", () => ({
  v4: vi.fn(() => "test-uuid-123"),
}));

import { Level } from "@/types/api/prompt";
import "fake-indexeddb/auto";
import { createStore } from "jotai";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { DraftModule, DraftModuleLabel } from "../../../types";
import { moduleDb, moduleLabelDb } from "../../core";
import {
  createModuleLabelAtom,
  deleteModuleLabelAtom,
  updateModuleLabel<PERSON>tom,
} from "../module-label-atoms";
describe("模块标签操作原子", () => {
  let store: ReturnType<typeof createStore>;

  beforeEach(async () => {
    store = createStore();

    // 清理数据库
    await store.set(moduleLabelDb.clear);

    // 重置所有 mock
    vi.clearAllMocks();
  });

  describe("createModuleLabelAtom", () => {
    it("应该创建新的草稿模块标签", async () => {
      const promptVersionId = "version-1";
      const name = "测试标签";

      const result = await store.set(
        createModuleLabelAtom,
        promptVersionId,
        name,
      );

      expect(result).toEqual({
        id: "draft-test-uuid-123",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
        promptVersionId,
        name,
      });

      // 验证数据已保存到数据库
      const savedLabel = store.get(moduleLabelDb.item("draft-test-uuid-123"));
      expect(savedLabel).toEqual(result);
    });

    it("应该生成唯一的ID", async () => {
      const promptVersionId = "version-1";
      const name = "测试标签";

      const result = await store.set(
        createModuleLabelAtom,
        promptVersionId,
        name,
      );

      expect(result?.id).toBe("draft-test-uuid-123");
    });
  });

  describe("updateModuleLabelAtom", () => {
    it("应该更新草稿模块标签", async () => {
      const labelId = "test-label-1";
      const testLabel: DraftModuleLabel = {
        id: labelId,
        name: "原始标签",
        promptVersionId: "version-1",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
      };

      // 先添加数据
      await store.set(moduleLabelDb.set, labelId, testLabel);

      // 更新标签名称
      const newName = "更新后的标签";
      const result = await store.set(updateModuleLabelAtom, labelId, newName);

      expect(result).toEqual({
        ...testLabel,
        name: newName,
        updatedAt: "2024-01-01T00:00:00Z",
      });

      // 验证数据已更新
      const updatedLabel = store.get(moduleLabelDb.item(labelId));
      expect(updatedLabel).toEqual(result);
    });

    it("当标签不存在时应该抛出错误", async () => {
      const nonExistentId = "non-existent-id";

      await expect(async () => {
        await store.set(updateModuleLabelAtom, nonExistentId, "新名称");
      }).rejects.toThrow("模块标签 non-existent-id 不存在");
    });

    it("应该正确更新时间戳", async () => {
      const labelId = "test-label-1";
      const testLabel: DraftModuleLabel = {
        id: labelId,
        name: "测试标签",
        promptVersionId: "version-1",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2023-12-31T00:00:00Z", // 旧的更新时间
      };

      // 先添加数据
      await store.set(moduleLabelDb.set, labelId, testLabel);

      // 更新标签
      const result = await store.set(updateModuleLabelAtom, labelId, "新名称");

      expect(result?.updatedAt).toBe("2024-01-01T00:00:00Z");
      expect(result?.createdAt).toBe("2024-01-01T00:00:00Z"); // 创建时间不变
    });
  });

  describe("deleteModuleLabelAtom", () => {
    it("应该删除指定的草稿模块标签", async () => {
      const labelId = "test-label-1";
      const testLabel: DraftModuleLabel = {
        id: labelId,
        name: "测试标签",
        promptVersionId: "version-1",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
      };

      // 先添加数据
      await store.set(moduleLabelDb.set, labelId, testLabel);
      expect(store.get(moduleLabelDb.item(labelId))).toEqual(testLabel);

      // 删除数据
      store.set(deleteModuleLabelAtom, labelId);

      // 验证数据已删除
      expect(store.get(moduleLabelDb.item(labelId))).toBeUndefined();
    });

    it("删除不存在的标签不应该抛出错误", () => {
      const nonExistentId = "non-existent-id";

      // 删除不存在的标签不应该抛出错误
      expect(() => {
        store.set(deleteModuleLabelAtom, nonExistentId);
      }).not.toThrow();
    });

    it("删除标签时应该删除模块中的对应标签ID", async () => {
      const labelId = "test-label-1";
      const moduleId = "test-module-1";
      const testLabel: DraftModuleLabel = {
        id: labelId,
        name: "测试标签",
        promptVersionId: "version-1",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
      };

      const testModule: DraftModule = {
        id: moduleId,
        name: "测试模块",
        level: Level.Product,
        prompt: "测试提示词",
        moduleLabelIds: `["${labelId}"]`,
        promptVersionId: "version-1",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
      };

      const testModule2: DraftModule = {
        id: "test-module-2",
        name: "测试模块2",
        level: Level.Product,
        prompt: "测试提示词2",
        moduleLabelIds: `["${labelId}", "test-label-2"]`,
        promptVersionId: "version-1",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
      };

      // 先添加数据
      await store.set(moduleLabelDb.set, labelId, testLabel);
      await store.set(moduleDb.set, moduleId, testModule);
      await store.set(moduleDb.set, "test-module-2", testModule2);

      // 删除标签
      store.set(deleteModuleLabelAtom, labelId);

      // 验证模块中的标签ID已删除
      const updatedModule = store.get(moduleDb.item(moduleId));
      expect(updatedModule?.moduleLabelIds).toBe("[]");
      const updatedModule2 = store.get(moduleDb.item("test-module-2"));
      expect(updatedModule2?.moduleLabelIds).toBe('["test-label-2"]');
    });
  });

  describe("数据完整性测试", () => {
    it("应该保持数据的完整性", async () => {
      const promptVersionId = "version-1";
      const labelName = "完整性测试标签";

      // 创建标签
      const createdLabel = await store.set(
        createModuleLabelAtom,
        promptVersionId,
        labelName,
      );

      // 验证创建的数据结构完整
      expect(createdLabel).toHaveProperty("id");
      expect(createdLabel).toHaveProperty("name");
      expect(createdLabel).toHaveProperty("promptVersionId");
      expect(createdLabel).toHaveProperty("createdAt");
      expect(createdLabel).toHaveProperty("updatedAt");

      // 验证数据类型正确
      expect(typeof createdLabel?.id).toBe("string");
      expect(typeof createdLabel?.name).toBe("string");
      expect(typeof createdLabel?.promptVersionId).toBe("string");
      expect(typeof createdLabel?.createdAt).toBe("string");
      expect(typeof createdLabel?.updatedAt).toBe("string");

      // 验证数据值正确
      expect(createdLabel?.name).toBe(labelName);
      expect(createdLabel?.promptVersionId).toBe(promptVersionId);
    });

    it("应该正确处理多个标签", async () => {
      const promptVersionId = "version-1";

      // 创建多个标签
      const label1 = await store.set(
        createModuleLabelAtom,
        promptVersionId,
        "标签1",
      );

      // Mock 返回不同的 UUID
      const { v4 } = await import("uuid");
      vi.mocked(v4).mockReturnValueOnce("test-uuid-456");
      const label2 = await store.set(
        createModuleLabelAtom,
        promptVersionId,
        "标签2",
      );

      // 验证标签都被正确保存
      expect(store.get(moduleLabelDb.item(label1!.id))).toEqual(label1);
      expect(store.get(moduleLabelDb.item(label2!.id))).toEqual(label2);

      // 验证标签有不同的ID
      expect(label1!.id).not.toBe(label2!.id);
    });
  });
});
