vi.mock("../../../utils", async (importOriginal) => {
  const actual = (await importOriginal()) as typeof import("../../../utils");

  return {
    ...actual,
    generateTimestamp: vi.fn(() => "2024-01-01T00:00:00Z"),
  };
});

vi.mock("uuid", () => ({
  v4: vi.fn(() => "test-uuid-123"),
}));

vi.mock("../../../services/", async (importOriginal) => {
  const actual =
    (await importOriginal()) as typeof import("../../../services/");

  return {
    ...actual,
    cleanupDraftDataAtom: {
      write: vi.fn(),
    },
  };
});

import "fake-indexeddb/auto";
import { createStore } from "jotai";
import { beforeEach, describe, expect, it, vi } from "vitest";
import {
  abilityDb,
  moduleDb,
  moduleLabelDb,
  selectedVersionIdAtom,
  sharedVariableDb,
  versionDb,
} from "../../core";
import {
  createDraftVersionAtom,
  deleteDraftVersionCompleteAtom,
} from "../version-atoms";

import { cleanupDraftDataAtom } from "../../../services/";

describe("版本操作原子", () => {
  let store: ReturnType<typeof createStore>;
  let mockCleanupWrite: ReturnType<typeof vi.fn>;

  beforeEach(async () => {
    // 获取 mock 函数的引用
    mockCleanupWrite = vi.mocked(cleanupDraftDataAtom.write);
    store = createStore();

    // 清理所有数据库
    await store.set(versionDb.clear);
    await store.set(abilityDb.clear);
    await store.set(moduleDb.clear);
    await store.set(moduleLabelDb.clear);
    await store.set(sharedVariableDb.clear);

    // 重置选中的版本ID
    store.set(selectedVersionIdAtom, null);

    // 重置所有 mock
    vi.clearAllMocks();
  });

  describe("createDraftVersionAtom", () => {
    it("应该创建新的草稿版本", () => {
      const versionName = "测试版本";

      store.set(createDraftVersionAtom, versionName);

      // 验证版本已保存到数据库
      const savedVersion = store.get(versionDb.item("draft-test-uuid-123"));
      expect(savedVersion).toEqual({
        id: "draft-test-uuid-123",
        draftName: "新草稿",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
        versionName,
      });

      // 验证选中的版本ID已更新
      const selectedVersionId = store.get(selectedVersionIdAtom);
      expect(selectedVersionId).toBe("draft-test-uuid-123");
    });

    it("应该生成唯一的草稿ID", () => {
      const versionName = "测试版本";

      store.set(createDraftVersionAtom, versionName);

      const savedVersion = store.get(versionDb.item("draft-test-uuid-123"));
      expect(savedVersion?.id).toBe("draft-test-uuid-123");
    });

    it("应该设置默认的草稿名称", () => {
      const versionName = "测试版本";

      store.set(createDraftVersionAtom, versionName);

      const savedVersion = store.get(versionDb.item("draft-test-uuid-123"));
      expect(savedVersion?.draftName).toBe("新草稿");
    });

    it("应该正确设置时间戳", () => {
      const versionName = "测试版本";

      store.set(createDraftVersionAtom, versionName);

      const savedVersion = store.get(versionDb.item("draft-test-uuid-123"));
      expect(savedVersion?.createdAt).toBe("2024-01-01T00:00:00Z");
      expect(savedVersion?.updatedAt).toBe("2024-01-01T00:00:00Z");
    });

    it("应该自动选中新创建的版本", () => {
      const versionName = "测试版本";

      // 初始状态没有选中版本
      expect(store.get(selectedVersionIdAtom)).toBeNull();

      store.set(createDraftVersionAtom, versionName);

      // 创建后应该自动选中新版本
      expect(store.get(selectedVersionIdAtom)).toBe("draft-test-uuid-123");
    });
  });

  describe("deleteDraftVersionCompleteAtom", () => {
    it("应该调用 cleanupDraftDataAtom 并重置当前选中版本", async () => {
      const versionId = "draft-version-1";

      // 设置当前选中版本
      store.set(selectedVersionIdAtom, versionId);
      expect(store.get(selectedVersionIdAtom)).toBe(versionId);

      // 执行完整删除
      store.set(deleteDraftVersionCompleteAtom, versionId);

      // 验证 cleanupDraftDataAtom 被调用，传入正确的参数
      expect(mockCleanupWrite).toHaveBeenCalledWith(
        expect.anything(), // get function
        expect.anything(), // set function
        versionId, // 要删除的版本ID
      );

      // 验证当前选中版本被重置为 null
      expect(store.get(selectedVersionIdAtom)).toBeNull();
    });

    it("应该在删除非当前选中版本时保持选中状态", async () => {
      const versionId1 = "draft-version-1";
      const versionId2 = "draft-version-2";

      // 选中版本2
      store.set(selectedVersionIdAtom, versionId2);

      // 删除版本1
      store.set(deleteDraftVersionCompleteAtom, versionId1);

      // 验证 cleanupDraftDataAtom 被调用
      expect(mockCleanupWrite).toHaveBeenCalledWith(
        expect.anything(),
        expect.anything(),
        versionId1,
      );

      // 验证版本2仍然被选中
      expect(store.get(selectedVersionIdAtom)).toBe(versionId2);
    });

    it("删除不存在的版本不应该抛出错误", () => {
      const nonExistentId = "non-existent-id";

      // 删除不存在的版本不应该抛出错误
      expect(() => {
        store.set(deleteDraftVersionCompleteAtom, nonExistentId);
      }).not.toThrow();

      // 验证 cleanupDraftDataAtom 仍然被调用
      expect(mockCleanupWrite).toHaveBeenCalledWith(
        expect.anything(),
        expect.anything(),
        nonExistentId,
      );
    });

    it("应该正确处理空的选中状态", async () => {
      const versionId = "draft-version-1";

      // 设置选中状态为 null
      store.set(selectedVersionIdAtom, null);

      // 删除版本
      store.set(deleteDraftVersionCompleteAtom, versionId);

      // 验证 cleanupDraftDataAtom 被调用
      expect(mockCleanupWrite).toHaveBeenCalledWith(
        expect.anything(),
        expect.anything(),
        versionId,
      );

      // 验证选中状态仍为 null
      expect(store.get(selectedVersionIdAtom)).toBeNull();
    });

    it("应该在每次调用时重置 mock", () => {
      const versionId1 = "draft-version-1";
      const versionId2 = "draft-version-2";

      // 第一次调用
      store.set(deleteDraftVersionCompleteAtom, versionId1);
      expect(mockCleanupWrite).toHaveBeenCalledTimes(1);

      // 重置 mock
      vi.clearAllMocks();

      // 第二次调用
      store.set(deleteDraftVersionCompleteAtom, versionId2);
      expect(mockCleanupWrite).toHaveBeenCalledTimes(1);
      expect(mockCleanupWrite).toHaveBeenCalledWith(
        expect.anything(),
        expect.anything(),
        versionId2,
      );
    });
  });

  describe("数据完整性测试", () => {
    it("应该保持版本数据的完整性", () => {
      const versionName = "完整性测试版本";

      store.set(createDraftVersionAtom, versionName);

      const savedVersion = store.get(versionDb.item("draft-test-uuid-123"));

      // 验证数据结构完整
      expect(savedVersion).toHaveProperty("id");
      expect(savedVersion).toHaveProperty("draftName");
      expect(savedVersion).toHaveProperty("versionName");
      expect(savedVersion).toHaveProperty("createdAt");
      expect(savedVersion).toHaveProperty("updatedAt");

      // 验证数据类型正确
      expect(typeof savedVersion?.id).toBe("string");
      expect(typeof savedVersion?.draftName).toBe("string");
      expect(typeof savedVersion?.versionName).toBe("string");
      expect(typeof savedVersion?.createdAt).toBe("string");
      expect(typeof savedVersion?.updatedAt).toBe("string");

      // 验证数据值正确
      expect(savedVersion?.versionName).toBe(versionName);
      expect(savedVersion?.draftName).toBe("新草稿");
    });

    it("应该正确处理多个版本", async () => {
      // 创建多个版本
      store.set(createDraftVersionAtom, "版本1");

      // Mock 返回不同的 UUID
      const { v4 } = await import("uuid");
      vi.mocked(v4).mockReturnValueOnce("test-uuid-456");
      store.set(createDraftVersionAtom, "版本2");

      // 验证两个版本都被正确保存
      const version1 = store.get(versionDb.item("draft-test-uuid-123"));
      const version2 = store.get(versionDb.item("draft-test-uuid-456"));

      expect(version1?.versionName).toBe("版本1");
      expect(version2?.versionName).toBe("版本2");

      // 验证最后创建的版本被选中
      expect(store.get(selectedVersionIdAtom)).toBe("draft-test-uuid-456");
    });
  });

  describe("边界情况测试", () => {
    it("应该处理空字符串版本名称", () => {
      const versionName = "";

      store.set(createDraftVersionAtom, versionName);

      const savedVersion = store.get(versionDb.item("draft-test-uuid-123"));
      expect(savedVersion?.versionName).toBe("");
    });

    it("应该处理特殊字符版本名称", () => {
      const versionName = "版本@#$%^&*()_+-={}[]|\\:;\"'<>?,./";

      store.set(createDraftVersionAtom, versionName);

      const savedVersion = store.get(versionDb.item("draft-test-uuid-123"));
      expect(savedVersion?.versionName).toBe(versionName);
    });
  });
});
