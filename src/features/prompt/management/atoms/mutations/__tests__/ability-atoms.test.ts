vi.mock("../../../utils", async (importOriginal) => {
  const actual = (await importOriginal()) as typeof import("../../../utils");

  return {
    ...actual,
    createDefaultAbilityNode: vi.fn((name: string) => ({
      type: "ability",
      attrs: { name },
      content: [],
    })),
    generateTimestamp: vi.fn(() => "2024-01-01T00:00:00Z"),
    validateNameDuplicate: vi.fn(() => Promise.resolve(true)),
    removeModuleFromAbilityContent: vi.fn(
      (content: string, moduleId: string) => {
        // 简单的模拟实现：如果内容包含模块引用，则返回修改后的内容
        if (content.includes(`"id":"${moduleId}"`)) {
          return content
            .replace(
              new RegExp(
                `\\{"type":"module","attrs":\\{"id":"${moduleId}"\\}\\}`,
                "g",
              ),
              "",
            )
            .replace(/,\s*,/g, ",")
            .replace(/\[\s*,/g, "[")
            .replace(/,\s*\]/g, "]");
        }
        return content;
      },
    ),
  };
});

vi.mock("uuid", () => ({
  v4: vi.fn(() => "test-uuid-123"),
}));

import "fake-indexeddb/auto";
import { createStore } from "jotai";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { DraftAbility } from "../../../types";
import { abilityDb } from "../../core";
import {
  createAbilityAtom,
  deleteAbilityAtom,
  removeModuleFromAbilitiesAtom,
  updateAbilityContentAtom,
  updateAbilityNameAtom,
} from "../ability-atoms";

describe("能力操作原子", () => {
  let store: ReturnType<typeof createStore>;

  beforeEach(async () => {
    store = createStore();

    // 清理数据库
    await store.set(abilityDb.clear);

    // 重置所有 mock
    vi.clearAllMocks();
  });

  describe("createAbilityAtom", () => {
    it("应该创建新的草稿能力", async () => {
      const promptVersionId = "version-1";
      const name = "测试能力";

      const result = await store.set(createAbilityAtom, promptVersionId, name);

      expect(result).toEqual({
        id: "draft-test-uuid-123",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
        content: '{"type":"ability","attrs":{"name":"测试能力"},"content":[]}',
        promptVersionId,
        name: "测试能力",
      });

      // 验证数据已保存到数据库
      const savedAbility = store.get(abilityDb.item("draft-test-uuid-123"));
      expect(savedAbility).toEqual(result);
    });

    it("应该使用默认名称创建能力", async () => {
      const promptVersionId = "version-1";
      const name = "未命名能力";

      const result = await store.set(createAbilityAtom, promptVersionId, name);

      // 验证数据已保存到数据库
      const savedAbility = store.get(abilityDb.item("draft-test-uuid-123"));

      expect(savedAbility?.name).toBe("未命名能力");
      expect(result?.name).toBe("未命名能力");
    });
  });

  describe("deleteAbilityAtom", () => {
    it("应该删除指定的草稿能力", async () => {
      const abilityId = "test-ability-1";
      const testAbility: DraftAbility = {
        id: abilityId,
        name: "测试能力",
        content: "测试内容",
        promptVersionId: "version-1",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
      };

      // 先添加数据
      await store.set(abilityDb.set, abilityId, testAbility);
      expect(store.get(abilityDb.item(abilityId))).toEqual(testAbility);

      // 删除数据
      store.set(deleteAbilityAtom, abilityId);

      // 验证数据已删除
      expect(store.get(abilityDb.item(abilityId))).toBeUndefined();
    });
  });

  describe("updateAbilityNameAtom", () => {
    it("应该更新草稿能力名称", async () => {
      const abilityId = "test-ability-1";
      const testAbility: DraftAbility = {
        id: abilityId,
        name: "原始名称",
        content: "测试内容",
        promptVersionId: "version-1",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
      };

      // 先添加数据
      await store.set(abilityDb.set, abilityId, testAbility);

      // 更新名称
      const newName = "新名称";
      const result = await store.set(updateAbilityNameAtom, abilityId, newName);

      // 验证更新结果
      expect(result).toEqual({
        ...testAbility,
        name: newName,
        updatedAt: "2024-01-01T00:00:00Z",
      });

      const updatedAbility = store.get(abilityDb.item(abilityId));

      expect(updatedAbility).toEqual(result);
    });

    it("当能力不存在时应该返回null", async () => {
      const nonExistentId = "non-existent-id";

      // 尝试更新不存在的能力
      const result = await store.set(
        updateAbilityNameAtom,
        nonExistentId,
        "新名称",
      );

      // 验证返回null
      expect(result).toBeNull();
      // 验证没有创建新数据
      expect(store.get(abilityDb.item(nonExistentId))).toBeUndefined();
    });
  });

  describe("updateAbilityContentAtom", () => {
    it("应该更新草稿能力内容", async () => {
      const abilityId = "test-ability-1";
      const testAbility: DraftAbility = {
        id: abilityId,
        name: "测试能力",
        content: "原始内容",
        promptVersionId: "version-1",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
      };

      // 先添加数据
      await store.set(abilityDb.set, abilityId, testAbility);

      // 更新内容
      const newContent = "新内容";
      try {
        store.set(updateAbilityContentAtom, abilityId, newContent);
      } catch (error) {
        // 忽略 refetch 错误
        if (
          !(error instanceof TypeError && error.message.includes("refetch"))
        ) {
          throw error;
        }
      }

      // 验证更新结果
      const updatedAbility = store.get(abilityDb.item(abilityId));
      expect(updatedAbility).toEqual({
        ...testAbility,
        content: newContent,
        updatedAt: "2024-01-01T00:00:00Z",
      });
    });

    it("当能力不存在时应该不执行任何操作", async () => {
      const nonExistentId = "non-existent-id";

      // 尝试更新不存在的能力
      store.set(updateAbilityContentAtom, nonExistentId, "新内容");

      // 验证没有创建新数据
      expect(store.get(abilityDb.item(nonExistentId))).toBeUndefined();
    });
  });

  describe("removeModuleFromAbilitiesAtom", () => {
    it("应该从所有能力中移除指定模块的引用", async () => {
      const moduleId = "module-1";
      const abilityId1 = "ability-1";
      const abilityId2 = "ability-2";

      // 创建包含模块引用的能力内容
      const abilityContentWithReference = JSON.stringify({
        type: "ability",
        attrs: { name: "测试能力" },
        content: [
          {
            type: "abilityItem",
            attrs: { type: "role" },
            content: [
              {
                type: "module",
                attrs: { id: moduleId },
              },
            ],
          },
        ],
      });

      // 创建不包含模块引用的能力内容
      const abilityContentWithoutReference = JSON.stringify({
        type: "ability",
        attrs: { name: "测试能力2" },
        content: [
          {
            type: "abilityItem",
            attrs: { type: "role" },
            content: [],
          },
        ],
      });

      const testAbility1: DraftAbility = {
        id: abilityId1,
        name: "测试能力1",
        content: abilityContentWithReference,
        promptVersionId: "version-1",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
      };

      const testAbility2: DraftAbility = {
        id: abilityId2,
        name: "测试能力2",
        content: abilityContentWithoutReference,
        promptVersionId: "version-1",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
      };

      // 添加测试能力
      await store.set(abilityDb.set, abilityId1, testAbility1);
      await store.set(abilityDb.set, abilityId2, testAbility2);

      // 执行移除操作
      store.set(removeModuleFromAbilitiesAtom, moduleId);

      // 验证包含引用的能力被更新
      const updatedAbility1 = store.get(abilityDb.item(abilityId1));
      expect(updatedAbility1?.content).not.toBe(abilityContentWithReference);
      expect(updatedAbility1?.updatedAt).toBe("2024-01-01T00:00:00Z");

      // 验证不包含引用的能力未被更新
      const updatedAbility2 = store.get(abilityDb.item(abilityId2));
      expect(updatedAbility2?.content).toBe(abilityContentWithoutReference);
      expect(updatedAbility2?.updatedAt).toBe("2024-01-01T00:00:00Z");
    });

    it("当能力内容没有模块引用时不应该更新能力", async () => {
      const moduleId = "module-1";
      const abilityId = "ability-1";

      const abilityContentWithoutReference = JSON.stringify({
        type: "ability",
        attrs: { name: "测试能力" },
        content: [
          {
            type: "abilityItem",
            attrs: { type: "role" },
            content: [],
          },
        ],
      });

      const testAbility: DraftAbility = {
        id: abilityId,
        name: "测试能力",
        content: abilityContentWithoutReference,
        promptVersionId: "version-1",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
      };

      // 添加测试能力
      await store.set(abilityDb.set, abilityId, testAbility);

      // 执行移除操作
      store.set(removeModuleFromAbilitiesAtom, moduleId);

      // 验证能力未被更新（内容和时间戳都应该保持不变）
      const updatedAbility = store.get(abilityDb.item(abilityId));
      expect(updatedAbility).toEqual(testAbility);
    });
  });
});
