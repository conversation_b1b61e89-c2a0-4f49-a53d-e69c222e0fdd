vi.mock("../../../utils", async (importOriginal) => {
  const actual = (await importOriginal()) as typeof import("../../../utils");

  return {
    ...actual,
    generateTimestamp: vi.fn(() => "2024-01-01T00:00:00Z"),
    createDefaultSharedVariableContent: vi.fn(() => ""),
    removeSharedVariableFromModuleContent: vi.fn(
      (content: string, variableId: string) => {
        if (content.includes(`"sharedVariableId":"${variableId}"`)) {
          return content
            .replace(
              new RegExp(
                `\\{"type":"sharedVariableReference","attrs":\\{"sharedVariableId":"${variableId}"\\}\\}`,
                "g",
              ),
              "",
            )
            .replace(/,\s*,/g, ",")
            .replace(/\[\s*,/g, "[")
            .replace(/,\s*\]/g, "]");
        }
        return content;
      },
    ),
  };
});

vi.mock("uuid", () => ({
  v4: vi.fn(() => "test-uuid-123"),
}));

import { Level } from "@/types/api/prompt";
import "fake-indexeddb/auto";
import { createStore } from "jotai";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { SharedVariableFormData } from "../../../schemas/shared-variable";
import { DraftModule, DraftSharedVariable } from "../../../types";
import { moduleDb, sharedVariableDb } from "../../core";
import {
  createSharedVariableAtom,
  deleteSharedVariableAtom,
  updateSharedVariableAtom,
  updateSharedVariableContentAtom,
  updateSharedVariableNameAtom,
} from "../shared-variable-atoms";

describe("共享变量操作原子", () => {
  let store: ReturnType<typeof createStore>;

  beforeEach(async () => {
    store = createStore();

    // 清理数据库
    await store.set(sharedVariableDb.clear);
    await store.set(moduleDb.clear);

    // 重置所有 mock
    vi.clearAllMocks();
  });

  describe("createSharedVariableAtom", () => {
    it("应该创建新的草稿共享变量", () => {
      const promptVersionId = "version-1";
      const formData: SharedVariableFormData = {
        name: "测试变量",
        promptName: "testVar",
        level: Level.Product,
        definition: "测试定义",
        scopeDetail: ["scope1", "scope2"],
      };

      const result = store.set(
        createSharedVariableAtom,
        promptVersionId,
        formData,
      );

      expect(result).toEqual({
        id: "draft-test-uuid-123",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
        promptVersionId,
        name: "测试变量",
        content: '""',
        definition: "测试定义",
        level: Level.Product,
        promptName: "testVar",
        scopeDetail: '["scope1","scope2"]',
      });

      // 验证数据已保存到数据库
      const savedVariable = store.get(
        sharedVariableDb.item("draft-test-uuid-123"),
      );
      expect(savedVariable).toEqual(result);
    });

    it("应该处理空的 scopeDetail", () => {
      const promptVersionId = "version-1";
      const formData: SharedVariableFormData = {
        name: "测试变量",
        promptName: "testVar",
        level: Level.Product,
        definition: "测试定义",
        scopeDetail: [],
      };

      const result = store.set(
        createSharedVariableAtom,
        promptVersionId,
        formData,
      );

      expect(result.scopeDetail).toBeUndefined();
    });

    it("应该处理未定义的 definition", () => {
      const promptVersionId = "version-1";
      const formData: SharedVariableFormData = {
        name: "测试变量",
        promptName: "testVar",
        level: Level.Product,
        scopeDetail: ["scope1"],
      };

      const result = store.set(
        createSharedVariableAtom,
        promptVersionId,
        formData,
      );

      expect(result.definition).toBe("");
    });
  });

  describe("updateSharedVariableAtom", () => {
    it("应该更新草稿共享变量", async () => {
      const variableId = "test-variable-1";
      const testVariable: DraftSharedVariable = {
        id: variableId,
        name: "原始变量",
        content: "原始内容",
        definition: "原始定义",
        level: Level.Product,
        promptName: "originalVar",
        scopeDetail: '["original"]',
        promptVersionId: "version-1",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
      };

      // 先添加数据
      await store.set(sharedVariableDb.set, variableId, testVariable);

      // 更新变量
      const formData: SharedVariableFormData = {
        name: "更新后的变量",
        promptName: "updatedVar",
        level: Level.Product, // 保持相同等级，不清除内容
        definition: "更新后的定义",
        scopeDetail: ["updated1", "updated2"],
      };
      const result = store.set(updateSharedVariableAtom, variableId, formData);

      expect(result).toEqual({
        ...testVariable,
        name: "更新后的变量",
        promptName: "updatedVar",
        level: Level.Product, // 等级未变更
        definition: "更新后的定义",
        scopeDetail: '["updated1","updated2"]',
        updatedAt: "2024-01-01T00:00:00Z",
        content: "原始内容", // 等级相同时保持原内容
      });

      // 验证数据已更新
      const updatedVariable = store.get(sharedVariableDb.item(variableId));
      expect(updatedVariable).toEqual(result);
    });

    it("当等级变更时应该清除内容", async () => {
      const variableId = "test-variable-1";
      const testVariable: DraftSharedVariable = {
        id: variableId,
        name: "测试变量",
        content: "原始内容",
        definition: "测试定义",
        level: Level.Product,
        promptName: "testVar",
        scopeDetail: undefined,
        promptVersionId: "version-1",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
      };

      // 先添加数据
      await store.set(sharedVariableDb.set, variableId, testVariable);

      // 更新等级
      const formData: SharedVariableFormData = {
        name: "测试变量",
        promptName: "testVar",
        level: Level.Shop, // 等级变更
        definition: "测试定义",
        scopeDetail: [],
      };
      const result = store.set(updateSharedVariableAtom, variableId, formData);

      expect(result.content).toBe(""); // 内容应该被清除
      expect(result.level).toBe(Level.Shop);
    });

    it("当变量不存在时应该抛出错误", () => {
      const nonExistentId = "non-existent-id";
      const formData: SharedVariableFormData = {
        name: "测试变量",
        promptName: "testVar",
        level: Level.Product,
        scopeDetail: [],
      };

      expect(() => {
        store.set(updateSharedVariableAtom, nonExistentId, formData);
      }).toThrow("共享变量 non-existent-id 不存在");
    });
  });

  describe("updateSharedVariableContentAtom", () => {
    it("应该更新草稿共享变量内容", async () => {
      const variableId = "test-variable-1";
      const testVariable: DraftSharedVariable = {
        id: variableId,
        name: "测试变量",
        content: "原始内容",
        definition: "测试定义",
        level: Level.Product,
        promptName: "testVar",
        scopeDetail: undefined,
        promptVersionId: "version-1",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
      };

      // 先添加数据
      await store.set(sharedVariableDb.set, variableId, testVariable);

      // 更新内容
      const newContent = "新的内容";
      const result = store.set(
        updateSharedVariableContentAtom,
        variableId,
        newContent,
      );

      expect(result).toEqual({
        ...testVariable,
        content: newContent,
        updatedAt: "2024-01-01T00:00:00Z",
      });

      // 验证数据已更新
      const updatedVariable = store.get(sharedVariableDb.item(variableId));
      expect(updatedVariable).toEqual(result);
    });

    it("当变量不存在时应该抛出错误", () => {
      const nonExistentId = "non-existent-id";

      expect(() => {
        store.set(updateSharedVariableContentAtom, nonExistentId, "新内容");
      }).toThrow("共享变量 non-existent-id 不存在");
    });
  });

  describe("deleteSharedVariableAtom", () => {
    it("应该删除指定的草稿共享变量", async () => {
      const variableId = "test-variable-1";
      const testVariable: DraftSharedVariable = {
        id: variableId,
        name: "测试变量",
        content: "测试内容",
        definition: "测试定义",
        level: Level.Product,
        promptName: "testVar",
        scopeDetail: undefined,
        promptVersionId: "version-1",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
      };

      // 先添加数据
      await store.set(sharedVariableDb.set, variableId, testVariable);
      expect(store.get(sharedVariableDb.item(variableId))).toEqual(
        testVariable,
      );

      // 删除数据
      store.set(deleteSharedVariableAtom, variableId);

      // 验证数据已删除
      expect(store.get(sharedVariableDb.item(variableId))).toBeUndefined();
    });

    it("应该删除共享变量并从所有模块中移除引用", async () => {
      const sharedVariableId = "shared-var-1";
      const moduleId = "module-1";

      // 创建共享变量
      const testVariable: DraftSharedVariable = {
        id: sharedVariableId,
        name: "测试共享变量",
        content: "测试内容",
        definition: "测试定义",
        level: Level.Product,
        promptName: "testVar",
        scopeDetail: undefined,
        promptVersionId: "version-1",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
      };

      // 创建包含共享变量引用的模块
      const moduleContentWithReference = JSON.stringify({
        type: "module",
        attrs: { name: "测试模块" },
        content: [
          {
            type: "prompt",
            content: [
              {
                type: "sharedVariableReference",
                attrs: { sharedVariableId: sharedVariableId },
              },
            ],
          },
        ],
      });

      const testModule: DraftModule = {
        id: moduleId,
        name: "测试模块",
        level: Level.Product,
        prompt: moduleContentWithReference,
        moduleLabelIds: "",
        promptVersionId: "version-1",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
      };

      // 添加测试数据
      await store.set(sharedVariableDb.set, sharedVariableId, testVariable);
      await store.set(moduleDb.set, moduleId, testModule);

      // 执行删除操作
      store.set(deleteSharedVariableAtom, sharedVariableId);

      // 验证共享变量已删除
      expect(
        store.get(sharedVariableDb.item(sharedVariableId)),
      ).toBeUndefined();

      // 验证模块中的引用已被移除
      const updatedModule = store.get(moduleDb.item(moduleId));
      expect(updatedModule?.prompt).not.toBe(moduleContentWithReference);
      // 验证时间戳已更新（不检查具体值，因为可能使用真实的时间戳）
      expect(updatedModule?.updatedAt).not.toBe("2024-01-01T00:00:00Z");
    });
  });

  describe("updateSharedVariableNameAtom", () => {
    it("应该更新草稿共享变量名称", async () => {
      const variableId = "test-variable-1";
      const testVariable: DraftSharedVariable = {
        id: variableId,
        name: "原始名称",
        content: "测试内容",
        definition: "测试定义",
        level: Level.Product,
        promptName: "testVar",
        scopeDetail: undefined,
        promptVersionId: "version-1",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
      };

      // 先添加数据
      await store.set(sharedVariableDb.set, variableId, testVariable);

      // 更新名称
      const newName = "新名称";
      const result = store.set(
        updateSharedVariableNameAtom,
        variableId,
        newName,
      );

      expect(result).toEqual({
        ...testVariable,
        name: newName,
        updatedAt: "2024-01-01T00:00:00Z",
      });

      // 验证数据已更新
      const updatedVariable = store.get(sharedVariableDb.item(variableId));
      expect(updatedVariable).toEqual(result);
    });

    it("当变量不存在时应该抛出错误", () => {
      const nonExistentId = "non-existent-id";

      expect(() => {
        store.set(updateSharedVariableNameAtom, nonExistentId, "新名称");
      }).toThrow("共享变量 non-existent-id 不存在");
    });
  });
});
