vi.mock("../../../utils", async (importOriginal) => {
  const actual = (await importOriginal()) as typeof import("../../../utils");

  return {
    ...actual,
    generateTimestamp: vi.fn(() => "2024-01-01T00:00:00Z"),
    getCompleteVersionData: vi.fn(),
    createDraftData: vi.fn(),
  };
});

vi.mock("uuid", () => ({
  v4: vi.fn(() => "test-uuid-123"),
}));

import { Level } from "@/types/api/prompt";
import { PromptVersionStatus } from "@/types/api/prompt-version";
import "fake-indexeddb/auto";
import { createStore } from "jotai";
import { beforeEach, describe, expect, it, vi } from "vitest";
import {
  CompleteVersionData,
  CompleteDraftVersionData,
  UniversalAbility,
  UniversalModule,
  UniversalModuleLabel,
  UniversalSharedVariable,
} from "../../../types";
import {
  abilityDb,
  moduleDb,
  moduleLabelDb,
  selectedVersionIdAtom,
  sharedVariableDb,
  versionDb,
} from "../../core";
import { createDraftFromVersionAtom } from "../draft-clone-atoms";

describe("草稿克隆操作原子", () => {
  let store: ReturnType<typeof createStore>;

  // 模拟完整版本数据
  const mockCompleteVersionData: CompleteVersionData = {
    version: {
      promptVersionId: "source-version-1",
      versionName: "源版本",
      version: "1.0.0",
      status: PromptVersionStatus.ONLINE,
      remark: "",
    },
    abilities: [
      {
        id: "ability-1",
        name: "测试能力",
        content: "能力内容",
        promptVersionId: "source-version-1",
      } as UniversalAbility,
    ],
    modules: [
      {
        id: "module-1",
        name: "测试模块",
        level: Level.Product,
        prompt: "模块提示词",
        moduleLabelIds: '["label-1"]',
        promptVersionId: "source-version-1",
      } as UniversalModule,
    ],
    moduleLabels: [
      {
        id: "label-1",
        name: "测试标签",
        promptVersionId: "source-version-1",
      } as UniversalModuleLabel,
    ],
    sharedVariables: [
      {
        id: "variable-1",
        name: "测试变量",
        content: "变量内容",
        definition: "变量定义",
        level: Level.Product,
        promptName: "testVar",
        promptVersionId: "source-version-1",
      } as UniversalSharedVariable,
    ],
  };

  // 模拟草稿创建结果
  const mockDraftCreationResult: CompleteDraftVersionData = {
    draftVersion: {
      id: "draft-test-uuid-123",
      draftName: "测试草稿",
      baseVersionId: "source-version-1",
      versionName: "测试草稿",
      createdAt: "2024-01-01T00:00:00Z",
      updatedAt: "2024-01-01T00:00:00Z",
    },
    draftModuleLabels: [
      {
        id: "label-1",
        name: "测试标签",
        promptVersionId: "draft-test-uuid-123",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
        sourceVersionId: "source-version-1",
        draftName: "测试草稿",
      },
    ],
    draftSharedVariables: [
      {
        id: "variable-1",
        name: "测试变量",
        content: "变量内容",
        definition: "变量定义",
        level: Level.Product,
        promptName: "testVar",
        promptVersionId: "draft-test-uuid-123",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
        sourceVersionId: "source-version-1",
        draftName: "测试草稿",
      },
    ],
    draftModules: [
      {
        id: "module-1",
        name: "测试模块",
        level: Level.Product,
        prompt: "模块提示词",
        moduleLabelIds: '["label-1"]',
        promptVersionId: "draft-test-uuid-123",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
        sourceVersionId: "source-version-1",
        draftName: "测试草稿",
      },
    ],
    draftAbilities: [
      {
        id: "ability-1",
        name: "测试能力",
        content: "能力内容",
        promptVersionId: "draft-test-uuid-123",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
        sourceVersionId: "source-version-1",
        draftName: "测试草稿",
      },
    ],
  };

  beforeEach(async () => {
    store = createStore();

    // 清理所有数据库
    await store.set(versionDb.clear);
    await store.set(moduleLabelDb.clear);
    await store.set(sharedVariableDb.clear);
    await store.set(moduleDb.clear);
    await store.set(abilityDb.clear);

    // 重置选中的版本ID
    store.set(selectedVersionIdAtom, null);

    // 重置所有 mock
    vi.clearAllMocks();
  });

  describe("createDraftFromVersionAtom", () => {
    it("应该基于现有版本创建草稿版本", async () => {
      const sourceVersionId = "source-version-1";
      const draftName = "测试草稿";

      // Mock 工具函数
      const { getCompleteVersionData, createDraftData } = await import(
        "../../../utils"
      );
      vi.mocked(getCompleteVersionData).mockResolvedValue(
        mockCompleteVersionData,
      );
      vi.mocked(createDraftData).mockReturnValue(mockDraftCreationResult);

      const result = await store.set(
        createDraftFromVersionAtom,
        sourceVersionId,
        draftName,
      );

      expect(result).toEqual({
        draftId: "draft-test-uuid-123",
      });

      // 验证工具函数被正确调用
      expect(getCompleteVersionData).toHaveBeenCalledWith(sourceVersionId);
      expect(createDraftData).toHaveBeenCalledWith(
        mockCompleteVersionData,
        sourceVersionId,
        draftName,
        "draft-test-uuid-123",
        "2024-01-01T00:00:00Z",
      );
    });

    it("应该将所有草稿数据保存到数据库", async () => {
      const sourceVersionId = "source-version-1";
      const draftName = "测试草稿";

      // Mock 工具函数
      const { getCompleteVersionData, createDraftData } = await import(
        "../../../utils"
      );
      vi.mocked(getCompleteVersionData).mockResolvedValue(
        mockCompleteVersionData,
      );
      vi.mocked(createDraftData).mockReturnValue(mockDraftCreationResult);

      await store.set(createDraftFromVersionAtom, sourceVersionId, draftName);

      // 验证版本数据已保存
      const savedVersion = store.get(versionDb.item("draft-test-uuid-123"));
      expect(savedVersion).toEqual(mockDraftCreationResult.draftVersion);

      // 验证模块标签数据已保存
      const savedLabel = store.get(moduleLabelDb.item("label-1"));
      expect(savedLabel).toEqual(mockDraftCreationResult.draftModuleLabels[0]);

      // 验证共享变量数据已保存
      const savedVariable = store.get(sharedVariableDb.item("variable-1"));
      expect(savedVariable).toEqual(
        mockDraftCreationResult.draftSharedVariables[0],
      );

      // 验证模块数据已保存
      const savedModule = store.get(moduleDb.item("module-1"));
      expect(savedModule).toEqual(mockDraftCreationResult.draftModules[0]);

      // 验证能力数据已保存
      const savedAbility = store.get(abilityDb.item("ability-1"));
      expect(savedAbility).toEqual(mockDraftCreationResult.draftAbilities[0]);
    });

    it("应该选中新创建的草稿版本", async () => {
      const sourceVersionId = "source-version-1";
      const draftName = "测试草稿";

      // Mock 工具函数
      const { getCompleteVersionData, createDraftData } = await import(
        "../../../utils"
      );
      vi.mocked(getCompleteVersionData).mockResolvedValue(
        mockCompleteVersionData,
      );
      vi.mocked(createDraftData).mockReturnValue(mockDraftCreationResult);

      // 初始状态没有选中版本
      expect(store.get(selectedVersionIdAtom)).toBeNull();

      await store.set(createDraftFromVersionAtom, sourceVersionId, draftName);

      // 创建后应该自动选中新的草稿版本
      expect(store.get(selectedVersionIdAtom)).toBe("draft-test-uuid-123");
    });

    it("当获取源版本数据失败时应该抛出错误", async () => {
      const sourceVersionId = "source-version-1";
      const draftName = "测试草稿";

      // Mock 工具函数抛出错误
      const { getCompleteVersionData } = await import("../../../utils");
      vi.mocked(getCompleteVersionData).mockRejectedValue(
        new Error("获取版本数据失败"),
      );

      await expect(
        store.set(createDraftFromVersionAtom, sourceVersionId, draftName),
      ).rejects.toThrow("获取版本数据失败");
    });

    it("当创建草稿数据失败时应该抛出错误", async () => {
      const sourceVersionId = "source-version-1";
      const draftName = "测试草稿";

      // Mock 工具函数
      const { getCompleteVersionData, createDraftData } = await import(
        "../../../utils"
      );
      vi.mocked(getCompleteVersionData).mockResolvedValue(
        mockCompleteVersionData,
      );
      vi.mocked(createDraftData).mockImplementation(() => {
        throw new Error("创建草稿数据失败");
      });

      await expect(
        store.set(createDraftFromVersionAtom, sourceVersionId, draftName),
      ).rejects.toThrow("创建草稿数据失败");
    });
  });

  describe("错误处理", () => {
    it("应该正确处理和记录错误", async () => {
      const sourceVersionId = "source-version-1";
      const draftName = "测试草稿";
      const consoleSpy = vi
        .spyOn(console, "error")
        .mockImplementation(() => {});

      // Mock 工具函数抛出错误
      const { getCompleteVersionData } = await import("../../../utils");
      const testError = new Error("测试错误");
      vi.mocked(getCompleteVersionData).mockRejectedValue(testError);

      await expect(
        store.set(createDraftFromVersionAtom, sourceVersionId, draftName),
      ).rejects.toThrow("测试错误");

      // 验证错误被记录
      expect(consoleSpy).toHaveBeenCalledWith("创建草稿版本失败:", testError);

      consoleSpy.mockRestore();
    });
  });
});
