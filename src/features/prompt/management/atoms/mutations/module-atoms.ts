import { Level } from "@/types/api/prompt";
import { safeParseJson } from "@/utils";
import { atom } from "jotai";
import { v4 as uuidv4 } from "uuid";
import {
  parseModuleNode,
  PromptNodeType,
  stringifyModuleNode,
} from "../../schemas/node";
import { DraftModule } from "../../types";
import {
  createDefaultModuleNode,
  generateTimestamp,
  removeSharedVariableFromModuleContent,
  validateNameDuplicate,
  ValidationType,
} from "../../utils";
import { moduleDb } from "../core";
import { modulesAtom, modulesFilteredByLabelIdAtom } from "../queries";
import { removeModuleFromAbilitiesAtom } from "./ability-atoms";

/**
 * 创建草稿模块
 */
export const createModuleAtom = atom(
  null,
  async (get, set, promptVersionId: string, module: Partial<DraftModule>) => {
    const defaultNode = createDefaultModuleNode();
    const moduleName = module.name || defaultNode.attrs.name;

    // 进行名称重复性检查
    const isValid = await validateNameDuplicate({
      type: ValidationType.MODULE,
      name: moduleName,
      promptVersionId,
      get,
    });

    if (!isValid) {
      // 验证失败，不创建模块
      return null;
    }

    const id = `draft-${uuidv4()}`;
    const newModule: DraftModule = {
      prompt: stringifyModuleNode(defaultNode) || "",
      level: Level.Product,
      promptVersionId,
      name: moduleName,
      moduleLabelIds: "",
      ...module,
      id,
      createdAt: generateTimestamp(),
      updatedAt: generateTimestamp(),
    };

    set(moduleDb.set, id, newModule);

    // 触发更新
    get(modulesFilteredByLabelIdAtom).refetch();
    // 触发更新
    get(modulesAtom).refetch();

    return newModule;
  },
);

/**
 * 更新草稿模块
 */
export const updateModuleAtom = atom(
  null,
  async (
    get,
    set,
    moduleId: string,
    updates: Partial<Omit<DraftModule, "id" | "createdAt" | "promptVersionId">>,
  ) => {
    const modules = get(moduleDb.values);
    const existingModule = modules.find((m) => m.id === moduleId);

    if (!existingModule) {
      throw new Error(`模块 ${moduleId} 不存在`);
    }

    // 如果更新包含名称，需要进行名称重复性检查
    if (updates.name && updates.name !== existingModule.name) {
      const isValid = await validateNameDuplicate({
        type: ValidationType.MODULE,
        name: updates.name,
        promptVersionId: existingModule.promptVersionId,
        excludeName: existingModule.name,
        get,
      });

      if (!isValid) {
        // 验证失败，不更新模块
        return null;
      }
    }

    const updatedModule: DraftModule = {
      ...existingModule,
      ...updates,
      updatedAt: generateTimestamp(),
    };

    set(moduleDb.set, moduleId, updatedModule);

    // 触发更新
    get(modulesFilteredByLabelIdAtom).refetch();
    // 触发更新
    get(modulesAtom).refetch();

    return updatedModule;
  },
);

/**
 * 更新模块提示词
 */
export const updateModulePromptAtom = atom(
  null,
  (get, set, moduleId: string, prompt: PromptNodeType[]) => {
    const modules = get(moduleDb.values);
    const existingModule = modules.find((m) => m.id === moduleId);

    if (!existingModule) {
      throw new Error(`模块 ${moduleId} 不存在`);
    }

    const updatedModule: DraftModule = {
      ...existingModule,
      prompt: stringifyModuleNode({
        ...parseModuleNode(safeParseJson(existingModule.prompt, {})).data!,
        content: prompt,
      }),
      updatedAt: generateTimestamp(),
    };

    set(moduleDb.set, moduleId, updatedModule);

    // 触发更新
    get(modulesFilteredByLabelIdAtom).refetch();
    get(modulesAtom).refetch();

    return updatedModule;
  },
);

/**
 * 删除草稿模块
 */
export const deleteModuleAtom = atom(null, (get, set, moduleId: string) => {
  set(moduleDb.delete, moduleId);

  // 从所有能力中移除对这个模块的引用
  set(removeModuleFromAbilitiesAtom, moduleId);

  // 触发更新
  get(modulesFilteredByLabelIdAtom).refetch();
  // 触发更新
  get(modulesAtom).refetch();
});

/**
 * 删除所有模块中的指定标签
 */
export const deleteModuleLabelFromModuleAtom = atom(
  null,
  (get, set, labelId: string) => {
    const modules = get(moduleDb.values);

    modules.forEach((module) => {
      const labelIds = safeParseJson(module.moduleLabelIds, []);
      const updatedLabelIds = labelIds.filter((id: string) => id !== labelId);

      // 只有当标签列表发生变化时才更新模块
      if (updatedLabelIds.length !== labelIds.length) {
        const updatedModule: DraftModule = {
          ...module,
          moduleLabelIds: JSON.stringify(updatedLabelIds),
          updatedAt: generateTimestamp(),
        };

        set(moduleDb.set, module.id, updatedModule);
      }
    });

    // 触发更新
    get(modulesFilteredByLabelIdAtom).refetch();
    get(modulesAtom).refetch();
  },
);

/**
 * 从所有模块中移除对这个共享变量的引用
 */
export const removeSharedVariableFromModulesAtom = atom(
  null,
  (get, set, sharedVariableId: string) => {
    const modules = get(moduleDb.values);

    modules.forEach((module) => {
      const updatedContent = removeSharedVariableFromModuleContent(
        module.prompt,
        sharedVariableId,
      );

      // 只有当内容发生变化时才更新模块
      if (updatedContent !== module.prompt) {
        const updatedModule = {
          ...module,
          prompt: updatedContent,
          updatedAt: generateTimestamp(),
        };

        set(moduleDb.set, module.id, updatedModule);
      }
    });

    // 触发更新
    get(modulesFilteredByLabelIdAtom).refetch();
    // 触发更新
    get(modulesAtom).refetch();
  },
);
