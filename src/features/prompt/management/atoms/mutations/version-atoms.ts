import { atom } from "jotai";
import { v4 as uuidv4 } from "uuid";
import { cleanupDraftDataAtom } from "../../services";
import { DraftVersion } from "../../types";
import { generateTimestamp } from "../../utils";
import { selectedVersionIdAtom, versionDb } from "../core";
import { versionsAtom } from "../queries";

/**
 * 创建草稿版本
 */
export const createDraftVersionAtom = atom(
  null,
  (get, set, versionName: string) => {
    const id = `draft-${uuidv4()}`;
    const newVersion: DraftVersion = {
      id,
      draftName: "新草稿",
      createdAt: generateTimestamp(),
      updatedAt: generateTimestamp(),
      versionName,
    };

    set(versionDb.set, id, newVersion);
    set(selectedVersionIdAtom, id);

    // 触发更新
    get(versionsAtom).refetch();
  },
);

/**
 * 完整删除草稿版本及其所有相关数据
 */
export const deleteDraftVersionCompleteAtom = atom(
  null,
  (get, set, versionId: string) => {
    // 使用现有的清理原子，它会删除版本及其所有相关数据
    set(cleanupDraftDataAtom, versionId);

    // 如果删除的是当前选中的版本，需要重置选中状态
    const currentSelectedId = get(selectedVersionIdAtom);

    if (currentSelectedId === versionId) {
      set(selectedVersionIdAtom, null);
    }

    // 触发更新
    get(versionsAtom).refetch();
  },
);
