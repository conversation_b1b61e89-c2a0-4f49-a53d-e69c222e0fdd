import { atom } from "jotai";
import { v4 as uuidv4 } from "uuid";
import { DraftModuleLabel } from "../../types";
import {
  ValidationType,
  generateTimestamp,
  validateNameDuplicate,
} from "../../utils";
import { moduleLabelDb } from "../core";
import { moduleLabelsAtom } from "../queries";
import { deleteModuleLabelFromModuleAtom } from "./module-atoms";

/**
 * 创建草稿模块标签
 */
export const createModuleLabelAtom = atom(
  null,
  async (get, set, promptVersionId: string, name: string) => {
    // 进行名称重复性检查
    const isValid = await validateNameDuplicate({
      type: ValidationType.MODULE_LABEL,
      name,
      promptVersionId,
      get,
    });

    if (!isValid) {
      // 验证失败，不创建模块标签
      return null;
    }

    const id = `draft-${uuidv4()}`;
    const newLabel: DraftModuleLabel = {
      id,
      createdAt: generateTimestamp(),
      updatedAt: generateTimestamp(),
      promptVersionId,
      name,
    };

    set(moduleLabelDb.set, id, newLabel);

    // 触发更新
    get(moduleLabelsAtom).refetch();

    return newLabel;
  },
);

/**
 * 更新草稿模块标签
 */
export const updateModuleLabelAtom = atom(
  null,
  async (get, set, labelId: string, name: string) => {
    const existingLabel = get(moduleLabelDb.item(labelId));

    if (!existingLabel) {
      throw new Error(`模块标签 ${labelId} 不存在`);
    }

    // 进行名称重复性检查
    const isValid = await validateNameDuplicate({
      type: ValidationType.MODULE_LABEL,
      name,
      promptVersionId: existingLabel.promptVersionId,
      excludeName: existingLabel.name,
      get,
    });

    if (!isValid) {
      // 验证失败，不更新标签
      return null;
    }

    const updatedLabel: DraftModuleLabel = {
      ...existingLabel,
      name,
      updatedAt: generateTimestamp(),
    };

    set(moduleLabelDb.set, labelId, updatedLabel);

    // 触发更新
    get(moduleLabelsAtom).refetch();

    return updatedLabel;
  },
);

/**
 * 删除草稿模块标签
 */
export const deleteModuleLabelAtom = atom(null, (get, set, labelId: string) => {
  set(moduleLabelDb.delete, labelId);
  // 删除模块中的对应标签ID
  set(deleteModuleLabelFromModuleAtom, labelId);

  // 触发更新
  get(moduleLabelsAtom).refetch();
});
