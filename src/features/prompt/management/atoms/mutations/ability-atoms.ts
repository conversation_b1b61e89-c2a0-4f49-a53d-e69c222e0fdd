import { stringifyAbilityNode } from "@/features/prompt/management/schemas/node";
import { AbilityItemType } from "@/types/api/prompt";
import { atom } from "jotai";
import { v4 as uuidv4 } from "uuid";
import { DraftAbility } from "../../types";
import {
  ValidationType,
  createDefaultAbilityNode,
  generateTimestamp,
  removeModuleFromAbilityContent,
  removeModuleFromAbilityItem,
  validateNameDuplicate,
} from "../../utils";
import { abilityDb } from "../core";
import { abilitiesAtom } from "../queries";

/**
 * 创建草稿能力
 */
export const createAbilityAtom = atom(
  null,
  async (get, set, promptVersionId: string, name: string) => {
    // 进行名称重复性检查
    const isValid = await validateNameDuplicate({
      type: ValidationType.ABILITY,
      name,
      promptVersionId,
      get,
    });

    if (!isValid) {
      // 验证失败，不创建能力
      return null;
    }

    const defaultNode = createDefaultAbilityNode(name);
    const id = `draft-${uuidv4()}`;
    const newAbility: DraftAbility = {
      id,
      createdAt: generateTimestamp(),
      updatedAt: generateTimestamp(),
      content: stringifyAbilityNode(defaultNode) || "",
      promptVersionId,
      name: defaultNode.attrs.name,
    };

    set(abilityDb.set, id, newAbility);

    // 触发更新
    get(abilitiesAtom).refetch();

    return newAbility;
  },
);

/**
 * 删除草稿能力
 */
export const deleteAbilityAtom = atom(null, (get, set, abilityId: string) => {
  set(abilityDb.delete, abilityId);

  // 触发更新
  get(abilitiesAtom).refetch();
});

/**
 * 更新草稿能力名称
 */
export const updateAbilityNameAtom = atom(
  null,
  async (get, set, abilityId: string, name: string) => {
    const ability = get(abilityDb.item(abilityId));

    if (!ability) {
      return null;
    }

    // 进行名称重复性检查
    const isValid = await validateNameDuplicate({
      type: ValidationType.ABILITY,
      name,
      promptVersionId: ability.promptVersionId,
      excludeName: ability.name,
      get,
    });

    if (!isValid) {
      // 验证失败，不更新名称
      return null;
    }

    const updatedAbility = {
      ...ability,
      updatedAt: generateTimestamp(),
      name,
    };

    set(abilityDb.set, abilityId, updatedAbility);

    // 触发更新
    get(abilitiesAtom).refetch();

    return updatedAbility;
  },
);

/**
 * 更新草稿能力内容
 */
export const updateAbilityContentAtom = atom(
  null,
  (get, set, abilityId: string, content: string) => {
    const ability = get(abilityDb.item(abilityId));

    if (ability) {
      set(abilityDb.set, abilityId, {
        ...ability,
        updatedAt: generateTimestamp(),
        content,
      });

      // 触发更新
      get(abilitiesAtom).refetch();
    }
  },
);

/**
 * 从所有能力中移除对这个模块的引用
 */
export const removeModuleFromAbilitiesAtom = atom(
  null,
  (get, set, moduleId: string) => {
    const abilities = get(abilityDb.values);

    abilities.forEach((ability) => {
      const updatedContent = removeModuleFromAbilityContent(
        ability.content,
        moduleId,
      );

      // 只有当内容发生变化时才更新能力
      if (updatedContent !== ability.content) {
        const updatedAbility: DraftAbility = {
          ...ability,
          updatedAt: generateTimestamp(),
          content: updatedContent,
        };

        set(abilityDb.set, ability.id, updatedAbility);
      }
    });

    // 触发更新
    get(abilitiesAtom).refetch();
  },
);

/**
 * 从指定能力中移除对这个模块的引用
 */
export const removeModuleFromAbilityItemAtom = atom(
  null,
  (
    get,
    set,
    abilityId: string,
    abilityItemType: AbilityItemType,
    moduleId: string,
  ) => {
    const ability = get(abilityDb.item(abilityId));

    if (ability) {
      const updatedContent = removeModuleFromAbilityItem(
        ability.content,
        abilityItemType,
        moduleId,
      );

      if (updatedContent !== ability.content) {
        const updatedAbility: DraftAbility = {
          ...ability,
          updatedAt: generateTimestamp(),
          content: updatedContent,
        };

        set(abilityDb.set, ability.id, updatedAbility);
      }
    }

    // 触发更新
    get(abilitiesAtom).refetch();
  },
);
