import { atom } from "jotai";
import { v4 as uuidv4 } from "uuid";
import {
  createDraftData,
  generateTimestamp,
  getCompleteVersionData,
} from "../../utils";
import {
  abilityDb,
  moduleDb,
  moduleLabelDb,
  selectedVersionIdAtom,
  sharedVariableDb,
  versionDb,
} from "../core";

/**
 * 基于现有版本创建草稿版本
 */
export const createDraftFromVersionAtom = atom(
  null,
  async (get, set, sourceVersionId: string, draftName: string) => {
    try {
      // 1. 获取源版本的完整数据
      const sourceData = await getCompleteVersionData(sourceVersionId);

      // 2. 生成新的草稿版本ID和时间戳
      const draftId = `draft-${uuidv4()}`;
      const timestamp = generateTimestamp();

      await get(versionDb.suspendBeforeInit);
      await get(abilityDb.suspendBeforeInit);
      await get(moduleDb.suspendBeforeInit);
      await get(moduleLabelDb.suspendBeforeInit);
      await get(sharedVariableDb.suspendBeforeInit);

      // 3. 使用纯函数创建草稿数据
      const {
        draftVersion,
        draftModuleLabels,
        draftSharedVariables,
        draftModules,
        draftAbilities,
      } = createDraftData(
        sourceData,
        sourceVersionId,
        draftName,
        draftId,
        timestamp,
      );

      // 4. 保存所有数据到数据库
      set(versionDb.set, draftId, draftVersion);

      draftModuleLabels.forEach((label) => {
        set(moduleLabelDb.set, label.id, label);
      });

      draftSharedVariables.forEach((variable) => {
        set(sharedVariableDb.set, variable.id, variable);
      });

      draftModules.forEach((module) => {
        set(moduleDb.set, module.id, module);
      });

      draftAbilities.forEach((ability) => {
        set(abilityDb.set, ability.id, ability);
      });

      // 5. 选中新创建的草稿版本
      set(selectedVersionIdAtom, draftId);

      return {
        draftId,
      };
    } catch (error) {
      console.error("创建草稿版本失败:", error);
      throw error;
    }
  },
);
