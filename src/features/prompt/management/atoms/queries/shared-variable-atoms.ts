import { getQueryClient } from "@/services/query-client";
import { SharedVariableService } from "@/services/shared-variable-service";
import { Getter } from "jotai";
import { atomWithQuery } from "jotai-tanstack-query";
import { loadable } from "jotai/utils";
import { UniversalSharedVariable } from "../../types";
import { isDraftId } from "../../utils";
import { sharedVariableDb } from "../core";
import { createUniversalQueryAtom, DataSource } from "./data-sources";

/**
 * 共享变量数据源适配器
 */
const sharedVariableDataSource: DataSource<UniversalSharedVariable[]> = {
  queryKey: (versionId: string | null) => {
    if (!versionId) {
      return ["shared-variables", "null"];
    }

    return ["shared-variables", versionId];
  },

  fetch: async (versionId: string, get: Getter) => {
    // 检查是否为草稿版本
    const isDraft = isDraftId(versionId);

    if (isDraft) {
      await get(sharedVariableDb.suspendBeforeInit);

      // 草稿数据处理
      const variables = get(sharedVariableDb.values);

      return variables
        .filter((variable) => variable.promptVersionId === versionId)
        .map<UniversalSharedVariable>((variable) => ({
          id: variable.id,
          name: variable.name,
          content: variable.content,
          definition: variable.definition,
          level: variable.level,
          scopeDetail: variable.scopeDetail,
          promptName: variable.promptName,
          promptVersionId: versionId,
        }));
    }

    // 服务端数据处理
    const variables = await SharedVariableService.selectSharedVariableList({
      promptVersionId: versionId,
    });

    return (variables ?? []).map<UniversalSharedVariable>((variable) => ({
      id: variable.sharedVariableId.toString(),
      name: variable.name,
      content: variable.content,
      definition: variable.definition,
      level: variable.level,
      scopeDetail: variable.scopeDetail,
      promptName: variable.promptName,
      promptVersionId: versionId,
    }));
  },
};

/**
 * 共享变量列表查询
 */
export const sharedVariablesAtom = atomWithQuery(
  createUniversalQueryAtom(sharedVariableDataSource),
  getQueryClient,
);

export const loadableSharedVariablesAtom = loadable(sharedVariablesAtom);
