import { variableQueryKeys } from "@/constants/query-keys";
import { getQueryClient } from "@/services/query-client";
import { variableService } from "@/services/variable-service";
import { VariableType } from "@/types/api/prompt";
import { atomWithQuery } from "jotai-tanstack-query";

/**
 * 系统变量列表
 */
export const systemVariablesAtom = atomWithQuery(
  () => ({
    queryKey: variableQueryKeys.system(),
    queryFn: async () =>
      variableService.selectVariableList({
        type: VariableType.System,
      }),
  }),
  getQueryClient,
);

/**
 * 店铺变量列表
 */
export const shopVariablesAtom = atomWithQuery(
  () => ({
    queryKey: variableQueryKeys.shop(),
    queryFn: async () =>
      variableService.selectVariableList({
        type: VariableType.Shop,
      }),
  }),
  getQueryClient,
);
