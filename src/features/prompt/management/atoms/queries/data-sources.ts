import { Query<PERSON>ey } from "@tanstack/react-query";
import { Getter } from "jotai";
import { selectedVersionIdAtom } from "../core";

/**
 * 数据源接口
 */
export interface DataSource<T> {
  queryKey: (versionId: string | null, get: Getter) => QueryKey;
  fetch: (versionId: string, get: Getter) => Promise<T>;
}

/**
 * 通用查询原子工厂
 */
export const createUniversalQueryAtom = <T>(dataSource: DataSource<T>) => {
  return (get: Getter) => {
    const versionId = get(selectedVersionIdAtom);

    return {
      queryKey: dataSource.queryKey(versionId, get),
      queryFn: () => dataSource.fetch(versionId!, get),
      enabled: versionId != null,
    };
  };
};
