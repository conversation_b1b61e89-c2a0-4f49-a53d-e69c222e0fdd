import { ModuleService } from "@/services/module-service";
import { getQueryClient } from "@/services/query-client";
import { Getter } from "jotai";
import { atomWithQuery } from "jotai-tanstack-query";
import { UniversalModule } from "../../types";
import { isDraftId } from "../../utils";
import { moduleDb, selectedModuleLabelIdAtom } from "../core";
import { createUniversalQueryAtom, DataSource } from "./data-sources";

/**
 * 获取模块列表
 *
 * @param versionId
 * @param get
 * @returns
 */
const getModules = async (versionId: string, get: Getter) => {
  // 检查是否为草稿版本
  const isDraft = isDraftId(versionId);

  if (isDraft) {
    await get(moduleDb.suspendBeforeInit);

    // 草稿数据处理
    const modules = get(moduleDb.values);

    return modules
      .filter((module) => module.promptVersionId === versionId)
      .map<UniversalModule>((module) => ({
        id: module.id,
        name: module.name,
        moduleLabelIds: module.moduleLabelIds,
        level: module.level,
        promptVersionId: versionId,
        prompt: module.prompt,
        scopeDetail: module.scopeDetail,
      }));
  }

  // 服务端数据处理
  const modules = await ModuleService.selectModuleList({
    promptVersionId: versionId,
  });

  return (modules ?? []).map<UniversalModule>((module) => ({
    id: module.moduleId.toString(),
    name: module.name,
    moduleLabelIds: module.moduleLabelIds,
    promptVersionId: versionId,
    prompt: module.prompt,
    scopeDetail: module.scopeDetail,
    level: module.level,
  }));
};

const createModuleTableDataSource: DataSource<{ data: UniversalModule[] }> = {
  queryKey: (versionId: string | null, get: Getter) => {
    const selectedModuleLabelId = get(selectedModuleLabelIdAtom);

    if (!versionId) {
      return ["modules", "table", "null", selectedModuleLabelId];
    }

    return ["modules", "table", versionId, selectedModuleLabelId];
  },

  fetch: async (versionId: string, get: Getter) => {
    const selectedModuleLabelId = get(selectedModuleLabelIdAtom);
    const modules = await getModules(versionId, get);
    const filterModules = modules.filter((module) =>
      selectedModuleLabelId
        ? module.moduleLabelIds?.includes(selectedModuleLabelId)
        : true,
    );

    return {
      data: filterModules,
    };
  },
};

const createModulesDataSource: DataSource<UniversalModule[]> = {
  queryKey: (versionId: string | null) => {
    if (!versionId) {
      return ["modules", "list", "null"];
    }

    return ["modules", "list", versionId];
  },

  fetch: getModules,
};

/**
 * 通过模块标签过滤的模块表格数据
 */
export const modulesFilteredByLabelIdAtom = atomWithQuery(
  createUniversalQueryAtom(createModuleTableDataSource),
  getQueryClient,
);

/**
 * 模块列表查询，包含所有数据
 */
export const modulesAtom = atomWithQuery(
  createUniversalQueryAtom(createModulesDataSource),
  getQueryClient,
);
