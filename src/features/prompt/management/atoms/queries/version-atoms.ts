import { promptVersionQueryKeys } from "@/constants/query-keys";
import { PromptVersionService } from "@/services/prompt-version-service";
import { getQueryClient } from "@/services/query-client";
import { atom } from "jotai";
import { atomWithQuery } from "jotai-tanstack-query";
import { loadable } from "jotai/utils";
import { versionDb } from "../core";

/**
 * 提示词版本列表查询
 */
export const versionsAtom = atomWithQuery(
  () => ({
    queryKey: promptVersionQueryKeys.list({}),
    queryFn: () => PromptVersionService.selectPromptVersionList({}),
    refetchOnWindowFocus: false,
  }),
  getQueryClient,
);

export const loadableVersionsAtom = loadable(versionsAtom);

/**
 * 草稿版本
 */
export const draftVersionsAtom = atom(async (get) => {
  await get(versionDb.suspendBeforeInit);

  return get(versionDb.values);
});

export const loadableDraftVersionsAtom = loadable(draftVersionsAtom);
