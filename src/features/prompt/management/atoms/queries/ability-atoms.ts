import { AbilityService } from "@/services/ability-service";
import { getQueryClient } from "@/services/query-client";
import { Getter } from "jotai";
import { atomWithQuery } from "jotai-tanstack-query";
import { loadable } from "jotai/utils";
import { UniversalAbility } from "../../types";
import { isDraftId } from "../../utils";
import { abilityDb } from "../core";
import { createUniversalQueryAtom, DataSource } from "./data-sources";

/**
 * 能力数据源适配器
 */
const abilityDataSource: DataSource<UniversalAbility[]> = {
  queryKey: (versionId: string | null) => {
    if (!versionId) {
      return ["abilities", "null"];
    }

    // 检查是否为草稿版本
    return ["abilities", versionId];
  },

  fetch: async (versionId: string, get: Getter) => {
    // 检查是否为草稿版本
    const isDraft = isDraftId(versionId);

    if (isDraft) {
      await get(abilityDb.suspendBeforeInit);

      // 草稿数据处理
      const abilities = get(abilityDb.values);

      return abilities
        .filter((ability) => ability.promptVersionId === versionId)
        .map((ability) => ({
          id: ability.id,
          name: ability.name,
          content: ability.content,
          promptVersionId: versionId,
        }));
    }

    // 服务端数据处理
    const abilities = await AbilityService.selectAbilityList({
      promptVersionId: versionId,
    });

    return (abilities ?? []).map((ability) => ({
      id: ability.abilityId.toString(),
      name: ability.name,
      content: ability.content,
      promptVersionId: versionId,
    }));
  },
};

/**
 * 能力列表查询
 */
export const abilitiesAtom = atomWithQuery(
  createUniversalQueryAtom(abilityDataSource),
  getQueryClient,
);

export const loadableAbilitiesAtom = loadable(abilitiesAtom);
