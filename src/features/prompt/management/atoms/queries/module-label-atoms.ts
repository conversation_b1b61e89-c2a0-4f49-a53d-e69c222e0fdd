import { ModuleService } from "@/services/module-service";
import { getQueryClient } from "@/services/query-client";
import { Get<PERSON> } from "jotai";
import { atomWithQuery } from "jotai-tanstack-query";
import { UniversalModuleLabel } from "../../types";
import { isDraftId } from "../../utils";
import { moduleLabelDb } from "../core";
import { createUniversalQueryAtom, DataSource } from "./data-sources";

const moduleLabelDataSource: DataSource<UniversalModuleLabel[]> = {
  queryKey: (versionId: string | null) => {
    if (!versionId) {
      return ["module-labels", "null"];
    }

    return ["module-labels", versionId];
  },

  fetch: async (versionId: string, get: Getter) => {
    // 检查是否为草稿版本
    const isDraft = isDraftId(versionId);

    if (isDraft) {
      await get(moduleLabelDb.suspendBeforeInit);

      // 草稿数据处理
      const labels = get(moduleLabelDb.values);

      return labels
        .filter((label) => label.promptVersionId === versionId)
        .map<UniversalModuleLabel>((label) => ({
          id: label.id,
          name: label.name,
          promptVersionId: versionId,
        }));
    }

    // 服务端数据处理
    const labels = await ModuleService.selectModuleLabelList({
      promptVersionId: versionId,
    });

    return (labels ?? []).map((label) => ({
      id: label.moduleLabelId.toString(),
      name: label.name,
      promptVersionId: versionId,
    }));
  },
};

export const moduleLabelsAtom = atomWithQuery(
  createUniversalQueryAtom(moduleLabelDataSource),
  getQueryClient,
);
