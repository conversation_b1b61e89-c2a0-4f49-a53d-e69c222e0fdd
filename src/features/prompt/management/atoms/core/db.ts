import { MiniDb } from "jotai-minidb";
import {
  DraftAbility,
  DraftModule,
  DraftModuleLabel,
  DraftSharedVariable,
  DraftVersion,
} from "../../types";
import { SyncQueue } from "../../services/sync/types";

/**
 * 共享变量表
 */
export const sharedVariableDb = new MiniDb<DraftSharedVariable>({
  name: "shared-variable",
  version: 0,
});

/**
 * 版本表
 */
export const versionDb = new MiniDb<DraftVersion>({
  name: "prompt-version",
  version: 0,
});

/**
 * 能力表
 */
export const abilityDb = new MiniDb<DraftAbility>({
  name: "ability",
  version: 0,
});

/**
 * 模块表
 */
export const moduleDb = new MiniDb<DraftModule>({
  name: "module",
  version: 0,
});

/**
 * 模块标签表
 */
export const moduleLabelDb = new MiniDb<DraftModuleLabel>({
  name: "module-label",
  version: 0,
});

/**
 * 同步队列表
 */
export const syncQueueDb = new MiniDb<SyncQueue>({
  name: "sync-queue",
  version: 0,
});
