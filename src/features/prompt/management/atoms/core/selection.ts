import { atom } from "jotai";
import { atomWithStorage } from "jotai/utils";

const SELECTED_PROMPT_VERSION_ID_KEY = "selectedPromptVersionId";

/**
 * 当前选中的提示词版本ID
 */
export const selectedVersionIdAtom = atomWithStorage<string | null>(
  SELECTED_PROMPT_VERSION_ID_KEY,
  null,
);

/**
 * 当前选中的能力ID
 */
export const selectedAbilityIdAtom = atom<string | null>(null);

/**
 * 当前选中的模块标签
 */
export const selectedModuleLabelIdAtom = atom<string | null>();

/**
 * 当前选中的共享变量
 */
export const selectedSharedVariableIdAtom = atom<string | null>();
