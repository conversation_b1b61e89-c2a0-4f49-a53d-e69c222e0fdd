import { atomEffect } from "jotai-effect";
import { isDraftVersion } from "../../utils";
import {
  selectedAbilityIdAtom,
  selectedSharedVariableIdAtom,
  selectedVersionIdAtom,
} from "../core";
import {
  loadableAbilitiesAtom,
  loadableDraftVersionsAtom,
  loadableSharedVariablesAtom,
  loadableVersionsAtom,
} from "../queries";

/**
 * 版本自动选择逻辑（特殊处理，因为需要合并两个数据源）
 */
export const autoSelectVersionEffect = atomEffect((get, set) => {
  const loadableVersions = get(loadableVersionsAtom);
  const loadableDraftVersions = get(loadableDraftVersionsAtom);

  // 错误处理：如果任一数据源出错，清空选择
  if (
    loadableVersions.state === "hasError" ||
    loadableDraftVersions.state === "hasError"
  ) {
    set(selectedVersionIdAtom, null);

    return;
  }

  // 只有当至少一个数据源有数据时才处理
  const hasVersionsData =
    loadableVersions.state === "hasData" &&
    loadableVersions.data.status !== "pending";
  const hasDraftData = loadableDraftVersions.state === "hasData";

  if (!hasVersionsData || !hasDraftData) {
    return;
  }

  const draftVersions = hasDraftData ? loadableDraftVersions.data : [];
  const versions = hasVersionsData ? loadableVersions.data.data : [];
  const hasVersions = (versions?.length ?? 0) + (draftVersions.length ?? 0) > 0;

  if (hasVersions) {
    const selectedVersionId = get(selectedVersionIdAtom);
    const allVersions = [...draftVersions, ...(versions ?? [])];

    if (
      // 没有选中的版本
      !selectedVersionId ||
      // 选中的版本不存在
      (!draftVersions.find((version) => version.id === selectedVersionId) &&
        !versions?.find(
          (version) => version.promptVersionId === selectedVersionId,
        ))
    ) {
      const firstVersion = allVersions[0];

      set(
        selectedVersionIdAtom,
        isDraftVersion(firstVersion)
          ? firstVersion.id
          : firstVersion.promptVersionId,
      );
    }
  } else {
    set(selectedVersionIdAtom, null);
  }
});

/**
 * 监听能力列表变化，自动选择第一个能力
 */
export const autoSelectAbilityEffect = atomEffect((get, set) => {
  const abilities = get(loadableAbilitiesAtom);

  // 错误处理：如果数据源出错，清空选择
  if (abilities.state === "hasError") {
    set(selectedAbilityIdAtom, null);

    return;
  }

  // 只有当数据源有数据时才处理
  if (abilities.state !== "hasData" || abilities.data.status === "pending") {
    return;
  }

  const abilitiesData = abilities.data.data;
  const hasAbilities = (abilitiesData?.length ?? 0) > 0;
  const selectedAbility = get(selectedAbilityIdAtom);

  if (hasAbilities) {
    if (
      // 没有选中的能力
      !selectedAbility ||
      // 选中的能力不存在
      !abilitiesData?.find((a) => a.id === selectedAbility)
    ) {
      const firstAbility = abilitiesData?.[0];

      if (!firstAbility) {
        console.error("Failed to auto select ability");

        return;
      }

      set(selectedAbilityIdAtom, firstAbility.id);
    }
  }
});

/**
 * 监听共享变量列表变化，自动选择第一个共享变量
 */
export const autoSelectSharedVariableEffect = atomEffect((get, set) => {
  const variables = get(loadableSharedVariablesAtom);

  // 错误处理：如果数据源出错，清空选择
  if (variables.state === "hasError") {
    set(selectedSharedVariableIdAtom, null);

    return;
  }

  // 只有当数据源有数据时才处理
  if (variables.state !== "hasData" || variables.data.status === "pending") {
    return;
  }

  const variablesData = variables.data.data;
  const hasVariables = (variablesData?.length ?? 0) > 0;
  const selectedSharedVariableId = get(selectedSharedVariableIdAtom);

  if (hasVariables) {
    if (
      // 没有选中的共享变量
      !selectedSharedVariableId ||
      // 选中的共享变量不存在
      !variablesData?.find((v) => v.id === selectedSharedVariableId)
    ) {
      const firstVariable = variablesData?.[0];

      if (!firstVariable) {
        console.error("Failed to auto select shared variable");

        return;
      }

      set(selectedSharedVariableIdAtom, firstVariable.id);
    }
  }
});
