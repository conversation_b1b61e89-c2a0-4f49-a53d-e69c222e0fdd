import { describe, it, expect } from "vitest";
import { Level, VariableType } from "@/types/api/prompt";
import { Variable } from "@/types/api/variable";
import { SharedVariable } from "@/types/api/shared-variable";
import { createVariableFormSchema } from "../variable-schema";

describe("variable-schema", () => {
  const systemVariables: Variable[] = [
    {
      variableId: "sys-1",
      name: "现有系统变量",
      promptName: "existingSysVar",
      level: Level.Product,
      type: VariableType.System,
      definition: "系统变量定义",
      example: "系统变量示例",
    },
  ];

  const shopVariables: Variable[] = [
    {
      variableId: "shop-1",
      name: "现有店铺变量",
      promptName: "existingShopVar",
      level: Level.Shop,
      type: VariableType.Shop,
      definition: "店铺变量定义",
      example: "店铺变量示例",
      scopeDetail: '["shop1"]',
    },
  ];

  const sharedVariables: SharedVariable[] = [
    {
      sharedVariableId: "shared-1",
      promptVersionId: "version-1",
      name: "现有共享变量",
      promptName: "existingSharedVar",
      level: Level.Product,
      definition: "共享变量定义",
      content: "共享变量内容",
      status: 1,
    },
  ];

  describe("createVariableFormSchema", () => {
    it("应该通过有效的数据验证", async () => {
      const schema = createVariableFormSchema(
        systemVariables,
        shopVariables,
        sharedVariables,
      );

      const validData = {
        type: VariableType.System,
        name: "新变量名",
        promptName: "newVarName",
        level: Level.Product,
        definition: "新变量定义",
        example: "新变量示例",
        scopeDetail: [],
      };

      const result = await schema.safeParseAsync(validData);
      expect(result.success).toBe(true);
    });

    it("应该拒绝重复的变量名称", async () => {
      const schema = createVariableFormSchema(
        systemVariables,
        shopVariables,
        sharedVariables,
      );

      const invalidData = {
        type: VariableType.System,
        name: "现有系统变量", // 重复的名称
        promptName: "newVarName",
        level: Level.Product,
        definition: "新变量定义",
        example: "新变量示例",
        scopeDetail: [],
      };

      const result = await schema.safeParseAsync(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        const nameError = result.error.issues.find((issue) =>
          issue.path.includes("name"),
        );
        expect(nameError?.message).toBe("变量名称与系统变量重复");
      }
    });

    it("应该拒绝重复的提示词变量名称", async () => {
      const schema = createVariableFormSchema(
        systemVariables,
        shopVariables,
        sharedVariables,
      );

      const invalidData = {
        type: VariableType.System,
        name: "新变量名",
        promptName: "existingShopVar", // 重复的提示词变量名
        level: Level.Product,
        definition: "新变量定义",
        example: "新变量示例",
        scopeDetail: [],
      };

      const result = await schema.safeParseAsync(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        const promptNameError = result.error.issues.find((issue) =>
          issue.path.includes("promptName"),
        );
        expect(promptNameError?.message).toBe("提示词变量名称与店铺变量重复");
      }
    });

    it("应该拒绝与共享变量重复的名称", async () => {
      const schema = createVariableFormSchema(
        systemVariables,
        shopVariables,
        sharedVariables,
      );

      const invalidData = {
        type: VariableType.System,
        name: "现有共享变量", // 与共享变量重复
        promptName: "newVarName",
        level: Level.Product,
        definition: "新变量定义",
        example: "新变量示例",
        scopeDetail: [],
      };

      const result = await schema.safeParseAsync(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        const nameError = result.error.issues.find((issue) =>
          issue.path.includes("name"),
        );
        expect(nameError?.message).toBe("变量名称与共享变量重复");
      }
    });

    it("应该在编辑模式下排除当前变量", async () => {
      const schema = createVariableFormSchema(
        systemVariables,
        shopVariables,
        sharedVariables,
        "sys-1", // 排除当前编辑的变量
      );

      const validData = {
        type: VariableType.System,
        name: "现有系统变量", // 与当前编辑的变量同名，应该被排除
        promptName: "existingSysVar",
        level: Level.Product,
        definition: "系统变量定义",
        example: "系统变量示例",
        scopeDetail: [],
      };

      const result = await schema.safeParseAsync(validData);
      expect(result.success).toBe(true);
    });

    it("应该同时检测变量名和提示词变量名重复", async () => {
      const schema = createVariableFormSchema(
        systemVariables,
        shopVariables,
        sharedVariables,
      );

      const invalidData = {
        type: VariableType.System,
        name: "现有系统变量", // 重复的变量名
        promptName: "existingShopVar", // 重复的提示词变量名
        level: Level.Product,
        definition: "新变量定义",
        example: "新变量示例",
        scopeDetail: [],
      };

      const result = await schema.safeParseAsync(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        const nameError = result.error.issues.find((issue) =>
          issue.path.includes("name"),
        );
        const promptNameError = result.error.issues.find((issue) =>
          issue.path.includes("promptName"),
        );

        expect(nameError?.message).toBe("变量名称与系统变量重复");
        expect(promptNameError?.message).toBe("提示词变量名称与店铺变量重复");
      }
    });

    it("应该保持原有的验证规则", async () => {
      const schema = createVariableFormSchema(
        systemVariables,
        shopVariables,
        sharedVariables,
      );

      // 测试空名称
      const invalidData1 = {
        type: VariableType.System,
        name: "", // 空名称
        promptName: "validPromptName",
        level: Level.Product,
        definition: "定义",
        example: "示例",
        scopeDetail: [],
      };

      const result1 = await schema.safeParseAsync(invalidData1);
      expect(result1.success).toBe(false);

      // 测试无效的提示词变量名格式
      const invalidData2 = {
        type: VariableType.System,
        name: "有效名称",
        promptName: "123invalid", // 不能以数字开头
        level: Level.Product,
        definition: "定义",
        example: "示例",
        scopeDetail: [],
      };

      const result2 = await schema.safeParseAsync(invalidData2);
      expect(result2.success).toBe(false);
    });
  });
});
