import {
  AsyncDataTable,
  DataTableToolbar,
  DataTableViewSwitcher,
  useTableState,
  ViewOption,
} from "@/components/common/data-table";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { VariableTypeLabels } from "@/constants/prompt";
import { variableQueryKeys } from "@/constants/query-keys";
import {
  sharedVariablesAtom,
  shopVariablesAtom,
  systemVariablesAtom,
} from "@/features/prompt/management/atoms";
import { variableService } from "@/services/variable-service";
import { VariableType } from "@/types/api/prompt";
import { Variable } from "@/types/api/variable";
import { useQuery } from "@tanstack/react-query";
import { useNavigate, useSearch } from "@tanstack/react-router";
import { useAtomValue } from "jotai";
import { Braces, Hash, Plus } from "lucide-react";
import React, { useCallback, useMemo, useState } from "react";
import { toast } from "sonner";
import {
  useDeleteVariable,
  useUpdateVariableField,
} from "../hooks/use-variable-data";
import { getVariableColumns } from "./variable-columns";

interface VariableTableProps {
  onAddVariable?: (type: VariableType) => void;
}

export const VariableTable: React.FC<VariableTableProps> = ({
  onAddVariable,
}) => {
  const navigate = useNavigate({
    from: "/prompt-management/variable",
  });
  const search = useSearch({
    from: "/_app/prompt-management/variable",
  });
  const type = search.type || VariableType.System;
  const [deleteConfirm, setDeleteConfirm] = useState<{
    variableIds: string[];
    variableNames: string[];
  } | null>(null);
  // 系统变量表格状态
  const tableState = useTableState<Variable>({
    enablePagination: false,
  });

  // 获取所有变量数据用于重复检查
  const { data: systemVariables = [] } = useAtomValue(systemVariablesAtom);
  const { data: shopVariables = [] } = useAtomValue(shopVariablesAtom);
  const { data: sharedVariables = [] } = useAtomValue(sharedVariablesAtom);

  // 数据查询
  const systemQuery = useQuery({
    queryKey: variableQueryKeys.systemTable(),
    queryFn: () =>
      variableService
        .selectVariableList({ type: VariableType.System })
        .then((data) => ({
          data,
        })),
  });
  const shopQuery = useQuery({
    queryKey: variableQueryKeys.shopTable(),
    queryFn: () =>
      variableService
        .selectVariableList({ type: VariableType.Shop })
        .then((data) => ({
          data,
        })),
  });
  // 变更操作
  const updateFieldMutation = useUpdateVariableField();
  const deleteVariableMutation = useDeleteVariable();

  // 定义视图选项
  const viewOptions: ViewOption[] = useMemo(
    () => [
      {
        id: VariableType.System,
        label: "系统变量",
        icon: <Hash className="size-4" />,
        count: systemQuery.data?.data.length,
      },
      {
        id: VariableType.Shop,
        label: "店铺变量",
        icon: <Braces className="size-4" />,
        count: shopQuery.data?.data.length,
      },
    ],
    [shopQuery.data, systemQuery.data],
  );

  // 处理字段更新
  const handleUpdateField = useCallback(
    async (variableId: string, field: string, value: any) => {
      const originalData = shopQuery.data?.data?.find(
        (v) => v.variableId === variableId,
      );

      if (!originalData) {
        toast.error("找不到要更新的变量");
        return;
      }

      await updateFieldMutation.mutateAsync({
        variableId,
        field,
        value,
        originalData,
      });
    },
    [shopQuery.data, updateFieldMutation],
  );

  // 处理单个删除
  const handleDeleteSingle = useCallback(
    (variableId: string) => {
      const variable = shopQuery.data?.data.find(
        (v) => v.variableId === variableId,
      );

      if (!variable) {
        return;
      }

      setDeleteConfirm({
        variableIds: [variableId],
        variableNames: [variable.name],
      });
    },
    [shopQuery.data],
  );

  // 确认删除
  const confirmDelete = async () => {
    if (!deleteConfirm) {
      return;
    }

    await deleteVariableMutation.mutateAsync(deleteConfirm.variableIds[0]);
    setDeleteConfirm(null);
  };

  // 表格列配置
  const columns = useMemo(
    () =>
      getVariableColumns({
        onUpdate: type === VariableType.Shop ? handleUpdateField : undefined,
        onDelete: type === VariableType.Shop ? handleDeleteSingle : undefined,
        systemVariables,
        shopVariables,
        sharedVariables,
      }),
    [
      type,
      handleUpdateField,
      handleDeleteSingle,
      systemVariables,
      shopVariables,
      sharedVariables,
    ],
  );

  // 处理视图切换
  const handleTypeChange = useCallback(
    (type: VariableType) => {
      navigate({
        search: {
          type,
        },
      });
    },
    [navigate],
  );

  return (
    <>
      <AsyncDataTable
        query={type === VariableType.System ? systemQuery : shopQuery}
        columns={columns}
        className="h-full"
        tableName={`variable-table-${type}`}
        initialState={{
          columnPinning: {
            right: ["actions"],
          },
        }}
        getRowId={(row) => row.variableId.toString()}
        {...tableState}
      >
        {(table) => (
          <DataTableViewSwitcher
            viewOptions={viewOptions}
            currentView={type}
            onViewChange={(view) => {
              handleTypeChange(view as VariableType);
            }}
          >
            <DataTableToolbar
              table={table}
              enableSort={false}
              filters={tableState.globalFilter}
              setFilterValue={tableState.setFilterValue}
              onResetFilters={tableState.resetFilters}
            >
              {type === VariableType.Shop && (
                <Button
                  onClick={() => {
                    onAddVariable?.(type);
                  }}
                  size="sm"
                  icon={<Plus />}
                >
                  添加{VariableTypeLabels[type]}
                </Button>
              )}
            </DataTableToolbar>
          </DataTableViewSwitcher>
        )}
      </AsyncDataTable>

      {/* 删除确认对话框 */}
      <AlertDialog
        open={deleteConfirm !== null}
        onOpenChange={(open) => !open && setDeleteConfirm(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              {deleteConfirm && (
                <span>
                  确定要删除变量
                  <strong>"{deleteConfirm.variableNames[0]}"</strong> 吗？
                </span>
              )}
              <div className="mt-2 text-sm text-muted-foreground">
                此操作不可撤销。
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              disabled={deleteVariableMutation.isPending}
            >
              {deleteVariableMutation.isPending ? "删除中..." : "确认删除"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};
