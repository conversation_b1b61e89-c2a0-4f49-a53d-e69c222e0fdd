import { CategoryDropdownSelector } from "@/components/common/category-dropdown-selector";
import { ExtendedDialog } from "@/components/common/extended-dialog";
import { ShopMultiSelector } from "@/components/common/shop-multi-selector";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { getLevelOptions } from "@/constants/prompt";
import {
  sharedVariablesAtom,
  shopVariablesAtom,
  systemVariablesAtom,
} from "@/features/prompt/management/atoms";
import { Level, VariableType } from "@/types/api/prompt";
import { Variable } from "@/types/api/variable";
import { zodResolver } from "@hookform/resolvers/zod";
import { useAtomValue } from "jotai";
import { FC, useCallback, useEffect, useMemo } from "react";
import { useForm, useWatch } from "react-hook-form";
import { useAddOrUpdateVariable } from "../hooks/use-variable-data";
import {
  VariableFormData,
  createVariableFormSchema,
} from "../schemas/variable-schema";

interface VariableFormProps {
  open: boolean;
  type: VariableType;
  editData?: Variable | null;
  onClose: () => void;
}

export const VariableForm: FC<VariableFormProps> = ({
  open,
  type,
  editData,
  onClose,
}) => {
  const addOrUpdateMutation = useAddOrUpdateVariable();

  // 获取所有变量数据用于重复检查
  const { data: systemVariables = [] } = useAtomValue(systemVariablesAtom);
  const { data: shopVariables = [] } = useAtomValue(shopVariablesAtom);
  const { data: sharedVariables = [] } = useAtomValue(sharedVariablesAtom);

  // 创建带有重复检查的schema
  const validationSchema = useMemo(
    () =>
      createVariableFormSchema(
        systemVariables,
        shopVariables,
        sharedVariables,
        editData?.variableId,
      ),
    [systemVariables, shopVariables, sharedVariables, editData?.variableId],
  );

  const form = useForm<VariableFormData>({
    resolver: zodResolver(validationSchema),
    mode: "onChange",
    defaultValues: {
      type,
      name: "",
      promptName: "",
      definition: "",
      example: "",
      level: Level.Product,
      scopeDetail: [],
    },
  });

  // 监听 level 字段变化
  const currentLevel = useWatch({
    control: form.control,
    name: "level",
  });

  // 解析 scopeDetail JSON 字符串为数组
  const parseScopeDetail = useCallback((scopeDetail?: string): string[] => {
    if (!scopeDetail) return [];

    try {
      const parsed = JSON.parse(scopeDetail);
      return Array.isArray(parsed) ? parsed : [];
    } catch {
      return [];
    }
  }, []);

  // 当编辑数据变化时，更新表单
  useEffect(() => {
    if (editData) {
      form.reset({
        type: editData.type,
        variableId: editData.variableId,
        name: editData.name,
        promptName: editData.promptName,
        definition: editData.definition,
        example: editData.example,
        level: editData.level,
        scopeDetail: parseScopeDetail(editData.scopeDetail),
      });
    } else {
      form.reset({
        type,
        name: "",
        promptName: "",
        definition: "",
        example: "",
        level: Level.Product,
        scopeDetail: [],
      });
    }
  }, [editData, type, form, parseScopeDetail]);

  const handleSubmit = async () => {
    const isSuccess = await form.trigger();

    if (!isSuccess) {
      return;
    }

    const data = form.getValues();

    try {
      await addOrUpdateMutation.mutateAsync({
        ...data,
        variableId: editData?.variableId,
        // 将 scopeDetail 数组转换为 JSON 字符串
        scopeDetail:
          data.scopeDetail && data.scopeDetail.length > 0
            ? JSON.stringify(data.scopeDetail)
            : undefined,
      });
      onClose();
    } catch (error) {
      console.error("表单提交失败:", error);
    }
  };

  const handleClose = () => {
    form.reset();
    onClose();
  };

  const title = useMemo(() => {
    const typeText = type === VariableType.System ? "系统变量" : "店铺变量";

    return editData ? `编辑${typeText}` : `添加${typeText}`;
  }, [editData, type]);

  const levelOptions = getLevelOptions();

  return (
    <ExtendedDialog
      title={title}
      open={open}
      fullHeight={false}
      width="60%"
      footer={
        <>
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={addOrUpdateMutation.isPending}
            data-testid="cancel-button"
          >
            取消
          </Button>
          <Button
            type="submit"
            disabled={addOrUpdateMutation.isPending}
            data-testid="submit-button"
            onClick={handleSubmit}
          >
            {addOrUpdateMutation.isPending
              ? "保存中..."
              : editData
                ? "更新"
                : "添加"}
          </Button>
        </>
      }
      onOpenChange={handleClose}
    >
      <Form {...form}>
        <div className="space-y-6 p-6 h-full overflow-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* 变量名称 */}
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    变量名称 <span className="text-destructive">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input placeholder="请输入变量名称" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* 提示词变量名称 */}
            <FormField
              control={form.control}
              name="promptName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    提示词变量名 <span className="text-destructive">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input placeholder="请输入提示词变量名" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* 等级 */}
            <FormField
              control={form.control}
              name="level"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    等级 <span className="text-destructive">*</span>
                  </FormLabel>
                  <Select
                    value={field.value || ""}
                    onValueChange={(value) => {
                      field.onChange(value);
                      form.setValue("scopeDetail", []);
                    }}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="请选择等级" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {levelOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* 动态范围选择器 */}
          {(currentLevel === Level.Shop || currentLevel === Level.Category) && (
            <FormField
              control={form.control}
              name="scopeDetail"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {currentLevel === Level.Shop ? "选择店铺" : "选择类目"}
                    <span className="text-red-500 ml-1">*</span>
                  </FormLabel>
                  <FormControl>
                    {currentLevel === Level.Shop ? (
                      <ShopMultiSelector
                        value={field.value || []}
                        onChange={field.onChange}
                        placeholder="请选择店铺"
                        triggerClassName="w-full"
                      />
                    ) : (
                      <CategoryDropdownSelector
                        value={field.value || []}
                        onChange={field.onChange}
                        placeholder="请选择类目"
                        triggerClassName="w-full"
                      />
                    )}
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}

          {/* 定义 */}
          <FormField
            control={form.control}
            name="definition"
            render={({ field }) => (
              <FormItem>
                <FormLabel>定义</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="请输入变量定义"
                    className="min-h-[80px]"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* 变量示例 */}
          <FormField
            control={form.control}
            name="example"
            render={({ field }) => (
              <FormItem>
                <FormLabel>变量示例</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="请输入变量示例"
                    className="min-h-[80px]"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </Form>
    </ExtendedDialog>
  );
};
