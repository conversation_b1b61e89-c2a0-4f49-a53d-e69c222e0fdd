import { CategoryDropdownSelector } from "@/components/common/category-dropdown-selector";
import { EditableField } from "@/components/common/editable-field";
import { LevelScopeSelector } from "@/components/common/level-scope-selector";
import {
  LevelBadge,
  LevelTip,
  ScopeBadge,
  ScopeTip,
  VariableStatusBadge,
} from "@/components/common/prompt";
import { ShopMultiSelector } from "@/components/common/shop-multi-selector";
import { Button } from "@/components/ui/button";
import { UniversalSharedVariable } from "@/features/prompt/management/types";
import { cn } from "@/lib/utils";
import { Level } from "@/types/api/prompt";
import { SharedVariable } from "@/types/api/shared-variable";
import { Variable, VariableStatus } from "@/types/api/variable";
import { ColumnDef } from "@tanstack/react-table";
import {
  checkVariableNameDuplication,
  generateDuplicateVariableNameMessage,
} from "../../utils";
import { validateField } from "../schemas/variable-schema";

interface VariableColumnsProps {
  onUpdate?: (
    variableId: string,
    field: string,
    value: string | Level,
  ) => Promise<void>;
  onDelete?: (variableId: string) => void;
  // 用于重复检查的变量数据
  systemVariables?: Variable[];
  shopVariables?: Variable[];
  sharedVariables?: (SharedVariable | UniversalSharedVariable)[];
}

/**
 * 获取变量管理表格列定义
 */
export const getVariableColumns = ({
  onUpdate,
  onDelete,
  systemVariables = [],
  shopVariables = [],
  sharedVariables = [],
}: VariableColumnsProps): ColumnDef<Variable>[] => [
  {
    accessorKey: "name",
    header: "变量名称",
    cell: ({ row, getValue }) => (
      <EditableField
        value={getValue() as string}
        type="text"
        onSave={
          onUpdate
            ? (value: string) =>
                onUpdate(row.original.variableId, "name", value)
            : undefined
        }
        validate={(value: string) => {
          // 先进行基础验证
          const basicValidation = validateField("name", value);
          if (!basicValidation.success) {
            return basicValidation;
          }

          // 进行重复检查
          const checkResult = checkVariableNameDuplication(
            value,
            row.original.promptName,
            systemVariables,
            shopVariables,
            sharedVariables,
            row.original.variableId,
          );

          if (checkResult.isNameDuplicate) {
            return {
              success: false,
              error: generateDuplicateVariableNameMessage(
                "变量名称",
                checkResult.duplicateNameSource!,
              ),
            };
          }

          return { success: true, error: null };
        }}
        placeholder="请输入变量名称"
      />
    ),
    meta: {
      label: "变量名称",
    },
  },
  {
    accessorKey: "promptName",
    header: "提示词变量名",
    cell: ({ row, getValue }) => (
      <EditableField
        value={getValue() as string}
        type="text"
        onSave={
          onUpdate
            ? (value: string) =>
                onUpdate(row.original.variableId, "promptName", value)
            : undefined
        }
        validate={(value: string) => {
          // 先进行基础验证
          const basicValidation = validateField("promptName", value);
          if (!basicValidation.success) {
            return basicValidation;
          }

          // 进行重复检查
          const checkResult = checkVariableNameDuplication(
            row.original.name,
            value,
            systemVariables,
            shopVariables,
            sharedVariables,
            row.original.variableId,
          );

          if (checkResult.isPromptNameDuplicate) {
            return {
              success: false,
              error: generateDuplicateVariableNameMessage(
                "提示词变量名称",
                checkResult.duplicatePromptNameSource!,
              ),
            };
          }

          return { success: true, error: null };
        }}
        placeholder="请输入提示词变量名"
      />
    ),
    meta: {
      label: "提示词变量名",
    },
  },
  {
    accessorKey: "definition",
    header: "定义",
    cell: ({ row, getValue }) => (
      <EditableField
        value={getValue() as string}
        type="textarea"
        title="定义"
        onSave={
          onUpdate
            ? (value: string) =>
                onUpdate(row.original.variableId, "definition", value)
            : undefined
        }
        validate={(value: string) => validateField("definition", value)}
        placeholder="请输入变量定义"
      />
    ),
    meta: {
      label: "定义",
    },
  },
  {
    accessorKey: "example",
    header: "示例",
    cell: ({ row, getValue }) => (
      <EditableField
        value={getValue() as string}
        type="textarea"
        title="示例"
        onSave={
          onUpdate
            ? (value: string) =>
                onUpdate(row.original.variableId, "example", value)
            : undefined
        }
        validate={(value: string) => validateField("example", value)}
      />
    ),
    meta: {
      label: "示例",
    },
  },
  {
    accessorKey: "level",
    header: () => (
      <div className="flex items-center justify-center gap-2">
        <span>等级</span>
        <LevelTip />
      </div>
    ),
    cell: ({ row, getValue }) => {
      const currentScopeDetail = row.original.scopeDetail
        ? JSON.parse(row.original.scopeDetail)
        : [];

      return (
        <EditableField
          value={getValue() as Level}
          type="custom"
          align="center"
          disabled={row.original.status === VariableStatus.USED}
          placeholder="请选择等级"
          renderDisplay={(value: Level) => <LevelBadge level={value} />}
          renderEditor={(value: Level, onChange: (value: Level) => void) => (
            <LevelScopeSelector
              value={value}
              currentScopeDetail={currentScopeDetail}
              disabled={row.original.status === VariableStatus.USED}
              onLevelChange={onChange}
              onScopeChange={(level, scopeDetail) => {
                // 同时更新等级和范围
                onUpdate?.(row.original.variableId, "level", level);
                onUpdate?.(
                  row.original.variableId,
                  "scopeDetail",
                  JSON.stringify(scopeDetail),
                );
              }}
              renderDisplay={(level: Level) => <LevelBadge level={level} />}
            />
          )}
          onSave={
            onUpdate
              ? (value: Level) =>
                  onUpdate(row.original.variableId, "level", value)
              : undefined
          }
        />
      );
    },
    meta: {
      label: "等级",
      align: "center",
    },
    minSize: 180,
  },
  {
    accessorKey: "scopeDetail",
    header: () => (
      <div className="flex items-center justify-center gap-2">
        <span>影响范围</span>
        <ScopeTip />
      </div>
    ),
    minSize: 180,
    cell: ({ row, getValue }) => {
      const variable = row.original;
      const scopeDetail = getValue() as string;

      return (
        <EditableField
          value={scopeDetail ? JSON.parse(scopeDetail) : []}
          type="custom"
          align="center"
          disabled={
            variable.level === Level.Product ||
            row.original.status === VariableStatus.USED
          }
          validate={(value: string[]) => validateField("scopeDetail", value)}
          placeholder="请选择影响范围"
          renderDisplay={(value: string[]) => (
            <ScopeBadge
              level={variable.level}
              scopeDetail={JSON.stringify(value)}
            />
          )}
          renderEditor={(
            value: string[],
            onChange: (value: string[]) => void,
          ) => {
            if (variable.level === Level.Product) {
              return null;
            }

            if (variable.level === Level.Shop) {
              return (
                <ShopMultiSelector
                  value={value}
                  onChange={onChange}
                  placeholder="请选择店铺"
                  triggerClassName="w-full"
                />
              );
            }

            if (variable.level === Level.Category) {
              return (
                <CategoryDropdownSelector
                  value={value}
                  onChange={onChange}
                  placeholder="请选择类目"
                  triggerClassName="w-full"
                />
              );
            }

            return null;
          }}
          onSave={
            onUpdate && variable.level !== Level.Product
              ? (value: string[]) =>
                  onUpdate(
                    row.original.variableId,
                    "scopeDetail",
                    JSON.stringify(value),
                  )
              : undefined
          }
        />
      );
    },
    meta: {
      label: "影响范围",
      align: "center",
    },
  },
  {
    accessorKey: "status",
    header: "状态",
    cell: ({ getValue }) => (
      <VariableStatusBadge status={getValue() as VariableStatus} />
    ),
    size: 110,
    meta: {
      label: "状态",
      align: "center",
    },
  },
  {
    id: "actions",
    header: "操作",
    size: 140,
    cell: ({ row }) => (
      <Button
        variant="link"
        disabled={row.original.status === VariableStatus.USED}
        className={cn("text-destructive", {
          invisible: !onDelete,
        })}
        onClick={() => onDelete?.(row.original.variableId)}
      >
        删除
      </Button>
    ),
    enableHiding: false,
    meta: {
      label: "操作",
    },
  },
];
