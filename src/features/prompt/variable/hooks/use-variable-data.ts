import { variableQueryKeys } from "@/constants/query-keys";
import { variableService } from "@/services/variable-service";
import { VariableType } from "@/types/api/prompt";
import { AddOrUpdateVariableParams, Variable } from "@/types/api/variable";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
/**
 * 添加或更新变量的 Mutation Hook
 */
export const useAddOrUpdateVariable = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (params: AddOrUpdateVariableParams) =>
      variableService.addOrUpdateVariable(params),
    onSuccess: (_, variables) => {
      // 刷新相关查询
      if (variables.type === VariableType.System) {
        queryClient.invalidateQueries({
          queryKey: variableQueryKeys.system(),
        });
      } else if (variables.type === VariableType.Shop) {
        queryClient.invalidateQueries({
          queryKey: variableQueryKeys.shop(),
        });
      }

      // 显示成功提示
      toast.success(variables.variableId ? "变量更新成功" : "变量添加成功");
    },
  });
};

/**
 * 删除变量的 Mutation Hook
 */
export const useDeleteVariable = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (variableId: string) =>
      variableService.deleteVariable({ variableId }),
    onSuccess: () => {
      // 刷新相关查询
      queryClient.invalidateQueries({
        queryKey: variableQueryKeys.system(),
      });
      queryClient.invalidateQueries({
        queryKey: variableQueryKeys.shop(),
      });

      toast.success("变量删除成功");
    },
  });
};

/**
 * 更新单个变量字段的 Hook
 */
export const useUpdateVariableField = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      variableId,
      field,
      value,
      originalData,
    }: {
      variableId: string;
      field: string;
      value: any;
      originalData: Variable;
    }) => {
      // 构建更新参数
      const updateParams: AddOrUpdateVariableParams = {
        variableId,
        [field]: value,
        // 保留其他必要字段
        type: originalData.type,
      };

      return variableService.addOrUpdateVariable(updateParams);
    },
    onSettled: () => {
      // 无论成功还是失败，都重新获取数据以确保一致性
      queryClient.invalidateQueries({
        queryKey: variableQueryKeys.system(),
      });
      queryClient.invalidateQueries({
        queryKey: variableQueryKeys.shop(),
      });
    },
  });
};
