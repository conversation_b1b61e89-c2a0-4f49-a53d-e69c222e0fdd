import { VariableType } from "@/types/api/prompt";
import { Variable } from "@/types/api/variable";
import React, { useState } from "react";
import { VariableForm } from "./components/variable-form";
import { VariableTable } from "./components/variable-table";

export const VariableManagement: React.FC = () => {
  const [formState, setFormState] = useState<{
    open: boolean;
    type: VariableType;
    editData?: Variable | null;
  }>({
    open: false,
    type: VariableType.System,
    editData: null,
  });

  const handleAddVariable = (type: VariableType) => {
    setFormState({
      open: true,
      type,
      editData: null,
    });
  };

  const handleCloseForm = () => {
    setFormState({
      open: false,
      type: VariableType.System,
      editData: null,
    });
  };

  return (
    <div className="h-full p-6 pt-2">
      <VariableTable onAddVariable={(type) => handleAddVariable(type)} />

      <VariableForm
        open={formState.open}
        onClose={handleCloseForm}
        type={formState.type}
        editData={formState.editData}
      />
    </div>
  );
};
