import { describe, it, expect } from "vitest";
import { Level, VariableType } from "@/types/api/prompt";
import { Variable } from "@/types/api/variable";
import { SharedVariable } from "@/types/api/shared-variable";
import { UniversalSharedVariable } from "@/features/prompt/management/types";
import {
  checkVariableNameDuplication,
  generateDuplicateVariableNameMessage,
} from "../variable-validation";

describe("variable-validation", () => {
  // 测试数据
  const systemVariables: Variable[] = [
    {
      variableId: "sys-1",
      name: "系统变量1",
      promptName: "sysVar1",
      level: Level.Product,
      type: VariableType.System,
      definition: "系统变量定义",
      example: "系统变量示例",
    },
    {
      variableId: "sys-2",
      name: "系统变量2",
      promptName: "sysVar2",
      level: Level.Product,
      type: VariableType.System,
      definition: "系统变量定义2",
      example: "系统变量示例2",
    },
  ];

  const shopVariables: Variable[] = [
    {
      variableId: "shop-1",
      name: "店铺变量1",
      promptName: "shopVar1",
      level: Level.Shop,
      type: VariableType.Shop,
      definition: "店铺变量定义",
      example: "店铺变量示例",
      scopeDetail: '["shop1"]',
    },
  ];

  const sharedVariables: SharedVariable[] = [
    {
      sharedVariableId: "shared-1",
      promptVersionId: "version-1",
      name: "共享变量1",
      promptName: "sharedVar1",
      level: Level.Product,
      definition: "共享变量定义",
      content: "共享变量内容",
      status: 1,
    },
  ];

  const universalSharedVariables: UniversalSharedVariable[] = [
    {
      id: "universal-1",
      name: "通用共享变量1",
      promptName: "universalVar1",
      level: Level.Product,
      definition: "通用共享变量定义",
      content: "通用共享变量内容",
      promptVersionId: "version-2",
    },
  ];

  describe("checkVariableNameDuplication", () => {
    it("应该检测到系统变量中的变量名重复", () => {
      const result = checkVariableNameDuplication(
        "系统变量1",
        "newPromptName",
        systemVariables,
        shopVariables,
        sharedVariables,
      );

      expect(result.isNameDuplicate).toBe(true);
      expect(result.duplicateNameSource).toBe("系统变量");
      expect(result.isPromptNameDuplicate).toBe(false);
    });

    it("应该检测到系统变量中的提示词变量名重复", () => {
      const result = checkVariableNameDuplication(
        "新变量名",
        "sysVar1",
        systemVariables,
        shopVariables,
        sharedVariables,
      );

      expect(result.isNameDuplicate).toBe(false);
      expect(result.isPromptNameDuplicate).toBe(true);
      expect(result.duplicatePromptNameSource).toBe("系统变量");
    });

    it("应该检测到店铺变量中的重复", () => {
      const result = checkVariableNameDuplication(
        "店铺变量1",
        "shopVar1",
        systemVariables,
        shopVariables,
        sharedVariables,
      );

      expect(result.isNameDuplicate).toBe(true);
      expect(result.duplicateNameSource).toBe("店铺变量");
      expect(result.isPromptNameDuplicate).toBe(true);
      expect(result.duplicatePromptNameSource).toBe("店铺变量");
    });

    it("应该检测到共享变量中的重复", () => {
      const result = checkVariableNameDuplication(
        "共享变量1",
        "sharedVar1",
        systemVariables,
        shopVariables,
        sharedVariables,
      );

      expect(result.isNameDuplicate).toBe(true);
      expect(result.duplicateNameSource).toBe("共享变量");
      expect(result.isPromptNameDuplicate).toBe(true);
      expect(result.duplicatePromptNameSource).toBe("共享变量");
    });

    it("应该检测到通用共享变量中的重复", () => {
      const result = checkVariableNameDuplication(
        "通用共享变量1",
        "universalVar1",
        systemVariables,
        shopVariables,
        universalSharedVariables,
      );

      expect(result.isNameDuplicate).toBe(true);
      expect(result.duplicateNameSource).toBe("共享变量");
      expect(result.isPromptNameDuplicate).toBe(true);
      expect(result.duplicatePromptNameSource).toBe("共享变量");
    });

    it("应该在没有重复时返回false", () => {
      const result = checkVariableNameDuplication(
        "新变量名",
        "newPromptName",
        systemVariables,
        shopVariables,
        sharedVariables,
      );

      expect(result.isNameDuplicate).toBe(false);
      expect(result.isPromptNameDuplicate).toBe(false);
      expect(result.duplicateNameSource).toBeUndefined();
      expect(result.duplicatePromptNameSource).toBeUndefined();
    });

    it("应该排除指定的变量ID", () => {
      const result = checkVariableNameDuplication(
        "系统变量1",
        "sysVar1",
        systemVariables,
        shopVariables,
        sharedVariables,
        "sys-1",
      );

      expect(result.isNameDuplicate).toBe(false);
      expect(result.isPromptNameDuplicate).toBe(false);
    });

    it("应该正确处理共享变量的ID排除", () => {
      const result = checkVariableNameDuplication(
        "共享变量1",
        "sharedVar1",
        systemVariables,
        shopVariables,
        sharedVariables,
        "shared-1",
      );

      expect(result.isNameDuplicate).toBe(false);
      expect(result.isPromptNameDuplicate).toBe(false);
    });

    it("应该正确处理通用共享变量的ID排除", () => {
      const result = checkVariableNameDuplication(
        "通用共享变量1",
        "universalVar1",
        systemVariables,
        shopVariables,
        universalSharedVariables,
        "universal-1",
      );

      expect(result.isNameDuplicate).toBe(false);
      expect(result.isPromptNameDuplicate).toBe(false);
    });

    it("应该处理空数组", () => {
      const result = checkVariableNameDuplication(
        "任意变量名",
        "anyPromptName",
        [],
        [],
        [],
      );

      expect(result.isNameDuplicate).toBe(false);
      expect(result.isPromptNameDuplicate).toBe(false);
    });

    it("应该处理混合类型的共享变量数组", () => {
      const mixedSharedVariables = [
        ...sharedVariables,
        ...universalSharedVariables,
      ];

      const result1 = checkVariableNameDuplication(
        "共享变量1",
        "newPromptName",
        systemVariables,
        shopVariables,
        mixedSharedVariables,
      );

      expect(result1.isNameDuplicate).toBe(true);
      expect(result1.duplicateNameSource).toBe("共享变量");

      const result2 = checkVariableNameDuplication(
        "新变量名",
        "universalVar1",
        systemVariables,
        shopVariables,
        mixedSharedVariables,
      );

      expect(result2.isPromptNameDuplicate).toBe(true);
      expect(result2.duplicatePromptNameSource).toBe("共享变量");
    });

    it("应该支持列表编辑场景的重复检查", () => {
      // 模拟在列表中编辑变量名称的场景
      const result = checkVariableNameDuplication(
        "系统变量2", // 尝试修改为另一个系统变量的名称
        "sysVar1", // 保持原有的提示词变量名
        systemVariables,
        shopVariables,
        sharedVariables,
        "sys-1", // 排除当前编辑的变量
      );

      expect(result.isNameDuplicate).toBe(true);
      expect(result.duplicateNameSource).toBe("系统变量");
      expect(result.isPromptNameDuplicate).toBe(false); // 提示词变量名没有重复
    });
  });

  describe("generateDuplicateVariableNameMessage", () => {
    it("应该生成正确的错误消息", () => {
      const message1 = generateDuplicateVariableNameMessage(
        "变量名称",
        "系统变量",
      );
      expect(message1).toBe("变量名称与系统变量重复");

      const message2 = generateDuplicateVariableNameMessage(
        "提示词变量名称",
        "店铺变量",
      );
      expect(message2).toBe("提示词变量名称与店铺变量重复");

      const message3 = generateDuplicateVariableNameMessage(
        "变量名称",
        "共享变量",
      );
      expect(message3).toBe("变量名称与共享变量重复");
    });
  });
});
