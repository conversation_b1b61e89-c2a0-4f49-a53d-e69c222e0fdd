import { Variable } from "@/types/api/variable";
import { SharedVariable } from "@/types/api/shared-variable";
import { UniversalSharedVariable } from "@/features/prompt/management/types";

/**
 * 变量名重复检查结果
 */
export interface VariableNameCheckResult {
  /** 变量名称是否重复 */
  isNameDuplicate: boolean;
  /** 提示词变量名是否重复 */
  isPromptNameDuplicate: boolean;
  /** 重复的变量名称来源 */
  duplicateNameSource?: string;
  /** 重复的提示词变量名来源 */
  duplicatePromptNameSource?: string;
}

/**
 * 检查变量名是否重复
 *
 * @param name 变量名称
 * @param promptName 提示词变量名称
 * @param systemVariables 系统变量列表
 * @param shopVariables 店铺变量列表
 * @param sharedVariables 共享变量列表（包括草稿和服务端数据）
 * @param excludeId 排除的变量ID（编辑时使用）
 * @returns 检查结果
 */
export function checkVariableNameDuplication(
  name: string,
  promptName: string,
  systemVariables: Variable[] = [],
  shopVariables: Variable[] = [],
  sharedVariables: (SharedVariable | UniversalSharedVariable)[] = [],
  excludeId?: string,
): VariableNameCheckResult {
  const result: VariableNameCheckResult = {
    isNameDuplicate: false,
    isPromptNameDuplicate: false,
  };

  // 检查系统变量
  for (const variable of systemVariables) {
    if (excludeId && variable.variableId === excludeId) {
      continue;
    }

    if (variable.name === name) {
      result.isNameDuplicate = true;
      result.duplicateNameSource = "系统变量";
    }

    if (variable.promptName === promptName) {
      result.isPromptNameDuplicate = true;
      result.duplicatePromptNameSource = "系统变量";
    }
  }

  // 检查店铺变量
  for (const variable of shopVariables) {
    if (excludeId && variable.variableId === excludeId) {
      continue;
    }

    if (variable.name === name) {
      result.isNameDuplicate = true;
      result.duplicateNameSource = "店铺变量";
    }

    if (variable.promptName === promptName) {
      result.isPromptNameDuplicate = true;
      result.duplicatePromptNameSource = "店铺变量";
    }
  }

  // 检查共享变量
  for (const variable of sharedVariables) {
    // 对于共享变量，使用不同的ID字段
    const variableId =
      "sharedVariableId" in variable
        ? variable.sharedVariableId.toString()
        : variable.id;

    if (excludeId && variableId === excludeId) continue;

    if (variable.name === name) {
      result.isNameDuplicate = true;
      result.duplicateNameSource = "共享变量";
    }

    if (variable.promptName === promptName) {
      result.isPromptNameDuplicate = true;
      result.duplicatePromptNameSource = "共享变量";
    }
  }

  return result;
}

/**
 * 生成变量名重复的错误消息
 *
 * @param fieldName 字段名称（"变量名称" 或 "提示词变量名称"）
 * @param source 重复来源
 * @returns 错误消息
 */
export function generateDuplicateVariableNameMessage(
  fieldName: string,
  source: string,
): string {
  return `${fieldName}与${source}重复`;
}
