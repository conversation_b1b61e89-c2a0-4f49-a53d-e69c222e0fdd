import { PromptVersionDiffDialog } from "@/components/common/prompt-version-diff-viewer";
import { PromptVersion } from "@/types/api/prompt-version";
import { UseQueryResult } from "@tanstack/react-query";
import { NodeTypes } from "@xyflow/react";
import "@xyflow/react/dist/style.css";
import { ComponentProps, useCallback, useState } from "react";
import { GenericDependencyGraph, GenericVersionNode } from "../components";
import { usePromptVersionDisplayConfig } from "./prompt-version-display-config";

// PromptVersion 的字段映射配置
const PROMPT_VERSION_FIELD_MAPPING = {
  versionId: "promptVersionId",
  baseVersionId: "basePromptVersionId",
  versionName: "versionName",
  version: "version",
};

// 创建节点组件
const PromptVersionNodeComponent = (props: ComponentProps<any>) => {
  // 获取显示配置
  const promptVersionDisplayConfig = usePromptVersionDisplayConfig();

  return (
    <GenericVersionNode {...props} displayConfig={promptVersionDisplayConfig} />
  );
};

const NODE_TYPES: NodeTypes = {
  promptVersionNode: PromptVersionNodeComponent,
};

export const PromptVersionDependencyGraph = ({
  versionId,
  versionsQuery,
}: {
  versionId: string | undefined;
  versionsQuery: UseQueryResult<PromptVersion[]>;
}) => {
  // 版本对比弹窗状态
  const [diffDialogOpen, setDiffDialogOpen] = useState(false);
  const [selectedVersion, setSelectedVersion] = useState<PromptVersion | null>(
    null,
  );

  // 处理节点点击事件
  const handleNodeClick = useCallback((version: PromptVersion) => {
    setSelectedVersion(version);
    setDiffDialogOpen(true);
  }, []);

  if (!versionId) {
    return null;
  }

  return (
    <>
      <GenericDependencyGraph
        versionId={versionId}
        versionsQuery={versionsQuery}
        fieldMapping={PROMPT_VERSION_FIELD_MAPPING}
        nodeTypeName="promptVersionNode"
        nodeTypes={NODE_TYPES}
        onNodeClick={handleNodeClick}
      />

      {/* 版本对比弹窗 */}
      <PromptVersionDiffDialog
        open={diffDialogOpen}
        onOpenChange={setDiffDialogOpen}
        title="版本对比"
        currentVersionId={selectedVersion?.promptVersionId}
        baseVersionId={selectedVersion?.basePromptVersionId}
      />
    </>
  );
};
