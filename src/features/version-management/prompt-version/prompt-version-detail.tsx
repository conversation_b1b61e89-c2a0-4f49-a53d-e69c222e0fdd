import { PromptVersionPublishButton } from "@/components/common/prompt-version-publish-button";
import { getPromptVersionName } from "@/components/common/version-select/prompt-version/helper";
import { PromptVersionStatusView } from "@/components/common/version-select/prompt-version/prompt-version-status-view";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { VersionDataStatistics } from "@/features/prompt/management/components/sync/version-data-statistics";
import { VersionChangeContent } from "@/features/prompt/management/utils/version-change-tracker";
import { PromptVersionService } from "@/services/prompt-version-service";
import { PromptVersion, PromptVersionStatus } from "@/types/api/prompt-version";
import {
  useMutation,
  UseQueryResult,
  useSuspenseQuery,
} from "@tanstack/react-query";
import { useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import {
  DependencyGraphRoot,
  EmptyVersionDetail,
  TaskTableView,
  VersionDetailView,
} from "../components";
import { PromptVersionDependencyGraph } from "./prompt-version-dependency-graph";
import { getTaskTableColumns } from "./task-table-columns";
import { usePermission } from "@/hooks/use-permission";
import { PERMISSION_CODES } from "@/constants/permission";

export const PromptVersionDetail = ({
  versionId,
  versionsQuery,
  setVersionId,
}: {
  versionId: string | undefined;
  versionsQuery: UseQueryResult<PromptVersion[]>;
  setVersionId: (versionId: string | undefined) => void;
}) => {
  const { t } = useTranslation();
  const { hasPermission } = usePermission();
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const columns = useMemo(() => getTaskTableColumns(t), [t]);

  // 解析版本变更内容
  const parseVersionChangeContent = (
    versionChangeContent?: string,
  ): VersionChangeContent | null => {
    if (!versionChangeContent) {
      return null;
    }

    try {
      return JSON.parse(versionChangeContent) as VersionChangeContent;
    } catch (error) {
      console.error("解析版本变更内容失败:", error);
      return null;
    }
  };
  const { data: versionData, refetch } = useSuspenseQuery({
    queryKey: ["getPromptVersionDetail", versionId],
    queryFn: () => {
      if (!versionId) {
        return null;
      }

      return PromptVersionService.getPromptVersionDetail({
        promptVersionId: versionId,
      });
    },
  });
  const { mutateAsync: deletePromptVersion } = useMutation({
    mutationFn: PromptVersionService.deletePromptVersion,
    onSuccess: () => {
      setVersionId(undefined);
      versionsQuery.refetch();
    },
  });

  const confirmDelete = () => {
    if (!versionId) {
      return;
    }

    deletePromptVersion({
      promptVersionId: versionId,
    });
  };

  // 显示空状态
  if (!versionData || !versionId) {
    return <EmptyVersionDetail />;
  }

  const basicInfoFields = [
    {
      label: t("version.name"),
      value: getPromptVersionName(versionData),
    },
    {
      label: t("version.num"),
      value: versionData.version,
    },
    {
      label: t("submit.time"),
      value: versionData.submitDate,
    },
    {
      label: t("version.status"),
      value: <PromptVersionStatusView version={versionData} />,
    },
    {
      label: t("version.base"),
      value: versionData.baseVersion,
    },
  ];

  // 解析版本变更内容
  const changeContent = parseVersionChangeContent(
    versionData.versionChange?.content,
  );

  const sections = [
    {
      title: t("relation.task"),
      content: (
        <TaskTableView
          data={versionData.evaluationTasks ?? []}
          columns={columns}
        />
      ),
    },
    {
      content: (
        <DependencyGraphRoot>
          <PromptVersionDependencyGraph
            versionId={versionId}
            versionsQuery={versionsQuery}
          />
        </DependencyGraphRoot>
      ),
    },
    // 版本变更历史 section
    ...(changeContent
      ? [
          {
            content: (
              <VersionDataStatistics
                changeContent={changeContent}
                mode="history"
                className="h-full"
              />
            ),
          },
        ]
      : []),
  ];

  const headerActions = (
    <div className="flex items-center gap-3">
      {versionData.status !== PromptVersionStatus.ONLINE && (
        <Button
          variant="destructive"
          onClick={() => {
            setIsDeleteDialogOpen(true);
          }}
        >
          {t("prompt.management.version.delete")}
        </Button>
      )}

      {versionData.status !== PromptVersionStatus.ONLINE &&
        hasPermission(PERMISSION_CODES.PROMPT_VERSION_PUBLISH) && (
          <PromptVersionPublishButton
            promptVersionId={versionData.promptVersionId}
            onFinish={() => {
              versionsQuery.refetch();
              refetch();
            }}
          />
        )}
    </div>
  );

  return (
    <>
      <VersionDetailView
        title={t("version.detail")}
        headerActions={headerActions}
        basicInfoFields={basicInfoFields}
        remarkLabel={t("version.remark")}
        remarkContent={versionData.remark}
        sections={sections}
        className="border-none shadow-none h-full flex flex-col"
      />

      <AlertDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {t("prompt.management.version.delete")}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {t("prompt.management.version.delete.description", {
                version: versionData.version,
              })}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t("common.cancel")}</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {t("common.delete.confirm.btn")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};
