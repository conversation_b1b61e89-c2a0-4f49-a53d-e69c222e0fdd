import { PromptVersionEvaluationTask } from "@/types/api/prompt-version";
import { ColumnDef } from "@tanstack/react-table";
import { TFunction } from "i18next";

export const getTaskTableColumns = (
  t: TFunction,
): ColumnDef<PromptVersionEvaluationTask>[] => [
  {
    accessorKey: "taskId",
    header: t("review.task.id"),
    cell: ({ row }) => row.original.taskId,
  },
  {
    accessorKey: "taskName",
    header: t("review.task.id"),
    cell: ({ row }) => row.original.taskName,
  },
  {
    accessorKey: "testSetName",
    header: t("test.set"),
    cell: ({ row }) => row.original.testSetName,
  },
  {
    accessorKey: "evaluationAt",
    header: t("rretewe"),
    cell: ({ row }) => row.original.evaluationAt,
  },
];
