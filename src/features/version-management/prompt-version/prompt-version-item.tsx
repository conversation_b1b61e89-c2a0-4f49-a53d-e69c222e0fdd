import { PromptVersionStatusView } from "@/components/common/version-select/prompt-version/prompt-version-status-view";
import {
  getPromptVersionDetails,
  getPromptVersionName,
} from "@/components/common/version-select/prompt-version/helper";
import { PromptVersion } from "@/types/api/prompt-version";
import { useTranslation } from "react-i18next";
import { VersionListItemView } from "../components";

export const PromptVersionItem = ({
  version,
  versionId,
  onSelect,
}: {
  version: PromptVersion;
  versionId: string | undefined;
  onSelect: (versionId: string) => void;
}) => {
  const { t } = useTranslation();
  const details = getPromptVersionDetails(version, t);
  const isSelected = versionId === version.promptVersionId.toString();

  return (
    <VersionListItemView
      title={getPromptVersionName(version)}
      isSelected={isSelected}
      details={details}
      statusBadge={<PromptVersionStatusView version={version} />}
      onClick={() => onSelect(version.promptVersionId.toString())}
    />
  );
};
