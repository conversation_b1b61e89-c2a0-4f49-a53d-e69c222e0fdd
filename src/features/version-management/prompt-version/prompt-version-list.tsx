import { PromptVersion } from "@/types/api/prompt-version";
import { UseQueryResult } from "@tanstack/react-query";
import { use, useEffect, useState } from "react";
import { EmptyVersion, VersionListView } from "../components";
import { PromptVersionItem } from "./prompt-version-item";

export const PromptVersionList = ({
  searchValue,
  versionsQuery,
  versionId,
  onSelect,
  onSearchChange,
}: {
  searchValue: string;
  versionsQuery: UseQueryResult<PromptVersion[]>;
  versionId: string | undefined;
  onSelect: (versionId?: string) => void;
  onSearchChange: (search: string) => void;
}) => {
  const versions = use(versionsQuery.promise);
  const [showOnlineOnly, setOnlineStatus] = useState<boolean>(false);
  const filteredVersions = (versions || []).filter((version) => {
    // 先按版本号或版本名称筛选
    const matchesSearch =
      !searchValue ||
      version.version.toLowerCase().includes(searchValue.toLowerCase()) ||
      (version.versionName &&
        version.versionName.toLowerCase().includes(searchValue.toLowerCase()));

    // 再按状态筛选
    const matchesStatus = !showOnlineOnly ? version.status !== "OFFLINE" : true;

    return matchesSearch && matchesStatus;
  });

  // 默认选择第一个版本
  useEffect(() => {
    if (filteredVersions.length > 0 && !versionId) {
      onSelect(filteredVersions[0].promptVersionId.toString());
    }
  }, [filteredVersions, onSelect, versionId]);

  return (
    <VersionListView
      searchValue={searchValue}
      showOnlineOnly={showOnlineOnly}
      onSearchChange={onSearchChange}
      onShowOnlineOnlyChange={setOnlineStatus}
    >
      <div className="space-y-3">
        {filteredVersions.length > 0 ? (
          filteredVersions.map((item, index: number) => (
            <PromptVersionItem
              key={index}
              version={item}
              onSelect={onSelect}
              versionId={versionId}
            />
          ))
        ) : (
          <EmptyVersion />
        )}
      </div>
    </VersionListView>
  );
};
