import { promptVersionQueryKeys } from "@/constants/query-keys";
import { usePanelSize } from "@/hooks/use-panel-size";
import { PromptVersionService } from "@/services/prompt-version-service";
import { useQuery } from "@tanstack/react-query";
import { Suspense, useDeferredValue, useState } from "react";
import {
  VERSION_LAYOUT_PANEL_ID,
  VersionDetailSkeleton,
  VersionLayout,
  VersionListSkeleton,
  VersionSkeleton,
} from "../components";
import { PromptVersionDetail } from "./prompt-version-detail";
import { PromptVersionList } from "./prompt-version-list";

export const PromptVersionManagement = () => {
  const leftPanelProps = usePanelSize({
    panelId: VERSION_LAYOUT_PANEL_ID,
    defaultSize: 20,
    minSize: 15,
  });
  const [versionId, setVersionId] = useState<string | undefined>();
  const [search, setSearch] = useState<string>("");
  const deferredSearch = useDeferredValue(search);
  const versionsQuery = useQuery({
    queryKey: promptVersionQueryKeys.list({ version: deferredSearch }),
    queryFn: () =>
      PromptVersionService.selectPromptVersionList({
        version: deferredSearch,
      }),
  });

  return (
    <Suspense fallback={<VersionSkeleton leftPanelProps={leftPanelProps} />}>
      <VersionLayout
        leftPanelProps={leftPanelProps}
        leftPanel={
          <Suspense fallback={<VersionListSkeleton />}>
            <PromptVersionList
              versionsQuery={versionsQuery}
              searchValue={search}
              versionId={versionId}
              onSelect={setVersionId}
              onSearchChange={setSearch}
            />
          </Suspense>
        }
        rightPanel={
          <Suspense fallback={<VersionDetailSkeleton />}>
            <PromptVersionDetail
              versionId={versionId}
              versionsQuery={versionsQuery}
              setVersionId={setVersionId}
            />
          </Suspense>
        }
      />
    </Suspense>
  );
};
