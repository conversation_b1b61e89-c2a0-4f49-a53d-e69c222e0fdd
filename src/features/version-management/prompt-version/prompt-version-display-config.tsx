import {
  PromptVersionStatusView,
  getPromptVersionDetails,
} from "@/components/common/version-select";
import { PromptVersion } from "@/types/api/prompt-version";
import { useTranslation } from "react-i18next";
import { VersionDisplayConfig } from "../components";

export const usePromptVersionDisplayConfig =
  (): VersionDisplayConfig<PromptVersion> => {
    const { t } = useTranslation();

    return {
      getDisplayName: (version) => version.versionName || version.version,
      getVersionId: (version) => version.promptVersionId,
      getHeader: (version) => <PromptVersionStatusView version={version} />,
      renderDetails: (version) => {
        const versionDetails = getPromptVersionDetails(version, t, false);

        return (
          <div className="space-y-1">
            <table className="w-full text-xs border-spacing-y-1">
              <tbody>
                <tr>
                  <td className="text-gray-600 pr-3 align-top pb-1.5">
                    {t("version.num")}
                  </td>
                  <td className="pb-1.5">{version.version}</td>
                </tr>
                {versionDetails.map((detail, index) => (
                  <tr key={index}>
                    <td className="text-gray-600 pr-3 align-top whitespace-nowrap pb-1.5">
                      {detail.label}
                    </td>
                    <td className="pb-1.5">{detail.value}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        );
      },
    };
  };
