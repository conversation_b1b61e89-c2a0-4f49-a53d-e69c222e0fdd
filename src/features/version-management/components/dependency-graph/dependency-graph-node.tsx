import { cn } from "@/lib/utils";
import { <PERSON><PERSON>, Position } from "@xyflow/react";
import { ReactNode } from "react";

export const DependencyGraphNode = ({
  displayName,
  header,
  isCurrentVersion,
  targetPosition = Position.Top,
  sourcePosition = Position.Bottom,
  className,
  children,
  onClick,
}: {
  displayName?: string;
  header?: ReactNode;
  isCurrentVersion?: boolean;
  targetPosition?: Position;
  sourcePosition?: Position;
  className?: string;
  children?: ReactNode;
  onClick: () => void;
}) => {
  return (
    <div
      className={cn(
        "px-4 py-3 shadow rounded-md bg-background border cursor-pointer transition-all duration-200 w-full",
        isCurrentVersion
          ? "border-2 border-primary shadow-lg ring-2 ring-primary/20 hover:shadow-xl"
          : "border-gray-200 hover:border-gray-300 hover:shadow-md",
        className,
      )}
      onClick={onClick}
      role="button"
    >
      <Handle type="target" position={targetPosition} />

      <div className="flex items-center justify-between gap-2">
        <span
          className={cn(
            "font-semibold text-sm mb-2",
            isCurrentVersion && "text-primary",
          )}
        >
          {displayName}
        </span>

        {header}
      </div>

      {children}

      <Handle type="source" position={sourcePosition} />
    </div>
  );
};
