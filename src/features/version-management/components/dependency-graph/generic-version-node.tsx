import { Position } from "@xyflow/react";
import { ReactNode, useCallback } from "react";
import { DependencyGraphNode } from "./dependency-graph-node";
import { GenericVersionNodeData } from "./types";

export interface VersionDisplayConfig<T = any> {
  /**
   * 获取显示名称的函数
   **/
  getDisplayName: (version: T) => string;
  /**
   * 获取版本ID的函数
   **/
  getVersionId: (version: T) => string;
  /**
   * 渲染详细内容的函数
   **/
  renderDetails: (version: T) => ReactNode;
  /**
   * 渲染头部内容的函数
   **/
  getHeader?: (version: T) => ReactNode;
}

export interface GenericVersionNodeProps<T = any> {
  data: GenericVersionNodeData<T>;
  sourcePosition?: Position;
  targetPosition?: Position;
  displayConfig: VersionDisplayConfig<T>;
}

export const GenericVersionNode = <T,>({
  data,
  sourcePosition,
  targetPosition,
  displayConfig,
}: GenericVersionNodeProps<T>) => {
  const { version, isCurrentVersion, onNodeClick } = data;
  const { getDisplayName, renderDetails, getHeader } = displayConfig;
  const displayName = getDisplayName(version);
  const header = getHeader?.(version);

  const handleClick = useCallback(() => {
    onNodeClick?.(version);
  }, [onNodeClick, version]);

  return (
    <DependencyGraphNode
      displayName={displayName}
      isCurrentVersion={isCurrentVersion}
      targetPosition={targetPosition}
      sourcePosition={sourcePosition}
      header={header}
      onClick={handleClick}
    >
      {renderDetails(version)}
    </DependencyGraphNode>
  );
};
