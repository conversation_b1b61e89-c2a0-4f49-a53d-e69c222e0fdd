import {
  Edge,
  Node,
  NodeTypes,
  useEdgesState,
  useNodesState,
  useReactFlow,
} from "@xyflow/react";
import "@xyflow/react/dist/style.css";
import { use, useCallback, useEffect } from "react";
import { DependencyGraph } from "../dependency-graph";
import { buildGenericDependencyGraph, getLayoutedElements } from "./helper";
import { GenericDependencyGraphProps, GenericVersionNodeData } from "./types";

export const GenericDependencyGraph = <T,>({
  versionId,
  versionsQuery,
  fieldMapping,
  nodeTypeName,
  nodeTypes,
  onNodeClick,
}: GenericDependencyGraphProps<T> & { nodeTypes: NodeTypes }) => {
  const { fitView, getNodes } = useReactFlow();
  const [nodes, setNodes, onNodesChange] = useNodesState<
    Node<GenericVersionNodeData<T>>
  >([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState<Edge>([]);
  const versionList = use(versionsQuery.promise);

  // 聚焦到当前节点
  const centerOnCurrentNode = useCallback(() => {
    const currentNode = getNodes().find(
      (node) => node.data.isCurrentVersion === true,
    );

    if (currentNode && currentNode.id) {
      fitView({ nodes: [currentNode], padding: 1.5 });
    } else {
      fitView({ padding: 0.1 });
    }
  }, [fitView, getNodes]);

  useEffect(() => {
    if (versionList && versionId) {
      const layoutedElements = buildGenericDependencyGraph({
        versionId,
        allVersionsList: versionList,
        fieldMapping,
        nodeTypeName,
      });

      // 为所有节点添加点击处理函数
      const nodesWithClickHandler = layoutedElements.nodes.map((node) => ({
        ...node,
        data: {
          ...node.data,
          onNodeClick,
        },
      }));

      setNodes(nodesWithClickHandler);
      setEdges(layoutedElements.edges);
      setTimeout(() => {
        centerOnCurrentNode();
      }, 100);
    }
  }, [
    versionList,
    versionId,
    fieldMapping,
    nodeTypeName,
    setNodes,
    setEdges,
    centerOnCurrentNode,
    onNodeClick,
  ]);

  const onLayout = useCallback(
    (direction: "TB" | "LR") => {
      const layoutedElements = getLayoutedElements(nodes, edges, direction);

      // 保持点击处理函数
      const nodesWithClickHandler = layoutedElements.nodes.map((node) => ({
        ...node,
        data: {
          ...node.data,
          onNodeClick,
        },
      }));

      setNodes([...nodesWithClickHandler]);
      setEdges([...layoutedElements.edges]);

      setTimeout(() => {
        centerOnCurrentNode();
      }, 100);
    },
    [nodes, edges, setNodes, setEdges, onNodeClick, centerOnCurrentNode],
  );

  return (
    <DependencyGraph
      nodes={nodes}
      edges={edges}
      nodeTypes={nodeTypes}
      onNodesChange={onNodesChange}
      onEdgesChange={onEdgesChange}
      onLayoutChange={onLayout}
    />
  );
};
