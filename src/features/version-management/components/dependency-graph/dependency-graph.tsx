import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  Background,
  Controls,
  Edge,
  Node,
  NodeTypes,
  ReactFlow,
} from "@xyflow/react";
import "@xyflow/react/dist/style.css";
import { useLocalStorageState } from "ahooks";
import { ArrowDownWideNarrow, ArrowRightFromLine } from "lucide-react";
import { Resizable } from "re-resizable";
import { ReactNode } from "react";

export type LayoutDirection = "TB" | "LR";

export interface DependencyGraphProps {
  nodes: Node[];
  edges: Edge[];
  nodeTypes: NodeTypes;
  fitView?: boolean;
  fitViewOptions?: {
    padding?: number;
  };
  nodesDraggable?: boolean;
  className?: string;
  storageKey?: string;
  minHeight?: number;
  defaultHeight?: number;
  additionalContent?: ReactNode;
  onNodesChange: (changes: any[]) => void;
  onEdgesChange: (changes: any[]) => void;
  onLayoutChange: (direction: LayoutDirection) => void;
}

export const DependencyGraph = ({
  nodes,
  edges,
  nodeTypes,
  fitView = true,
  fitViewOptions = { padding: 0.1 },
  nodesDraggable = false,
  className,
  storageKey = "dependency-graph-size",
  minHeight = 200,
  defaultHeight = 320,
  additionalContent,
  onNodesChange,
  onEdgesChange,
  onLayoutChange,
}: DependencyGraphProps) => {
  const [size, setSize] = useLocalStorageState<{
    height: number;
  }>(storageKey, {
    defaultValue: {
      height: defaultHeight,
    },
  });

  return (
    <Resizable
      defaultSize={size}
      size={size}
      minHeight={minHeight}
      enable={{
        bottom: true,
        top: false,
        left: false,
        right: false,
        topLeft: false,
        bottomLeft: false,
        topRight: false,
        bottomRight: false,
      }}
      onResize={(_e, _direction, ref) => {
        setSize({
          height: ref.clientHeight,
        });
      }}
    >
      <div className={`flex flex-col h-full ${className || ""}`}>
        <h3 className="text-sm font-medium mb-3 flex items-center justify-between">
          <span>版本依赖图</span>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              icon={<ArrowDownWideNarrow />}
              onClick={() => onLayoutChange("TB")}
            >
              垂直布局
            </Button>
            <Button
              variant="outline"
              size="sm"
              icon={<ArrowRightFromLine />}
              onClick={() => onLayoutChange("LR")}
            >
              水平布局
            </Button>
          </div>
        </h3>

        <Card className="flex-1 min-h-0">
          <CardContent className="p-0 h-full">
            <ReactFlow
              nodes={nodes}
              edges={edges}
              nodeTypes={nodeTypes}
              fitView={fitView}
              fitViewOptions={fitViewOptions}
              nodesDraggable={nodesDraggable}
              className="bg-gray-50"
              onNodesChange={onNodesChange}
              onEdgesChange={onEdgesChange}
            >
              <Controls />
              <Background />
            </ReactFlow>
          </CardContent>
        </Card>

        {additionalContent}
      </div>
    </Resizable>
  );
};
