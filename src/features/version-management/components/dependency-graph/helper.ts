import dagre from "@dagrejs/dagre";
import { Edge, MarkerType, Node, Position } from "@xyflow/react";
import {
  DependencyGraphConfig,
  DependencyGraphElements,
  GenericVersionNodeData,
  VersionFieldMapping,
} from "./types";

// 布局相关常量
const NODE_WIDTH = 280;
const NODE_HEIGHT = 150;

const LAYOUT_CONFIG = {
  nodesep: 50,
  ranksep: 100,
  marginx: 20,
  marginy: 20,
};

const EDGE_STYLE = {
  stroke: "var(--grayed)",
  strokeWidth: 2,
};

const DAGRE_GRAPH = new dagre.graphlib.Graph();

DAGRE_GRAPH.setDefaultEdgeLabel(() => ({}));

/**
 * 获取版本字段值的辅助函数
 */
const getVersionField = <T>(
  version: T,
  fieldName: string,
): string | undefined => {
  return (version as any)[fieldName]?.toString();
};

/**
 * 创建版本节点
 */
const createVersionNode = <T>(
  version: T,
  fieldMapping: VersionFieldMapping,
  nodeTypeName: string,
  isCurrentVersion = false,
): Node<GenericVersionNodeData<T>> => {
  const versionId = getVersionField(version, fieldMapping.versionId);

  return {
    id: `version-${versionId}`,
    type: nodeTypeName,
    position: { x: 0, y: 0 },
    data: {
      version,
      isCurrentVersion,
      onNodeClick: undefined, // 将在组件中设置
    },
    width: NODE_WIDTH,
  };
};

/**
 * 创建依赖边
 */
const createDependencyEdge = (sourceId: string, targetId: string): Edge => ({
  id: `edge-${sourceId}-${targetId}`,
  source: `version-${sourceId}`,
  target: `version-${targetId}`,
  type: "smoothstep",
  animated: true,
  style: EDGE_STYLE,
  markerEnd: {
    type: MarkerType.ArrowClosed,
    color: EDGE_STYLE.stroke,
  },
});

/**
 * 使用 dagre 进行布局
 */
export const getLayoutedElements = <T>(
  nodes: Node<GenericVersionNodeData<T>>[],
  edges: Edge[],
  direction: "TB" | "LR" = "LR",
): DependencyGraphElements<T> => {
  const isHorizontal = direction === "LR";

  DAGRE_GRAPH.setGraph({
    rankdir: direction,
    ...LAYOUT_CONFIG,
  });

  nodes.forEach((node) => {
    DAGRE_GRAPH.setNode(node.id, { width: NODE_WIDTH, height: NODE_HEIGHT });
  });

  edges.forEach((edge) => {
    DAGRE_GRAPH.setEdge(edge.source, edge.target);
  });

  dagre.layout(DAGRE_GRAPH);

  const layoutedNodes = nodes.map((node) => {
    const nodeWithPosition = DAGRE_GRAPH.node(node.id);

    return {
      ...node,
      targetPosition: isHorizontal ? Position.Left : Position.Top,
      sourcePosition: isHorizontal ? Position.Right : Position.Bottom,
      position: {
        x: nodeWithPosition.x - NODE_WIDTH / 2,
        y: nodeWithPosition.y - NODE_HEIGHT / 2,
      },
      width: NODE_WIDTH,
    };
  });

  return { nodes: layoutedNodes, edges };
};

/**
 * 构建通用依赖图
 */
export const buildGenericDependencyGraph = <T>({
  versionId,
  allVersionsList,
  fieldMapping,
  nodeTypeName,
}: DependencyGraphConfig<T>): DependencyGraphElements<T> => {
  const versionMap = new Map<string, T>();

  // 创建版本映射
  allVersionsList.forEach((version) => {
    const id = getVersionField(version, fieldMapping.versionId);
    if (id) {
      versionMap.set(id, version);
    }
  });

  const processedVersions = new Set<string>();
  const newNodes: Node<GenericVersionNodeData<T>>[] = [];
  const newEdges: Edge[] = [];

  const processVersion = (currentVersionId: string) => {
    if (
      processedVersions.has(currentVersionId) ||
      !versionMap.has(currentVersionId)
    ) {
      return;
    }

    processedVersions.add(currentVersionId);

    const version = versionMap.get(currentVersionId)!;
    const isCurrentVersion = currentVersionId === versionId;

    newNodes.push(
      createVersionNode(
        version,
        fieldMapping,
        nodeTypeName,
        isCurrentVersion,
      ) as Node<GenericVersionNodeData<T>>,
    );

    // 递归处理基础版本并创建边
    const baseVersionId = getVersionField(version, fieldMapping.baseVersionId);
    if (baseVersionId && baseVersionId !== currentVersionId) {
      processVersion(baseVersionId);
      newEdges.push(createDependencyEdge(baseVersionId, currentVersionId));
    }
  };

  // 从当前版本开始构建图
  if (versionId) {
    processVersion(versionId);
  }

  // 查找所有依赖于已处理版本的其他版本
  let foundNewVersions = true;

  while (foundNewVersions) {
    foundNewVersions = false;

    for (const version of allVersionsList) {
      const currentId = getVersionField(version, fieldMapping.versionId);
      const baseId = getVersionField(version, fieldMapping.baseVersionId);

      if (
        currentId &&
        !processedVersions.has(currentId) &&
        baseId &&
        processedVersions.has(baseId)
      ) {
        processVersion(currentId);
        foundNewVersions = true;
      }
    }
  }

  return getLayoutedElements(newNodes, newEdges, "LR");
};
