import { UseQueryResult } from "@tanstack/react-query";
import { Edge, Node } from "@xyflow/react";

/**
 * 版本字段映射配置
 * 用于定义不同版本类型的字段名称映射
 */
export interface VersionFieldMapping {
  /** 版本ID字段名 */
  versionId: string;
  /** 基础版本ID字段名 */
  baseVersionId: string;
  /** 版本名称字段名 */
  versionName?: string;
  /** 版本号字段名 */
  version?: string;
}

/**
 * 通用版本节点数据接口
 */
export interface GenericVersionNodeData<T = any>
  extends Record<string, unknown> {
  version: T;
  isCurrentVersion?: boolean;
  onNodeClick?: (version: T) => void;
}

/**
 * 依赖图构建结果
 */
export interface DependencyGraphElements<T = any> {
  nodes: Node<GenericVersionNodeData<T>>[];
  edges: Edge[];
}

/**
 * 通用依赖图组件属性
 */
export interface GenericDependencyGraphProps<T = any> {
  /** 当前版本ID */
  versionId: string | undefined;
  /** 版本列表查询结果 */
  versionsQuery: UseQueryResult<T[]>;
  /** 版本字段映射配置 */
  fieldMapping: VersionFieldMapping;
  /** 节点类型名称 */
  nodeTypeName: string;
  /** 节点点击处理函数 */
  onNodeClick?: (version: T) => void;
}

/**
 * 依赖图构建配置
 */
export interface DependencyGraphConfig<T = any> {
  /** 当前版本ID */
  versionId: string;
  /** 所有版本列表 */
  allVersionsList: T[];
  /** 版本字段映射配置 */
  fieldMapping: VersionFieldMapping;
  /** 节点类型名称 */
  nodeTypeName: string;
}
