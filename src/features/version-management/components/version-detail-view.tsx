import { Card, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { MemoizedMarkdownBlock } from "@/components/ui/markdown-content";
import { Separator } from "@/components/ui/separator";
import { ReactNode } from "react";
import { useTranslation } from "react-i18next";

export interface VersionDetailSection {
  title?: string;
  content: ReactNode;
}

export interface BasicInfoField {
  label: string;
  value: ReactNode;
}

export interface VersionDetailViewProps {
  title: string;
  headerActions?: ReactNode;
  basicInfoFields?: BasicInfoField[];
  remarkLabel?: string;
  remarkContent?: string;
  sections: VersionDetailSection[];
  className?: string;
}

export const VersionDetailView = ({
  title,
  headerActions,
  basicInfoFields,
  remarkLabel,
  remarkContent,
  sections,
  className,
}: VersionDetailViewProps) => {
  const { t } = useTranslation();

  const renderBasicInfo = () => {
    if (!basicInfoFields || basicInfoFields.length === 0) {
      return null;
    }

    return (
      <div className="space-y-3">
        <h3 className="text-sm font-medium">{t("base.info")}</h3>
        <div className="grid grid-cols-2 gap-x-4 gap-y-2">
          {basicInfoFields.map((field, index) => (
            <div
              key={index}
              className="flex h-10 items-center justify-between border-b pb-2"
            >
              <span className="text-sm text-muted-foreground">
                {field.label}
              </span>
              <span>{field.value}</span>
            </div>
          ))}
        </div>

        {remarkLabel && (
          <div>
            <div className="text-sm text-muted-foreground mb-2">
              {remarkLabel}
            </div>
            <MemoizedMarkdownBlock
              content={remarkContent || "-"}
              className="rounded-md bg-accent/50 p-3 text-sm"
            />
          </div>
        )}
      </div>
    );
  };

  return (
    <Card className={className}>
      <CardHeader className="border-b px-4 py-3 flex-row items-center justify-between space-y-0 h-[61px]">
        <CardTitle className="text-base font-medium">{title}</CardTitle>
        {headerActions && headerActions}
      </CardHeader>

      <CardContent className="p-0 flex-1 overflow-auto">
        <div className="p-4 space-y-6">
          {basicInfoFields && basicInfoFields.length > 0 && (
            <>
              {renderBasicInfo()}
              <Separator />
            </>
          )}

          {sections.map((section, index) => (
            <div key={index}>
              {section.title && (
                <h3 className="text-sm font-medium mb-3">{section.title}</h3>
              )}
              {section.content}
              {index < sections.length - 1 && <Separator className="my-6" />}
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};
