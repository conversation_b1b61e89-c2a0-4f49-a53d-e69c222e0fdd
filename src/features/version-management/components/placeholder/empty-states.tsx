import { Card, CardContent } from "@/components/ui/card";
import { EmptyState } from "@/components/ui/empty-state";
import { cn } from "@/lib/utils";
import React from "react";
import { useTranslation } from "react-i18next";

export interface EmptyVersionProps {
  actionLabel?: string;
  className?: string;
  onAction?: () => void;
}

export const EmptyVersion = ({ actionLabel, onAction }: EmptyVersionProps) => {
  const { t } = useTranslation();

  return (
    <EmptyState
      className="min-h-[200px]"
      title={t("version.empty.title")}
      lucideIcon="square-dashed"
      action={
        onAction && actionLabel
          ? {
              label: actionLabel,
              onClick: onAction,
            }
          : undefined
      }
    />
  );
};

export interface EmptyVersionDetailProps {
  className?: string;
}

export const EmptyVersionDetail = ({ className }: EmptyVersionDetailProps) => {
  const { t } = useTranslation();

  return (
    <Card
      className={cn("border-none shadow-none h-full flex flex-col", className)}
    >
      <CardContent className="flex flex-col items-center justify-center h-full p-8">
        <EmptyVersionDetailIllustration />

        <h3 className="mt-4 text-base font-medium">
          {t("version.empty.title")}
        </h3>
      </CardContent>
    </Card>
  );
};

export const EmptyVersionDetailIllustration: React.FC = () => (
  <svg
    width="120"
    height="120"
    viewBox="0 0 120 120"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    {/* 主文档 */}
    <rect
      x="25"
      y="25"
      width="60"
      height="70"
      rx="4"
      fill="#F7F8FA"
      stroke="#E5E7EB"
      strokeWidth="2"
    />

    {/* 文档内容线 */}
    <rect x="35" y="40" width="40" height="3" rx="1.5" fill="#E5E7EB" />
    <rect x="35" y="50" width="40" height="3" rx="1.5" fill="#E5E7EB" />
    <rect x="35" y="60" width="30" height="3" rx="1.5" fill="#E5E7EB" />

    {/* 版本标识 */}
    <circle cx="75" cy="75" r="20" fill="#EEF2FF" />

    {/* "v" 符号用路径绘制 */}
    <path
      d="M70 70l5 10 5-10"
      stroke="#6366F1"
      strokeWidth="2.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
