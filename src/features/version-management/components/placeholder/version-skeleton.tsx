import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from "@/components/ui/resizable";
import { Skeleton } from "@/components/ui/skeleton";

interface VersionSkeletonProps {
  leftPanelProps: {
    defaultSize: number;
    onResize?: (newSize: number) => void;
  };
}

export const VersionSkeleton = ({ leftPanelProps }: VersionSkeletonProps) => {
  return (
    <ResizablePanelGroup
      className="h-full"
      direction="horizontal"
      id="version-list-skeleton"
    >
      <ResizablePanel collapsible={false} minSize={15} {...leftPanelProps}>
        <VersionListSkeleton />
      </ResizablePanel>

      <ResizableHandle id="version-skeleton-handle" />

      <ResizablePanel>
        <VersionDetailSkeleton />
      </ResizablePanel>
    </ResizablePanelGroup>
  );
};

export const VersionListSkeleton = () => {
  return (
    <Card className="border-none shadow-none h-full flex flex-col">
      <CardHeader className="h-[61px] space-y-0 border-b py-0 justify-center">
        {/* 搜索和过滤区域 */}
        <div className="flex gap-2 items-center">
          <Skeleton className="h-9 flex-1" />
          <Skeleton className="h-5 w-24" />
        </div>
      </CardHeader>

      <CardContent className="p-4 flex-1 min-h-0 overflow-hidden">
        <div className="space-y-3">
          {/* 版本列表项骨架 */}
          {Array(3)
            .fill(0)
            .map((_, index) => (
              <Card key={index} className="overflow-hidden">
                <CardContent className="p-4">
                  <div className="flex justify-between items-center mb-2">
                    <Skeleton className="h-5 w-24" />
                    <Skeleton className="h-5 w-16" />
                  </div>

                  <div className="space-y-2">
                    <div className="flex py-0.5">
                      <Skeleton className="h-4 w-20" />
                      <Skeleton className="h-4 w-full ml-4" />
                    </div>
                    <div className="flex py-0.5">
                      <Skeleton className="h-4 w-20" />
                      <Skeleton className="h-4 w-full ml-4" />
                    </div>
                    <div className="flex py-0.5">
                      <Skeleton className="h-4 w-20" />
                      <Skeleton className="h-4 w-32 ml-4" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
        </div>
      </CardContent>
    </Card>
  );
};

export const VersionDetailSkeleton = () => {
  return (
    <Card className="border-none shadow-none h-full flex flex-col">
      <CardHeader className="border-b h-[61px] flex-row items-center justify-between space-y-0">
        <Skeleton className="h-5 w-32" />
        <Skeleton className="h-8 w-20" />
      </CardHeader>

      <CardContent className="p-0 flex-1 overflow-auto">
        <div className="p-4 space-y-6">
          {/* 基本信息区域 */}
          <div>
            <Skeleton className="h-5 w-24 mb-3" />
            <div className="grid grid-cols-2 gap-x-4 gap-y-2">
              {Array(4)
                .fill(0)
                .map((_, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between border-b pb-2"
                  >
                    <Skeleton className="h-4 w-20" />
                    <Skeleton className="h-4 w-24" />
                  </div>
                ))}
            </div>

            <div className="mt-3 rounded-md p-3 bg-accent/30">
              <Skeleton className="h-4 w-32 mb-2" />
              <Skeleton className="h-4 w-full" />
            </div>
          </div>

          <Skeleton className="h-px w-full" />

          {/* 关联任务区域 */}
          <div>
            <Skeleton className="h-5 w-24 mb-3" />
            <Skeleton className="h-32 w-full" />
          </div>

          <Skeleton className="h-px w-full" />

          {/* 变更详情区域 */}
          <div>
            <Skeleton className="h-5 w-24 mb-3" />
            <Skeleton className="h-64 w-full" />
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
