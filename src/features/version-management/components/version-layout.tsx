import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from "@/components/ui/resizable";
import { ReactNode } from "react";

export interface VersionLayoutProps {
  leftPanel: ReactNode;
  rightPanel: ReactNode;
  leftPanelProps?: {
    defaultSize?: number;
    minSize?: number;
  };
  className?: string;
}

export const VERSION_LAYOUT_PANEL_ID = "version-layout-panel";
export const VERSION_LAYOUT_HANDLE_ID = "version-layout-handle";

export const VersionLayout = ({
  leftPanel,
  rightPanel,
  leftPanelProps,
  className,
}: VersionLayoutProps) => {
  return (
    <ResizablePanelGroup
      className={className}
      direction="horizontal"
      id={VERSION_LAYOUT_PANEL_ID}
    >
      <ResizablePanel {...leftPanelProps}>{leftPanel}</ResizablePanel>

      <ResizableHandle id={VERSION_LAYOUT_HANDLE_ID} />

      <ResizablePanel>{rightPanel}</ResizablePanel>
    </ResizablePanelGroup>
  );
};
