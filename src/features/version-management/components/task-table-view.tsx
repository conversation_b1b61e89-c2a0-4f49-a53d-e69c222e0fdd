import { StaticDataTable } from "@/components/common/data-table/static-data-table";
import { ResizablePanel, ResizablePanelGroup } from "@/components/ui/resizable";
import { ColumnDef } from "@tanstack/react-table";

export interface TaskTableViewProps<TData> {
  data: TData[];
  columns: ColumnDef<TData>[];
  tableName?: string;
  className?: string;
  panelGroupId?: string;
}

export const TaskTableView = <TData,>({
  data,
  columns,
  tableName = "version-task-table",
  className = "h-full",
  panelGroupId = "task-table-panel",
}: TaskTableViewProps<TData>) => {
  return (
    <ResizablePanelGroup
      className="h-full"
      direction="horizontal"
      id={panelGroupId}
    >
      <ResizablePanel>
        <StaticDataTable<TData>
          tableName={tableName}
          className={className}
          data={data}
          columns={columns}
        />
      </ResizablePanel>
    </ResizablePanelGroup>
  );
};
