import { Tooltip } from "@/components/common/tooltip";
import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { ReactNode } from "react";

export interface VersionListItemViewProps {
  isSelected: boolean;
  isDeleted: boolean;
  details: Array<{
    label: string;
    value: string | undefined;
  }>;
  title?: string;
  titleTooltip?: string;
  statusBadge?: ReactNode;
  className?: string;
  onClick: () => void;
}

export const VersionListItemView = ({
  title,
  titleTooltip,
  isSelected,
  isDeleted,
  statusBadge,
  details,
  className,
  onClick,
}: VersionListItemViewProps) => {
  return (
    <Card
      className={cn(
        "cursor-pointer transition-colors bg-card-background",
        isSelected
          ? "border-primary bg-primary/5 shadow-xs"
          : "border-border hover:border-primary/30 hover:bg-primary/5",
        className,
        isDeleted && "hidden",
      )}
      onClick={onClick}
    >
      <CardContent className="p-3">
        <div className="flex justify-between items-center mb-1 gap-2 break-all">
          <Tooltip content={titleTooltip || title} autoDetectOverflow>
            <span className="font-semibold text-sm line-clamp-2">{title}</span>
          </Tooltip>
          {statusBadge}
        </div>

        <div className="space-y-0.5">
          {details.map((detail, index) => (
            <div key={index} className="flex text-sm">
              <div className="w-18 text-muted-foreground">{detail.label}</div>
              <Tooltip content={detail.value} autoDetectOverflow>
                <div className="flex-1 line-clamp-1">{detail.value}</div>
              </Tooltip>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};
