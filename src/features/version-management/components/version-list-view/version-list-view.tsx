import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Switch } from "@/components/ui/switch";
import { cn } from "@/lib/utils";
import { Search } from "lucide-react";
import { ReactNode } from "react";
import { useTranslation } from "react-i18next";

export interface VersionListViewProps {
  searchValue: string;
  showOnlineOnly: boolean;
  children: ReactNode;
  className?: string;
  onSearchChange: (value: string) => void;
  onShowOnlineOnlyChange: (checked: boolean) => void;
}

export const VersionListView = ({
  searchValue,
  showOnlineOnly,
  children,
  className,
  onSearchChange,
  onShowOnlineOnlyChange,
}: VersionListViewProps) => {
  const { t } = useTranslation();

  return (
    <Card
      className={cn("border-none shadow-none h-full flex flex-col", className)}
    >
      <CardHeader className="px-4 py-3 space-y-0 border-b">
        <CardTitle className="text-sm font-medium">
          <div className="flex gap-2 items-center">
            <div className="relative flex-1">
              <Input
                value={searchValue}
                icon={<Search className="size-4" />}
                placeholder={t("version.search.placeholder")}
                onChange={(e) => onSearchChange(e.target.value)}
              />
            </div>

            <div className="flex items-center gap-2">
              <Switch
                checked={showOnlineOnly}
                onCheckedChange={onShowOnlineOnlyChange}
                id="show-offline"
              />
              <label
                htmlFor="show-offline"
                className="text-sm cursor-pointer select-none"
              >
                {t("version.show.offline")}
              </label>
            </div>
          </div>
        </CardTitle>
      </CardHeader>

      <CardContent className="flex-1 min-h-0 p-0">
        <ScrollArea horizontal={true} className="h-full px-4">
          <div className="py-4">{children}</div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
};
