import { Tooltip } from "@/components/common/tooltip";
import { EvaluationTask } from "@/types/api/version";
import { ColumnDef } from "@tanstack/react-table";
import { TFunction } from "i18next";
export type TaskTableData = Partial<EvaluationTask>;
export interface TaskAction<TData> {
  row?: TData;
  type:
    | "createEvent"
    | "editEvent"
    | "addBenefit"
    | "editBenefit"
    | "selectGood";
}

/**
 * 表格列
 *
 * @returns
 */
export const getTaskTableColumns = (
  t: TFunction,
): ColumnDef<TaskTableData>[] => {
  return [
    {
      accessorKey: "taskName",
      header: t("review.task.name"),
      meta: {
        label: t("review.task.name"),
      },
      minSize: 100,
      cell: ({ row }) => (
        <Tooltip content={row.original.taskName}>
          <div className="w-full truncate">{row.original.taskName}</div>
        </Tooltip>
      ),
    },
    {
      accessorKey: "taskId",
      header: t("review.task.id"),
      meta: {
        label: t("review.task.id"),
      },
      minSize: 100,
      cell: ({ row }) => (
        <Tooltip content={row.original.taskId}>
          <div className="w-full truncate">{row.original.taskId}</div>
        </Tooltip>
      ),
    },
    {
      accessorKey: "testSetName",
      header: t("test.set"),
      meta: {
        label: t("test.set"),
      },
      minSize: 100,
      cell: ({ row }) => (
        <Tooltip content={row.original.testSetName}>
          <div className="w-full truncate">{row.original.testSetName}</div>
        </Tooltip>
      ),
    },
    {
      accessorKey: "evaluationDate",
      header: t("review.time"),
      meta: {
        label: t("review.time"),
      },
      minSize: 100,
      cell: ({ row }) => (
        <Tooltip content={row.original.evaluationDate}>
          <div className="w-full truncate">{row.original.evaluationDate}</div>
        </Tooltip>
      ),
    },
  ];
};
