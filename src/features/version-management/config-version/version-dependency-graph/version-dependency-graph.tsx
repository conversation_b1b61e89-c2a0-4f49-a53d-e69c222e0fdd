import { ConfigDiffDialog } from "@/components/common/config-diff-viewer";
import { Version } from "@/types/api/version";
import { UseQueryResult } from "@tanstack/react-query";
import { NodeTypes } from "@xyflow/react";
import "@xyflow/react/dist/style.css";
import { useCallback, useState } from "react";
import { GenericDependencyGraph, GenericVersionNode } from "../../components";
import { useVersionDisplayConfig } from "../version-display-config";

// Version 的字段映射配置
const VERSION_FIELD_MAPPING = {
  versionId: "configId",
  baseVersionId: "baseConfigId",
  versionName: "versionName",
  version: "version",
};

export const VersionDependencyGraph = ({
  configId,
  versionsQuery,
}: {
  configId: string;
  versionsQuery: UseQueryResult<Version[]>;
}) => {
  // 配置对比弹窗状态
  const [diffDialogOpen, setDiffDialogOpen] = useState(false);
  const [selectedVersion, setSelectedVersion] = useState<Version | null>(null);

  // 获取版本显示配置
  const versionDisplayConfig = useVersionDisplayConfig();

  // 创建节点组件
  const VersionNodeComponent = (props: any) => (
    <GenericVersionNode {...props} displayConfig={versionDisplayConfig} />
  );

  const NODE_TYPES: NodeTypes = {
    versionNode: VersionNodeComponent,
  };

  // 处理节点点击事件
  const handleNodeClick = useCallback((version: Version) => {
    setSelectedVersion(version);
    setDiffDialogOpen(true);
  }, []);

  return (
    <>
      <GenericDependencyGraph
        versionId={configId}
        versionsQuery={versionsQuery}
        fieldMapping={VERSION_FIELD_MAPPING}
        nodeTypeName="versionNode"
        onNodeClick={handleNodeClick}
        nodeTypes={NODE_TYPES}
      />
      <ConfigDiffDialog
        currentVersionId={selectedVersion?.configId}
        baseVersionId={selectedVersion?.baseConfigId}
        open={diffDialogOpen}
        onOpenChange={setDiffDialogOpen}
      />
    </>
  );
};
