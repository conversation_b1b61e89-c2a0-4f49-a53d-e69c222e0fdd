import {
  ConfigDiffData,
  ConfigDiffViewer,
} from "@/components/common/config-diff-viewer";
import { VersionDetail } from "@/types/api/version";
import { safeParseJson } from "@/utils/safe-parse-json";
import { memo, useMemo } from "react";

const ChangeDetail = memo(
  ({ versionDetail }: { versionDetail: VersionDetail }) => {
    const diffData = useMemo((): ConfigDiffData => {
      const data: ConfigDiffData = {};

      if (versionDetail.shopConfig !== versionDetail.baseShopConfig) {
        data.shopConfig = {
          old: safeParseJson(versionDetail.baseShopConfig, {}),
          new: safeParseJson(versionDetail.shopConfig, {}),
        };
      }

      if (versionDetail.intentConfig !== versionDetail.baseIntentConfig) {
        data.intentConfig = {
          old: safeParseJson(versionDetail.baseIntentConfig, {}),
          new: safeParseJson(versionDetail.intentConfig, {}),
        };
      }

      return data;
    }, [versionDetail]);

    return <ConfigDiffViewer data={diffData} />;
  },
);

export default ChangeDetail;
