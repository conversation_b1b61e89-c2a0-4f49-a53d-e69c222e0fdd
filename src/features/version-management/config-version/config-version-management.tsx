import { shopAtom } from "@/atoms/shop";
import { versionQueryKeys } from "@/constants/query-keys";
import { usePanelSize } from "@/hooks/use-panel-size";
import { EvaluationConfigService } from "@/services/evaluation-config-service";
import { useQuery } from "@tanstack/react-query";
import { useAtomValue } from "jotai";
import { Suspense, useDeferredValue, useState } from "react";
import {
  VERSION_LAYOUT_PANEL_ID,
  VersionDetailSkeleton,
  VersionLayout,
  VersionListSkeleton,
  VersionSkeleton,
} from "../components";
import { VersionDetail } from "./version-detail";
import { VersionList } from "./version-list";

export const ConfigVersionManagement = () => {
  const leftPanelProps = usePanelSize({
    panelId: VERSION_LAYOUT_PANEL_ID,
    defaultSize: 20,
    minSize: 15,
  });
  const [configId, setVersionId] = useState<string | undefined>();
  const [search, setSearch] = useState<string>("");
  const deferredSearch = useDeferredValue(search);
  const shop = useAtomValue(shopAtom);
  const versionsQuery = useQuery({
    queryKey: versionQueryKeys.list({ version: deferredSearch }, shop?.id),
    queryFn: () =>
      EvaluationConfigService.getVersionList(
        { version: deferredSearch },
        { shopId: shop?.id },
      ),
  });

  return (
    <Suspense fallback={<VersionSkeleton leftPanelProps={leftPanelProps} />}>
      <VersionLayout
        leftPanelProps={leftPanelProps}
        leftPanel={
          <Suspense fallback={<VersionListSkeleton />}>
            <VersionList
              versionsQuery={versionsQuery}
              searchValue={search}
              versionId={configId}
              onSelect={setVersionId}
              onSearchChange={setSearch}
            />
          </Suspense>
        }
        rightPanel={
          <Suspense fallback={<VersionDetailSkeleton />}>
            <VersionDetail configId={configId} versionsQuery={versionsQuery} />
          </Suspense>
        }
      />
    </Suspense>
  );
};
