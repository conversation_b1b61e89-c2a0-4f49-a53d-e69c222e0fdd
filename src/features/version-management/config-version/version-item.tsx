import {
  ConfigVersionStatusView,
  getConfigVersionDetails,
  getConfigVersionName,
} from "@/components/common/version-select";
import { Version } from "@/types/api/version";
import { useTranslation } from "react-i18next";
import { VersionListItemView } from "../components";

export const VersionItem = ({
  version,
  versionId,
  onSelect,
}: {
  version: Version;
  versionId: string | undefined;
  onSelect: (versionId: string) => void;
}) => {
  const { t } = useTranslation();
  const details = getConfigVersionDetails(version, t);
  const isSelected = versionId === version.configId;

  return (
    <VersionListItemView
      title={getConfigVersionName(version)}
      isSelected={isSelected}
      isDeleted={version.isDeleted}
      details={details}
      statusBadge={<ConfigVersionStatusView version={version} />}
      onClick={() => onSelect(version.configId)}
    />
  );
};
