import { shop<PERSON>tom } from "@/atoms/shop";
import { PublishButton } from "@/components/common/shop-config-release-button";
import {
  ConfigVersionStatusView,
  getConfigVersionName,
} from "@/components/common/version-select";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { EvaluationConfigService } from "@/services/evaluation-config-service";
import { ConfigVersionStatus, Version } from "@/types/api/version";
import {
  UseQueryResult,
  useSuspenseQuery,
  useMutation,
} from "@tanstack/react-query";
import { useAtomValue } from "jotai";
import { useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import ChangeDetail from "./change-detail";
import {
  DependencyGraphRoot,
  EmptyVersionDetail,
  TaskTableView,
  VersionDetailView,
} from "../components";
import { getTaskTableColumns } from "./task-table-columns";
import { VersionDependencyGraph } from "./version-dependency-graph";
import { toast } from "sonner";
import { PERMISSION_CODES } from "@/constants/permission";
import { usePermission } from "@/hooks/use-permission";
import { ModalJsonEdit } from "@/components/common/modal-json-edit";
import { Can } from "@/components/common/can";
import { Code } from "lucide-react";
export interface VersionDetailProps {
  configId: string | undefined;
  versionsQuery: UseQueryResult<Version[]>;
  setConfigId?: (configId: string | undefined) => void;
}

export const VersionDetail = ({
  configId,
  versionsQuery,
  setConfigId,
}: VersionDetailProps) => {
  const { t } = useTranslation();
  const shop = useAtomValue(shopAtom);
  const { hasPermission } = usePermission();
  const [jsonModalOpen, setJsonModalOpen] = useState<boolean>(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const columns = useMemo(() => getTaskTableColumns(t), [t]);
  const { data: versionData, refetch } = useSuspenseQuery({
    queryKey: ["getVersionDetail", configId, shop],
    queryFn: () => {
      if (!configId || !shop) {
        return null;
      }

      return EvaluationConfigService.getVersionDetail({
        configId,
        shopId: shop.id,
      });
    },
  });
  //删除版本
  const { mutateAsync: deleteVersion } = useMutation({
    mutationFn: EvaluationConfigService.deleteVersion,
    onSuccess: () => {
      setConfigId?.(undefined);
      toast.success("删除成功");
      versionsQuery.refetch();
    },
  });

  const confirmDelete = () => {
    if (!configId) {
      return;
    }
    deleteVersion({
      configId,
      shopId: shop?.id || "",
    });
  };

  // 显示空状态
  if (!versionData || !configId) {
    return <EmptyVersionDetail />;
  }

  const basicInfoFields = [
    {
      label: t("version.name"),
      value: getConfigVersionName(versionData),
    },
    {
      label: t("version.num"),
      value: versionData.version,
    },
    {
      label: t("create.time"),
      value: versionData.createdAt,
    },
    {
      label: t("version.status"),
      value: <ConfigVersionStatusView version={versionData} />,
    },
    {
      label: t("version.base"),
      value: versionData.baseVersion,
    },
  ];

  const sections = [
    {
      title: t("relation.task"),
      content: (
        <TaskTableView data={versionData.evaluationTasks} columns={columns} />
      ),
    },
    {
      content: (
        <DependencyGraphRoot>
          <VersionDependencyGraph
            configId={configId}
            versionsQuery={versionsQuery}
          />
        </DependencyGraphRoot>
      ),
    },
    {
      title: t("change.detail"),
      content: <ChangeDetail versionDetail={versionData} />,
    },
  ];

  const headerActions = (
    <div className="flex items-center gap-3">
      <Can permissionCode="shop:config:code:edit">
        <Button
          icon={<Code />}
          variant="outline"
          className="mr-2"
          aria-label="编辑JSON"
          onClick={() => setJsonModalOpen(true)}
        />
      </Can>
      {(versionData.onlineStatus === ConfigVersionStatus.OFFLINE ||
        versionData.onlineStatus === ConfigVersionStatus.INIT) &&
        hasPermission(PERMISSION_CODES.SHOP_CONFIG_VERSION_DELETE) && (
          <Button
            variant="destructive"
            onClick={() => {
              setIsDeleteDialogOpen(true);
            }}
          >
            {t("prompt.management.version.delete")}
          </Button>
        )}

      {versionData.onlineStatus !== ConfigVersionStatus.ONLINE &&
        hasPermission(PERMISSION_CODES.SHOP_CONFIG_VERSION_PUBLISH) && (
          <PublishButton
            configId={configId}
            onFinish={() => {
              versionsQuery.refetch();
              refetch();
            }}
          />
        )}
    </div>
  );

  return (
    <>
      <VersionDetailView
        title={t("version.detail")}
        headerActions={headerActions}
        basicInfoFields={basicInfoFields}
        remarkLabel={t("version.remark")}
        remarkContent={versionData.remark}
        sections={sections}
        className="border-none shadow-none h-full flex flex-col"
      />

      <AlertDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t("common.delete")}</AlertDialogTitle>
            <AlertDialogDescription>
              确定要删除版本 {versionData.version} 吗？此操作不可撤销。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t("common.cancel")}</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {t("common.delete")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      <ModalJsonEdit
        type="look"
        open={jsonModalOpen}
        onCancel={() => setJsonModalOpen(false)}
        onSubmit={() => {}}
        data={versionData.shopConfig || ""}
        title="店铺配置JSON"
      />
    </>
  );
};
