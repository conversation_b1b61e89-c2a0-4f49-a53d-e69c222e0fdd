import {
  ConfigVersionStatusView,
  getConfigVersionDetails,
  getConfigVersionName,
} from "@/components/common/version-select";
import { Version } from "@/types/api/version";
import { useTranslation } from "react-i18next";
import { VersionDisplayConfig } from "../components";

export const useVersionDisplayConfig = (): VersionDisplayConfig<Version> => {
  const { t } = useTranslation();

  return {
    getDisplayName: (version) =>
      getConfigVersionName(version) || version.version,
    getVersionId: (version) => version.configId,
    getHeader: (version) => <ConfigVersionStatusView version={version} />,
    renderDetails: (version) => {
      const versionDetails = getConfigVersionDetails(version, t, false);

      return (
        <div className="space-y-1">
          <table className="w-full text-xs border-spacing-y-1">
            <tbody>
              <tr>
                <td className="text-gray-600 pr-3 w-12 pb-1.5">
                  {t("version.config.id")}
                </td>
                <td className="font-mono pb-1.5">{version.configId}</td>
              </tr>
              {versionDetails.map((detail, index) => (
                <tr key={index}>
                  <td className="text-gray-600 pr-3 align-top whitespace-nowrap pb-1.5">
                    {detail.label}
                  </td>
                  <td className="pb-1.5">{detail.value}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      );
    },
  };
};
