import { types, Instance } from "mobx-state-tree";

// 质检点项目
export const QualityItemModel = types.model("QualityItem", {
  uniqueId: types.maybe(types.string),
  itemId: types.string,
  itemName: types.string,
  itemScore: types.number,
  itemAnalyse: types.maybeNull(types.string),
});

// 质检详情数据模型
export const InspectionDialogueDetailModel = types.model({
  dialogueQualityAnalyse: types.maybeNull(types.string),
  dialogueQualityId: types.string,
  dialogueQualityItemList: types.maybeNull(types.array(QualityItemModel)),
  dialogueQualityScore: types.maybeNull(types.string),
  isReQuality: types.maybeNull(types.number),
});

export type QualityItem = Instance<typeof QualityItemModel>;

export type InspectionDialogueDetail = Instance<
  typeof InspectionDialogueDetailModel
>;
