import { Instance, types } from "mobx-state-tree";

/**
 * 质检明细模型
 */
export const QualityDetailModel = types.model("QualityDetail", {
  dialogueQualityId: types.string,
  qualityDate: types.maybeNull(types.string),
  conversationId: types.string,
  dialogueQuestionId: types.string,
  dialogueContent: types.maybeNull(types.string),
  qualityScore: types.maybeNull(types.string),
  dialogueQualityItemList: types.maybeNull(types.array(types.string)),
  isReQuality: types.string,
  qualityAnalyse: types.maybeNull(types.string),
  emotionScoreStr: types.maybeNull(types.string),
});

export type QualityDetail = Instance<typeof QualityDetailModel>;
