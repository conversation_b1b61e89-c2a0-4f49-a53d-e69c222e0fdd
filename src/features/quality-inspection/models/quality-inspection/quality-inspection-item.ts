import { Instance, types } from "mobx-state-tree";

/**
 * 质检项模型
 */
export const QualityInspectionItemModel = types.model("QualityInspectionItem", {
  qualityInspectionItemId: types.maybeNull(types.string),
  qualityInspectionItemName: types.maybeNull(types.string),
  qualityInspectionItemScore: types.maybeNull(types.string),
  qualityInspectionItemType: types.maybeNull(types.string),
  qualityInspectionItemList: types.maybeNull(
    types.late(() => types.array(QualityInspectionItemModel)),
  ),
});

export type QualityInspectionItem = Instance<typeof QualityInspectionItemModel>;
