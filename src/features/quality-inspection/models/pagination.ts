import { types } from "mobx-state-tree";

/**
 * 通用分页模型
 * 可以被不同的 store 引用和复用
 */
export const PaginationModel = types
  .model("PaginationModel", {
    current: types.optional(types.number, 1),
    pageSize: types.optional(types.number, 10),
    total: types.optional(types.number, 0),
  })
  .views((self) => ({
    get totalPages() {
      return Math.ceil(self.total / self.pageSize);
    },
    get hasNextPage() {
      return self.current < Math.ceil(self.total / self.pageSize);
    },
    get hasPreviousPage() {
      return self.current > 1;
    },
  }))
  .actions((self) => ({
    setPage(pageNum: number) {
      self.current = pageNum;
    },
    setPageSize(size: number) {
      self.pageSize = size;
    },
    setTotal(total: number) {
      self.total = total;
    },
    reset() {
      self.current = 1;
      self.total = 0;
    },
  }));
