import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from "@/components/ui/resizable";
import { InspectionDetails } from "@/features/quality-inspection";
import { usePanelControl } from "@/hooks";
import { store } from "@/store";
import { ChevronRight } from "lucide-react";
import { observer } from "mobx-react-lite";
import { useState } from "react";
import { InspectionDetailTable } from "./components";

export const InspectionDetailPage: React.FC = observer(() => {
  const { qualityInspection } = store;
  const [selectedDialogueId, setSelectedDialogueId] = useState<string | null>(
    null,
  );

  const { minSize, panelRef, expandPanel, collapsePanel, onResize } =
    usePanelControl({
      panelGroupId: "inspection-detail-page",
      handleId: "inspection-detail-page-handle",
    });

  const handleDialogueIdChange = (dialogueId: string | null) => {
    if (dialogueId && !(dialogueId === selectedDialogueId)) {
      setSelectedDialogueId(dialogueId);
      expandPanel();
    } else {
      collapsePanel();
      setSelectedDialogueId(null);
    }
  };

  return (
    <ResizablePanelGroup
      className="h-full"
      direction="horizontal"
      id="inspection-detail-page"
    >
      <ResizablePanel>
        <InspectionDetailTable setSelectedDialogueId={handleDialogueIdChange} />
      </ResizablePanel>

      <ResizableHandle id="inspection-detail-page-handle" />
      <ResizablePanel
        className="overflow-visible!"
        ref={panelRef}
        collapsible={true}
        minSize={minSize}
        defaultSize={0}
        collapsedSize={0}
        onResize={onResize}
      >
        {selectedDialogueId && (
          <div className="h-full p-4 relative">
            <button
              className="absolute top-1/2 -translate-y-1/2 -translate-x-full left-0 w-4 h-16 rounded-l-md
              bg-white border border-border hover:bg-gray-50 transition-colors duration-200"
              onClick={() => {
                collapsePanel();
                setSelectedDialogueId(null);
              }}
            >
              <ChevronRight className="w-4 h-4" />
            </button>

            <InspectionDetails
              dialogueId={selectedDialogueId}
              onCompleted={() => {
                // 刷新表格数据
                qualityInspection.fetchQualityDetails();
                // 清空选中的对话ID
                setSelectedDialogueId(null);
                collapsePanel();
              }}
              onScoreChange={() => {
                // 刷新表格数据
                qualityInspection.fetchQualityDetails();
              }}
            />
          </div>
        )}
      </ResizablePanel>
    </ResizablePanelGroup>
  );
});

InspectionDetailPage.displayName = "InspectionDetail";
