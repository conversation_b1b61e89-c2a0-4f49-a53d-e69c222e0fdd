import { Tag } from "antd";
import React from "react";
import { useTranslation } from "react-i18next";
interface EmotionItemsCellProps {
  type: string;
}

export const EmotionItemsCell: React.FC<EmotionItemsCellProps> = ({ type }) => {
  const { t } = useTranslation();
  const items = [
    { label: t("customer.emotion.monitoring.1"), value: 0, color: "error" },
    { label: t("customer.emotion.monitoring.2"), value: 1, color: "error" },
    { label: t("customer.emotion.monitoring.3"), value: 2, color: "error" },
    {
      label: t("customer.emotion.monitoring.4"),
      value: 3,
      color: "processing",
    },
    { label: t("customer.emotion.monitoring.5"), value: 4, color: "success" },
    { label: t("customer.emotion.monitoring.6"), value: 5, color: "success" },
    { label: t("customer.emotion.monitoring.7"), value: -9, color: "" },
  ];
  if (type === "-") return <span>-</span>;
  const item = items?.filter((item: any) => item.label === type)[0];

  return (
    <div className="flex flex-wrap gap-2">
      <Tag color={item?.color}>{item?.label}</Tag>
    </div>
  );
};
