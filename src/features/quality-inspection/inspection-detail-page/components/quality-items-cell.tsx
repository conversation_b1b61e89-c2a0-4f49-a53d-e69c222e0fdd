import { Label } from "@/components/ui/label";
import React from "react";

interface QualityItemsCellProps {
  items: string;
}

export const QualityItemsCell: React.FC<QualityItemsCellProps> = ({
  items,
}) => {
  if (items === "-") return <span>-</span>;

  const itemList = items.split(", ");
  return (
    <div className="flex flex-wrap gap-2">
      {itemList.map((item, index) => (
        <Label key={index} variant="error">
          {item}
        </Label>
      ))}
    </div>
  );
};
