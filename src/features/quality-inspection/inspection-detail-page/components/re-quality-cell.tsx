import { Status } from "@/components/ui/status";
import React from "react";
import { useTranslation } from "react-i18next";

interface ReQualityCellProps {
  isReQuality: boolean;
}

export const ReQualityCell: React.FC<ReQualityCellProps> = ({
  isReQuality,
}) => {
  const { t } = useTranslation();

  return (
    <Status status={isReQuality ? "success" : "normal"}>
      {isReQuality
        ? t("quality.inspection.requalified")
        : t("quality.inspection.not.requalified")}
    </Status>
  );
};
