import React from "react";
import { Popover } from "antd";
import { useTranslation } from "react-i18next";
import { cn } from "@/lib/utils";
import clsx from "clsx";
import { isEmpty } from "lodash";
import { Label } from "@/components/ui/label";

interface RemarksExpandableListProps {
  jsonString: string;
}

// 单个备注项的渲染组件
const RemarkItem: React.FC<{
  className?: string;
  remark: Record<string, string>;
  truncate?: boolean;
}> = ({ className, remark, truncate = false }) => {
  const key = Object.keys(remark)[0];
  const value = remark[key];

  return (
    <>
      <div className="table-cell p-1 leading-8 whitespace-nowrap">
        <Label variant="warning">{key}</Label>
      </div>
      <div className={cn("table-cell p-1 leading-8", className)}>
        <span
          className={clsx(
            "self-start w-full text-gray-600 text-sm flex-1 min-w-0",
            {
              truncate,
            },
          )}
        >
          {value}
        </span>
      </div>
    </>
  );
};

export const RemarksExpandableList: React.FC<RemarksExpandableListProps> = ({
  jsonString,
}) => {
  const { t } = useTranslation();

  if (jsonString === "-") return <span>-</span>;

  try {
    const remarks = JSON.parse(jsonString);

    if (!Array.isArray(remarks)) {
      return <span>{jsonString}</span>;
    }

    // 过滤掉分析内容为空的备注
    const displayedRemarks = remarks.filter(
      (remark) => !Object.values(remark).some((value) => isEmpty(value)),
    );

    // 完整内容的渲染
    const fullContent = (
      <ul className="table list-none p-0 m-0 max-w-md space-y-2">
        {displayedRemarks.map((remark, index) => (
          <li className="table-row" key={index}>
            <RemarkItem remark={remark} />
          </li>
        ))}
      </ul>
    );

    // 只显示前两条记录
    const displayedContent = displayedRemarks
      .slice(0, 2)
      .map((remark, index) => (
        <div className="flex" key={index}>
          <RemarkItem className="truncate" remark={remark} truncate />
        </div>
      ));

    return (
      <div className="space-y-1">
        <Popover
          content={fullContent}
          title={
            <span className="px-1">{t("quality.inspection.analyse")}</span>
          }
          placement="left"
          trigger={["click", "hover"]}
          overlayInnerStyle={{ maxHeight: "60vh", overflowY: "auto" }}
        >
          {displayedContent}
        </Popover>
      </div>
    );
  } catch (err) {
    return <span>{jsonString}</span>;
  }
};
