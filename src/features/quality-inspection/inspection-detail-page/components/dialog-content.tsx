interface Message {
  type: string;
  time: string;
  sender: string;
  content: string[];
}

export const DialogContent = ({
  dialogueContent,
}: {
  dialogueContent: string;
}) => {
  // 解析对话内容
  const lines = dialogueContent.split("\n").filter((line) => line.trim());

  // 分组消息
  const messages: Message[] = [];
  let currentType = "";
  let currentTime = "";
  let currentSender = "";
  let currentContent: string[] = [];

  for (const line of lines) {
    if (
      line.includes("系统昵称") ||
      line.includes("客服昵称") ||
      line.includes("顾客昵称")
    ) {
      // 如果已经有消息内容，先保存之前的消息
      if (currentContent.length > 0) {
        messages.push({
          type: currentType,
          time: currentTime,
          sender: currentSender,
          content: [...currentContent],
        });
        currentContent = [];
      }

      // 设置新消息的发送者类型
      if (line.includes("系统昵称")) {
        currentType = "system";
        currentSender = line.split("：")[1] || "";
      } else if (line.includes("客服昵称")) {
        currentType = "service";
        currentSender = line.split("：")[1] || "";
      } else if (line.includes("顾客昵称")) {
        currentType = "customer";
        currentSender = line.split("：")[1] || "";
      }
    } else if (
      line.includes("时间：") &&
      (line.includes("系统消息时间") ||
        line.includes("客服消息时间") ||
        line.includes("顾客消息时间"))
    ) {
      currentTime = line.split("：").pop() || "";
    } else {
      // 其他行都视为内容
      currentContent.push(line);
    }
  }

  // 添加最后一条消息
  if (currentContent.length > 0) {
    messages.push({
      type: currentType,
      time: currentTime,
      sender: currentSender,
      content: [...currentContent],
    });
  }

  return (
    <div className="p-3 bg-gray-50">
      <div className="bg-white rounded-lg border border-gray-200 space-y-3 p-4 overflow-y-auto max-h-[400px]">
        {messages.map((msg, idx) => (
          <div key={idx}>
            {/* 消息头部：发送者和时间 */}
            <div className="flex items-center mb-1 text-sm">
              <div
                className={`flex items-center font-medium ${
                  msg.type === "system"
                    ? "text-gray-500"
                    : msg.type === "service"
                      ? "text-blue-600"
                      : "text-green-600"
                }`}
              >
                {msg.type === "system" && (
                  <span className="inline-block w-2 h-2 rounded-full bg-gray-400 mr-2"></span>
                )}
                {msg.type === "service" && (
                  <span className="inline-block w-2 h-2 rounded-full bg-blue-500 mr-2"></span>
                )}
                {msg.type === "customer" && (
                  <span className="inline-block w-2 h-2 rounded-full bg-green-500 mr-2"></span>
                )}
                {msg.sender}
              </div>
              {msg.time && (
                <div className="text-xs text-gray-400 ml-3">{msg.time}</div>
              )}
            </div>

            {/* 消息内容 */}
            <div
              className={`rounded-lg p-3 ${
                msg.type === "system"
                  ? "bg-gray-100 text-gray-700"
                  : msg.type === "service"
                    ? "bg-blue-50 text-gray-800"
                    : "bg-green-50 text-gray-800"
              }`}
            >
              {msg.content.map((line, lineIdx) => (
                <div key={lineIdx} className={lineIdx > 0 ? "mt-2" : ""}>
                  {line}
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
