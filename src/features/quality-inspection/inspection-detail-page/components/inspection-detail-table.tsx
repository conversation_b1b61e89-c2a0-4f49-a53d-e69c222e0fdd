import { CopyableText } from "@/components/common/copyable-text";
import { TableFilter } from "@/components/common/table-filter";
import {
  DateRangeFilterConfig,
  FilterFieldConfig,
  FilterFieldType,
  SelectFilterConfig,
  StringFilterConfig,
} from "@/components/common/table-filter/types";
import { Button } from "@/components/ui/button";
import { InspectionItemCascader } from "@/features/quality-inspection";
import { useInspectionItems } from "@/hooks";
import { store } from "@/store";
import { FilterValues } from "@/store/qualityInspection";
import { Table } from "antd";
import type { ColumnsType } from "antd/es/table";
import { Download } from "lucide-react";
import { observer } from "mobx-react-lite";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { Can } from "@/components/common/can";
import {
  DialogContent,
  EmotionItemsCell,
  QualityItemsCell,
  RemarksExpandableList,
  ReQ<PERSON>ity<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
} from ".";

interface DataType {
  key: string;
  date: string;
  conversationId: string;
  dialogueId: string;
  dialogueContent: string;
  qualityScore: string;
  qualityItems: string;
  isReQuality: boolean;
  qualityAnalyse: string;
  emotionScoreStr: string;
}

export const InspectionDetailTable = observer(
  ({
    setSelectedDialogueId,
  }: {
    setSelectedDialogueId: (dialogueId: string | null) => void;
  }) => {
    const { t } = useTranslation();
    const { qualityInspection } = store;
    const [expandedRowKeys, setExpandedRowKeys] = useState<string[]>([]);
    const { inspectionItems, searchValue, setSearchValue, loading } =
      useInspectionItems();

    const filterConfigs: FilterFieldConfig[] = [
      {
        key: "date",
        label: t("quality.inspection.date"),
        type: FilterFieldType.DATE_RANGE,
        placeholder: [t("start.date"), t("end.date")],
        showTime: false,
        allowClear: false,
      } as DateRangeFilterConfig,
      {
        key: "dialogueContent",
        label: t("quality.inspection.dialogue.content"),
        type: FilterFieldType.STRING,
        componentType: "search",
        placeholder: t("quality.inspection.dialogue.content.placeholder"),
      } as StringFilterConfig,
      {
        key: "dialogueQualityId",
        label: t("quality.inspection.dialogue.id"),
        type: FilterFieldType.STRING,
        componentType: "search",
        placeholder: t("quality.inspection.dialogue.id.placeholder"),
      },
      {
        key: "qualityInspectionItem",
        label: t("quality.inspection.quality.point"),
        type: FilterFieldType.SELECT,
        placeholder: t("quality.inspection.quality.point"),
        renderer: (config) => (
          <InspectionItemCascader
            className="min-w-[160px]"
            placeholder={config.placeholder}
            inspectionItems={inspectionItems}
            searchValue={searchValue}
            multiple={true}
            loading={loading}
            setSearchValue={setSearchValue}
            allowClear={true}
          />
        ),
      } as SelectFilterConfig,
      {
        key: "isReQuality",
        label: t("quality.inspection.is.re.quality"),
        type: FilterFieldType.SELECT,
        options: [
          { label: t("common.all"), value: "all" },
          { label: t("quality.inspection.requalified"), value: "1" },
          { label: t("quality.inspection.not.requalified"), value: "0" },
        ],
        allowClear: false,
      } as SelectFilterConfig,
      {
        key: "qualityScoreRange",
        label: t("quality.inspection.score.range"),
        type: FilterFieldType.SELECT,
        allowClear: false,
        options: [
          { label: t("common.all"), value: "all" },
          {
            label: (
              <span className="text-green-700">
                {t("quality.inspection.score.range.1")}
              </span>
            ),
            value: "1",
          },
          {
            label: (
              <span className="text-green-700">
                {t("quality.inspection.score.range.2")}
              </span>
            ),
            value: "2",
          },
          {
            label: (
              <span className="text-red-700">
                {t("quality.inspection.score.range.3")}
              </span>
            ),
            value: "3",
          },
        ],
      } as SelectFilterConfig,
      {
        key: "emotionScores",
        label: t("customer.emotion.monitoring"),
        type: FilterFieldType.SELECT,
        multiple: true,
        placeholder: t("please.select.customer.emotion.monitoring"),
        options: [
          { label: t("customer.emotion.monitoring.1"), value: 0 },
          { label: t("customer.emotion.monitoring.2"), value: 1 },
          { label: t("customer.emotion.monitoring.3"), value: 2 },
          { label: t("customer.emotion.monitoring.4"), value: 3 },
          { label: t("customer.emotion.monitoring.5"), value: 4 },
          { label: t("customer.emotion.monitoring.6"), value: 5 },
          { label: t("customer.emotion.monitoring.7"), value: -9 },
        ],
        allowClear: false,
      } as SelectFilterConfig,
    ];

    // 处理过滤器值变化
    const handleFilterChange = (values: Record<string, any>) => {
      // 清空展开的行
      setExpandedRowKeys([]);
      qualityInspection.setFilterValues(values as FilterValues);
    };

    // 编辑
    const handleEdit = (record: DataType) => {
      setSelectedDialogueId(record.dialogueId);
    };

    // 切换展开行
    const toggleExpand = (record: DataType) => {
      setExpandedRowKeys((keys) => {
        const key = record.key;

        return keys.includes(key)
          ? keys.filter((k) => k !== key)
          : [...keys, key];
      });
    };

    const columns: ColumnsType<DataType> = [
      {
        title: t("quality.inspection.date"),
        dataIndex: "date",
        key: "date",
        width: 110,
      },
      {
        title: t("conversation.id"),
        dataIndex: "conversationId",
        key: "conversationId",
        width: 160,
        ellipsis: true,
        render: (text) => <CopyableText text={text} />,
      },
      {
        title: t("quality.inspection.dialogue.id"),
        dataIndex: "dialogueId",
        key: "dialogueId",
        width: 160,
        ellipsis: true,
        render: (text) => <CopyableText text={text} />,
      },
      {
        title: t("quality.inspection.dialogue.content"),
        dataIndex: "dialogueContent",
        key: "dialogueContent",
        width: 300,
        render: (text, record) => (
          <div className="flex gap-1 items-center">
            <div className="line-clamp-2 text-sm">{text}</div>
            {text !== "-" && (
              <div className="mt-0.5 flex items-center gap-1 justify-end">
                <Button
                  variant="link"
                  onClick={(e) => {
                    e.stopPropagation();
                    toggleExpand(record);
                  }}
                  className="p-0"
                >
                  {expandedRowKeys.includes(record.key)
                    ? t("common.collapse")
                    : t("common.expand")}
                </Button>
                <CopyableText text={text} showText={false} />
              </div>
            )}
          </div>
        ),
      },
      {
        title: t("customer.emotion.monitoring"),
        dataIndex: "emotionScoreStr",
        key: "emotionScoreStr",
        width: 120,
        align: "center",
        render: (type) => <EmotionItemsCell type={type} />,
      },
      {
        title: t("quality.inspection.score"),
        dataIndex: "qualityScore",
        key: "qualityScore",
        width: 80,
        align: "center",
        render: (score) => <ScoreCell score={score} />,
      },
      {
        title: t("quality.inspection.deduct.items"),
        dataIndex: "qualityItems",
        key: "qualityItems",
        width: 200,
        render: (items) => <QualityItemsCell items={items} />,
      },
      {
        title: t("quality.inspection.is.re.quality"),
        dataIndex: "isReQuality",
        key: "isReQuality",
        width: 100,
        align: "center",
        render: (isReQuality) => <ReQualityCell isReQuality={isReQuality} />,
      },
      {
        title: t("quality.inspection.analyse"),
        dataIndex: "qualityAnalyse",
        key: "qualityAnalyse",
        width: 300,
        render: (text) => <RemarksExpandableList jsonString={text} />,
      },
      {
        title: t("common.operation"),
        key: "action",
        width: 100,
        align: "center",
        fixed: "right",
        render: (_, record) => (
          <Can permissionCode="quality:inspection:detail:crud">
            <Button variant="link" size="sm" onClick={() => handleEdit(record)}>
              {t("common.edit")}
            </Button>
          </Can>
        ),
      },
    ];

    // 将后端数据转换为表格数据结构
    const data: DataType[] = qualityInspection.detailList.map(
      (item, index) => ({
        key: item.dialogueQualityId || `${index}`,
        date: item.qualityDate || "-",
        conversationId: item.conversationId || "-",
        dialogueId: item.dialogueQuestionId || "-",
        dialogueContent: item.dialogueContent || "-",
        qualityScore: item.qualityScore || "-",
        qualityItems: item.dialogueQualityItemList?.join(", ") || "-",
        isReQuality: item.isReQuality === "1",
        qualityAnalyse: item.qualityAnalyse || "-",
        emotionScoreStr: item.emotionScoreStr || "-",
      }),
    );

    useEffect(() => {
      qualityInspection.fetchQualityDetails();
    }, [qualityInspection]);

    return (
      <div className="p-6 h-full overflow-auto">
        <TableFilter
          filters={filterConfigs}
          initialValues={qualityInspection.filterValues}
          onChange={handleFilterChange}
          extra={
            <Can permissionCode="quality:inspection:detail:crud">
              <Button
                variant="outline"
                size="sm"
                type="button"
                onClick={() => qualityInspection.exportQualityDetails()}
              >
                <Download className="size-3 mr-1.5" />
                {t("common.export")}
              </Button>
            </Can>
          }
        />

        <Table
          columns={columns}
          dataSource={data}
          loading={qualityInspection.loading}
          expandable={{
            expandedRowKeys,
            expandIconColumnIndex: -1,
            expandedRowRender: (record) => (
              <DialogContent dialogueContent={record.dialogueContent} />
            ),
            expandIcon: () => null,
            rowExpandable: (record) => record.dialogueContent !== "-",
          }}
          pagination={{
            current: qualityInspection.pagination.current,
            pageSize: qualityInspection.pagination.pageSize,
            total: qualityInspection.pagination.total,
            onChange: (page, pageSize) => {
              qualityInspection.pagination.setPage(page);
              qualityInspection.pagination.setPageSize(pageSize);
              qualityInspection.fetchQualityDetails();
            },
            showSizeChanger: true,
            showTotal: (total) => t("common.pager.total", { count: total }),
          }}
          scroll={{ x: "min-content" }}
          bordered={false}
        />
      </div>
    );
  },
);
