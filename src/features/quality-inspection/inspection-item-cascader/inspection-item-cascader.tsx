import { ScoreText } from "@/components/common/score-text";
import { QualityInspectionItem } from "@/features/quality-inspection/models";
import { Cascader, CascaderProps, Empty, Spin } from "antd";
import clsx from "clsx";
import { debounce } from "lodash";
import { useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { transformToOptions } from "./utils";

/**
 * InspectionItemCascader 组件的属性
 *
 * 当使用 useInspectionItems Hook 时，提供：
 * - inspectionItems: 质检项列表
 * - searchValue: 搜索值
 * - setSearchValue: 设置搜索值的方法
 */
export interface InspectionItemCascaderProps
  extends Omit<CascaderProps, "options" | "onSearch"> {
  /**
   * 质检项列表
   */
  inspectionItems: QualityInspectionItem[];
  /**
   * 当前搜索值
   */
  searchValue?: string;
  /**
   * 防抖延迟时间（毫秒），默认300ms
   */
  debounceDelay?: number;
  /**
   * 是否正在加载数据
   */
  loading?: boolean;
  /**
   * 自定义空数据提示
   */
  emptyText?: string;
  /**
   * 设置搜索值的方法
   */
  setSearchValue?: (value?: string) => void;
}

/**
 * 质检项级联选择器组件
 */
export const InspectionItemCascader = ({
  className,
  inspectionItems,
  searchValue,
  debounceDelay = 300,
  loading = false,
  emptyText,
  setSearchValue,
  onChange,
  ...props
}: InspectionItemCascaderProps) => {
  const { t } = useTranslation();
  const options = useMemo(
    () => transformToOptions(inspectionItems),
    [inspectionItems],
  );

  const [localSearchValue, setLocalSearchValue] = useState<string | undefined>(
    searchValue,
  );

  // 使用 lodash 的 debounce 创建一个防抖版本的设置搜索值函数
  const debouncedSetSearchValue = useMemo(
    () =>
      debounce((value?: string) => {
        setSearchValue?.(value);
      }, debounceDelay),
    [setSearchValue, debounceDelay],
  );

  // 组件卸载时取消防抖函数
  useEffect(() => {
    return () => {
      debouncedSetSearchValue.cancel();
    };
  }, [debouncedSetSearchValue]);

  // 加载中的内容
  const loadingContent = (
    <div className="flex items-center justify-center py-4">
      <Spin size="small" />
      <span className="ml-2 text-gray-500">{t("common.loading")}</span>
    </div>
  );

  // 空数据提示
  const emptyContent = (
    <Empty
      image={Empty.PRESENTED_IMAGE_SIMPLE}
      description={emptyText || t("common.no.data")}
      className="my-4"
    />
  );

  // 自定义下拉菜单渲染
  const customDropdownRender = (menu: React.ReactElement) => {
    // 判断是否应该显示空数据提示
    const isEmptyData =
      options.length === 0 ||
      (localSearchValue && inspectionItems.length === 0);

    // 如果正在加载，显示加载中
    if (loading) {
      return loadingContent;
    }

    // 如果结果为空且有搜索关键词，显示空提示
    if (isEmptyData && localSearchValue) {
      return emptyContent;
    }

    // 其他情况返回正常菜单
    return menu;
  };

  return (
    // @ts-expect-error CascaderProps 的类型定义不正确
    <Cascader
      className={clsx("w-full", className)}
      options={options}
      placeholder={t("quality.inspection.point.select.placeholder")}
      showSearch={true}
      searchValue={localSearchValue}
      allowClear={true}
      changeOnSelect={false}
      showCheckedStrategy={Cascader.SHOW_CHILD}
      displayRender={(labels) => labels.join(" / ")}
      loading={loading}
      maxTagCount={2}
      {...props}
      dropdownRender={customDropdownRender}
      optionRender={(option) => {
        if (option.score != null) {
          return (
            <span className="flex items-center gap-2">
              {option.label}
              <ScoreText score={Number(option.score)} showTooltip={false} />
            </span>
          );
        }

        return <span>{option.label}</span>;
      }}
      onChange={(...args) => {
        onChange?.(...args);
        setLocalSearchValue(undefined);
        setSearchValue?.(undefined);
      }}
      onSearch={(value) => {
        setLocalSearchValue(value);
        debouncedSetSearchValue(value);
      }}
    />
  );
};
