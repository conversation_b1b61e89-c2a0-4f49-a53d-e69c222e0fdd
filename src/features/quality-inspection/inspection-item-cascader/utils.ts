import { QualityInspectionItem } from "@/features/quality-inspection/models/quality-inspection";

// 将质检点数据转换为 Cascader 选项格式
export const transformToOptions = (
  items: QualityInspectionItem[] = [],
  depth = 0,
) =>
  items.map((item) => ({
    label: item.qualityInspectionItemType || item.qualityInspectionItemName,
    // 如果 itemId 为空，就说明是父级归类而已
    value: item.qualityInspectionItemType || item.qualityInspectionItemId,
    score:
      item.qualityInspectionItemScore != null
        ? -Number(item.qualityInspectionItemScore)
        : null,
    children: item.qualityInspectionItemList
      ? transformToOptions(item.qualityInspectionItemList, depth + 1)
      : undefined,
    disableCheckbox: depth === 0,
  }));

// 递归查找质检点
export const findPoint = (
  items: QualityInspectionItem[],
  pointId: string,
): QualityInspectionItem | null => {
  for (const item of items) {
    if (item.qualityInspectionItemId === pointId) {
      return item;
    }

    if (item.qualityInspectionItemList) {
      const found = findPoint(item.qualityInspectionItemList, pointId);

      if (found) return found;
    }
  }

  return null;
};
