import { Button } from "@/components/ui/button";
import {
  findPoint,
  InspectionItemCascader,
} from "@/features/quality-inspection";
import { useInspectionItems } from "@/hooks";
import { Tooltip } from "antd";
import { AnimatePresence, motion } from "framer-motion";
import { Trash2 } from "lucide-react";
import { observer } from "mobx-react-lite";
import { Instance } from "mobx-state-tree";
import React from "react";
import { useTranslation } from "react-i18next";
import { QualityInspectionDialogueModel } from "../quality-inspection-dialogueModel";

interface EditingQualityItemListProps {
  model: Instance<typeof QualityInspectionDialogueModel>;
  onScoreChange?: (score: number) => void;
}

export const EditingQualityItemList: React.FC<EditingQualityItemListProps> =
  observer(({ model, onScoreChange }) => {
    const { t } = useTranslation();
    const editingItems = model.editingQualityItemList ?? [];
    const { inspectionItems, searchValue, loading, setSearchValue } =
      useInspectionItems();

    return (
      <div className="space-y-3">
        <AnimatePresence>
          {editingItems.map((_, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 10 }}
              transition={{ duration: 0.2, ease: "easeOut" }}
              className="group relative space-y-2 py-3 px-4 bg-white rounded-lg border border-gray-200 transition-all duration-200 hover:bg-gray-50"
            >
              <div className="flex items-center gap-3">
                <InspectionItemCascader
                  inspectionItems={inspectionItems}
                  searchValue={searchValue}
                  loading={loading}
                  className="w-auto flex-1 min-w-0"
                  setSearchValue={setSearchValue}
                  dropdownMatchSelectWidth={false}
                  onChange={async (value) => {
                    const pointId = value?.[value.length - 1];

                    if (pointId) {
                      const point = findPoint(
                        inspectionItems,
                        pointId as string,
                      );

                      if (point) {
                        await model.applyPointToEditingQualityItem(
                          index,
                          point,
                        );

                        onScoreChange?.(model.score);
                      }
                    }
                  }}
                />

                <Tooltip title={t("common.delete")}>
                  <Button
                    variant="icon"
                    status="error"
                    icon={<Trash2 />}
                    onClick={() => model.deleteEditingQualityItem(index)}
                  />
                </Tooltip>
              </div>
            </motion.div>
          ))}
        </AnimatePresence>
      </div>
    );
  });
