import { ExtendedTooltip } from "@/components/common/extended-tooltip";
import { Button } from "@/components/ui/button";
import { Input, Popconfirm, Tooltip } from "antd";
import { AnimatePresence, motion } from "framer-motion";
import { ChevronDown, Save, Trash2 } from "lucide-react";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";

interface QualityItemProps {
  itemName: string;
  itemScore: number;
  itemAnalyse: string | null;
  index: number;
  isExpanded: boolean;
  loading: boolean;
  onToggle: () => void;
  onDelete: () => void;
  onAnalyseChange?: (value: string) => void;
}

export const QualityItem: React.FC<QualityItemProps> = ({
  itemName,
  itemScore,
  itemAnalyse,
  index,
  isExpanded,
  loading,
  onToggle,
  onDelete,
  onAnalyseChange,
}) => {
  const { t } = useTranslation();
  const [analyseValue, setAnalyseValue] = useState(itemAnalyse || "");
  const isDirty = itemAnalyse !== analyseValue;

  const handleAnalyseChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setAnalyseValue(e.target.value);
  };

  const handleSave = () => {
    onAnalyseChange?.(analyseValue);
  };

  return (
    <div className="overflow-hidden rounded-md border border-gray-200 bg-white">
      <div
        onClick={onToggle}
        data-testid={`quality-item-${index}`}
        className="flex cursor-pointer items-center justify-between bg-gray-50 px-4 py-3 transition-colors gap-2 hover:bg-gray-100"
      >
        <div className="flex items-center gap-2 flex-1 min-w-0">
          <motion.span
            animate={{ rotate: isExpanded ? 180 : 0 }}
            transition={{ duration: 0.2 }}
          >
            <ChevronDown className="h-4 w-4 text-gray-400" />
          </motion.span>
          <ExtendedTooltip
            className="flex-1 min-w-0 truncate"
            title={itemName}
            placement="topLeft"
          >
            {itemName}
          </ExtendedTooltip>
          <span className="font-medium text-red-500">-{itemScore}</span>
        </div>
        <Popconfirm
          title={t("quality.inspection.confirm.delete", {
            itemName,
            itemScore,
          })}
          description={t("quality.inspection.confirm.delete.description")}
          onConfirm={onDelete}
          onPopupClick={(event) => {
            event.stopPropagation();
          }}
          okText={t("common.confirm")}
          cancelText={t("common.cancel")}
        >
          <Tooltip title={t("common.delete")} placement="left">
            <Button
              variant="icon"
              status="error"
              icon={<Trash2 />}
              onClick={(event) => {
                event.stopPropagation();
              }}
            />
          </Tooltip>
        </Popconfirm>
      </div>
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="overflow-hidden"
          >
            <div className="border-t border-gray-200 p-3">
              <div className="flex flex-col gap-2">
                <Input.TextArea
                  value={analyseValue}
                  autoSize={{
                    minRows: 4,
                  }}
                  autoFocus={true}
                  placeholder={t("quality.inspection.analyse.placeholder")}
                  data-testid={`quality-item-analyse-${index}`}
                  onChange={handleAnalyseChange}
                />
                <div className="flex justify-end">
                  <Button
                    withAnimation={true}
                    variant="default"
                    disabled={!isDirty}
                    size="sm"
                    loading={loading}
                    onClick={handleSave}
                    icon={<Save />}
                    data-testid={`quality-item-save-${index}`}
                  >
                    {t("common.save")}
                  </Button>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
