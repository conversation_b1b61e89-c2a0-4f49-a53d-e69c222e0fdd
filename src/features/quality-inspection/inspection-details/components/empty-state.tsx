import { motion } from "framer-motion";
import React from "react";
import { useTranslation } from "react-i18next";

export const EmptyState: React.FC = () => {
  const { t } = useTranslation();

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="flex h-24 items-center justify-center rounded-md border border-dashed border-gray-300 bg-gray-50 p-4 text-gray-500"
    >
      {t("quality.inspection.point.empty")}
    </motion.div>
  );
};
