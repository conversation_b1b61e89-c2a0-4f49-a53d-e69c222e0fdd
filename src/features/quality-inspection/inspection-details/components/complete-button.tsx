import { But<PERSON> } from "@/components/ui/button";
import { Check } from "lucide-react";
import { Instance } from "mobx-state-tree";
import React from "react";
import { useTranslation } from "react-i18next";
import { QualityInspectionDialogueModel } from "../quality-inspection-dialogueModel";
import { observer } from "mobx-react-lite";
import { message } from "antd";

interface CompleteButtonProps {
  model: Instance<typeof QualityInspectionDialogueModel>;
  onCompleted?: () => void;
}

export const CompleteButton: React.FC<CompleteButtonProps> = observer(
  ({ model, onCompleted }) => {
    const { t } = useTranslation();
    const isReQuality = model.detail?.isReQuality === 1;

    const handleComplete = async () => {
      if (await model.completeInspection()) {
        message.success(t("quality.inspection.re.quality.complete.success"));
        onCompleted?.();
      } else {
        message.error(t("quality.inspection.re.quality.complete.error"));
      }
    };

    return (
      <div className="flex justify-center py-2">
        <Button
          withAnimation
          variant="default"
          size="sm"
          data-testid="complete-inspection-btn"
          className="w-32"
          icon={<Check />}
          loading={model.completingInspection}
          disabled={isReQuality}
          onClick={handleComplete}
        >
          {isReQuality
            ? t("common.completed")
            : t("quality.inspection.re.quality.complete")}
        </Button>
      </div>
    );
  },
);
