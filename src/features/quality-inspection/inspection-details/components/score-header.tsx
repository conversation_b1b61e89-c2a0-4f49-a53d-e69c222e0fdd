import { CopyableText } from "@/components/common/copyable-text";
import { ScoreText } from "@/components/common/score-text";
import { motion } from "framer-motion";
import React from "react";
import { useTranslation } from "react-i18next";

interface ScoreHeaderProps {
  score: number;
  dialogueId: string;
}

export const ScoreHeader: React.FC<ScoreHeaderProps> = ({
  score,
  dialogueId,
}) => {
  const { t } = useTranslation();

  return (
    <div className="flex items-center justify-between">
      <div className="flex items-baseline">
        <motion.span
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5, ease: "easeOut" }}
          className="text-3xl font-bold leading-none"
        >
          <ScoreText score={score} showTooltip={false} />
        </motion.span>
      </div>

      <div className="flex flex-col items-end gap-0.5">
        <span className="text-xs text-gray-500">
          {t("quality.inspection.dialogue.id")}
        </span>
        <CopyableText
          className="font-mono text-sm text-gray-700"
          text={dialogueId}
        />
      </div>
    </div>
  );
};
