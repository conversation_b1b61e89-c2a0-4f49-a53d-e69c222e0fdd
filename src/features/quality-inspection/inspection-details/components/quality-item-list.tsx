import { Button } from "@/components/ui/button";
import { AnimatePresence } from "framer-motion";
import { Plus } from "lucide-react";
import { observer } from "mobx-react-lite";
import { Instance } from "mobx-state-tree";
import React, { useRef } from "react";
import { useTranslation } from "react-i18next";
import { QualityInspectionDialogueModel } from "../quality-inspection-dialogueModel";
import { EditingQualityItemList } from "./editing-quality-item-list";
import { EmptyState } from "./empty-state";
import { QualityItem } from "./quality-item";

export interface QualityPointType {
  itemName: string;
  itemScore: number;
}

interface QualityItemListProps {
  model: Instance<typeof QualityInspectionDialogueModel>;
  onDelete?: () => void;
  onScoreChange?: (score: number) => void;
}

export const QualityItemList: React.FC<QualityItemListProps> = observer(
  ({ model, onDelete, onScoreChange }) => {
    const { t } = useTranslation();
    const qualityItems = model.detail?.dialogueQualityItemList ?? [];
    const editingItems = model.editingQualityItemList ?? [];
    const containerRef = useRef<HTMLDivElement>(null);

    const handleAddQualityItem = () => {
      model.addEditingQualityItem();
      // 滚动到顶部
      containerRef.current?.scrollTo({
        top: 0,
        behavior: "smooth",
      });
    };

    return (
      <div className="flex flex-col flex-1 gap-2 min-h-0">
        <div className="flex items-center justify-between px-2">
          <h2 className="text-lg font-medium text-gray-700">
            {t("quality.inspection.deduct.items")}
          </h2>

          <div className="flex items-center gap-2">
            <Button
              withAnimation
              variant="default"
              size="sm"
              onClick={handleAddQualityItem}
              data-testid="add-quality-point-btn"
              icon={<Plus />}
            >
              {t("quality.inspection.point.add")}
            </Button>
          </div>
        </div>

        <div
          ref={containerRef}
          className="space-y-2 flex-1 min-h-0 overflow-y-auto overflow-x-hidden px-2"
        >
          <EditingQualityItemList model={model} onScoreChange={onScoreChange} />

          {qualityItems.length === 0 && editingItems.length === 0 ? (
            <EmptyState />
          ) : (
            <div className="space-y-2">
              <AnimatePresence>
                {[...qualityItems].map((item, index) => (
                  <QualityItem
                    key={item.uniqueId}
                    index={index}
                    loading={model.saving}
                    itemName={item.itemName}
                    itemScore={item.itemScore}
                    itemAnalyse={item.itemAnalyse}
                    isExpanded={model.isItemExpanded(item.uniqueId!)}
                    onToggle={() => model.toggleExpand(item.uniqueId!)}
                    onDelete={async () => {
                      await model.deletePoint(index);

                      onDelete?.();
                      onScoreChange?.(model.score);
                    }}
                    onAnalyseChange={async (value) => {
                      await model.setAnalyseAndSubmit(index, value);

                      onScoreChange?.(model.score);
                    }}
                  />
                ))}
              </AnimatePresence>
            </div>
          )}
        </div>
      </div>
    );
  },
);
