import { Separator } from "@/components/ui/separator";
import { useTransientModel } from "@/hooks";
import { Spin } from "antd";
import { observer } from "mobx-react-lite";
import React, { useEffect } from "react";
import { CompleteB<PERSON>on, QualityItemList, ScoreHeader } from "./components";
import { QualityInspectionDialogueModel } from "./quality-inspection-dialogueModel";

interface InspectionDetailsProps {
  dialogueId: string;
  onCompleted?: () => void;
  onDelete?: () => void;
  onScoreChange?: (score: number) => void;
}

export const InspectionDetails: React.FC<InspectionDetailsProps> = observer(
  ({ dialogueId, onCompleted, onDelete, onScoreChange }) => {
    const model = useTransientModel(QualityInspectionDialogueModel);

    // 初始加载数据
    useEffect(() => {
      model.fetchDetail(dialogueId);
    }, [dialogueId, model]);

    if (model.loading) {
      return (
        <div className="flex h-full items-center justify-center">
          <Spin data-testid="inspection-details-loading" size="large" />
        </div>
      );
    }

    return (
      <div
        className="flex h-full flex-col text-gray-800"
        data-testid="inspection-details-container"
      >
        {/* 头部区域 */}
        <ScoreHeader score={model.score} dialogueId={dialogueId} />

        <Separator className="my-3" />
        {/* 主体区域 */}
        <QualityItemList
          model={model}
          onDelete={onDelete}
          onScoreChange={onScoreChange}
        />

        <Separator className="my-3" />

        {/* 底部区域 */}
        <CompleteButton model={model} onCompleted={onCompleted} />
      </div>
    );
  },
);

InspectionDetails.displayName = "InspectionDetails";
