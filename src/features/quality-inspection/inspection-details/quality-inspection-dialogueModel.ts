import { api } from "@/api";
import {
  InspectionDialogueDetail,
  InspectionDialogueDetailModel,
  QualityInspectionItem,
} from "@/features/quality-inspection/models";
import type {
  CompleteQualityInspectionDialogueParams,
  QualityInspectionDialogueListResponse,
  SaveQualityInspectionDialogueParams,
} from "@/types/api/quality-inspection";
import i18next from "i18next";
import { isEmpty } from "lodash-es";
import { cast, flow, isAlive, types } from "mobx-state-tree";
import { v4 as uuidv4 } from "uuid";

// 编辑中的质检点
const EditingQualityItem = types.model("EditingQualityItem", {
  itemId: types.maybe(types.string),
  itemName: types.maybe(types.string),
  itemScore: types.maybe(types.number),
  itemAnalyse: types.maybe(types.string),
});

// 质检对话 store
export const QualityInspectionDialogueModel = types
  .model("QualityInspectionDialogueModel", {
    // 质检对话详情
    detail: types.maybeNull(InspectionDialogueDetailModel),
    // 是否加载中
    loading: types.optional(types.boolean, false),
    // 是否保存中
    saving: types.optional(types.boolean, false),
    // 是否完成质检中
    completingInspection: types.optional(types.boolean, false),
    // 展开的质检点索引
    expandedItems: types.optional(types.array(types.string), []),
    // 编辑中的质检点列表
    editingQualityItemList: types.optional(types.array(EditingQualityItem), []),
  })
  .views((self) => ({
    // 计算总分
    get score(): number {
      try {
        return Math.max(
          100 -
            (self.detail?.dialogueQualityItemList?.reduce(
              (acc, item) => acc + item.itemScore,
              0,
            ) ?? 0),
          0,
        );
      } catch (error) {
        console.error(error);

        return 0;
      }
    },
    // 判断某个质检点是否展开
    isItemExpanded(uniqueId: string): boolean {
      return self.expandedItems.includes(uniqueId);
    },
    // 将质检分析集合转换为字符串
    get analyseString(): string {
      return JSON.stringify(
        Array.from(self.detail?.dialogueQualityItemList ?? [])
          .filter((item) => !isEmpty(item.itemAnalyse))
          .map((item) => ({
            [item.itemName]: item.itemAnalyse,
          })),
      );
    },
  }))
  .actions((self) => {
    const setDetail = (detail: InspectionDialogueDetail | null) => {
      self.detail = detail
        ? cast({
            ...detail,
            dialogueQualityItemList: (
              detail?.dialogueQualityItemList ?? []
            ).map((dialogueQualityItem) => ({
              ...dialogueQualityItem,
              uniqueId: uuidv4(),
            })),
          })
        : null;

      // 如果存在分析，则将分析写入质检点
      if (detail?.dialogueQualityAnalyse) {
        try {
          const analyses = JSON.parse(detail.dialogueQualityAnalyse);

          analyses.forEach((item: Record<string, string>) => {
            Object.entries(item).forEach(([key, value]) => {
              // 找到可以插入的质检点，并写入
              const index =
                self.detail?.dialogueQualityItemList?.findIndex(
                  (item) => item.itemName === key && !item.itemAnalyse,
                ) ?? -1;

              // 如果找到了可以插入的质检点，则写入
              if (index !== -1 && self.detail?.dialogueQualityItemList) {
                self.detail.dialogueQualityItemList[index].itemAnalyse = value;
              }
            });
          });
        } catch (error) {
          console.error(error);
        }
      }
    };

    // 获取质检对话列表
    const fetchDetail = flow(function* (dialogueId: string) {
      self.loading = true;

      try {
        const response: QualityInspectionDialogueListResponse =
          yield api.getQualityInspectionDialogueList({
            dialogueQualityId: dialogueId,
          });

        // 如果已经从 store 中移除，则不进行操作
        if (!isAlive(self)) {
          return null;
        }

        if (response.success && response.data) {
          setDetail(response.data);
        } else {
          setDetail(null);
        }

        return self.detail;
      } catch (error) {
        console.error(error);
        throw error;
      } finally {
        if (isAlive(self)) {
          self.loading = false;
        }
      }
    });

    // 保存质检对话
    const saveInspection = flow(function* (dialogueId: string) {
      if (!self.detail) {
        return;
      }

      self.saving = true;

      try {
        const params: SaveQualityInspectionDialogueParams = {
          dialogueQualityId: dialogueId,
          qualityAnalyse: self.analyseString,
          qualityScore: self.score.toString(),
          reQualityItemList:
            self.detail.dialogueQualityItemList
              ?.map((item) => item.itemId)
              .join(",") ?? "",
        };

        const response = yield api.saveQualityInspectionDialogue(params);

        if (response.success) {
          return true;
        }

        return false;
      } catch (error) {
        console.error(error);

        return false;
      } finally {
        self.saving = false;
      }
    });

    return {
      // 设置质检对话详情
      setDetail,

      // 获取质检对话详情
      fetchDetail,

      // 保存质检对话
      saveInspection,

      // 切换质检点的展开/折叠状态
      toggleExpand(uniqueId: string) {
        const expandedIndex = self.expandedItems.indexOf(uniqueId);

        if (expandedIndex > -1) {
          self.expandedItems.remove(self.expandedItems[expandedIndex]);
        } else {
          self.expandedItems.push(uniqueId);
        }
      },

      // 删除质检点
      deletePoint: flow(function* (index: number) {
        if (!self.detail?.dialogueQualityItemList) {
          throw new Error(
            i18next.t("quality.inspection.error.detail.not.found"),
          );
        }

        const itemToDelete = self.detail.dialogueQualityItemList[index];

        if (!itemToDelete) {
          return;
        }

        const newList = self.detail.dialogueQualityItemList.filter(
          (_, i) => i !== index,
        );
        const deletedItemUniqueId = itemToDelete.uniqueId!;

        self.detail.dialogueQualityItemList.replace(newList);

        // 如果删除的质检点是展开的，则从展开的质检点列表中删除
        if (self.expandedItems.includes(deletedItemUniqueId)) {
          self.expandedItems.replace(
            self.expandedItems.filter((item) => item !== deletedItemUniqueId),
          );
        }

        // 更新到后端
        return yield saveInspection(self.detail.dialogueQualityId);
      }),

      // 设置质检点分析，并提交到后端
      setAnalyseAndSubmit: flow(function* (index: number, value: string) {
        if (!self.detail?.dialogueQualityItemList) {
          throw new Error(
            i18next.t("quality.inspection.error.detail.not.found"),
          );
        }

        self.detail.dialogueQualityItemList[index].itemAnalyse = value;

        return yield saveInspection(self.detail.dialogueQualityId);
      }),

      // 添加编辑中的质检点
      addEditingQualityItem() {
        self.editingQualityItemList.push(cast({}));
      },

      // 删除编辑中的质检点
      deleteEditingQualityItem(index: number) {
        self.editingQualityItemList.remove(self.editingQualityItemList[index]);
      },

      // 编辑中的质检点选择质检点项目
      applyPointToEditingQualityItem: flow(function* (
        index: number,
        pointItem: QualityInspectionItem,
      ) {
        if (!self.detail?.dialogueQualityItemList) {
          throw new Error(
            i18next.t("quality.inspection.error.detail.not.found"),
          );
        }

        // 从编辑中的质检点列表中删除
        self.editingQualityItemList.remove(self.editingQualityItemList[index]);

        // 插到最前面
        self.detail?.dialogueQualityItemList.unshift(
          cast({
            uniqueId: uuidv4(),
            itemId: pointItem.qualityInspectionItemId as string,
            itemName: pointItem.qualityInspectionItemName as string,
            itemScore: Number(pointItem.qualityInspectionItemScore),
          }),
        );

        // 自动展开新加入的质检点
        self.expandedItems.unshift(
          self.detail.dialogueQualityItemList[0].uniqueId!,
        );

        // 提交到后端
        return yield saveInspection(self.detail.dialogueQualityId);
      }),

      // 完成质检
      completeInspection: flow(function* () {
        if (!self.detail) {
          throw new Error(
            i18next.t("quality.inspection.error.detail.not.found"),
          );
        }

        self.completingInspection = true;

        const params: CompleteQualityInspectionDialogueParams = {
          dialogueQualityId: self.detail.dialogueQualityId,
          qualityAnalyse: self.analyseString,
          qualityScore: self.score,
          reQualityItemList: self.detail.dialogueQualityItemList
            ?.map((item) => item.itemId)
            .join(","),
        };

        try {
          const response = yield api.completeQualityInspectionDialogue(params);

          if (response.success) {
            self.detail.isReQuality = 1;

            return true;
          }

          return false;
        } catch (error) {
          console.error(error);

          return false;
        } finally {
          self.completingInspection = false;
        }
      }),
    };
  });
