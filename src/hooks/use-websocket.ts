import { useState, useEffect, useCallback, useRef } from "react";
import SocketWorkerManager from "@/lib/socket-worker-manager";
import { ConnectionType } from "@/workers/socket.worker";
import * as Sentry from "@sentry/react";
import { toast } from "sonner";

export { ConnectionType } from "@/workers/socket.worker";

export interface WebSocketConfig {
  socketUrl: string;
  connectionType: ConnectionType;
  reconnectAttempts: number;
  reconnectInterval: number;
  timeout?: number;
  shopId?: string;
  authorization?: string;
}

export interface WebSocketEvents {
  [event: string]: (...args: any[]) => void;
}

const useWebSocket = <T extends WebSocketEvents>(
  connectionType: ConnectionType,
) => {
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const workerManagerRef = useRef<SocketWorkerManager | null>(null);

  useEffect(() => {
    workerManagerRef.current = SocketWorkerManager.getInstance(connectionType);
    const workerManager = workerManagerRef.current;

    const handleStatusChange = (
      isConnected: boolean,
      isConnecting: boolean,
    ) => {
      setIsConnected(isConnected);
      setIsConnecting(isConnecting);
    };

    workerManager.addStatusChangeHandler(handleStatusChange);

    return () => {
      workerManager.removeStatusChangeHandler(handleStatusChange);
    };
  }, [connectionType]);

  const connect = useCallback(
    (config: WebSocketConfig) => {
      if (isConnected || isConnecting) {
        return;
      }
      console.log(config.shopId, workerManagerRef.current);
      if (!config.shopId) return;
      const workerManager = workerManagerRef.current;
      if (!workerManager) return;

      setIsConnecting(true);

      workerManager.setConfig(config);

      workerManager.addErrorHandler((message) => {
        toast.error("会话消息服务连接错误", {
          description: message,
        });

        Sentry.captureEvent({
          message,
          level: "error",
          extra: {
            source: "use-websocket-hook",
          },
        });
        setIsConnecting(false);
      });

      workerManager.connect();
    },
    [isConnected, isConnecting],
  );

  const disconnect = useCallback(() => {
    workerManagerRef.current?.disconnect();
    workerManagerRef.current?.clearEventHandlers();
    setIsConnected(false);
  }, []);

  const sendMessage = useCallback(
    (event: keyof T, data: any, callback?: (response: any) => void) => {
      if (isConnected && workerManagerRef.current) {
        workerManagerRef.current.sendMessage(event as string, data);

        if (callback) {
          const responseEvent = `${event as string}`;
          const tempHandler = (responseData: any) => {
            callback(responseData);
            workerManagerRef.current?.removeEventHandler(
              responseEvent,
              tempHandler,
            );
          };
          workerManagerRef.current.addEventListener(responseEvent, tempHandler);
        }
      }
    },
    [isConnected],
  );

  const addEventListener = useCallback(
    (event: keyof T, handler: (data: any) => void) => {
      workerManagerRef.current?.addEventListener(event as string, handler);
    },
    [],
  );

  const removeEventListener = useCallback(
    (event: keyof T, handler: (data: any) => void) => {
      workerManagerRef.current?.removeEventHandler(event as string, handler);
    },
    [],
  );

  const destroy = useCallback(() => {
    workerManagerRef.current?.terminateWorker();
    setIsConnected(false);
    setIsConnecting(false);
  }, []);

  return {
    isConnected,
    isConnecting,
    connect,
    disconnect,
    sendMessage,
    addEventListener,
    removeEventListener,
    destroy,
  };
};

export default useWebSocket;
