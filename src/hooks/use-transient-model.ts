import { IAnyModelType, Instance, destroy } from "mobx-state-tree";
import { useRef, useEffect } from "react";

/**
 * 在组件生命周期内创建和管理 MST 模型实例的钩子函数
 *
 * @param ModelType MST 模型类型
 * @param initialProps 模型初始属性
 * @returns 模型实例
 */
export function useTransientModel<T extends IAnyModelType>(
  ModelType: T,
  initialProps?: any,
) {
  const modelRef = useRef<Instance<T>>(ModelType.create(initialProps));

  useEffect(() => {
    const model = modelRef.current;

    return () => {
      // 添加检查，确保模型还活着时才销毁
      if (model && !model.isDestroyed) {
        destroy(model);
      }
    };
  }, []);

  return modelRef.current;
}
