import { loadableUserAtom } from "@/atoms/auth";
import { PermissionCodeValueType } from "@/constants/permission";
import { UserPermission } from "@/types/api/user";
import { getDefaultStore } from "jotai";

/**
 * 、用于处理用户权限
 * @returns 包含hasPermission方法的对象
 */
export const usePermission = () => {
  /**
   * 检查用户是否拥有指定权限
   * @param permission 权限代码
   * @returns 如果用户拥有权限返回true，否则返回false
   */
  const hasPermission = (permission: PermissionCodeValueType): boolean => {
    const store = getDefaultStore();
    const userLoadable = store.get(loadableUserAtom);
    if (
      userLoadable.state !== "hasData" ||
      !userLoadable.data?.data?.permissionList
    ) {
      return false;
    }

    const permissionList: UserPermission[] =
      userLoadable.data.data.permissionList;
    return permissionList.some((p) => p.permissionCode === permission);
  };

  return {
    hasPermission,
  };
};
