import { useLocalStorageState } from "ahooks";
import { useCallback, useLayoutEffect, useRef, useState } from "react";
import { ImperativePanelHandle } from "react-resizable-panels";

export const usePanelControl = ({
  panelGroupId,
  handleId,
  defaultMinSize = 400,
  defaultSize = 25,
}: {
  panelGroupId: string;
  handleId: string;
  defaultSize?: number;
  defaultMinSize?: number;
}) => {
  const [minSize, setMinSize] = useState(defaultSize);
  const [size, setSize] = useLocalStorageState(panelGroupId, {
    defaultValue: defaultSize,
  });
  const [isExpanded, setIsExpanded] = useState(false);
  const panelRef = useRef<ImperativePanelHandle>(null);

  const onResize = useCallback(
    (newSize: number) => {
      if (newSize === 0) {
        setIsExpanded(false);
      } else {
        setSize(newSize);
      }
    },
    [setSize],
  );

  // 展开面板
  const expandPanel = useCallback(() => {
    if (panelRef.current) {
      setIsExpanded(true);
      // 使用setTimeout确保在DOM更新后执行
      setTimeout(() => {
        if (panelRef.current) {
          panelRef.current.resize(size !== undefined && size > 0 ? size : 30);
        }
      }, 0);
    }
  }, [size]);

  // 收起面板
  const collapsePanel = useCallback(() => {
    if (panelRef.current) {
      panelRef.current.collapse();
      setIsExpanded(false);
    }
  }, []);

  useLayoutEffect(() => {
    const panelGroup = document.querySelector<HTMLDivElement>(
      `[data-panel-group-id="${panelGroupId}"]`,
    );
    const resizeHandles = panelGroup?.querySelectorAll<HTMLDivElement>(
      `[data-panel-resize-handle-id="${handleId}"]`,
    );
    const observer = new ResizeObserver(() => {
      if (!panelGroup || !resizeHandles) {
        return;
      }

      let width = panelGroup.offsetWidth;

      resizeHandles.forEach((resizeHandle) => {
        width -= resizeHandle.offsetWidth;
      });

      setMinSize((defaultMinSize / width) * 100);
    });

    if (!panelGroup || !resizeHandles) {
      return;
    }

    observer.observe(panelGroup);
    resizeHandles.forEach((resizeHandle) => {
      observer.observe(resizeHandle);
    });

    return () => {
      observer.unobserve(panelGroup);
      resizeHandles.forEach((resizeHandle) => {
        observer.unobserve(resizeHandle);
      });
      observer.disconnect();
    };
  }, [defaultMinSize, handleId, panelGroupId]);

  return {
    minSize,
    size,
    panelRef,
    isExpanded,
    expandPanel,
    collapsePanel,
    onResize,
  };
};
