import { QualityInspectionItem } from "@/features/quality-inspection/models";
import { useCallback, useEffect, useState } from "react";
import { api } from "@/api";

interface UseInspectionItemsProps {
  initialSearchValue?: string;
}

/**
 * 用于处理质检项搜索的自定义 Hook
 *
 * @param initialSearchValue 初始搜索关键字
 * @returns 质检项数据和相关方法
 */
export function useInspectionItems({
  initialSearchValue,
}: UseInspectionItemsProps = {}) {
  const [inspectionItems, setInspectionItems] = useState<
    QualityInspectionItem[]
  >([]);
  const [searchValue, setSearchValue] = useState<string | undefined>(
    initialSearchValue,
  );
  const [loading, setLoading] = useState(false);

  // 使用 API 搜索质检项
  const searchOptions = useCallback(async (value?: string) => {
    try {
      setLoading(true);
      const response = await api.getQualityInspectionItemList({
        name: value,
      });

      if (response.success && response.data) {
        setInspectionItems(response.data);
      } else {
        setInspectionItems([]);
      }
    } catch (error) {
      console.error("Failed to search inspection items:", error);
      setInspectionItems([]);
    } finally {
      setLoading(false);
    }
  }, []);

  // 当搜索值变化时，执行搜索
  useEffect(() => {
    searchOptions(searchValue);
  }, [searchValue, searchOptions]);

  return {
    inspectionItems,
    searchValue,
    setSearchValue,
    loading,
    searchOptions,
  };
}
