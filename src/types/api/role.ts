import { BaseResponse, BasePagerResponse } from "./base";
import { Permission, PermissionType } from "./permission";
import { Brand } from "./brand";
import { Shop } from "./shop";
import { Permissions } from "./permission";

export interface PermissionRole {
  id: number;
  name: string;
  roleCode: string;
}

export type RoleWithPermission = {
  roleId: number;
  roleName: string;
  roleCode: string;
  menus: Permissions[];
};

export type RolePermissionListResponse = BaseResponse<{
  roles: RoleWithPermission[];
}>;

export enum RoleType {
  SUPER_ADMIN = "admin",
  AI_ENGINEER = "ai_engineer",
  AI_TRAINER = "ai_trainer",
  BRAND_ADMIN = "brand_admin",
  BRAND_OPERATOR = "brand_operator",
  BRAND_CUSTOMER_SERVICE = "brand_customer_service",
}

export type UserPermission = Pick<Permission, "permissionCode"> & {
  id: string;
  name: string;
  type: PermissionType.BUTTON | PermissionType.INTERFACE;
};

export type Role = {
  createdAt: string;
  updatedAt: string;
  id: string;
  name: string;
  roleCode: string;
};

export interface RoleUser {
  userId: string;
  username: string;
  creator: string;
  createdAt: string;
  updatedAt: string;
  roleList: Role[];
  permissionList: UserPermission[];
  brandList: Brand[];
  shopList: Shop[];
}

/**
 * 角色用户列表响应
 */
export type RoleUserListResponse = BasePagerResponse<RoleUser>;

/**
 * 角色用户列表请求参数
 */
export interface RoleUserListParams {
  pageNum?: number;
  pageSize?: number;
}
// 角色列表接口响应
export type RoleListResponse = BaseResponse<PermissionRole[]>;

/**
 * 删除账号
 */
export interface DeleteUserParams {
  userId?: string;
}
export type DeleteUserResponse = BaseResponse<void>;

/**
 * 设置密码
 */
export interface SetPasswordParams {
  userId: RoleUser["userId"];
  password: string;
}

export interface SetOwnPasswordParams {
  password: string;
}
export type SetPasswordResponse = BaseResponse<void>;

/**
 * 更新账号
 */
export interface UpdateUserParams
  extends Pick<RoleUser, "username" | "userId"> {
  roleId?: string;
  shopIds?: string[];
}
export type UpdateUserResponse = BaseResponse<void>;

/**
 * 添加账号
 */
export interface AddUserParams extends Omit<UpdateUserParams, "userId"> {
  password: string;
}
export type AddUserResponse = BaseResponse<void>;
export interface userList {
  id: string;
  username: string;
}
export interface RoleUserService {
  roleId: string;
  roleName: string;
  roleCode: string;
  userList: userList[];
}
