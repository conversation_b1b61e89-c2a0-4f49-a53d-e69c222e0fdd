import { BasePagerResponse, BaseResponse } from "./base";
export interface getUserBandParams {
  shopId?: string;
}
export interface getAgentConfigParams {
  /**
   * 页码（可选）
   */
  pageNum?: number;
  /**
   * 每页显示数量（可选）
   */
  pageSize?: number;
  /**
   * 排序字段（可选）
   */
  sortField?: string;
  /**
   * 排序方向（可选，如 'asc' 或 'desc'）
   */
  direction?: string;
  /**
   * 店铺ID（必填）
   */
  shopId: string;
}

export interface AgentConfig {
  /**
   * 智能体配置id*/
  aiAgentConfigId: string;
  /**
   * 品牌 ID
   */
  brandId: string;
  /**
   * 店铺 ID
   */
  shopId: string;
  /**
   * 接待客服id
   */
  customerId: string;
  /**
   * 接待客服name
   */
  customerName: string;
  /**
   * 接待模式：SILENCE-静默, ASSISTANT-辅助，OFFICIAL-正式
   */
  receptionMode: string;
  /**
   * 智能体名称
   */
  name: string;
  /**
   * 描述
   */
  description: string;
  /**
   * 配置内容 JSON
   */
  configContent: string;
  /**
   * 关联牧言账号
   */
  userNames: string[];
  /**
   * 店铺配置id
   */
  configId: string;
  /**
   * 店铺配置名称
   */
  configName: string;
  /**
   * 提示词版本配置id
   */
  promptVersionId: string;
  /**
   * 提示词版本配置名称
   */
  promptVersionName: string;
  /**
   * 创建时间
   */
  createdAt: string;
}
export type AgentConfigTableData = Partial<AgentConfig>;

export interface PromptVersion {
  /**
   * 版本id
   */
  promptVersionId: string;
  /**
   * 版本名称
   */
  versionName: string;
  /**
   * 版本号
   */
  version: string;
}
// 接待模式
export enum ReceptionModeType {
  SILENCE = "静默",
  ASSISTANT = "ai辅助",
  OFFICIAL = "正式接待",
}

export type AgentListResponse = BasePagerResponse<AgentConfig>;

export interface UpdateAgentParams {
  aiAgentConfigId?: string;
  name: string;
  shopId: string;
  customerId?: string;
  receptionMode: string;
  description: string;
  configContent?: string;
  userIds?: string[];
}

export interface BandListType {
  id: string;
  username: string;
}
export interface CustomerListType {
  id: string;
  customerServiceName: string;
  noUse?: boolean;
}
export interface delAiAgentConfig {
  aiAgentConfigId: string;
}
export type BandListResponse = BaseResponse<BandListType>;

export interface ChooseVersionParams {
  aiAgentConfigId: string;
  promptVersionId: string;
  configId: string;
}
// 获取版本列表params
export interface PromptVersionParams {
  statusList?: string[];
  version?: string;
  promptVersionId?: string;
}
// 版本变更信息接口
interface VersionChange {
  createdAt: string;
  updatedAt: string;
  promptVersionId: number;
  creator: string;
  creatorName: string;
  content: string;
}

// 评估任务接口
interface EvaluationTask {
  taskId: number;
  taskName: string;
  testSetId: number;
  testSetName: string;
  evaluationAt: string;
}
export interface PromptVersionResponse {
  // 版本id
  promptVersionId: string;
  versionName: string;
  version: string;
  status: string;
  remark: string;
  creator: string;
  creatorName: string;
  onlineDate: string;
  offlineDate: string;
  submitDate: string;
  basePromptVersionId: string;
  baseVersion: string;
  versionChange: VersionChange;
  evaluationTasks: EvaluationTask[];
}

export interface CodeEditParams extends delAiAgentConfig {
  configContent: string;
}
