import { BaseResponse } from "./base";

export interface AddOrUpdatePromptVersionParams {
  /*能力版本id，新增时为空，修改时必传 */
  promptVersionId?: string;

  /*版本名称，新增时必填，修改时存在覆盖，空不处理 */
  versionName?: string;

  /*版本状态：ONLINE-上线，OFFLINE-下线，SUBMIT-已提交，新增时必填，修改时存在覆盖，空不处理 */
  status?: string;

  /*版本备注，新增时必填，修改时存在覆盖，空不处理 */
  remark?: string;

  /*操作用户id，新增时必填，修改时存在覆盖，空不处理 */
  creator?: string;

  /*基准版本id，新增时必填，修改时存在覆盖，空不处理 */
  basePromptVersionId?: string;

  /*基准版本号，新增时必填，修改时存在覆盖，空不处理 */
  baseVersion?: string;

  /*创建时间，新增时必填，修改时存在覆盖，空不处理 */
  createdAt?: string;

  /*变跟内容，存在时不为空 */
  changeContent?: string;
}

export type AddOrUpdatePromptVersionResponse = BaseResponse<string>;

export interface DeletePromptVersionParams {
  promptVersionId: string;
}

export type DeletePromptVersionResponse = BaseResponse<void>;

export enum PromptVersionStatus {
  ONLINE = "ONLINE",
  OFFLINE = "OFFLINE",
  SUBMIT = "SUBMIT",
}

export interface GetPromptVersionParams {
  /*版本号 */
  version?: string;

  /*版本id */
  promptVersionId?: string;
}
export interface PromptVersionEvaluationTask {
  /*任务id */
  taskId: number;

  /*任务名称 */
  taskName: string;

  /*测试集ID */
  testSetId: number;

  /*测试集名称 */
  testSetName: string;

  /*测评时间 */
  evaluationAt: string;
}

export interface PromptVersion {
  /*版本id */
  promptVersionId: string;

  /*版本名称 */
  versionName: string;

  /*版本号 */
  version: string;

  /*版本状态：ONLINE-上线，OFFLINE-下线，SUBMIT-已提交 */
  status: PromptVersionStatus;

  /*版本备注 */
  remark: string;

  /*上线时间 */
  onlineDate?: string;

  /*下线时间 */
  offlineDate?: string;

  /*提交时间 */
  submitDate?: string;

  /*基准版本id */
  basePromptVersionId?: string;

  /*基准版本号 */
  baseVersion?: string;

  versionChange?: {
    /*版本id */
    promptVersionId: string;

    /*操作用户id */
    creator: string;

    /*操作用户名称 */
    creatorName: string;

    /*变更内容 JSON */
    content: string;
  } | null;

  /*测评任务 */
  evaluationTasks?: PromptVersionEvaluationTask[] | null;
}

export type GetPromptVersionResponse = BaseResponse<PromptVersion>;

export interface PublishPromptVersionParams {
  promptVersionId: string;
}

export type PublishPromptVersionResponse = BaseResponse<void>;

export interface SelectPromptVersionListParams {
  /*版本号 */
  version?: string;

  /*版本id */
  promptVersionId?: string;
}

export type SelectPromptVersionListResponse = BaseResponse<PromptVersion[]>;
