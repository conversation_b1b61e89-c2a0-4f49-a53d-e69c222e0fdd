import { BaseResponse } from "./base";
import { Level } from "./prompt";

export interface AddOrUpdateSharedVariableParams {
  /* 共享变量id，新增时为空，修改时必传 */
  sharedVariableId?: string;

  /* 当前生效版本ID，必填 */
  promptVersionId?: string;

  /* 变量名称，新增时必填，修改时存在覆盖，空不处理 */
  name?: string;

  /* 等级:PRODUCT-产品级，CATEGORY-类目级，STORE-店铺级 */
  level?: Level;

  /* 影响范围详情JSON，"["床","床垫"]"(店铺级时为店铺id) */
  scopeDetail?: string;

  /* 提示词变量名称 */
  promptName?: string;

  /* 定义 */
  definition?: string;

  /* 变量内容 */
  content?: string;
}

export enum SharedVariableStatus {
  USED = 1,
  UN_USED = 2,
}

export type AddOrUpdateSharedVariableResponse = BaseResponse<string>;

export interface DeleteSharedVariableParams {
  sharedVariableId: string;
}

export type DeleteSharedVariableResponse = BaseResponse<void>;

export interface SelectSharedVariableListParams {
  promptVersionId: string;
}

export interface SharedVariable {
  /*共享变量id，新增时为空，修改时必传 */
  sharedVariableId: string;

  /*当前生效版本ID，必填 */
  promptVersionId: string;

  /*变量名称，新增时必填，修改时存在覆盖，空不处理 */
  name: string;

  /*等级:PRODUCT-产品级，CATEGORY-类目级，SHOP-店铺级，新增时必填，修改时存在覆盖，空不处理 */
  level: Level;

  /*影响范围详情JSON，"["床","床垫"]"(店铺级时为店铺id)，修改时存在覆盖，空不处理 */
  scopeDetail?: string;

  /*提示词变量名称，新增时必填，修改时存在覆盖，空不处理 */
  promptName: string;

  /*定义，新增时必填，修改时存在覆盖，空不处理 */
  definition: string;

  /*变量内容 */
  content: string;

  /*状态：1-已使用，2-未使用 */
  status: SharedVariableStatus;
}

export type SelectSharedVariableListResponse = BaseResponse<SharedVariable[]>;
