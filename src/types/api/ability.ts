import { type AbilityNodeType } from "../../features/prompt/management/schemas/node";
import { BaseResponse } from "./base";

export interface AddOrUpdateAbilityParams {
  /*能力id，新增时为空，修改时必传 */
  abilityId?: string;

  /*当前版本id，必传 */
  promptVersionId?: string;

  /*能力名称，新增时必填，修改时存在覆盖，空不处理 */
  name?: string;

  /*能力内容 JSON，新增时必填，修改时存在覆盖，空不处理 */
  content?: string;
}

/**
 * 类型安全的能力参数接口，content 字段使用结构化类型
 */
export interface TypedAddOrUpdateAbilityParams
  extends Omit<AddOrUpdateAbilityParams, "content"> {
  /*能力内容结构化对象 */
  content?: AbilityNodeType;
}

export type AddOrUpdateAbilityResponse = BaseResponse<string>;

export interface DeleteAbilityParams {
  abilityId: string;
}

export type DeleteAbilityResponse = BaseResponse<void>;

export interface GetAbilityParams {
  abilityId: string;
  promptVersionId: string;
}

export interface Ability {
  /*能力id */
  abilityId: string;

  /*当前生效版本ID */
  promptVersionId: string;

  /*能力名称 */
  name: string;

  /*能力内容 JSON */
  content: string;
}

/**
 * 类型安全的能力接口，content 字段使用结构化类型
 */
export interface TypedAbility extends Omit<Ability, "content"> {
  /*能力内容结构化对象 */
  content?: AbilityNodeType;
}

export type GetAbilityResponse = BaseResponse<Ability>;

export type GetTypedAbilityResponse = BaseResponse<TypedAbility>;

export interface SelectAbilityListParams {
  /*能力id */
  abilityId?: string;

  /*版本id */
  promptVersionId?: string;
}

export type SelectAbilityListResponse = BaseResponse<Ability[]>;

export interface SelectAbilityNameListParams {
  promptVersionId: string;
}

export type SelectAbilityNameListResponse = BaseResponse<string[]>;
