import { BaseResponse } from "./base";
import { Level, VariableType } from "./prompt";

export interface ModuleVariable {
  /**
   * 变量 id
   */
  variableId: string;

  /**
   * 变量类型
   */
  type: VariableType;
}

export interface AddOrUpdateModuleParams {
  /*模块id，新增时为空，修改时必传 */
  moduleId?: string;

  /*当前版本id，必传 */
  promptVersionId?: string;

  /*模块名称，新增时必填，修改时存在覆盖，空不处理 */
  name?: string;

  /*等级:PRODUCT-产品级，CATEGORY-类目级，SHOP-店铺级，新增时必填，修改时存在覆盖，空不处理 */
  level?: Level;

  /*提示词 JSON，修改时存在覆盖，空不处理 */
  prompt?: string;

  /*影响范围详情 JSON */
  scopeDetail?: string;

  /*模块标签id集合 JSON，修改时存在覆盖，空不处理，空字符串为置空 */
  moduleLabelIds?: string;
}

export type AddOrUpdateModuleResponse = BaseResponse<string>;

export interface AddOrUpdateModuleLabelParams {
  /*模块标签id，新增时为空，修改时必传 */
  moduleLabelId?: string;

  /*当前版本id，必传 */
  promptVersionId?: string;

  /*标签名称，新增时必填，修改时存在覆盖，空不处理 */
  name?: string;
}

export type AddOrUpdateModuleLabelResponse = BaseResponse<string>;

export interface DeleteModuleParams {
  moduleId: string;
}

export type DeleteModuleResponse = BaseResponse<void>;

export interface DeleteModuleLabelParams {
  moduleLabelId: string;
}

export type DeleteModuleLabelResponse = BaseResponse<void>;

export interface SelectModuleLabelListParams {
  promptVersionId: string;
}

export interface ModuleLabel {
  /*模块标签id */
  moduleLabelId: string;

  /*当前生效版本ID */
  promptVersionId: string;

  /*标签名称 */
  name: string;
}

export type SelectModuleLabelListResponse = BaseResponse<ModuleLabel[]>;

export interface SelectModuleListParams {
  /*版本id */
  promptVersionId?: string;

  /*模块标签id，没有时查全部 */
  moduleLabelId?: string;
}

export enum ModuleStatus {
  USED = 1,
  UN_USED = 2,
}

export interface Module {
  /*模块id */
  moduleId: string;

  /*当前生效版本ID */
  promptVersionId: string;

  /*模块标签id */
  moduleLabelIds?: string | null;

  /*模块名称 */
  name: string;

  /*等级:PRODUCT-产品级，CATEGORY-类目级，SHOP-店铺级 */
  level: Level;

  /*影响范围详情 JSON */
  scopeDetail?: string;

  /*提示词 JSON */
  prompt: string;

  /*状态：1-已使用，2-未使用 */
  status: ModuleStatus;
}

export type SelectModuleListResponse = BaseResponse<Module[]>;

export interface SelectModuleLabelNameListParams {
  promptVersionId: string;
}

export type SelectModuleLabelNameListResponse = BaseResponse<string[]>;

export interface SelectModuleNameListParams {
  promptVersionId: string;
}

export type SelectModuleNameListResponse = BaseResponse<string[]>;
