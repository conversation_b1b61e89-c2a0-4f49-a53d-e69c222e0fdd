// 等级类型枚举
export enum Level {
  // 产品级
  Product = "PRODUCT",
  // 类目级
  Category = "CATEGORY",
  // 店铺级
  Shop = "SHOP",
}

// 变量类型枚举
export enum VariableType {
  System = "SYSTEM",
  Shop = "SHOP",
}

// 能力项类型枚举
export enum AbilityItemType {
  Role = "role",
  Background = "background",
  Goals = "goals",
  Profile = "profile",
  Skills = "skills",
  Workflow = "workflow",
  Examples = "examples",
  OutputFormat = "outputFormat",
}
