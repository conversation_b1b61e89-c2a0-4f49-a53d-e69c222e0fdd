import { BasePagerResponse, BaseResponse } from "./base";

export interface KnowledgeItem {
  /*id */
  qaKnowledgeId: string;

  /*店铺id */
  shopId: string;

  /*问题 */
  question: string;

  /*答案 */
  answer: string;

  /*类目列表 */
  categoryList?: string[];

  /*图片列表 */
  imageList?: string[];
}
/**
 * 获取知识库
 */

export interface getKnowledgeListParams {
  pageNum?: number;
  pageSize?: number;
  shopId?: string;
  /*查询关键字 */
  keyword?: string;

  /*类目 */
  category?: string;
}

export type getKnowledgeResponse = BasePagerResponse<KnowledgeItem>;

/**
 * 复制知识库
 */
export interface KnowledgeCopyParams {
  shopId: string;
  qaKnowledgeId: string;
}
export type KnowledgeCopyResponse = BaseResponse<void>;
/**
 * 删除知识库
 */
export interface DeleteKnowledgeParams extends KnowledgeCopyParams {}

export type DeleteKnowledgeResponse = BaseResponse<void>;

/**
 * 更新知识库
 */
export interface KnowledgeUpdateParams extends KnowledgeItem {}
export type KnowledgeUpdateResponse = BaseResponse<void>;

/**
 * 新增知识库
 */
export interface KnowledgeAddParams
  extends Omit<KnowledgeItem, "qaKnowledgeId"> {}

export type KnowledgeAddResponse = BaseResponse<void>;

/**
 * 下载知识库
 */
export type KnowledgeDownload = Pick<KnowledgeItem, "shopId">;

export type KnowledgeDownloadResponse = BaseResponse<{ url: string }>;

export type KnowledgeImportParams = {
  shopId: string;
  url: string;
};
export type KnowledgeImportResponse = BaseResponse<void>;

export interface GetConversationQuestionsParams {
  shopId: string;
}

export type GetConversationQuestionsResponse = BaseResponse<{
  question: string;
  answer: string;
}>;
