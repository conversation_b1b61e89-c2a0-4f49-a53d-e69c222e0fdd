import { BaseResponse } from "./base";

export enum PermissionType {
  /**
   * 按钮
   */
  BUTTON = 1,
  /**
   * 接口
   */
  INTERFACE = 2,
  /**
   * 菜单
   */
  MENU = 3,
}

/**
 * 权限
 */
export interface Permission {
  permissionId: number | string;
  permissionName: string;
  permissionCode: string;
  permissionType: number | string;
  permissionUrl?: string;
}

export interface Permissions {
  menuId: string;
  menuName: string;
  menuCode: string;
  type: number;
  menuUrl: string;
  subMenus: Permissions[] | null;
  permissions: Permission[] | null;
}

export type MenuPermissionResponse = Permissions[];

/**
 * 权限菜单
 */
export interface PermissionMenu {
  menuId: string;
  menuName: string;
  menuCode: string;
  type: PermissionType.MENU;
  menuUrl: string;
  subMenus?: PermissionMenu[];
  permissions?: Permission[];
}

/**
 * 权限列表响应
 */
export type PermissionListResponse = BaseResponse<PermissionMenu[]>;

/**
 * 添加权限参数
 */
export type AddPermissionParams = Omit<
  Permission,
  "permissionId" | "permissionType"
> & {
  permissionId?: string;
  permissionType: PermissionType;
  parentId?: string;
};

/**
 * 添加权限响应
 */
export type AddPermissionResponse = BaseResponse<void>;
