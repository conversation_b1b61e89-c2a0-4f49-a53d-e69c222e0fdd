export interface BrandSaveParams {
  brandId?: string;
  brandName: string;
  brandDescription: string;
}

export interface Brand {
  id: string;
  name: string;
  description: string;
}
export interface CustomerServiceDetailDTO {
  id?: string;
  customerServiceName: string;
  createdAt?: string;
  updatedAt?: string;
  shopId?: string;
  brandId?: string;
}
export interface ShopChildren {
  /**
   * 店铺id
   */
  shopId?: string;
  /**
   * 品牌id
   */
  brandId?: string;
  /**
   * 平台id
   */
  platformId?: string;
  /**
   * 店铺名称
   */
  shopName?: string;
  brandName: string;
  /**
   * 平台
   */
  platformName?: string;
  /**
   * 店铺描述
   */
  shopDescription?: string;
  /**
   * 编辑时间
   */
  shopUpdateTime?: string;
}
export interface ShopDetailDTO extends Omit<ShopChildren, "shopUpdateTime"> {
  customerServiceList: CustomerServiceDetailDTO[];
}

export interface BrandData {
  /**
   * 品牌id
   */
  brandId?: string;
  /**
   * 品牌名称
   */
  brandName?: string;
  /**
   * 平台id
   */
  platformId?: string;
  /**
   * 平台
   */
  platformName?: string;
  /**
   * 描述
   */
  brandDescription?: string;
  /**
   * 编辑时间
   */
  brandUpdateTime?: string;
  /**
   * 编辑人
   */
  shopList: ShopChildren[];
}
export type BrandDataTableData = Partial<BrandData>;

export interface getBrandListParams {
  shopId: string;
  pageNum?: number;
  pageSize?: number;
}
export type brandTableData = Partial<BrandData> & Partial<ShopChildren>;

export interface PlatformData {
  /**
   * 创建时间
   */
  createdAt: string;
  /**
   * 更新时间
   */
  updatedAt: string;
  /**
   * 平台id
   */
  id: string;
  /**
   * 平台名称
   */
  name: string;
  /**
   * 描述
   */
  description: string;
  /**
   * 状态
   */
  status: number;
  code: string;
}

export interface EditAddShopParams {
  shopId?: string;
  shopName: string;
  shopDescription: string;
  userIds?: string[];
  brandId: string;
  platformId: string;
  customerServices: CustomerServiceDetailDTO[];
}
