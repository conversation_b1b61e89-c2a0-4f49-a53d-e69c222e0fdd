import { BaseResponse } from "./base";
import { Brand } from "./brand";
import { Shop } from "./shop";

export interface LoginParams {
  username: string;
  password: string;
}

export interface User {
  userId: string;
  username: string;
}

export interface LoginToken {
  accessToken: string;
  refreshToken: string;
}

export type LoginResponse = BaseResponse<{
  user: User;
  token: LoginToken;
}>;

export type LogoutResponse = BaseResponse<void>;

export interface UserRole {
  id: string;
  name: string;
  roleCode: string;
}

export interface UserPermission {
  id: number;
  name: string;
  permissionCode: string;
  type: string;
  url?: string;
}

export type UserDetail = {
  roleList: UserRole[];
  permissionList: UserPermission[];
  brandList: Brand[];
  shopList: Shop[];
} & User;

export type UserDetailResponse = BaseResponse<UserDetail>;
