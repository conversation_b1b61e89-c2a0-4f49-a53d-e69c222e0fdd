import { BaseResponse } from "./base";
import { Level, VariableType } from "./prompt";

export interface AddOrUpdateVariableParams {
  /*变量id，新增时为空，修改时必传 */
  variableId?: string;

  /*变量名称，新增时必填，修改时存在覆盖，空不处理 */
  name?: string;

  /*等级:PRODUCT-产品级，CATEGORY-类目级，SHOP-店铺级，新增时必填，修改时存在覆盖，空不处理 */
  level?: Level;

  /*影响范围详情JSON，"["床","床垫"]"(店铺级时为店铺id)，修改时存在覆盖，空不处理 */
  scopeDetail?: string;

  /*提示词变量名称，新增时必填，修改时存在覆盖，空不处理 */
  promptName?: string;

  /*定义，新增时必填，修改时存在覆盖，空不处理 */
  definition?: string;

  /*变量示例，新增时必填，修改时存在覆盖，空不处理 */
  example?: string;

  /*变量类型:SHOP-店铺变量， SYSTEM-系统变量，新增时必填，修改时存在覆盖，空不处理 */
  type?: VariableType;
}

export type AddOrUpdateVariableResponse = BaseResponse<string>;

export interface DeleteVariableParams {
  variableId: string;
}

export type DeleteVariableResponse = BaseResponse<void>;

export interface SelectVariableListParams {
  type?: VariableType;
}

export enum VariableStatus {
  USED = 1,
  UN_USED = 2,
}

export interface Variable {
  /*变量id */
  variableId: string;

  /*变量名称 */
  name: string;

  /*等级:PRODUCT-产品级，CATEGORY-类目级，SHOP-店铺级 */
  level: Level;

  /*影响范围详情 JSON */
  scopeDetail?: string;

  /*提示词变量名称 */
  promptName: string;

  /*定义 */
  definition?: string;

  /*变量示例 */
  example?: string;

  /*变量类型:SHOP-店铺变量， SYSTEM-系统变量 */
  type: VariableType;

  /*状态：1-已使用，2-未使用 */
  status?: VariableStatus;

  /*引用此变量能力名称集合 */
  abilityNames?: Record<string, unknown>[];
}

export type SelectVariableListResponse = BaseResponse<Variable[]>;
