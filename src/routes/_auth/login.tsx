import { hasLogin } from "@/atoms/auth";
import { LoginCard } from "@/features/login";
import { redirect } from "@tanstack/react-router";
import z from "zod";

export const LoginSearchSchema = z.object({
  redirect: z.string().optional(),
});

export const Route = createFileRoute({
  component: Login,
  validateSearch: LoginSearchSchema,
  beforeLoad: () => {
    if (hasLogin()) {
      throw redirect({
        to: "/",
      });
    }
  },
});

function Login() {
  return <LoginCard />;
}
