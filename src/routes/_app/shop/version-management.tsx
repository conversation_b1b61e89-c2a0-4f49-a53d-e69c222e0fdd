import { Can } from "@/components/common/can";
import { Page } from "@/components/common/page";
import { PERMISSION_CODES } from "@/constants/permission";
import { ConfigVersionManagement } from "@/features/version-management";
import { beforeLoadPermissionValid } from "@/utils/permission";

export const Route = createFileRoute({
  component: RouteComponent,
  loader: () => ({
    crumb: true,
  }),
  beforeLoad({ context, location }) {
    beforeLoadPermissionValid(context, location);
  },
});

function RouteComponent() {
  return (
    <Page>
      <Can permissionCode={PERMISSION_CODES.SHOP_CONFIG_VERSION_VIEW}>
        <ConfigVersionManagement />
      </Can>
    </Page>
  );
}
