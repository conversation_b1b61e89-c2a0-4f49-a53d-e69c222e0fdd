import { Can } from "@/components/common/can";
import { Page } from "@/components/common/page";
import { GoodLibrary } from "@/features/good-library";
import { PERMISSION_CODES } from "@/constants/permission";

export const Route = createFileRoute({
  component: RouteComponent,
  loader: () => ({
    crumb: true,
  }),
});

function RouteComponent() {
  return (
    <Page>
      <Can permissionCode={PERMISSION_CODES.SHOP_GOODS_VIEW}>
        <GoodLibrary />
      </Can>
    </Page>
  );
}
