import { Can } from "@/components/common/can";
import { Page } from "@/components/common/page";
import { PERMISSION_CODES } from "@/constants/permission";
import { PromptVersionManagement } from "@/features/version-management";

export const Route = createFileRoute({
  component: RouteComponent,
  loader: () => ({
    crumb: true,
  }),
});

function RouteComponent() {
  return (
    <Page showShopSelect={false}>
      <Can permissionCode={PERMISSION_CODES.PROMPT_VERSION_VIEW}>
        <PromptVersionManagement />
      </Can>
    </Page>
  );
}
