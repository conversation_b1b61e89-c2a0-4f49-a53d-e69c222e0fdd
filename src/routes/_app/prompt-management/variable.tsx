import { Can } from "@/components/common/can";
import { Page } from "@/components/common/page";
import { PERMISSION_CODES } from "@/constants/permission";
import { VariableManagement } from "@/features/prompt/variable";
import { VariableType } from "@/types/api/prompt";
import z from "zod";

const searchSchema = z.object({
  type: z.enum(VariableType).optional(),
});

export const Route = createFileRoute({
  validateSearch: searchSchema,
  component: RouteComponent,
  loader: () => ({
    crumb: true,
  }),
});

function RouteComponent() {
  return (
    <Page showShopSelect={false}>
      <Can permissionCode={PERMISSION_CODES.PROMPT_VARIABLE_CRUD}>
        <VariableManagement />
      </Can>
    </Page>
  );
}
