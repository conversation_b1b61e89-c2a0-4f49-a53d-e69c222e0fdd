import { Page } from "@/components/common/page";
import { PromptManagement } from "@/features/prompt/management";
import { beforeLoadPermissionValid } from "@/utils/permission";
import { redirect } from "@tanstack/react-router";

export const Route = createFileRoute({
  component: RouteComponent,
  loader: () => ({
    crumb: true,
  }),
  beforeLoad({ context, location }) {
    if (location.pathname === "/prompt-management/prompt") {
      throw redirect({
        to: "/prompt-management/prompt/ability",
      });
    }

    beforeLoadPermissionValid(context, location);
  },
});

function RouteComponent() {
  return (
    <Page showShopSelect={false}>
      <PromptManagement />
    </Page>
  );
}
