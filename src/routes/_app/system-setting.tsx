import { beforeLoadPermissionValid } from "@/utils/permission";
import { redirect } from "@tanstack/react-router";

export const Route = createFileRoute({
  beforeLoad: ({ context, location }) => {
    beforeLoadPermissionValid(context, location);

    if (location.pathname === "/system-setting") {
      throw redirect({ to: "/system-setting/field-configure" });
    }
  },
  loader: () => ({
    crumb: true,
  }),
});
