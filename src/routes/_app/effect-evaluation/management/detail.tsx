import { Page } from "@/components/common/page";
import { Detail } from "@/features/effect-evaluation/management/detail";
import z from "zod";

const ManagementDetailSearchSchema = z.object({
  id: z.string(),
});

export const Route = createFileRoute({
  component: RouteComponent,
  validateSearch: ManagementDetailSearchSchema,
  loader: () => ({
    crumb: true,
  }),
});

function RouteComponent() {
  return (
    <Page>
      <Detail />
    </Page>
  );
}
