import { Can } from "@/components/common/can";
import { Page } from "@/components/common/page";
import { Management } from "@/features/effect-evaluation/management";
import z from "zod";
import { PERMISSION_CODES } from "@/constants/permission";

const ManagementSearchSchema = z.object({
  testSetId: z.string().optional(),
});

export const Route = createFileRoute({
  component: RouteComponent,
  validateSearch: ManagementSearchSchema,
});

function RouteComponent() {
  return (
    <Page>
      <Can permissionCode={PERMISSION_CODES.EFFECT_EVALUATION_MANAGEMENT_VIEW}>
        <Management />
      </Can>
    </Page>
  );
}
