import { Page } from "@/components/common/page";
import { ContrastResult } from "@/features/effect-evaluation/management/contrast-result";
import z from "zod";

const ManagementContrastResultSearchSchema = z.object({
  id: z.string(),
});

export const Route = createFileRoute({
  component: RouteComponent,
  validateSearch: ManagementContrastResultSearchSchema,
  loader: () => ({
    crumb: true,
  }),
});

function RouteComponent() {
  return (
    <Page>
      <ContrastResult />
    </Page>
  );
}
