import { Can } from "@/components/common/can";
import { Page } from "@/components/common/page";
import Tuning from "@/features/effect-evaluation/tuning";
import { beforeLoadPermissionValid } from "@/utils/permission";
import { t } from "i18next";
import { PERMISSION_CODES } from "@/constants/permission";

export const Route = createFileRoute({
  component: RouteComponent,
  beforeLoad({ context, location }) {
    beforeLoadPermissionValid(context, location);
  },
  loader: async ({ params }) => ({
    crumb: `${t("message.id")} - ${params.id}`,
  }),
});

function RouteComponent() {
  return (
    <Page>
      <Can permissionCode={PERMISSION_CODES.EFFECT_EVALUATION_TUNING_CRUD}>
        <Tuning />
      </Can>
    </Page>
  );
}
