import { Can } from "@/components/common/can";
import { Page } from "@/components/common/page";
import { Samples } from "@/features/effect-evaluation/samples";
import { PERMISSION_CODES } from "@/constants/permission";

export const Route = createFileRoute({
  component: RouteComponent,
  loader: () => ({
    crumb: true,
  }),
});

function RouteComponent() {
  return (
    <Page>
      <Can permissionCode={PERMISSION_CODES.EFFECT_EVALUATION_SAMPLE_VIEW}>
        <Samples />
      </Can>
    </Page>
  );
}
