import { Page } from "@/components/common/page";
import EmptyPage from "@/features/effect-evaluation/tuning/empty-page";
import { Outlet, useLocation } from "@tanstack/react-router";

export const Route = createFileRoute({
  component: RouteComponent,
  loader: () => ({
    crumb: true,
  }),
});

function RouteComponent() {
  const location = useLocation();

  if (location.pathname === "/effect-evaluation/tuning") {
    return (
      <Page>
        <EmptyPage />
      </Page>
    );
  }

  return <Outlet />;
}
