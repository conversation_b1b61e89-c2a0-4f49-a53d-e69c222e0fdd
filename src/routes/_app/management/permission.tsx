import { Page } from "@/components/common/page";
import { PermissionManagement } from "@/features/permission-management";
import { beforeLoadPermissionValid } from "@/utils/permission";

export const Route = createFileRoute({
  component: RouteComponent,
  loader: () => ({
    crumb: true,
  }),
  beforeLoad({ context, location }) {
    beforeLoadPermissionValid(context, location);
  },
});

function RouteComponent() {
  return (
    <Page showShopSelect={false}>
      <PermissionManagement />
    </Page>
  );
}
