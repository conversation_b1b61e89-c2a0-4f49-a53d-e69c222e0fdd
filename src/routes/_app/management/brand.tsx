import { Can } from "@/components/common/can";
import { Page } from "@/components/common/page";
import { PERMISSION_CODES } from "@/constants/permission";
import Brand from "@/features/management/brand";

export const Route = createFileRoute({
  component: RouteComponent,
  loader: () => ({
    crumb: true,
  }),
});

function RouteComponent() {
  return (
    <Page showShopSelect={false}>
      <Can permissionCode={PERMISSION_CODES.SYSTEM_MANAGEMENT_BRAND_VIEW}>
        <Brand />
      </Can>
    </Page>
  );
}
