import { Can } from "@/components/common/can";
import { Page } from "@/components/common/page";
import { PERMISSION_CODES } from "@/constants/permission";
import Role from "@/features/management/role";

export const Route = createFileRoute({
  component: RouteComponent,
  loader: () => ({
    crumb: true,
  }),
});

function RouteComponent() {
  return (
    <Page showShopSelect={false}>
      <Can permissionCode={PERMISSION_CODES.SYSTEM_MANAGEMENT_ROLE_CRUD}>
        <Role />
      </Can>
    </Page>
  );
}
