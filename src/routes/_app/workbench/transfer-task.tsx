import { Page } from "@/components/common/page";
import { TransferTask } from "@/features/transfer-task";
import { beforeLoadPermissionValid } from "@/utils/permission";

export const Route = createFileRoute({
  component: RouteComponent,
  loader: () => ({
    crumb: true,
  }),
  beforeLoad: ({ context, location }) => {
    beforeLoadPermissionValid(context, location);
  },
});

function RouteComponent() {
  return (
    <Page>
      <TransferTask />
    </Page>
  );
}
