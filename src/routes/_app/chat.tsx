import { Page } from "@/components/common/page";
import Chat from "@/features/chat";
import z from "zod";

const ChatSearchSchema = z.object({
  msgId: z.string().optional(),
  conversationId: z.string().optional(),
});

export const Route = createFileRoute({
  component: RouteComponent,
  validateSearch: ChatSearchSchema,
  loader: () => ({
    crumb: true,
  }),
});

function RouteComponent() {
  return (
    <Page showShopSelect={false}>
      <Chat />
    </Page>
  );
}
