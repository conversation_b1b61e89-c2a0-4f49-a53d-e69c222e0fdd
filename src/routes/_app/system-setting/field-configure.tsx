import { Page } from "@/components/common/page";
import {
  FieldConfigure,
  FieldConfigureType,
} from "@/features/system-setting/configure";
import { beforeLoadPermissionValid } from "@/utils/permission";
import z from "zod";

const FieldConfigureSearchSchema = z.object({
  tab: z.enum(FieldConfigureType).optional(),
});

export const Route = createFileRoute({
  component: RouteComponent,
  validateSearch: FieldConfigureSearchSchema,
  beforeLoad({ context, location }) {
    beforeLoadPermissionValid(context, location);
  },
  loader: () => ({
    crumb: true,
  }),
});

function RouteComponent() {
  return (
    <Page showShopSelect={false}>
      <FieldConfigure />
    </Page>
  );
}
