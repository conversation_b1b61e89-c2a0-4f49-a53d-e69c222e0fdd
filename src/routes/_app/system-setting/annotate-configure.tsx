import { Page } from "@/components/common/page";
import {
  AnnotateConfigure,
  AnnotateConfigureType,
} from "@/features/system-setting/annotate-configure";
import { beforeLoadPermissionValid } from "@/utils/permission";
import { z } from "zod";

const AnnotateConfigureSearchSchema = z.object({
  tab: z.enum(AnnotateConfigureType).optional(),
});

export const Route = createFileRoute({
  component: RouteComponent,
  validateSearch: AnnotateConfigureSearchSchema,
  loader: () => ({
    crumb: true,
  }),
  beforeLoad({ context, location }) {
    beforeLoadPermissionValid(context, location);
  },
});

function RouteComponent() {
  return (
    <Page showShopSelect={false}>
      <AnnotateConfigure />
    </Page>
  );
}
