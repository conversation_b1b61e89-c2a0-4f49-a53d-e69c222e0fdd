import { beforeLoadPermissionValid } from "@/utils/permission";
import { redirect } from "@tanstack/react-router";

export const Route = createFileRoute({
  beforeLoad: ({ context, location }) => {
    beforeLoadPermissionValid(context, location);

    if (location.pathname === "/management") {
      throw redirect({ to: "/management/account" });
    }
  },
  loader: () => ({
    crumb: true,
  }),
});
