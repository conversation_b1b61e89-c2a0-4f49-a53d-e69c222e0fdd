import { beforeLoadPermissionValid } from "@/utils/permission";
import { redirect } from "@tanstack/react-router";

export const Route = createFileRoute({
  beforeLoad: ({ context, location }) => {
    beforeLoadPermissionValid(context, location);
    if (location.pathname === "/effect-evaluation") {
      throw redirect({ to: "/effect-evaluation/samples" });
    }
  },
  loader: () => ({
    crumb: true,
  }),
});
