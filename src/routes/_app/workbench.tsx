import { beforeLoadPermissionValid } from "@/utils/permission";
import { redirect } from "@tanstack/react-router";

export const Route = createFileRoute({
  loader: () => ({
    crumb: true,
  }),
  beforeLoad: ({ context, location }) => {
    beforeLoadPermissionValid(context, location);

    if (location.pathname === "/workbench") {
      throw redirect({
        to: "/workbench/transfer-task",
      });
    }
  },
});
