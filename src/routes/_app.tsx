import { hasLogin, loadableUser<PERSON>tom } from "@/atoms/auth";
import { autoSelectShopEffect } from "@/atoms/shop";
import { shopChangeEffect, threadChangeEffect } from "@/atoms/conversation";
import { AppSidebar } from "@/components/common/app-sidebar";
import { CommandPaletteProvider } from "@/components/common/command-palette";
import { NotFoundPage } from "@/components/common/not-found";
import { AppSkeleton } from "@/components/ui/app-skeleton";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import { routePermissionMap } from "@/constants/routes-permission";
import { usePermission } from "@/hooks/use-permission";
import { Outlet, redirect, useRouter } from "@tanstack/react-router";
import { getDefaultStore, useAtom, useAtomValue } from "jotai";
import { useEffect } from "react";

export const Route = createFileRoute({
  beforeLoad: ({ location }) => {
    if (!hasLogin()) {
      throw redirect({
        to: "/login",
        search: {
          redirect: location.href,
        },
      });
    }
    // 等待用户数据加载完成
    return new Promise((resolve) => {
      const store = getDefaultStore();

      // 先检查用户数据状态
      const userLoadable = store.get(loadableUserAtom);

      // 创建一个订阅来监听用户数据状态
      const unsubscribe = store.sub(loadableUserAtom, () => {
        const userLoadable = store.get(loadableUserAtom);

        if (
          userLoadable.state === "hasData" &&
          userLoadable.data?.data?.permissionList
        ) {
          unsubscribe();
          resolve(undefined);
        }
      });

      // 立即检查一次，可能已经加载完成
      if (
        userLoadable.state === "hasData" &&
        userLoadable.data?.data?.permissionList
      ) {
        unsubscribe();
        resolve(undefined);
      }
    });
  },
  component: LayoutComponent,
  pendingComponent: AppSkeleton,
  notFoundComponent: NotFoundPage,
  pendingMs: 1,
});

function LayoutComponent() {
  const userLoadable = useAtomValue(loadableUserAtom);
  const router = useRouter();
  const { hasPermission } = usePermission();

  useEffect(() => {
    // 只有在根路径且用户信息已加载完成时执行
    if (window.location.pathname === "/" && userLoadable.state === "hasData") {
      // 查找有权限的路由
      const authorizedRoutes = Object.entries(routePermissionMap)
        .filter(([, permission]) => permission && hasPermission(permission))
        .map(([route]) => route);

      if (authorizedRoutes.length > 0) {
        // 直接使用第一个有权限的路由
        router.navigate({ to: authorizedRoutes[0] });
      }
    }
  }, [userLoadable.state, router, hasPermission]);

  useAtom(autoSelectShopEffect);
  useAtom(shopChangeEffect);
  useAtom(threadChangeEffect);

  return (
    <CommandPaletteProvider>
      <SidebarProvider className="w-screen h-screen">
        <AppSidebar />
        <SidebarInset className="flex-1 min-w-0">
          <Outlet />
        </SidebarInset>
      </SidebarProvider>
    </CommandPaletteProvider>
  );
}
