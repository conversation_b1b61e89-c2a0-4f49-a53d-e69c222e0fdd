import { Tooltip } from "@/components/common/tooltip";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import { Check, Edit, Pencil, Trash2, X } from "lucide-react";
import React, { useCallback, useEffect, useRef } from "react";

export interface ListItem {
  id: string;
  name: string;
  [key: string]: any;
}

export interface EditableListItemProps<T extends ListItem> {
  /** 列表项数据 */
  item: T;
  /** 是否选中 */
  isSelected: boolean;
  /** 是否处于编辑状态 */
  isEditing: boolean;
  /** 编辑时的值 */
  editValue: string;
  /** 是否禁用 */
  disabled: boolean;
  /** 项目名称的最大长度 */
  maxLength?: number;
  /** 编辑项目名称回调 */
  onEditItemName?: (id: string, name: string) => void;
  /** 选中项目回调 */
  onSelectItem: (id: string) => void;
  /** 开始编辑回调 */
  onStartEdit: (item: T) => void;
  /** 编辑项目回调 */
  onEditItem?: (item: T) => Promise<void>;
  /** 删除项目回调 */
  onDeleteItem?: (id: string) => void;
  /** 编辑值变化回调 */
  onEditValueChange: (value: string) => void;
  /** 保存编辑回调 */
  onSaveEdit: () => void;
  /** 取消编辑回调 */
  onCancelEdit: () => void;
  /** 键盘事件回调 */
  onKeyDown: (e: React.KeyboardEvent) => void;
}

/**
 * 可编辑列表项组件
 */
export const EditableListItem = <T extends ListItem>({
  item,
  isSelected,
  isEditing,
  editValue,
  maxLength = 50,
  disabled = false,
  onEditItemName,
  onSelectItem,
  onStartEdit,
  onEditItem,
  onDeleteItem,
  onEditValueChange,
  onSaveEdit,
  onCancelEdit,
  onKeyDown,
}: EditableListItemProps<T>) => {
  const inputRef = useRef<HTMLInputElement>(null);

  // 聚焦输入框
  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
      inputRef.current.select();
    }
  }, [isEditing]);

  // 开始编辑处理
  const handleStartEdit = useCallback(() => {
    onStartEdit(item);
  }, [item, onStartEdit]);

  // 删除处理
  const handleDelete = useCallback(() => {
    if (onDeleteItem) {
      onDeleteItem(item.id);
    }
  }, [item.id, onDeleteItem]);

  // 渲染编辑状态
  if (isEditing) {
    return (
      <div className="flex items-center gap-2 px-2 h-10 rounded bg-primary/10">
        <Input
          ref={inputRef}
          value={editValue}
          onChange={(e) => onEditValueChange(e.target.value)}
          onKeyDown={onKeyDown}
          boxClassName="flex-1 min-w-0"
          className="h-8 px-1 text-sm rounded-none border-0 border-b border-b-primary bg-transparent focus-visible:ring-0 focus-visible:outline-none shadow-none"
          maxLength={maxLength}
        />
        <div className="flex items-center gap-0.5">
          <Button
            size="sm"
            variant="ghost"
            onClick={onSaveEdit}
            className="h-6 w-6 p-0 text-emerald-600 hover:text-emerald-700 hover:bg-emerald-100/80"
          >
            <Check className="h-3.5 w-3.5" />
          </Button>
          <Button
            size="sm"
            variant="ghost"
            onClick={onCancelEdit}
            className="h-6 w-6 p-0 text-rose-600 hover:text-rose-700 hover:bg-rose-100/80"
          >
            <X className="h-3.5 w-3.5" />
          </Button>
        </div>
      </div>
    );
  }

  // 渲染正常状态
  return (
    <div
      className={cn(
        "group flex items-center justify-between px-3 h-10 rounded cursor-pointer transition-colors hover:bg-primary/10 hover:text-slate-800",
        {
          "bg-primary/10 text-primary hover:text-primary": isSelected,
        },
      )}
      onClick={() => onSelectItem(item.id)}
    >
      <Tooltip
        tooltipContentProps={{ side: "left" }}
        content="双击编辑名称"
        disabled={!onStartEdit || disabled}
      >
        <div
          className="flex items-center gap-2 flex-1 min-w-0"
          onDoubleClick={(e) => {
            e.stopPropagation();

            if (!onStartEdit || disabled) {
              return;
            }

            handleStartEdit();
          }}
        >
          <span
            className="text-sm truncate cursor-pointer hover:text-slate-900"
            title={item.name}
          >
            {item.name}
          </span>
        </div>
      </Tooltip>

      <div className="flex items-center gap-1">
        {onEditItemName && !disabled && (
          <Tooltip content="修改名称">
            <Button
              size="sm"
              variant="ghost"
              onClick={(e) => {
                e.stopPropagation();
                handleStartEdit();
              }}
              className="size-6 p-1 opacity-0 group-hover:opacity-100 transition-opacity text-green-600/90 hover:text-green-600 hover:bg-green-100/80"
            >
              <Pencil className="size-3" />
            </Button>
          </Tooltip>
        )}
        {onEditItem && !disabled && (
          <Tooltip content="编辑">
            <Button
              size="sm"
              variant="ghost"
              onClick={(e) => {
                e.stopPropagation();
                onEditItem(item);
              }}
              className="size-6 p-1 opacity-0 group-hover:opacity-100 transition-opacity text-primary/90 hover:text-primary/100 hover:bg-primary/10"
            >
              <Edit className="size-3" />
            </Button>
          </Tooltip>
        )}
        {onDeleteItem && !disabled && (
          <Tooltip content="删除">
            <Button
              size="sm"
              variant="ghost"
              onClick={(e) => {
                e.stopPropagation();
                handleDelete();
              }}
              className="size-6 p-1 opacity-0 group-hover:opacity-100 transition-opacity text-rose-600/90 hover:text-rose-600 hover:bg-rose-100/80"
            >
              <Trash2 className="size-3" />
            </Button>
          </Tooltip>
        )}
      </div>
    </div>
  );
};
