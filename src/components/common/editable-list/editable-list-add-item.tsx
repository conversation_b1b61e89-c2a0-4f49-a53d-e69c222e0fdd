import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Check, X } from "lucide-react";
import React, { useEffect, useRef } from "react";

export interface EditableListAddItemProps {
  /** 新项目名称 */
  newItemName: string;
  /** 新增项目的占位符 */
  addPlaceholder?: string;
  /** 项目名称的最大长度 */
  maxLength?: number;
  /** 新项目名称变化回调 */
  onNewItemNameChange: (name: string) => void;
  /** 保存新项目回调 */
  onSaveAdd: () => void;
  /** 取消添加回调 */
  onCancelAdd: () => void;
  /** 键盘事件回调 */
  onKeyDown: (e: React.KeyboardEvent) => void;
}

/**
 * 可编辑列表添加项目组件
 */
export const EditableListAddItem: React.FC<EditableListAddItemProps> = ({
  newItemName,
  addPlaceholder = "输入名称",
  maxLength = 50,
  onNewItemNameChange,
  onSaveAdd,
  onCancelAdd,
  onKeyDown,
}) => {
  const addInputRef = useRef<HTMLInputElement>(null);

  // 聚焦输入框
  useEffect(() => {
    if (addInputRef.current) {
      addInputRef.current.focus();
    }
  }, []);

  return (
    <div className="flex items-center gap-2 px-2 h-10 rounded bg-primary/10">
      <Input
        ref={addInputRef}
        value={newItemName}
        onChange={(e) => onNewItemNameChange(e.target.value)}
        onKeyDown={onKeyDown}
        placeholder={addPlaceholder}
        boxClassName="flex-1 min-w0"
        className="h-8 px-1 text-sm border-0 border-b rounded-none border-b-primary bg-transparent focus-visible:ring-0 focus-visible:outline-none shadow-none"
        maxLength={maxLength}
      />
      <div className="flex items-center gap-0.5">
        <Button
          size="sm"
          variant="ghost"
          onClick={onSaveAdd}
          className="h-6 w-6 p-0 text-emerald-600 hover:text-emerald-700 hover:bg-emerald-100/80"
        >
          <Check className="h-3.5 w-3.5" />
        </Button>
        <Button
          size="sm"
          variant="ghost"
          onClick={onCancelAdd}
          className="h-6 w-6 p-0 text-rose-600 hover:text-rose-700 hover:bg-rose-100/80"
        >
          <X className="h-3.5 w-3.5" />
        </Button>
      </div>
    </div>
  );
};
