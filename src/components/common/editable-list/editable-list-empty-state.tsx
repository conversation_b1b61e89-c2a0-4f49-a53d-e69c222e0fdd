import React from "react";

export interface EditableListEmptyStateProps {
  /** 空数据提示文案 */
  emptyText?: string;
  /** 过滤结果为空的提示文案 */
  noResultsText?: string;
  /** 是否为过滤状态（用于区分空数据和过滤结果为空） */
  isFiltered?: boolean;
}

/**
 * 可编辑列表空状态组件
 */
export const EditableListEmptyState: React.FC<EditableListEmptyStateProps> = ({
  emptyText = "暂无数据",
  noResultsText = "未找到匹配的结果",
  isFiltered = false,
}) => {
  return (
    <div className="p-4 text-center text-slate-500 text-sm">
      {isFiltered ? noResultsText : emptyText}
    </div>
  );
};
