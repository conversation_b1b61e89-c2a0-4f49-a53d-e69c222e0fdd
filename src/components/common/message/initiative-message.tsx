import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";
import { parseMessageContent } from "@/utils/message-formatter";
import { Zap } from "lucide-react";

interface InitiativeMessageProps {
  messageId: string;
  content: string;
  compact?: boolean;
  className?: string;
}

export const InitiativeMessage = ({
  messageId,
  content,
  compact = false,
  className,
}: InitiativeMessageProps) => {
  const parsedContent = parseMessageContent(content);

  // 提取主动触发消息文本
  const extractMessageText = () => {
    if (!parsedContent.success) {
      return content;
    }

    // 主要处理文本和卡片类型
    const item = parsedContent.data[0];

    if (!item) {
      return content;
    }

    return item.text[0]?.value || content;
  };

  const messageText = extractMessageText();

  return (
    <div
      id={messageId}
      className={cn(
        "w-full flex justify-center my-8",
        {
          "my-4": compact,
        },
        className,
      )}
    >
      <div className="inline-flex flex-col items-center">
        <Label
          variant="secondary"
          className={cn(
            "py-3 px-4 whitespace-normal text-gray-600 bg-gray-100",
            {
              "py-2 px-3": compact,
            },
          )}
        >
          <div className="flex items-center gap-2">
            <Zap
              className={cn(
                "text-gray-500 flex-shrink-0",
                compact ? "size-3.5" : "size-4",
              )}
            />
            <div className={cn("text-sm", compact ? "text-xs" : "text-sm")}>
              {messageText}
            </div>
          </div>
        </Label>
      </div>
    </div>
  );
};
