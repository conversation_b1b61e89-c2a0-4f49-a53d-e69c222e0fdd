import { CopyableText } from "@/components/common/copyable-text";
import { Tooltip } from "@/components/common/tooltip";
import { Button } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { PERMISSION_CODES } from "@/constants/permission";
import { usePermission } from "@/hooks/use-permission";
import { cn } from "@/lib/utils";
import {
  ConversationMessage,
  MessageSource,
  MessageThumb,
  MessageType,
} from "@/types/api/message";
import { MessageBody } from "@/utils";
import {
  FileText,
  FlaskConical,
  History,
  Plus,
  RefreshCw,
  ThumbsDown,
  ThumbsUp,
  X,
} from "lucide-react";
import { useState, useRef, Suspense, useEffect } from "react";
import { MessageReference } from "./message-reference";
import { Link } from "@tanstack/react-router";
import { t } from "i18next";
import { SampleAddToTaskModal } from "@/features/effect-evaluation/samples/sample-add-to-task-modal";
import { SampleService } from "@/services/sample-service";
import { MessageService } from "@/services/message-service";
import { toast } from "sonner";
import { shopAtom } from "@/atoms/shop";
import { assistantAtom, threadAtom } from "@/atoms/conversation";
import { useAtomValue } from "jotai";
import { ScrollArea } from "@/components/ui/scroll-area";

// 只导入builtInServices，不再使用MessageAnnotate组件
import { builtInServices } from "@/features/inspect-annotate-toolkit/message-annotate";
import { MessageAnnotateProvider } from "@/features/inspect-annotate-toolkit/message-annotate/message-annotate-provider";
import { MessageAnnotateSkeleton } from "@/features/inspect-annotate-toolkit/message-annotate/message-annotate-skeleton";
import { useQueryClient } from "@tanstack/react-query";

// 初始化assistantAtom和threadAtom的值
import { store } from "@/store";

// 组件外面初始化
const initializeAtoms = () => {
  const { assistant, thread } = store;
  assistantAtom.init = {
    currentAssistantId: assistant.currentAssistantId,
  };
  threadAtom.init = {
    currentThreadId: thread.currentThreadId,
  };
};

// 尝试初始化
try {
  initializeAtoms();
} catch (error) {
  console.error("初始化atom失败:", error);
}

interface MessageActionButtonsProps {
  position: "left" | "right";
  message: MessageBody[number];
  className?: string;
  questions?: ConversationMessage[] | null;
  replyId?: string;
  setTableAction: () => void;
  originalMessage: ConversationMessage;
  onRerunStatusChange?: (isRerunning: boolean) => void;
  isPortalled?: boolean;
  containerRect?: DOMRect | null;
  onPopoverOpenChange?: (isOpen: boolean) => void; // 新增属性，用于通知父组件Popover状态变化
  onMouseEnter?: () => void; // 新增属性，用于通知父组件鼠标进入
  onMouseLeave?: () => void; // 新增属性，用于通知父组件鼠标离开
}

export const MessageActionButtons = ({
  position,
  message,
  className,
  questions = [],
  replyId,
  setTableAction,
  originalMessage,
  onRerunStatusChange,
  isPortalled,
  containerRect,
  onPopoverOpenChange,
  onMouseEnter,
  onMouseLeave,
}: MessageActionButtonsProps) => {
  const [referencesOpen, setReferencesOpen] = useState(false);
  const [thumbUpPopoverOpen, setThumbUpPopoverOpen] = useState(false);
  const [thumbDownPopoverOpen, setThumbDownPopoverOpen] = useState(false);
  const [refreshLock, setRefreshLock] = useState(false);
  const timerRef = useRef<number | undefined>(undefined);
  const [addToSamplePopoverOpen, setAddToSamplePopoverOpen] = useState(false);
  const { hasPermission } = usePermission();
  const shop = useAtomValue(shopAtom);
  const isRequesting = useRef(false);
  const queryClient = useQueryClient();

  // 从originalMessage中提取必要的数据
  const { messageId, role, source, thumbs, conversationId } = originalMessage;

  // 使用本地状态来跟踪点赞/点踩状态，确保UI能立即更新
  const [thumbValue, setThumbValue] = useState<number>(+thumbs);

  // 每次originalMessage变化时更新thumbValue，确保hover后看到最新状态
  useEffect(() => {
    setThumbValue(+thumbs);
  }, [thumbs]);

  // 向父组件通知Popover状态变化
  useEffect(() => {
    const isAnyPopoverOpen =
      thumbUpPopoverOpen ||
      thumbDownPopoverOpen ||
      referencesOpen ||
      addToSamplePopoverOpen;
    onPopoverOpenChange?.(isAnyPopoverOpen);
  }, [
    thumbUpPopoverOpen,
    thumbDownPopoverOpen,
    referencesOpen,
    addToSamplePopoverOpen,
    // onPopoverOpenChange,
  ]);

  // 清除定时器
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        window.clearInterval(timerRef.current);
      }
    };
  }, []);

  // 计算UI状态
  const isThumbUp = thumbValue === 2;
  const isThumbDown = thumbValue === -2;

  // 根据role和source确定按钮显示
  const isUser = role === MessageSource.user;
  const isAssistant = role === MessageSource.assistant;

  // 是否显示添加样本按钮 - 仅在assistant或(user且source为agent)时显示
  const canLabelMessage = hasPermission(PERMISSION_CODES.MESSAGE_LABEL);
  const showAddToSampleButton =
    canLabelMessage &&
    (isAssistant || (isUser && source === "agent")) &&
    hasPermission(PERMISSION_CODES.MESSAGE_ACTION_ADD_TO_SAMPLE);

  const positionStyles = {
    left: "left-0 -translate-x-[calc(100%+8px)] bottom-0",
    right: "right-0 translate-x-[calc(100%+8px)] bottom-0",
  };
  const hasRefMessage = replyId != null && (questions || []).length > 0;
  const canSeeDebug = hasPermission(PERMISSION_CODES.MESSAGE);

  const portalStyle: React.CSSProperties = {};
  if (isPortalled && containerRect) {
    Object.assign(portalStyle, {
      position: "fixed",
      top: containerRect.bottom,
      zIndex: 50,
    });
    if (position === "left") {
      portalStyle.left = containerRect.left - 8;
      portalStyle.transform = "translate(-100%, -100%)";
    } else {
      // right
      portalStyle.left = containerRect.right + 8;
      portalStyle.transform = "translateY(-100%)";
    }
  }

  if (!hasRefMessage && message.type !== MessageType.TEXT) {
    return null;
  }

  const handleAdd = async (testSetId: string[]) => {
    const res = await SampleService.addSample({
      messageId: messageId || "",
      testSetIds: testSetId,
      shopId: shop?.id || "",
    });
    if (res.success) {
      toast.success(t("common.add.success"));
    }
  };

  // 处理点赞/点踩
  function handleThumb(value: 2 | -2 | 0) {
    if (!messageId) return;

    // 立即更新本地状态，确保UI响应
    setThumbValue(value);

    // 将值转换为API需要的值
    let thumbValueApi: MessageThumb;
    if (value === 2) {
      thumbValueApi = MessageThumb.THUMBS_UP;
    } else if (value === -2) {
      thumbValueApi = MessageThumb.THUMBS_DOWN;
    } else {
      thumbValueApi = MessageThumb.THUMBS_NONE;
    }

    // 发送API请求
    MessageService.updateMessageLabel({
      messageId,
      thumbs: thumbValueApi,
    })
      .then(() => {
        // 使当前会话的消息缓存失效，以触发重新获取
        queryClient.invalidateQueries({
          queryKey: ["messages", conversationId],
        });
      })
      .catch((error) => {
        console.error("更新消息标签失败:", error);
        toast.error("操作失败，请重试");

        // 发生错误时回滚本地状态
        setThumbValue(+thumbs);
      });

    // 添加到样本 - 只有当消息是助手回复时才添加
    if (role === MessageSource.assistant && value !== 0) {
      // 防止重复请求
      if (isRequesting.current) return;
      isRequesting.current = true;
      SampleService.addSample({
        messageId: messageId || "",
        testSetIds: ["0"],
        shopId: shop?.id || "",
      })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          isRequesting.current = false;
        });
    }
  }

  // 渲染MessageAnnotate内容
  const renderMessageAnnotateContent = ({
    showReason = true,
    showScene = true,
  }: {
    showReason?: boolean;
    showScene?: boolean;
  }) => {
    const services = builtInServices.messageGenerator(messageId);

    return (
      <Suspense
        fallback={
          <MessageAnnotateSkeleton
            columns={3}
            showReason={showReason}
            showScene={showScene}
          />
        }
      >
        <MessageAnnotateProvider
          key={`message-annotate-provider-${messageId}`}
          columns={3}
          messageId={messageId || ""}
          showReason={showReason}
          showScene={showScene}
          messageLabelQueryFnKey={services.messageLabelQueryFnKey}
          messageLabelQueryFn={services.messageLabelQueryFn}
          messageLabelMutationFn={services.messageLabelMutationFn}
        />
      </Suspense>
    );
  };

  // 处理重跑逻辑
  const handleRerun = () => {
    // 如果已经在重跑中，直接返回
    if (refreshLock) {
      toast.error(t("message.rerun.already"));
      return;
    }

    // 设置重跑锁，防止重复点击
    setRefreshLock(true);

    // 通知父组件更新重跑状态
    onRerunStatusChange?.(true);

    // 调用重试创建API
    MessageService.messageRetryCreate({
      messageId: messageId || "",
    })
      .then(() => {
        // 立即检查一次消息状态
        checkMessageStatus();

        // 设置轮询，每5秒检查一次消息状态，最多检查20次
        let checkCount = 0;
        timerRef.current = window.setInterval(() => {
          checkCount++;
          checkMessageStatus();

          // 超过20次检查（约100秒）时，自动解锁
          if (checkCount >= 20) {
            setRefreshLock(false);
            onRerunStatusChange?.(false);
            window.clearInterval(timerRef.current);
            toast.error(t("message.rerun.timeout"));
          }
        }, 5000);
      })
      .catch((error) => {
        console.error("重跑消息失败:", error);
        toast.error(t("message.rerun.failed"));
        setRefreshLock(false);
        onRerunStatusChange?.(false);
      });
  };

  // 检查消息状态
  const checkMessageStatus = () => {
    MessageService.messageRetryReply({
      messageId: messageId || "",
    })
      .then((res) => {
        // 当有数据返回，表示重跑完成
        if (res.data) {
          // 重跑完成，解锁重跑按钮
          setRefreshLock(false);
          onRerunStatusChange?.(false);

          // 清除定时器
          if (timerRef.current) {
            window.clearInterval(timerRef.current);
            timerRef.current = undefined;
          }
          queryClient.invalidateQueries({ queryKey: ["messages"] });

          // 提示用户重跑成功
          toast.success(t("message.rerun.success"));
        }
      })
      .catch((err) => {
        console.error("检查重跑状态失败:", err);
        setRefreshLock(false);
        onRerunStatusChange?.(false);

        // 清除定时器
        if (timerRef.current) {
          window.clearInterval(timerRef.current);
          timerRef.current = undefined;
        }
      });
  };

  return (
    <div
      style={portalStyle}
      className={cn(
        "absolute flex items-center gap-1 transition-opacity",
        !isPortalled && positionStyles[position],
        isPortalled ? "opacity-100" : "opacity-0 group-hover:opacity-100",
        className,
      )}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
    >
      <div className="flex items-center gap-1.5 rounded-lg border bg-background p-0.5 shadow-sm">
        {/* Add to sample button */}
        {showAddToSampleButton && (
          <SampleAddToTaskModal
            ActionBtn={
              <Tooltip content={t("sample.add-to-title-tips")}>
                <Button
                  variant="icon"
                  size="sm"
                  onClick={() => setAddToSamplePopoverOpen(true)}
                >
                  <Plus className="w-4 h-4 text-[#7C7C7C]" />
                </Button>
              </Tooltip>
            }
            onSubmit={(testSetId) => {
              handleAdd(testSetId);
              setAddToSamplePopoverOpen(false);
            }}
            onClose={() => setAddToSamplePopoverOpen(false)}
          />
        )}

        {/* Tuning button - 仅对助手消息显示 */}
        {isAssistant &&
          hasPermission(PERMISSION_CODES.MESSAGE_ACTION_DEBUG) && (
            <Tooltip content={t("action.task")}>
              <Link
                to={`/effect-evaluation/tuning/$id`}
                params={{ id: messageId || "" }}
                target="_blank"
                className="inline-flex items-center justify-center rounded-md h-8 w-8 text-sm font-medium bg-background hover:bg-accent transition-colors"
              >
                <FlaskConical className="w-4 h-4 text-[#7C7C7C]" />
              </Link>
            </Tooltip>
          )}
        {message.type === MessageType.TEXT && (
          <CopyableText text={message.text?.[0].value || ""} showText={false} />
        )}

        {hasRefMessage && (
          <Popover
            open={referencesOpen}
            onOpenChange={(open) => {
              setReferencesOpen(open);
            }}
          >
            <PopoverTrigger asChild>
              <Tooltip content="关联消息">
                <Button
                  variant="icon"
                  size="sm"
                  icon={<History className="w-4 h-4" />}
                />
              </Tooltip>
            </PopoverTrigger>
            <PopoverContent
              className="w-72 p-3 max-h-[60vh] overflow-y-auto "
              align="center"
              side="top"
            >
              <div className="text-sm font-medium mb-2">关联消息</div>
              <div className="flex flex-col gap-2">
                {questions!.map((question) => (
                  <MessageReference
                    key={question.messageId}
                    message={question}
                  />
                ))}
              </div>
            </PopoverContent>
          </Popover>
        )}

        {/* Debug button - 不显示在manual角色和有datatype的消息上 */}
        {canSeeDebug && messageId && (
          <Tooltip content="日志">
            <Button variant="icon" size="sm" onClick={setTableAction}>
              <FileText size={14} />
            </Button>
          </Tooltip>
        )}

        {/* Thumbs up/down buttons - 使用自定义Popover */}
        {role !== MessageSource.user && role !== MessageSource.customer && (
          <>
            {/* Thumb Up Button */}
            <Popover
              open={thumbUpPopoverOpen}
              onOpenChange={(isOpen) => {
                if (!isOpen) {
                  setThumbUpPopoverOpen(isOpen);
                }
              }}
            >
              <PopoverTrigger asChild>
                <Tooltip content="赞">
                  <Button
                    variant="icon"
                    size="sm"
                    onClick={() => {
                      if (isThumbUp) {
                        // 如果已经点赞，直接取消，不显示弹窗
                        setThumbUpPopoverOpen(false); // 确保弹窗关闭
                        handleThumb(0);
                      } else {
                        // 先更新UI显示
                        setThumbValue(2);

                        // 关闭点踩弹窗，避免两个弹窗同时显示
                        setThumbDownPopoverOpen(false);

                        // 如果有权限，显示弹窗

                        hasPermission(PERMISSION_CODES.MESSAGE_LABEL) &&
                          hasPermission(PERMISSION_CODES.MESSAGE_LABEL_SCENE) &&
                          setThumbUpPopoverOpen(true);
                        // 立即调用API，不等待弹窗关闭
                        handleThumb(2);
                      }
                    }}
                  >
                    <ThumbsUp
                      key={`${messageId}-${isThumbUp}`}
                      className="w-[15px] h-[15px]"
                      style={{ color: "#7C7C7C" }}
                      fill={isThumbUp ? "currentColor" : "none"}
                    />
                  </Button>
                </Tooltip>
              </PopoverTrigger>
              <PopoverContent
                className="flex flex-col w-[570px] max-h-[60vh] p-0 overflow-hidden"
                align="end"
                alignOffset={5}
                sideOffset={10}
              >
                <ScrollArea className="flex flex-col p-4 h-full">
                  <div className="flex items-center justify-between mb-1">
                    <div className="text-base font-medium">
                      {/* {t("message.reason.title")} */}
                    </div>
                    <Button
                      variant="icon"
                      size="sm"
                      onClick={() => setThumbUpPopoverOpen(false)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                  {renderMessageAnnotateContent({
                    showReason: false,
                    showScene: true,
                  })}
                </ScrollArea>
              </PopoverContent>
            </Popover>

            {/* Thumb Down Button */}
            <Popover
              open={thumbDownPopoverOpen}
              onOpenChange={(isOpen) => {
                if (!isOpen) {
                  setThumbDownPopoverOpen(isOpen);
                }
              }}
            >
              <PopoverTrigger asChild>
                <Tooltip content="踩">
                  <Button
                    variant="icon"
                    size="sm"
                    onClick={() => {
                      if (isThumbDown) {
                        // 如果已经点踩，直接取消，不显示弹窗
                        setThumbDownPopoverOpen(false); // 确保弹窗关闭
                        handleThumb(0);
                      } else {
                        // 先更新UI显示
                        setThumbValue(-2);

                        // 关闭点赞弹窗，避免两个弹窗同时显示
                        setThumbUpPopoverOpen(false);

                        // 显示弹窗
                        hasPermission(PERMISSION_CODES.MESSAGE_LABEL) &&
                          (hasPermission(
                            PERMISSION_CODES.MESSAGE_LABEL_REASON,
                          ) ||
                            hasPermission(
                              PERMISSION_CODES.MESSAGE_LABEL_INPUT,
                            )) &&
                          setThumbDownPopoverOpen(true);
                        // 立即调用API，不等待弹窗关闭
                        handleThumb(-2);
                      }
                    }}
                  >
                    <ThumbsDown
                      key={`${messageId}-${isThumbDown}`}
                      className="w-[15px] h-[15px]"
                      style={{ color: "#7C7C7C" }}
                      fill={isThumbDown ? "currentColor" : "none"}
                    />
                  </Button>
                </Tooltip>
              </PopoverTrigger>
              <PopoverContent
                className="flex flex-col w-[570px] max-h-[60vh] p-0 overflow-hidden"
                align="end"
                alignOffset={5}
                sideOffset={10}
                collisionPadding={{ bottom: -100 }}
              >
                <ScrollArea className="flex flex-col p-4 h-full">
                  <div className="flex items-center justify-between mb-1">
                    <div className="text-base font-medium">
                      {/* {t("message.reason.title")} */}
                    </div>
                    <Button
                      variant="icon"
                      size="sm"
                      onClick={() => setThumbDownPopoverOpen(false)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                  {renderMessageAnnotateContent({
                    showReason: true,
                    showScene: true,
                  })}
                </ScrollArea>
              </PopoverContent>
            </Popover>
          </>
        )}

        {canSeeDebug &&
          role !== MessageSource.user &&
          role !== MessageSource.customer &&
          hasPermission(PERMISSION_CODES.MESSAGE_ACTION_ADD_TO_RERUN) && (
            <Tooltip content={t("rerun.title")}>
              <Button
                variant="icon"
                size="sm"
                onClick={handleRerun}
                disabled={refreshLock}
                className={refreshLock ? "animate-spin" : ""}
              >
                <RefreshCw size={14} />
              </Button>
            </Tooltip>
          )}
      </div>
    </div>
  );
};
