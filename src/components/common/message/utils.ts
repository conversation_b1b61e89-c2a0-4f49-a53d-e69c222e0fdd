import { BaseMessage, MessageSource } from "@/types/api/message";

/**
 * 获取消息发送者名称
 * @param message 消息
 * @returns 消息发送者名称
 */
export const getSenderName = (
  message: Partial<BaseMessage> | BaseMessage,
  fixedSendName?: boolean,
) => {
  if (message.role === MessageSource.user) {
    if (fixedSendName) {
      return "顾客";
    }

    return message.uid;
  }

  if (message.role === MessageSource.customer) {
    return message.customerName || "未知";
  }

  if (fixedSendName) {
    if (message.role === MessageSource.assistant) {
      return "AI 生成";
    }
  }

  return message.assistantName || "未知";
};

/**
 * 判断消息是否是入消息
 *
 * @param message 消息
 * @returns 是否是入消息
 */
export const isIncomingMessage = (
  message: Partial<BaseMessage> | BaseMessage,
) => {
  return message.role === MessageSource.user;
};

/**
 * 判断消息是否是AI消息
 *
 * @param message 消息
 * @returns 是否是AI消息
 */
export const isAssistantMessage = (
  message: Partial<BaseMessage> | BaseMessage,
) => {
  return message.role === MessageSource.assistant;
};

/**
 * 判断消息是否是系统消息
 *
 * @param message 消息
 * @returns 是否是系统消息
 */
export const isSystemMessage = (
  message: Partial<BaseMessage> | BaseMessage,
) => {
  return message.role === MessageSource.sys;
};

/**
 * 判断是否是主动触发消息
 *
 * @param message
 * @returns
 */
export const isInitiativeMessage = (
  message: Partial<BaseMessage> | BaseMessage,
) => {
  return message.role === MessageSource.trackOrderTactics;
};
