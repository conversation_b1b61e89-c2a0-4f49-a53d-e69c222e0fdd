import { Button } from "@/components/ui/button";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { X } from "lucide-react";
import { ReactNode } from "react";

export interface PanelHeaderTab {
  value: string;
  label: string;
  icon?: ReactNode;
}

export interface PanelHeaderProps {
  title?: string;
  tabs?: PanelHeaderTab[];
  activeTab?: string;
  onTabChange?: (value: string) => void;
  onClose?: () => void;
  extra?: ReactNode;
  className?: string;
}

export const PanelHeader = ({
  title,
  tabs,
  activeTab,
  onTabChange,
  onClose,
  extra,
  className,
}: PanelHeaderProps) => {
  // 如果有 tabs，使用 tabs 模式
  if (tabs && tabs.length > 0) {
    return (
      <div
        className={`flex items-center justify-between px-4 py-2 bg-muted/50 border-b border-border/50 ${className || ""}`}
      >
        <Tabs value={activeTab} onValueChange={onTabChange} className="flex-1">
          <TabsList variant="solid" className="h-8">
            {tabs.map((tab) => (
              <TabsTrigger
                key={tab.value}
                value={tab.value}
                className="text-xs px-2 py-1 h-6"
              >
                <div className="flex items-center gap-1">
                  {tab.icon}
                  <span>{tab.label}</span>
                </div>
              </TabsTrigger>
            ))}
          </TabsList>
        </Tabs>

        <div className="flex items-center gap-2 ml-2">
          {extra}
          {onClose && (
            <Button
              variant="icon"
              icon={<X />}
              onClick={onClose}
              aria-label="关闭"
            />
          )}
        </div>
      </div>
    );
  }

  // 如果没有 tabs，使用简单的 title 模式
  return (
    <div
      className={`flex items-center justify-between px-4 py-2 bg-muted/50 border-b border-border/50 ${className || ""}`}
    >
      <span className="text-sm font-medium truncate">{title}</span>

      <div className="flex items-center gap-2">
        {extra}
        {onClose && (
          <Button
            variant="icon"
            icon={<X />}
            onClick={onClose}
            aria-label="关闭"
          />
        )}
      </div>
    </div>
  );
};
