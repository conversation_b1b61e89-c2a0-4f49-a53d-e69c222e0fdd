import { useState } from "react";
import { PanelHeader } from "./panel-header";
import { FileText, Settings, User } from "lucide-react";
import { Button } from "@/components/ui/button";

// 使用示例组件
export const PanelHeaderExample = () => {
  const [activeTab, setActiveTab] = useState("info");

  // 定义 tabs
  const tabs = [
    {
      value: "info",
      label: "信息",
      icon: <FileText className="w-3 h-3" />,
    },
    {
      value: "settings",
      label: "设置",
      icon: <Settings className="w-3 h-3" />,
    },
    {
      value: "user",
      label: "用户",
      icon: <User className="w-3 h-3" />,
    },
  ];

  return (
    <div className="space-y-4">
      {/* 简单标题模式 */}
      <div className="border rounded-lg">
        <PanelHeader title="简单标题面板" onClose={() => console.log("关闭")} />
        <div className="p-4">
          <p>这是一个简单的标题面板示例</p>
        </div>
      </div>

      {/* Tabs 模式 */}
      <div className="border rounded-lg">
        <PanelHeader
          tabs={tabs}
          activeTab={activeTab}
          onTabChange={setActiveTab}
          extra={
            <Button variant="outline" size="sm">
              额外按钮
            </Button>
          }
          onClose={() => console.log("关闭")}
        />
        <div className="p-4">
          <p>当前选中的 Tab: {activeTab}</p>
        </div>
      </div>

      {/* 带额外内容的标题模式 */}
      <div className="border rounded-lg">
        <PanelHeader
          title="带额外内容的面板"
          extra={
            <div className="flex gap-2">
              <Button variant="outline" size="sm">
                保存
              </Button>
              <Button variant="outline" size="sm">
                重置
              </Button>
            </div>
          }
          onClose={() => console.log("关闭")}
        />
        <div className="p-4">
          <p>这是一个带额外内容的面板示例</p>
        </div>
      </div>
    </div>
  );
};
