import { shopAtom } from "@/atoms/shop";
import { ExtendedDialog } from "@/components/common/extended-dialog";
import { ConfigVersionSelect } from "@/components/common/version-select";
import { EvaluationConfigService } from "@/services/evaluation-config-service";
import { safeParseJson } from "@/utils/safe-parse-json";
import { useQuery, useSuspenseQuery } from "@tanstack/react-query";
import { useAtomValue } from "jotai";
import { memo, useEffect, useState } from "react";
import { ConfigDiffData, ConfigDiffViewer } from "./config-diff-viewer";
import { deepSortKeys } from "@/utils/deep-sort-key";
interface ConfigDiffDialogProps {
  data?: ConfigDiffData;
  title?: string;
  className?: string;
  currentVersionId?: string;
  baseVersionId?: string;
  open?: boolean;
  showVersionSelector?: boolean;
  footer?: React.ReactNode;
  children?: React.ReactNode;
  onOpenChange?: (open: boolean) => void;
  localJSON?: string;
}

export const ConfigDiffDialog = memo(
  ({
    data,
    title = "配置对比",
    className,
    currentVersionId,
    baseVersionId,
    open,
    showVersionSelector = true,
    footer,
    children,
    onOpenChange,
    localJSON = "",
  }: ConfigDiffDialogProps) => {
    const [selectedOldVersion, setSelectedOldVersion] = useState<string>("");
    const [selectedNewVersion, setSelectedNewVersion] = useState<string>("");
    const shop = useAtomValue(shopAtom);
    const { data: versionList } = useSuspenseQuery({
      queryKey: ["getVersionList", shop?.id],
      queryFn: () => {
        return EvaluationConfigService.getVersionList(
          {
            version: "",
          },
          { shopId: shop?.id },
        );
      },
    });

    // 初始化版本选择
    useEffect(() => {
      if (versionList.length > 0) {
        // 优先使用传入的版本ID
        if (currentVersionId) {
          setSelectedNewVersion(currentVersionId);
        }

        if (baseVersionId) {
          setSelectedOldVersion(baseVersionId);
        } else if (currentVersionId) {
          // 如果没有baseVersionId，默认选择第一个不同的版本作为对比版本
          const otherVersion = versionList.find(
            (v) => v.configId !== currentVersionId,
          );
          if (otherVersion) {
            setSelectedOldVersion(otherVersion.configId);
          }
        }
      }
    }, [currentVersionId, baseVersionId, versionList]);

    // 获取选中版本的详情数据
    const { data: oldVersionDetail } = useQuery({
      queryKey: ["getVersionDetail", selectedOldVersion, shop],
      queryFn: () => {
        if (!shop) {
          return null;
        }

        return EvaluationConfigService.getVersionDetail({
          configId: selectedOldVersion,
          shopId: shop.id,
        });
      },
      enabled: !!selectedOldVersion && open,
    });

    const { data: newVersionDetail } = useQuery({
      queryKey: ["getVersionDetail", selectedNewVersion, shop],
      queryFn: () => {
        if (!shop) {
          return null;
        }

        return EvaluationConfigService.getVersionDetail({
          configId: selectedNewVersion,
          shopId: shop.id,
        });
      },
      enabled: !!selectedNewVersion && open,
    });

    const generateComparisonData = (): ConfigDiffData => {
      if (
        !selectedOldVersion ||
        !selectedNewVersion ||
        versionList.length === 0
      ) {
        return data || {};
      }

      if (!oldVersionDetail || !newVersionDetail) {
        return data || {};
      }

      // 从版本详情中解析shopConfig和intentConfig字符串
      const oldShopConfig = safeParseJson(
        localJSON || oldVersionDetail.shopConfig || "",
        {
          defaultValue: {},
        },
      );
      const newShopConfig = safeParseJson(newVersionDetail.shopConfig || "", {
        defaultValue: {},
      });
      const oldIntentConfig = safeParseJson(
        oldVersionDetail.intentConfig || "",
        { defaultValue: {} },
      );
      const newIntentConfig = safeParseJson(
        newVersionDetail.intentConfig || "",
        { defaultValue: {} },
      );

      return {
        shopConfig: {
          old: deepSortKeys(oldShopConfig),
          new: deepSortKeys(newShopConfig),
        },
        intentConfig: {
          old: oldIntentConfig,
          new: newIntentConfig,
        },
      };
    };

    const comparisonData = generateComparisonData();

    return (
      <ExtendedDialog
        open={open}
        onClose={() => onOpenChange?.(false)}
        title={title}
        width="95vw"
        modal={false}
        fullHeight={true}
        className={className}
        description="配置对比"
        contentClassName="overflow-hidden p-0"
        footer={footer}
      >
        <div className="h-full p-6 flex flex-col gap-3">
          {showVersionSelector && (
            <div className="flex items-center gap-4 text-sm font-normal whitespace-nowrap">
              <div className="flex items-center gap-2">
                <span className="text-gray-600">当前版本:</span>
                <div className="w-fit min-w-[260px]">
                  <ConfigVersionSelect
                    value={selectedNewVersion}
                    sideOffset={20}
                    versions={versionList}
                    onChange={setSelectedNewVersion}
                  />
                </div>
              </div>

              <span className="text-gray-400">vs</span>

              <div className="flex items-center gap-2">
                <span className="text-gray-600">对比版本:</span>
                <div className="w-fit min-w-[260px]">
                  <ConfigVersionSelect
                    value={selectedOldVersion}
                    sideOffset={20}
                    versions={versionList}
                    onChange={setSelectedOldVersion}
                  />
                </div>
              </div>
            </div>
          )}

          <ConfigDiffViewer data={comparisonData} className="flex-1 min-h-0" />
        </div>

        {children}
      </ExtendedDialog>
    );
  },
);

ConfigDiffDialog.displayName = "ConfigDiffDialog";
