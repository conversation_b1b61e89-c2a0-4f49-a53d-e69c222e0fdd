import { useMemo } from "react";
import { CurrentEditContext, VariableProvider } from "./contexts";
import { VariableCompletion } from "./extensions";
import { UniversalDataSource } from "./hooks";
import { ModuleDoc, Prompt } from "./nodes";
import { PromptEditor, PromptEditorProps } from "./prompt-editor";

export interface ModulePromptEditorProps extends PromptEditorProps {
  /** 数据源配置（可选，用于变量补全） */
  dataSources?: UniversalDataSource[];
  /** 当前编辑上下文（可选，用于变量影响范围过滤） */
  currentEditContext?: CurrentEditContext;
  /** 变量补全面板容器 DOM，用来解决在弹窗焦点不一致的问题 */
  containerDomGetter?: () => HTMLElement | null;
}

export const ModulePromptEditor = ({
  dataSources = [],
  extensions,
  currentEditContext,
  containerDomGetter,
  ...props
}: ModulePromptEditorProps) => {
  const modulePromptExtensions = useMemo(
    () => [
      ModuleDoc,
      Prompt,
      VariableCompletion.configure({
        containerDomGetter,
      }),
    ],
    [containerDomGetter],
  );
  const allExtensions = useMemo(
    () => [...modulePromptExtensions, ...(extensions || [])],
    [extensions, modulePromptExtensions],
  );

  return (
    <VariableProvider
      dataSources={dataSources}
      currentEditContext={currentEditContext}
    >
      <PromptEditor {...props} extensions={allExtensions} />
    </VariableProvider>
  );
};
