import { cn } from "@/lib/utils";
import CodeBlockLowlight from "@tiptap/extension-code-block-lowlight";
import { Dropcursor } from "@tiptap/extension-dropcursor";
import { Gapcursor } from "@tiptap/extension-gapcursor";
import { History } from "@tiptap/extension-history";
import { UniqueID } from "@tiptap/extension-unique-id";
import {
  Editor,
  EditorContent,
  Extension,
  JSONContent,
  Node,
  useEditor,
} from "@tiptap/react";
import { common, createLowlight } from "lowlight";
import { Ref, useEffect, useImperativeHandle, useMemo } from "react";
import {
  createSlashCommand,
  DiffExtension,
  TrailingNode,
  updateDiffData,
} from "./extensions";
import {
  ListItem,
  OrderedList,
  Paragraph,
  StringVariable,
  Text,
} from "./nodes";
import { computeModuleDiff, DiffOptions } from "./utils";

export interface PromptEditorProps {
  content: JSONContent;
  extensions: (Node<any, any> | Extension<any, any>)[];
  className?: string;
  editable?: boolean;
  /** 是否启用 diff 模式 */
  enableDiff?: boolean;
  /** 用于对比的之前版本内容 */
  previousContent?: JSONContent;
  /** diff 计算选项 */
  diffOptions?: DiffOptions;
  ref?: Ref<Editor | null>;
  onChange?: (content: JSONContent) => void;
}

const lowlight = createLowlight(common);

const baseExtensions = [
  Paragraph,
  Text,
  ListItem,
  OrderedList,
  StringVariable,
  History,
  Gapcursor,
  Dropcursor,
  createSlashCommand(),
  CodeBlockLowlight.configure({
    lowlight,
  }),
  UniqueID,
];

export const PromptEditor = ({
  content,
  className,
  extensions,
  editable = true,
  ref,
  enableDiff = false,
  previousContent,
  diffOptions,
  onChange,
}: PromptEditorProps) => {
  // 计算差异数据
  const diffData = useMemo(() => {
    if (!enableDiff || !previousContent) {
      return null;
    }

    try {
      // 创建临时编辑器来获取文档实例，包含必要的基础扩展
      const allExtensions = [...baseExtensions, ...extensions];

      const tempEditor = new Editor({
        extensions: allExtensions,
        content: previousContent,
        editable: false,
      });

      const currentEditor = new Editor({
        extensions: allExtensions,
        content: content,
        editable: false,
      });

      const oldDoc = tempEditor.state.doc;
      const newDoc = currentEditor.state.doc;

      const result = computeModuleDiff(oldDoc, newDoc, diffOptions);

      // 清理临时编辑器
      tempEditor.destroy();
      currentEditor.destroy();

      return result;
    } catch (error) {
      console.error("计算差异失败:", error);
      return null;
    }
  }, [enableDiff, previousContent, content, extensions, diffOptions]);

  // 创建扩展列表
  const editorExtensions = useMemo(() => {
    const allExtensions = [...baseExtensions, ...extensions];

    // 如果启用 diff 模式，添加 DiffExtension
    if (enableDiff) {
      const diffExtension = DiffExtension.configure({
        enabled: true,
      });

      allExtensions.push(diffExtension);
    } else {
      allExtensions.push(TrailingNode);
    }

    return allExtensions;
  }, [extensions, enableDiff]);

  // 当启用 diff 时，强制设置为只读
  const finalEditable = enableDiff ? false : editable;

  const editor = useEditor({
    extensions: editorExtensions,
    content,
    editable: finalEditable,
    editorProps: {
      attributes: {
        class: cn(
          "prose prose-sm mx-auto focus:outline-none",
          "min-h-full max-w-none",
          "prose-a:text-primary prose-a:no-underline hover:prose-a:underline",
        ),
      },
    },
    onUpdate: ({ editor }) => {
      const json = editor.getJSON();
      onChange?.(json);
    },
  });

  // 更新编辑器中的 diff 数据
  useEffect(() => {
    if (editor && enableDiff) {
      updateDiffData(editor, diffData);
    }
  }, [editor, enableDiff, diffData]);

  useImperativeHandle(ref, () => editor!, [editor]);

  return (
    <div
      className={cn(
        "relative w-full rounded-lg border border-input bg-background outline-none overflow-hidden",
        "focus-within:border-ring focus-within:ring-ring/50 focus-within:ring-[3px] transition-[color,box-shadow]",
        className,
      )}
    >
      <EditorContent
        className="h-full min-h-8 m-1 overflow-auto"
        editor={editor}
      />
    </div>
  );
};
