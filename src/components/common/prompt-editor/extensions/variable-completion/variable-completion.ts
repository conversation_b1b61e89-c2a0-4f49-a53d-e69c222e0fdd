import { Extension } from "@tiptap/core";
import { PluginKey } from "@tiptap/pm/state";
import { <PERSON>act<PERSON>enderer } from "@tiptap/react";
import { Suggestion } from "@tiptap/suggestion";
import tippy, { Instance as TippyInstance } from "tippy.js";
import { UniversalVariable, UseVariableSelectorResult } from "../../hooks";
import { createVariableNodeContent } from "../../utils";
import { VariableCompletionPanel } from "./variable-completion-panel.js";

/**
 * VariableCompletion 扩展的插件键
 */
export const VariableCompletionPluginKey = new PluginKey("variableCompletion");

/**
 * 变量补全选项接口
 */
export interface VariableCompletionOptions {
  /** 触发字符 */
  char: string;
  /** 是否允许空格 */
  allowSpaces: boolean;
  /** 是否只在行首触发 */
  startOfLine: boolean;
  /** 最大查询长度 */
  maxLength: number;
  /** 面板配置 */
  panel: {
    /** 最大显示项目数 */
    maxItems: number;
    /** 面板宽度 */
    width: number;
    /** 最大高度 */
    maxHeight: number;
  };
  /** 变量选择器结果（可选，用于外部数据注入） */
  variableSelectorResult?: UseVariableSelectorResult;
  containerDomGetter?: () => HTMLElement | null;
}

/**
 * 默认扩展选项
 */
const defaultOptions: VariableCompletionOptions = {
  char: "{",
  allowSpaces: true,
  startOfLine: false,
  maxLength: 50,
  panel: {
    maxItems: 10,
    width: 320,
    maxHeight: 400,
  },
};

/**
 * VariableCompletion 扩展
 * 监听 { 字符输入，直接展示变量选择面板
 */
export const VariableCompletion = Extension.create<VariableCompletionOptions>({
  name: "variableCompletion",

  addOptions() {
    return defaultOptions;
  },

  addProseMirrorPlugins() {
    return [
      Suggestion({
        editor: this.editor,
        char: this.options.char,
        allowSpaces: this.options.allowSpaces,
        allowedPrefixes: null,
        startOfLine: this.options.startOfLine,
        pluginKey: VariableCompletionPluginKey,
        command: ({ editor, range, props }) => {
          const variable = props as UniversalVariable;

          try {
            // 验证变量数据完整性
            const name = variable.name;
            const promptName = variable.promptName;

            if (!variable || !promptName || !name) {
              console.error("Invalid variable data:", variable);
              return;
            }

            // 创建变量节点内容
            const nodeContent = createVariableNodeContent(variable);

            // 删除触发字符和查询文本
            editor.chain().focus().deleteRange(range).run();

            // 插入变量节点
            const success = editor
              .chain()
              .focus()
              .insertContent(nodeContent)
              .run();

            if (!success) {
              console.error("Failed to insert variable into editor");
              return;
            }
          } catch (error) {
            console.error("Error inserting variable:", error);
          }
        },

        items: ({ query }) => {
          // 返回一个空数组，实际的变量筛选在面板组件中处理
          // suggestion 插件需要返回数组才能触发面板显示
          return query !== undefined ? [query] : [];
        },

        render: () => {
          let component: ReactRenderer<any>;
          let popup: TippyInstance;

          return {
            onStart: (props) => {
              // 如果当前编辑器没有聚焦，不显示面板
              if (!props.editor.isFocused) {
                return;
              }

              // 创建 React 组件渲染器
              component = new ReactRenderer(VariableCompletionPanel, {
                props: {
                  query: props.query || "",
                  onSelect: (variable: UniversalVariable) => {
                    props.command(variable);
                  },
                  onClose: () => {
                    if (!popup.state.isDestroyed) {
                      popup?.hide();
                    }
                  },
                },
                editor: props.editor,
              });

              // 如果没有 items，不显示面板
              if (!props.items || props.items.length === 0) {
                return;
              }

              // 创建弹出层
              popup = tippy(
                this.options.containerDomGetter?.() || document.body,
                {
                  getReferenceClientRect: () => {
                    const rect = props.clientRect?.();
                    return rect || new DOMRect(0, 0, 0, 0);
                  },
                  content: component.element,
                  showOnCreate: true,
                  interactive: true,
                  trigger: "manual",
                  placement: "bottom-start",
                  theme: "light-border",
                  maxWidth: this.options.panel.width,
                  offset: [0, 8],
                  zIndex: 10000,
                  hideOnClick: true,
                  animation: "shift-away-subtle",
                  duration: [200, 150],
                  onHidden: () => {
                    component?.destroy();
                  },
                  popperOptions: {
                    modifiers: [
                      {
                        name: "flip",
                        options: {
                          fallbackPlacements: ["top-start", "bottom-start"],
                        },
                      },
                      {
                        name: "preventOverflow",
                        options: {
                          boundary: "viewport",
                          padding: 8,
                        },
                      },
                    ],
                  },
                },
              );
            },

            onUpdate: (props) => {
              // 更新组件属性
              component?.updateProps({
                query: props.query || "",
                variableSelectorResult: this.options.variableSelectorResult,
                onSelect: (variable: UniversalVariable) => {
                  props.command(variable);
                },
                onClose: () => {
                  popup?.hide();
                },
              });

              // 如果没有 items，隐藏面板
              if (!props.items || props.items.length === 0) {
                popup?.hide();
                return;
              }

              // 更新弹出层位置
              popup?.setProps({
                getReferenceClientRect: () => {
                  const rect = props.clientRect?.();
                  return rect || new DOMRect(0, 0, 0, 0);
                },
              });
            },

            onKeyDown: (props) => {
              props.event.stopPropagation();

              // 处理键盘事件
              if (props.event.key === "Escape") {
                popup?.hide();
                return true;
              }

              // 将键盘事件传递给组件
              return component?.ref?.onKeyDown?.(props.event) || false;
            },

            onExit: () => {
              // 清理资源
              popup?.destroy();
              component?.destroy();
            },
          };
        },
      }),
    ];
  },

  addKeyboardShortcuts() {
    return {
      // 可以添加额外的键盘快捷键
      "Mod-{": () => {
        // 手动触发变量补全
        const { state, dispatch } = this.editor.view;
        const { selection } = state;
        // 在当前位置插入 '{' 字符来触发面板
        const tr = state.tr.insertText("{", selection.from, selection.to);

        dispatch(tr);

        return true;
      },
    };
  },
});
