import { Editor, Extension } from "@tiptap/core";
import { Plugin, PluginKey } from "@tiptap/pm/state";
import { Decoration, DecorationSet } from "@tiptap/pm/view";

export interface DiffOptions {
  /**
   * 是否启用 diff 模式
   */
  enabled: boolean;
  /**
   * diff 数据
   */
  diffData?: DiffData;
}

export interface DiffData {
  /**
   * 新增的文本范围
   */
  added: DiffRange[];
  /**
   * 删除的文本范围
   */
  removed: DiffRange[];
}

export interface DiffRange {
  /**
   * 起始位置
   */
  from: number;
  /**
   * 结束位置
   */
  to: number;
  /**
   * 原始文本
   */
  originalText?: string;
  /**
   * 新文本
   */
  newText?: string;
  /**
   * 节点类型（用于识别变量节点）
   */
  nodeType?: string;
  /**
   * 是否为变量节点
   */
  isVariable?: boolean;
}

const diffPluginKey = new PluginKey("diff");

export const DiffExtension = Extension.create<DiffOptions>({
  name: "diff",

  addOptions() {
    return {
      enabled: false,
      diffData: undefined,
    };
  },

  addGlobalAttributes() {
    return [
      {
        types: ["textStyle"],
        attributes: {
          diffType: {
            default: null,
            parseHTML: (element) => element.getAttribute("data-diff-type"),
            renderHTML: (attributes) => {
              if (!attributes.diffType) return {};
              return {
                "data-diff-type": attributes.diffType,
                class: `diff-${attributes.diffType}`,
              };
            },
          },
        },
      },
    ];
  },

  addProseMirrorPlugins() {
    return [
      new Plugin({
        key: diffPluginKey,
        state: {
          init: (): {
            decorations: DecorationSet;
            diffData: DiffData | null;
            enabled: boolean;
          } => {
            return {
              decorations: DecorationSet.empty,
              diffData: null,
              enabled: false,
            };
          },
          apply: (
            tr,
            oldState: {
              decorations: DecorationSet;
              diffData: DiffData | null;
              enabled: boolean;
            },
          ) => {
            // 检查是否有 diff 数据更新的 meta
            const diffMeta = tr.getMeta(diffPluginKey) as
              | { diffData?: DiffData; enabled?: boolean }
              | undefined;

            const newState = { ...oldState };

            if (diffMeta) {
              newState.diffData = diffMeta.diffData || null;
              newState.enabled = diffMeta.enabled || false;
            }

            // 如果没有启用或没有 diff 数据，返回空装饰
            if (!newState.enabled || !newState.diffData) {
              return {
                ...newState,
                decorations: DecorationSet.empty,
              };
            }

            const decorations: Decoration[] = [];
            const { diffData } = newState;

            // 创建新增内容的装饰
            diffData.added.forEach((range: DiffRange) => {
              decorations.push(
                Decoration.inline(range.from, range.to, {
                  class: "diff-added",
                }),
              );
            });

            // 创建删除内容的装饰
            diffData.removed.forEach((range: DiffRange) => {
              decorations.push(
                Decoration.widget(range.from, () => {
                  const span = document.createElement("span");
                  span.className = "diff-removed";
                  span.textContent = range.originalText || "";
                  return span;
                }),
              );
            });

            return {
              ...newState,
              decorations: DecorationSet.create(tr.doc, decorations),
            };
          },
        },
        props: {
          decorations(state) {
            return this.getState(state)?.decorations || DecorationSet.empty;
          },
        },
      }),
    ];
  },
});

/**
 * 创建 Diff 扩展的工厂函数
 */
export const createDiffExtension = (options: Partial<DiffOptions> = {}) => {
  return DiffExtension.configure(options);
};

/**
 * 更新编辑器中的 diff 数据
 */
export const updateDiffData = (
  editor: Editor,
  diffData: DiffData | null,
  enabled: boolean = true,
) => {
  const tr = editor.state.tr;

  tr.setMeta(diffPluginKey, { diffData, enabled });

  editor.view.dispatch(tr);
};
