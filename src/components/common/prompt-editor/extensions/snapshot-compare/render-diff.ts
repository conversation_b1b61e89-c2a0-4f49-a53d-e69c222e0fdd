import { EditorState, Plugin, PluginKey } from '@tiptap/pm/state'
import { EditorView } from '@tiptap/pm/view'
import { DecorationSet } from '@tiptap/pm/view'
import { Editor } from '@tiptap/core'
import { RenderDiffParams, MapDiffToDecorationsParams } from './types'
import { defaultMapDiffToDecorations } from './decorations'
import { ChangeTrackingTransform } from './change-tracking-transform'

export function renderDiff({
  editor,
  element,
  tr,
  mapDiffToDecorations = defaultMapDiffToDecorations
}: RenderDiffParams): EditorView {
  const diffs = tr.toDiff()
  
  if (!Array.isArray(diffs)) {
    throw new Error('diffs must be an array')
  }

  const decorations = diffs
    .map((diff) => mapDiffToDecorations({ diff, editor, tr }))
    .filter(Boolean)
    .flat()
    .filter(Boolean)

  const decorationSet = DecorationSet.create(tr.doc, decorations)

  const state = EditorState.create({
    doc: tr.doc,
    plugins: [
      new Plugin({
        key: new PluginKey('diffs'),
        props: {
          decorations: () => decorationSet
        },
        filterTransaction: () => false
      })
    ]
  })

  return new EditorView(element, {
    state,
    attributes: {
      class: 'ProseMirror tiptap'
    }
  })
}
