import { Extension } from '@tiptap/core'
import { Plugin, Plugin<PERSON>ey } from '@tiptap/pm/state'
import { DecorationSet } from '@tiptap/pm/view'
import { SnapshotCompareOptions, CompareVersionsParams, MapDiffToDecorationsParams } from './types'
import { defaultMapDiffToDecorations } from './decorations'
import { getVersions } from './version-provider'
import { getChangesetTransform } from './changeset-transform'

export const snapshotComparePluginKey = new PluginKey('snapshotCompare')

export const SnapshotCompare = Extension.create<SnapshotCompareOptions>({
  name: 'snapshotCompare',

  addOptions() {
    return {
      mapDiffToDecorations: undefined,
      provider: null
    }
  },

  addStorage() {
    return {
      previousContent: null,
      isPreviewing: false,
      diffs: [],
      tr: null
    }
  },

  addCommands() {
    return {
      showDiff: (tr, { diffs } = {}) => ({ chain, dispatch, state }) => {
        if (tr.steps.length === 0) {
          if (dispatch) {
            chain().hideDiff().run()
          }
          return false
        }

        if (dispatch) {
          this.storage.isPreviewing = true
          
          if (!this.storage.previousContent) {
            Object.assign(this.storage, { 
              previousContent: state.doc.toJSON() 
            })
          }
          
          Object.assign(this.storage, { 
            diffs: diffs ?? tr.toDiff(), 
            tr 
          })
          
          chain()
            .setMeta('addToHistory', false)
            .setMeta(snapshotComparePluginKey, true)
            .setContent(tr.doc.toJSON())
            .run()
        }

        return true
      },

      hideDiff: () => ({ chain, dispatch }) => {
        if (!this.storage.previousContent) {
          return false
        }

        if (dispatch) {
          chain()
            .setMeta('addToHistory', false)
            .setMeta(snapshotComparePluginKey, false)
            .setContent(this.storage.previousContent)
            .run()
          
          Object.assign(this.storage, { 
            previousContent: null, 
            isPreviewing: false, 
            diffs: [] 
          })
        }

        return true
      },

      compareVersions: ({
        fromVersion,
        toVersion,
        onCompare,
        hydrateUserData,
        enableDebugging = false,
        field = 'default',
        permanentUserDataMapField = '__tiptapcollab__users'
      }: CompareVersionsParams) => ({ dispatch }) => {
        const provider = this.options.provider

        if (!provider) {
          throw new Error('No provider passed!')
        }

        if (!('getVersions' in provider)) {
          console.error('This provider does not support version history!')
          return false
        }

        if (dispatch) {
          ;(async () => {
            try {
              const versionData = await getVersions({
                provider,
                fromVersion,
                toVersion
              })

              const transform = await getChangesetTransform({
                snapshot: versionData.snapshot,
                prevSnapshot: versionData.prevSnapshot,
                schema: this.editor.schema,
                hydrateUserData,
                permanentUserDataMapField,
                field,
                enableDebugging
              })

              if (!onCompare) {
                this.editor.commands.showDiff(transform)
                return
              }

              onCompare({
                editor: this.editor,
                tr: transform,
                get diffSet() {
                  return transform.toDiff()
                }
              })
            } catch (error) {
              if (!onCompare) {
                this.editor.commands.hideDiff()
                return
              }

              onCompare({ error: error as Error })
            }
          })()
        }

        return true
      }
    }
  },

  addProseMirrorPlugins() {
    const editor = this.editor
    const storage = this.storage

    return [
      new Plugin({
        key: snapshotComparePluginKey,
        state: {
          init: () => ({ decorations: DecorationSet.empty }),
          apply: (tr, state) => {
            const meta = tr.getMeta(snapshotComparePluginKey)

            if (meta === true) {
              if (!storage.diffs || !storage.tr) {
                return state
              }

              const decorations = storage.diffs
                .map((diff) => 
                  (this.options.mapDiffToDecorations || defaultMapDiffToDecorations)({
                    diff,
                    editor,
                    defaultMapDiffToDecorations,
                    tr: storage.tr
                  })
                )
                .flat()
                .filter(Boolean)

              return { decorations: DecorationSet.create(tr.doc, decorations) }
            }

            if (meta === false) {
              return { decorations: DecorationSet.empty }
            }

            return state
          }
        },
        props: {
          decorations(state) {
            return this.getState(state)?.decorations
          }
        }
      })
    ]
  }
})
