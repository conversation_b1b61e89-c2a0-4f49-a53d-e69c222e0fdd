import { Slice, Node, Fragment } from '@tiptap/pm/model'
import { ReplaceStep } from '@tiptap/pm/transform'
import { Attribution } from './types'

export class ChangeTrackingStep extends ReplaceStep {
  public attribution: Attribution

  constructor(from: number, to: number, slice: Slice, attribution: Attribution) {
    super(from, to, slice)
    this.attribution = attribution
  }

  merge(other: any): ChangeTrackingStep | null {
    if (other instanceof ChangeTrackingStep) {
      if (!this.attribution.userId || !other.attribution.userId || 
          this.attribution.userId !== other.attribution.userId) {
        return null
      }

      if (this.from + this.slice.size === other.from && 
          !this.slice.openEnd && !other.slice.openStart) {
        const newSlice = this.slice.size + other.slice.size === 0 
          ? Slice.empty 
          : new Slice(
              this.slice.content.append(other.slice.content),
              this.slice.openStart,
              other.slice.openEnd
            )
        return new ChangeTrackingStep(this.from, this.to + (other.to - other.from), newSlice, this.attribution)
      }

      if (other.to === this.from && 
          !this.slice.openStart && !other.slice.openEnd) {
        const newSlice = this.slice.size + other.slice.size === 0 
          ? Slice.empty 
          : new Slice(
              other.slice.content.append(this.slice.content),
              other.slice.openStart,
              this.slice.openEnd
            )
        return new ChangeTrackingStep(other.from, this.to, newSlice, this.attribution)
      }
    }

    const merged = super.merge(other)
    return merged ? ChangeTrackingStep.fromReplaceStep(merged, this.attribution) : null
  }

  invert(doc: Node): ChangeTrackingStep {
    return ChangeTrackingStep.fromReplaceStep(super.invert(doc), this.attribution)
  }

  map(mapping: any): ChangeTrackingStep | null {
    const mapped = super.map(mapping)
    return mapped ? ChangeTrackingStep.fromReplaceStep(mapped, this.attribution) : null
  }

  toJSON(): any {
    return {
      ...super.toJSON(),
      attribution: JSON.parse(JSON.stringify(this.attribution)),
      stepType: 'changeTrack'
    }
  }

  static toReplaceStep(step: ChangeTrackingStep): ReplaceStep {
    return new ReplaceStep(step.from, step.to, step.slice)
  }

  static fromReplaceStep(step: ReplaceStep, attribution: Attribution): ChangeTrackingStep {
    return new ChangeTrackingStep(step.from, step.to, step.slice, attribution)
  }

  static isChangeTrackingStep(step: any): step is ChangeTrackingStep {
    return step instanceof ChangeTrackingStep
  }

  static fromJSON(schema: any, json: any): ChangeTrackingStep {
    if (typeof json.from !== 'number' || 
        typeof json.to !== 'number' || 
        typeof json.attribution !== 'object') {
      throw new RangeError('Invalid input for ChangeTrackingStep.fromJSON')
    }
    return new ChangeTrackingStep(
      json.from,
      json.to,
      Slice.fromJSON(schema, json.slice),
      json.attribution
    )
  }
}

// Register the step type
;(ChangeTrackingStep as any).jsonID('changeTrack', ChangeTrackingStep)
