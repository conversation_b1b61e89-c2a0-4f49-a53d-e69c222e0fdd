// Main exports
export { SnapshotCompare, snapshotComparePluginKey } from './extension'
export { ChangeTrackingStep } from './change-tracking-step'
export { ChangeTrackingTransform } from './change-tracking-transform'

// Utility functions
export { changesToDiff } from './utils'
export { defaultMapDiffToDecorations, extractAttributeChanges, renderNodeAsHTML } from './decorations'
export { getChangesetTransform } from './changeset-transform'
export { getVersions } from './version-provider'
export { renderDiff } from './render-diff'

// Types
export type {
  Attribution,
  AttributionColor,
  DiffItem,
  SnapshotCompareOptions,
  MapDiffToDecorationsParams,
  GetChangesetTransformParams,
  CompareVersionsParams,
  CompareResult,
  VersionProvider,
  RenderDiffParams,
  AttributeChanges
} from './types'
