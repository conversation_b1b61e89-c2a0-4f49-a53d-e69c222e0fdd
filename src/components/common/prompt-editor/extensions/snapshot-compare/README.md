# SnapshotCompare Extension

A TipTap extension for comparing document snapshots and visualizing changes with attribution tracking.

## Features

- **Version Comparison**: Compare different versions of documents using Y.js snapshots
- **Change Tracking**: Track insertions, deletions, and updates with user attribution
- **Visual Diff**: Display changes with customizable decorations
- **Attribution Support**: Associate changes with specific users
- **Flexible Provider**: Support for custom version providers

## Installation

The extension requires the following dependencies:

```bash
pnpm add yjs y-prosemirror @tiptap/core @tiptap/extension-document @tiptap/extension-paragraph @tiptap/extension-text
```

## Basic Usage

```typescript
import { Editor } from '@tiptap/core'
import { Document } from '@tiptap/extension-document'
import { Paragraph } from '@tiptap/extension-paragraph'
import { Text } from '@tiptap/extension-text'
import { SnapshotCompare } from './snapshot-compare'

// Create a provider that implements the VersionProvider interface
const provider = {
  getVersions: () => [
    { version: 1 },
    { version: 2 },
    { version: 3 }
  ],
  sendStateless: (message: string) => {
    // Send stateless message to server
  },
  on: (event: string, callback: (data: any) => void) => {
    // Listen to events
  },
  off: (event: string, callback: (data: any) => void) => {
    // Remove event listeners
  }
}

const editor = new Editor({
  extensions: [
    Document,
    Paragraph,
    Text,
    SnapshotCompare.configure({
      provider,
      mapDiffToDecorations: ({ diff, editor, defaultMapDiffToDecorations }) => {
        // Custom decoration mapping (optional)
        return defaultMapDiffToDecorations?.({ diff, editor, defaultMapDiffToDecorations })
      }
    })
  ],
  content: '<p>Hello world!</p>'
})
```

## Commands

### compareVersions

Compare two document versions:

```typescript
editor.commands.compareVersions({
  fromVersion: 1,
  toVersion: 2,
  onCompare: (result) => {
    if (result.error) {
      console.error('Comparison failed:', result.error)
      return
    }
    
    console.log('Diff items:', result.diffSet?.length)
  },
  hydrateUserData: (attribution) => ({
    ...attribution,
    displayName: `User ${attribution.userId}`,
    color: '#ff0000'
  }),
  enableDebugging: false
})
```

### showDiff

Display differences in the editor:

```typescript
editor.commands.showDiff(transform, { diffs: customDiffs })
```

### hideDiff

Hide the currently displayed differences:

```typescript
editor.commands.hideDiff()
```

## Types

### Attribution

```typescript
interface Attribution {
  id?: Y.ID
  userId?: string
  type: 'added' | 'removed'
  color?: string | AttributionColor
}
```

### DiffItem

```typescript
interface DiffItem {
  attribution: Attribution
  type: 'inline-insert' | 'block-insert' | 'inline-delete' | 'block-delete' | 'inline-update' | 'block-update'
  from: number
  to: number
  content: Fragment
  previousContent?: Fragment
  previousAttributes?: Record<string, any>
  attributes?: Record<string, any>
}
```

### VersionProvider

```typescript
interface VersionProvider {
  getVersions(): Array<{ version: number }>
  sendStateless(message: string): void
  on(event: string, callback: (data: any) => void): void
  off(event: string, callback: (data: any) => void): void
}
```

## Testing

The extension includes comprehensive unit tests:

```bash
pnpm test src/components/common/prompt-editor/extensions/snapshot-compare/__tests__
```

## Architecture

The extension consists of several key components:

- **ChangeTrackingStep**: Extends ProseMirror's ReplaceStep with attribution information
- **ChangeTrackingTransform**: Manages document transformations with change tracking
- **Decorations**: Handles visual representation of differences
- **Version Provider**: Manages version snapshots and communication
- **Extension**: Main TipTap extension that ties everything together

## Limitations

- Requires Y.js for snapshot management
- Currently uses deprecated y-prosemirror functions (will be updated in future versions)
- Some advanced Y.js features may not be fully supported in the simplified implementation

## Contributing

When contributing to this extension:

1. Ensure all tests pass: `pnpm test`
2. Follow TypeScript best practices
3. Add tests for new functionality
4. Update documentation as needed
