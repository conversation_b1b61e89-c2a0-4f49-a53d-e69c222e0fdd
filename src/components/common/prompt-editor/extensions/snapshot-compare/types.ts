import { Node, Fragment, Slice } from '@tiptap/pm/model'
import { ReplaceStep } from '@tiptap/pm/transform'
import { Decoration } from '@tiptap/pm/view'
import { Editor } from '@tiptap/core'
import * as Y from 'yjs'

export interface Attribution {
  id?: Y.ID
  userId?: string
  type: 'added' | 'removed'
  color?: string | AttributionColor
}

export interface AttributionColor {
  backgroundColor?: string
  color?: string
  insert?: string | AttributionColor
  delete?: string | AttributionColor
  update?: string | AttributionColor
}

export interface DiffItem {
  attribution: Attribution
  type: 'inline-insert' | 'block-insert' | 'inline-delete' | 'block-delete' | 'inline-update' | 'block-update'
  from: number
  to: number
  content: Fragment
  previousContent?: Fragment
  previousAttributes?: Record<string, any>
  attributes?: Record<string, any>
  trimLeft?: number
  trimLeftIndex?: number
  trimRight?: number
  trimRightIndex?: number
}

export interface SnapshotCompareOptions {
  mapDiffToDecorations?: (params: MapDiffToDecorationsParams) => Decoration | Decoration[] | null
  provider?: any
}

export interface MapDiffToDecorationsParams {
  diff: DiffItem
  editor: Editor
  tr?: any
  defaultMapDiffToDecorations?: (params: MapDiffToDecorationsParams) => Decoration | Decoration[] | null
}

export interface GetChangesetTransformParams {
  snapshot: Uint8Array
  prevSnapshot: Uint8Array
  permanentUserDataMapField?: string
  field?: string
  hydrateUserData?: (data: any) => any
  schema: any
  enableDebugging?: boolean
}

export interface CompareVersionsParams {
  fromVersion: number
  toVersion?: number
  onCompare?: (result: CompareResult) => void
  hydrateUserData?: (data: any) => any
  enableDebugging?: boolean
  field?: string
  permanentUserDataMapField?: string
}

export interface CompareResult {
  editor?: Editor
  tr?: any
  diffSet?: DiffItem[]
  error?: Error
}

export interface VersionProvider {
  getVersions(): Array<{ version: number }>
  sendStateless(message: string): void
  on(event: string, callback: (data: any) => void): void
  off(event: string, callback: (data: any) => void): void
}

export interface RenderDiffParams {
  editor: Editor
  element: HTMLElement
  tr: any
  mapDiffToDecorations?: (params: MapDiffToDecorationsParams) => Decoration | Decoration[] | null
}

export interface AttributeChanges {
  before: Record<string, any>
  after: Record<string, any>
  changedAttributes: string[]
  isDiffing: boolean
}
