import { Editor } from '@tiptap/core'
import { Document } from '@tiptap/extension-document'
import { Paragraph } from '@tiptap/extension-paragraph'
import { Text } from '@tiptap/extension-text'
import { SnapshotCompare } from './index'
import * as Y from 'yjs'

// Example usage of the SnapshotCompare extension
export function createEditorWithSnapshotCompare() {
  // Mock provider for demonstration
  const mockProvider = {
    getVersions: () => [
      { version: 1 },
      { version: 2 },
      { version: 3 }
    ],
    sendStateless: (message: string) => {
      console.log('Sending stateless message:', message)
    },
    on: (event: string, callback: (data: any) => void) => {
      console.log('Listening to event:', event)
    },
    off: (event: string, callback: (data: any) => void) => {
      console.log('Removing listener for event:', event)
    }
  }

  // Create editor with SnapshotCompare extension
  const editor = new Editor({
    extensions: [
      Document,
      Paragraph,
      Text,
      SnapshotCompare.configure({
        provider: mockProvider,
        mapDiffToDecorations: ({ diff, editor, defaultMapDiffToDecorations }) => {
          // Custom decoration mapping
          console.log('Mapping diff to decorations:', diff.type)
          return defaultMapDiffToDecorations?.({ diff, editor, defaultMapDiffToDecorations })
        }
      })
    ],
    content: '<p>Hello world!</p>'
  })

  return editor
}

// Example of comparing versions
export async function compareVersionsExample() {
  const editor = createEditorWithSnapshotCompare()

  // Compare versions 1 and 2
  editor.commands.compareVersions({
    fromVersion: 1,
    toVersion: 2,
    onCompare: (result) => {
      if (result.error) {
        console.error('Comparison failed:', result.error)
        return
      }

      console.log('Comparison successful!')
      console.log('Diff items:', result.diffSet?.length)
      
      // Show the diff in the editor
      if (result.tr) {
        editor.commands.showDiff(result.tr)
      }
    },
    hydrateUserData: (attribution) => ({
      ...attribution,
      displayName: `User ${attribution.userId}`,
      color: attribution.userId === 'user1' ? '#ff0000' : '#00ff00'
    }),
    enableDebugging: true
  })

  // Hide diff after 5 seconds
  setTimeout(() => {
    editor.commands.hideDiff()
  }, 5000)

  return editor
}

// Example of creating snapshots for testing
export function createTestSnapshots() {
  // Create two Y.Doc instances
  const doc1 = new Y.Doc()
  const doc2 = new Y.Doc()

  // Add content to doc1
  const fragment1 = doc1.get('default', Y.XmlFragment)
  // Note: In a real implementation, you would add proper content here
  
  // Create doc2 with additional content
  Y.applyUpdateV2(doc2, Y.encodeStateAsUpdateV2(doc1))
  const fragment2 = doc2.get('default', Y.XmlFragment)
  // Note: In a real implementation, you would modify content here

  return {
    snapshot1: Y.encodeStateAsUpdateV2(doc1),
    snapshot2: Y.encodeStateAsUpdateV2(doc2)
  }
}

// Example of using getChangesetTransform directly
export async function directTransformExample() {
  const { snapshot1, snapshot2 } = createTestSnapshots()
  
  // Create a simple schema
  const schema = {
    nodeFromJSON: (json: any) => ({
      type: json.type,
      content: json.content || [],
      attrs: json.attrs || {}
    })
  }

  try {
    const { getChangesetTransform } = await import('./changeset-transform')
    
    const transform = await getChangesetTransform({
      snapshot: snapshot2,
      prevSnapshot: snapshot1,
      schema,
      hydrateUserData: (attribution) => ({
        ...attribution,
        displayName: `User ${attribution.userId}`
      })
    })

    console.log('Transform created with', transform.steps.length, 'steps')
    console.log('Diff items:', transform.toDiff().length)

    return transform
  } catch (error) {
    console.error('Transform creation failed:', error)
    throw error
  }
}

// Export for use in other files
export { SnapshotCompare }
