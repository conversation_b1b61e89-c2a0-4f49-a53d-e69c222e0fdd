import { Decoration, DecorationSet } from "@tiptap/pm/view";
import { <PERSON><PERSON><PERSON>, <PERSON>lug<PERSON>, Plugin<PERSON><PERSON> } from "@tiptap/pm/state";
import { EditorView } from "@tiptap/pm/view";
import { Fragment, DOMSerializer } from "@tiptap/pm/model";
import { Editor } from "@tiptap/core";
import {
  DiffItem,
  AttributionColor,
  MapDiffToDecorationsParams,
  AttributeChanges,
} from "./types";

// Type mappings
const DIFF_TYPE_TO_ACTION = {
  "inline-insert": "insert",
  "block-insert": "insert",
  "inline-delete": "delete",
  "block-delete": "delete",
  "inline-update": "update",
  "block-update": "update",
} as const;

const DIFF_TYPE_TO_TAG = {
  "inline-insert": "span",
  "block-insert": "div",
  "inline-delete": "span",
  "block-delete": "div",
  "inline-update": "span",
  "block-update": "div",
} as const;

// Render node content as HTML
export function renderNodeAsHTML({
  node,
  editor,
  pos = 0,
}: {
  node: Fragment;
  editor: Editor;
  pos?: number;
}): DocumentFragment {
  const nodeViews = editor.extensionManager.nodeViews;
  const fragment = new DocumentFragment();
  let currentPos = pos;

  Fragment.from(node).forEach((child) => {
    if (!(child.type.name in nodeViews)) {
      fragment.appendChild(
        DOMSerializer.fromSchema(editor.schema).serializeNode(child),
      );
      currentPos += child.nodeSize;
      return;
    }

    const nodeView = nodeViews[child.type.name](
      child,
      editor.view,
      () => currentPos,
      [],
      DecorationSet.empty,
    );

    if (nodeView.contentDOM && nodeView.contentDOM.appendChild) {
      nodeView.contentDOM.appendChild(
        renderNodeAsHTML({ node: child.content, editor, pos: currentPos }),
      );
    }

    fragment.appendChild(nodeView.dom);
    currentPos += child.nodeSize;
  });

  return fragment;
}

// Generate CSS styles from color configuration
function generateColorStyles(
  color: string | AttributionColor | undefined,
  diffType: string,
): string {
  let styles = "";

  if (!color) {
    return styles;
  }

  if (typeof color === "string" && color.startsWith("#")) {
    return `background-color: ${color};`;
  }

  if (typeof color === "object") {
    if (color.backgroundColor) {
      styles += `background-color: ${color.backgroundColor};`;
    }
    if (color.color) {
      styles += `color: ${color.color};`;
    }

    // Check for specific diff type styles
    const action =
      DIFF_TYPE_TO_ACTION[diffType as keyof typeof DIFF_TYPE_TO_ACTION];
    if (action && color[action]) {
      styles += generateColorStyles(color[action], diffType);
    }
  }

  return styles;
}

// Create DOM element for diff visualization
function createDiffElement({
  tag,
  className,
  diff,
  getAsHTML,
  editor,
  color,
  attributes,
  isEmpty = false,
}: {
  tag: string;
  className: string;
  diff: DiffItem;
  getAsHTML: (params: { diff: DiffItem; editor: Editor }) => DocumentFragment;
  editor: Editor;
  color?: string | AttributionColor;
  attributes: Record<string, string>;
  isEmpty?: boolean;
}): HTMLElement {
  const element = document.createElement(tag);

  if (color) {
    element.setAttribute("style", generateColorStyles(color, diff.type));
  }

  element.setAttribute("class", className);

  Object.keys(attributes).forEach((key) => {
    element.setAttribute(key, attributes[key]);
  });

  element.dataset.diffType = diff.type;
  if (diff.attribution.userId) {
    element.dataset.diffUserId = diff.attribution.userId;
  }

  if (!isEmpty) {
    element.appendChild(getAsHTML({ diff, editor }));
  }

  return element;
}

// Extract attribute changes from decorations
export function extractAttributeChanges(
  decorations: Decoration[],
): AttributeChanges {
  if (decorations.length === 0) {
    return {
      before: {},
      after: {},
      changedAttributes: [],
      isDiffing: false,
    };
  }

  const before: Record<string, any> = {};
  const after: Record<string, any> = {};
  const changedAttributes = new Set<string>();
  let isDiffing = false;

  decorations.forEach((decoration) => {
    const { diff } = decoration.spec;
    if (!diff?.type) return;

    if (diff.type === "inline-update" || diff.type === "block-update") {
      isDiffing = true;

      Object.keys(diff.previousAttributes).forEach((attr) => {
        if (!(attr in before)) {
          before[attr] = diff.previousAttributes[attr];
          after[attr] = diff.previousAttributes[attr];
        }

        if (diff.previousAttributes[attr] !== diff.attributes[attr]) {
          changedAttributes.add(attr);
          before[attr] = diff.previousAttributes[attr];
          after[attr] = diff.attributes[attr];
        }
      });

      Object.keys(diff.attributes).forEach((attr) => {
        if (diff.previousAttributes[attr] !== diff.attributes[attr]) {
          changedAttributes.add(attr);
          before[attr] = diff.previousAttributes[attr];
          after[attr] = diff.attributes[attr];
        }
      });
    }
  });

  return {
    before,
    after,
    changedAttributes: Array.from(changedAttributes),
    isDiffing,
  };
}

// Default decoration mapping function
export function defaultMapDiffToDecorations({
  diff,
  editor,
  tr,
  attributes = {},
}: MapDiffToDecorationsParams & {
  tr?: any;
  attributes?: Record<string, string>;
}): Decoration | Decoration[] | null {
  const getAsHTML = () =>
    renderNodeAsHTML({ node: diff.content, editor, pos: diff.from });

  const className = `diff ${DIFF_TYPE_TO_ACTION[diff.type]} ${diff.type}`;
  const elementAttributes = {
    class: className,
    style: diff.attribution.color
      ? generateColorStyles(diff.attribution.color, diff.type)
      : undefined,
    nodeName: DIFF_TYPE_TO_TAG[diff.type],
    "data-diff-type": diff.type,
    "data-diff-user-id": diff.attribution.userId || "",
    ...attributes,
  };

  const spec = { diff };

  switch (diff.type) {
    case "inline-delete":
    case "block-delete":
      return Decoration.widget(
        diff.from,
        createDiffElement({
          tag: DIFF_TYPE_TO_TAG[diff.type],
          className,
          diff,
          getAsHTML,
          editor,
          color: diff.attribution.color,
          attributes,
        }),
        spec,
      );

    case "inline-update":
    case "block-update": {
      if (!tr) return null;
      const node = tr.doc.nodeAt(diff.from);
      return node
        ? Decoration.node(
            diff.from,
            diff.from + node.nodeSize,
            elementAttributes,
            spec,
          )
        : null;
    }

    case "inline-insert":
    case "block-insert": {
      const decorations: Decoration[] = [];

      diff.content.forEach((child, offset, index) => {
        // Add line break decorations for block inserts
        const lineBreakDecorations = createLineBreakDecorations({
          node: child,
          diff,
          offset,
          tr,
          getAsHTML,
          editor,
          attributes,
          spec,
        });
        decorations.push(...lineBreakDecorations);

        // Calculate positions with trimming
        const startTrim =
          diff.trimLeft && index === diff.trimLeftIndex ? diff.trimLeft + 1 : 0;
        const endTrim =
          diff.trimRight && index === diff.trimRightIndex ? diff.trimRight : 0;
        const from = diff.from + offset + startTrim;
        const to = diff.from + offset + child.nodeSize - endTrim;

        if (from >= to) return;

        // Add appropriate decoration based on node type
        if (
          !child.isBlock ||
          (diff.trimLeft && index === diff.trimLeftIndex) ||
          (diff.trimRight && index === diff.trimRightIndex)
        ) {
          decorations.push(
            Decoration.inline(from, to, elementAttributes, spec),
          );
        } else if (child.content.size || child.type.name !== "paragraph") {
          decorations.push(Decoration.node(from, to, elementAttributes, spec));
        }
      });

      return decorations;
    }

    default:
      throw new Error(`Unknown diff type: ${diff.type}`);
  }
}

// Create line break decorations for block elements
function createLineBreakDecorations({
  node,
  diff,
  offset,
  tr,
  getAsHTML,
  editor,
  attributes,
  spec,
}: {
  node: any;
  diff: DiffItem;
  offset: number;
  tr?: any;
  getAsHTML: () => DocumentFragment;
  editor: Editor;
  attributes: Record<string, string>;
  spec: any;
}): Decoration[] {
  const decorations: Decoration[] = [];

  if (!node.isBlock && diff.type !== "block-insert") {
    return decorations;
  }

  let pos = diff.from + offset - 1;
  while (pos > 0 && !tr?.doc.resolve(pos).parent.inlineContent) {
    pos -= 1;
  }

  if (pos <= 0) {
    return decorations;
  }

  const resolved = tr?.doc.resolve(pos);
  if (pos !== resolved?.end()) {
    return decorations;
  }

  const parent = resolved?.parent;
  const isEmpty = parent?.type.name === "paragraph" && !parent.content.size;

  decorations.push(
    Decoration.widget(
      pos,
      createDiffElement({
        tag: "span",
        className: `diff line-break ${isEmpty ? "line-break--empty-paragraph" : ""}`,
        diff,
        getAsHTML,
        editor,
        color: diff.attribution.color,
        attributes,
        isEmpty: true,
      }),
      spec,
    ),
  );

  return decorations;
}

  const resolved = tr?.doc.resolve(pos);
  if (pos !== resolved?.end()) {
    return decorations;
  }

  const parent = resolved?.parent;
  const isEmpty = parent?.type.name === "paragraph" && !parent.content.size;

  decorations.push(
    Decoration.widget(
      pos,
      createDiffElement({
        tag: "span",
        className: `diff line-break ${isEmpty ? "line-break--empty-paragraph" : ""}`,
        diff,
        getAsHTML,
        editor,
        color: diff.attribution.color,
        attributes,
        isEmpty: true,
      }),
      spec,
    ),
  );

  return decorations;
}
