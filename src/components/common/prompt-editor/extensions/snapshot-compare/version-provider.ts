import * as Y from 'yjs'
import { VersionProvider } from './types'

export interface VersionData {
  version: number
  snapshot: Uint8Array
  prevSnapshot: Uint8Array
}

// Get version snapshots from provider
export async function getVersions({
  provider,
  fromVersion,
  toVersion
}: {
  provider: VersionProvider
  fromVersion: number
  toVersion?: number
}): Promise<VersionData> {
  const versions = provider.getVersions()
  const actualToVersion = toVersion ?? versions.length - 1
  
  const minVersion = Math.min(fromVersion, actualToVersion)
  const maxVersion = Math.max(fromVersion, actualToVersion)
  
  const fromVersionData = versions.find(v => v.version === minVersion)
  const toVersionData = versions.find(v => v.version === maxVersion)
  
  if (fromVersionData === toVersionData || !fromVersionData || !toVersionData) {
    throw new Error('Versions are the same!')
  }
  
  // Request version previews
  provider.sendStateless(JSON.stringify({ 
    action: 'version.preview', 
    version: toVersionData.version 
  }))
  provider.sendStateless(JSON.stringify({ 
    action: 'version.preview', 
    version: fromVersionData.version 
  }))
  
  return new Promise((resolve, reject) => {
    let toSnapshot: Uint8Array | null = null
    let fromSnapshot: Uint8Array | null = null
    
    const timeout = setTimeout(() => {
      reject(new Error('Operation timed out'))
    }, 10000)
    
    function handleStateless(event: any) {
      const data = JSON.parse(event.payload)
      
      if (data.event === 'version.preview' && 
          (data.version === toVersionData.version || data.version === fromVersionData.version)) {
        
        const ydocBase64 = data.ydoc
        const binaryString = atob(ydocBase64)
        const length = binaryString.length
        const bytes = new Uint8Array(length)
        
        for (let i = 0; i < length; i++) {
          bytes[i] = binaryString.charCodeAt(i)
        }
        
        if (data.version === toVersionData.version) {
          toSnapshot = bytes
        } else {
          fromSnapshot = bytes
        }
        
        if (toSnapshot && fromSnapshot) {
          provider.off('stateless', handleStateless)
          clearTimeout(timeout)
          resolve({
            version: maxVersion,
            snapshot: Y.convertUpdateFormatV1ToV2(toSnapshot),
            prevSnapshot: Y.convertUpdateFormatV1ToV2(fromSnapshot)
          })
        }
      }
    }
    
    provider.on('stateless', handleStateless)
  })
}
