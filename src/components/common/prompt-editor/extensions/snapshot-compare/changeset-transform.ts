import { Node } from "@tiptap/pm/model";
import {
  yDocToProsemirrorJSON,
  yXmlFragmentToProsemirrorJSON,
} from "y-prosemirror";
import * as Y from "yjs";
import { ChangeTrackingTransform } from "./change-tracking-transform";
import { GetChangesetTransformParams, Attribution } from "./types";

// Check if Y.Item is deleted in snapshot
function isDeleted(item: Y.Item | null, snapshot?: Y.Snapshot): boolean {
  return (
    item !== null &&
    (snapshot === undefined
      ? !item.deleted
      : snapshot.sv.has(item.id.client) &&
        snapshot.sv.get(item.id.client)! > item.id.clock &&
        !Y.isDeleted(snapshot.ds, item.id))
  );
}

// Process Y.XmlElement content
function processXmlElement(
  xmlElement: Y.XmlElement,
  schema: any,
  currentSnapshot: Y.Snapshot,
  prevSnapshot: Y.Snapshot,
  transform: ChangeTrackingTransform,
  offset: number,
  createAttribution: (type: string, id?: Y.ID) => Attribution,
): number {
  const currentNode = transform.doc.nodeAt(offset);
  const isSameNodeType = currentNode?.type.name === xmlElement.nodeName;
  const visibleInPrev = isDeleted(xmlElement._item, prevSnapshot);
  const visibleInCurrent = isDeleted(xmlElement._item, currentSnapshot);

  if (visibleInCurrent && visibleInPrev) {
    if (isSameNodeType) {
      // Update attributes
      const newAttrs = xmlElement.getAttributes(currentSnapshot);
      const oldAttrs = currentNode!.attrs;

      Object.keys(newAttrs)
        .filter((key) => oldAttrs[key] !== newAttrs[key])
        .forEach((key) => {
          const attrId = xmlElement._map.get(key)?.id;
          transform.setNodeAttributeWithAttribution(
            offset,
            key,
            newAttrs[key],
            createAttribution("added", attrId),
          );
        });

      Object.keys(oldAttrs).forEach((key) => {
        if (newAttrs[key] === undefined) {
          const attrId = xmlElement._map.get(key)?.id;
          transform.setNodeAttributeWithAttribution(
            offset,
            key,
            undefined,
            createAttribution("removed", attrId),
          );
        }
      });

      offset += 1;
      offset += currentNode!.isAtom && !currentNode!.isText ? 0 : 1;
    } else {
      offset = insertXmlElement(
        xmlElement,
        schema,
        currentSnapshot,
        transform,
        offset,
        createAttribution,
      );
    }
  } else if (visibleInCurrent && !visibleInPrev) {
    if (isSameNodeType) {
      transform.deleteWithAttribution(
        offset,
        offset + currentNode!.nodeSize,
        createAttribution("removed", xmlElement._item?.id),
      );
    }
  } else if (!visibleInCurrent && visibleInPrev) {
    offset = insertXmlElement(
      xmlElement,
      schema,
      currentSnapshot,
      transform,
      offset,
      createAttribution,
    );
  }

  return offset;
}

// Insert XML element as new node
function insertXmlElement(
  xmlElement: Y.XmlElement,
  schema: any,
  snapshot: Y.Snapshot,
  transform: ChangeTrackingTransform,
  offset: number,
  createAttribution: (type: string, id?: Y.ID) => Attribution,
): number {
  const { content } = yXmlFragmentToProsemirrorJSON(xmlElement);
  const node = schema.nodeFromJSON({
    type: xmlElement.nodeName,
    attrs: xmlElement.getAttributes(snapshot),
    content: content.flat(1),
  });

  transform.insertWithAttribution(
    offset,
    node,
    createAttribution("added", xmlElement._item?.id),
  );

  return offset + node.nodeSize;
}

export async function getChangesetTransform({
  snapshot,
  prevSnapshot,
  permanentUserDataMapField = "__tiptapcollab__users",
  field = "default",
  hydrateUserData,
  schema,
  enableDebugging = false,
}: GetChangesetTransformParams): Promise<ChangeTrackingTransform> {
  if (
    !(snapshot instanceof Uint8Array) ||
    !(prevSnapshot instanceof Uint8Array)
  ) {
    throw new Error("Expected both snapshots to be v2 updates");
  }

  // Create history document and apply snapshots
  const historyDoc = new Y.Doc({ gc: false });
  Y.applyUpdateV2(historyDoc, prevSnapshot);
  const prevSnapshotData = Y.snapshot(historyDoc);
  const initialDoc = Node.fromJSON(
    schema,
    yDocToProsemirrorJSON(historyDoc, field),
  );
  Y.applyUpdateV2(historyDoc, snapshot);
  const currentSnapshotData = Y.snapshot(historyDoc);

  return new Promise((resolve, reject) => {
    let transform = new ChangeTrackingTransform(initialDoc);
    const permanentUserData = new Y.PermanentUserData(
      historyDoc,
      historyDoc.getMap(permanentUserDataMapField),
    );

    historyDoc.transact((transaction) => {
      try {
        // Process deleted structures
        if (permanentUserData) {
          permanentUserData.dss.forEach((ds) => {
            Y.iterateDeletedStructs(transaction, ds, () => {});
          });
        }

        // Create attribution function
        const createAttribution = (type: string, id?: Y.ID): Attribution => {
          let userData: any;

          if (id && permanentUserData) {
            if (type === "removed") {
              userData = permanentUserData.getUserByDeletedId(id);
            }
            if (type !== "added" && userData) {
              // Use existing userData
            } else {
              userData = permanentUserData.getUserByClientId(id.client);
            }
          }

          const attribution = { id, userId: userData, type };

          return {
            ...attribution,
            ...(hydrateUserData?.(attribution) || {}),
          };
        };

        // Process document changes
        Y.typeListToArraySnapshot(
          historyDoc.get(field),
          new Y.Snapshot(prevSnapshotData.ds, currentSnapshotData.sv),
        ).reduce((offset, item) => {
          if (
            !item._item.deleted ||
            isDeleted(item._item, currentSnapshotData) ||
            isDeleted(item._item, prevSnapshotData)
          ) {
            return processXmlElement(
              item,
              schema,
              currentSnapshotData,
              prevSnapshotData,
              transform,
              offset,
              createAttribution,
            );
          }
          return offset;
        }, 0);

        transform = transform.simplify();
        resolve(transform);
      } catch (error) {
        reject(error);
      }
    });
  });
}
