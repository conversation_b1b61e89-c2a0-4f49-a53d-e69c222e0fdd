import { Node } from "@tiptap/pm/model";
import {
  yDocToProsemirrorJSON,
  yXmlFragmentToProsemirrorJSON,
} from "y-prosemirror";
import * as Y from "yjs";
import { ChangeTrackingTransform } from "./change-tracking-transform";
import { GetChangesetTransformParams, Attribution } from "./types";
import { log, group, groupCollapsed, groupEnd } from "./utils";

// Check if Y.Item is deleted in snapshot
function isDeleted(item: Y.Item | null, snapshot?: Y.Snapshot): boolean {
  return (
    item !== null &&
    (snapshot === undefined
      ? !item.deleted
      : snapshot.sv.has(item.id.client) &&
        snapshot.sv.get(item.id.client)! > item.id.clock &&
        !Y.isDeleted(snapshot.ds, item.id))
  );
}

// Process Y.XmlText content
function processXmlText(
  xmlText: Y.XmlText,
  schema: any,
  doc: Node,
  currentSnapshot: Y.Snapshot,
  prevSnapshot: Y.Snapshot,
  transform: ChangeTrackingTransform,
  offset: number,
  createAttribution: (type: string, id?: Y.ID) => Attribution,
): number {
  groupCollapsed(`textNodes for "${xmlText.toString()}"`);
  log({ initialOffset: offset, state: transform.doc.toString() });

  const delta = xmlText.toDelta(
    currentSnapshot,
    prevSnapshot,
    createAttribution,
  );

  // Find next content string item
  const findNextContentString = (item: Y.Item | null): Y.Item | null => {
    do {
      if (!item) return null;
      item = item.right;
      if (item) {
        log("intermediate", item, item.content.getContent().join(""), {
          isContentString: item.content.constructor === Y.ContentString,
          isVisible: isDeleted(item, currentSnapshot),
        });
      }
    } while (item && item.content.constructor !== Y.ContentString);
    return item;
  };

  log(JSON.stringify(delta));

  try {
    let currentYItem =
      xmlText._start?.content.constructor !== Y.ContentString
        ? findNextContentString(xmlText._start)
        : xmlText._start;
    let itemOffset = 0;

    for (let i = 0; i < delta.length; i++) {
      const deltaOp = delta[i];
      log({
        delta: deltaOp,
        currentYItem,
        offset,
        cur: transform.doc.toString(),
      });

      const marks: any[] = [];

      // Process attributes to create marks
      if (deltaOp.attributes) {
        Object.keys(deltaOp.attributes).forEach((attr) => {
          if (attr !== "ychange") {
            let markType = attr;
            // Handle nested mark types
            while (markType.includes("--") && !schema.marks[markType]) {
              const parts = markType.split("--");
              if (parts.length <= 1 && parts[0] === "") break;
              parts.pop();
              markType = parts.join("--");
            }
            if (schema.marks[markType]) {
              marks.push(schema.mark(markType, deltaOp.attributes[attr]));
            }
          }
        });
      }

      // Handle ychange operations
      if (deltaOp.attributes?.ychange) {
        log(deltaOp.attributes.ychange);

        switch (deltaOp.attributes.ychange.type) {
          case "removed":
            log("removing", offset, deltaOp.insert);
            try {
              log("before", transform.doc.toJSON());
              transform.deleteWithAttribution(
                offset,
                offset + deltaOp.insert.length,
                deltaOp.attributes.ychange,
              );
              offset -= deltaOp.insert.length;
              log("at", offset, transform.doc.toJSON());
            } catch (error) {
              log("oops", error);
              groupEnd();
              throw error;
            }
            break;

          case "added":
            log("adding", offset, deltaOp.insert);
            try {
              log("before", transform.doc.toJSON());
              transform.replaceWithAttribution(
                offset,
                offset,
                schema.text(deltaOp.insert, marks),
                deltaOp.attributes.ychange,
              );
              log("at", offset, transform.doc.toJSON());
            } catch (error) {
              log("oops", error);
              groupEnd();
              throw error;
            }
            break;

          default:
            throw new Error(
              `Unexpected ychange type: ${deltaOp.attributes.ychange.type}`,
            );
        }
      } else {
        // Check if content matches current document
        const currentContent = transform.doc.slice(
          offset,
          offset + deltaOp.insert.length,
        ).content;
        const expectedContent = schema.text(deltaOp.insert, marks);

        if (!currentContent.eq(expectedContent.content)) {
          log("replacing", offset, deltaOp.insert, currentYItem);

          do {
            const textSlice = deltaOp.insert.slice(0, currentYItem?.length);
            let moved = false;

            if (
              currentYItem?.content.getContent().join("") === textSlice &&
              currentYItem?.left
            ) {
              log("no change in content", currentYItem);
              currentYItem = currentYItem.left;
              moved = true;
              log("moved to", currentYItem);
            }

            const attribution = createAttribution("added", currentYItem?.id);

            if (moved) {
              currentYItem = currentYItem?.right ?? null;
            }

            transform.replaceWithAttribution(
              offset,
              offset + textSlice.length,
              schema.text(textSlice, marks),
              attribution,
            );

            log("at", offset, transform.doc.toString());

            deltaOp.insert = deltaOp.insert.slice(textSlice.length);
            offset += textSlice.length;
            currentYItem = findNextContentString(currentYItem);
            itemOffset = 0;
          } while (
            currentYItem &&
            deltaOp.insert.length >= currentYItem.length
          );

          continue;
        }
      }

      itemOffset += deltaOp.insert.length;

      if (currentYItem && itemOffset >= currentYItem.length) {
        log(
          "moving to next YItem from",
          currentYItem,
          currentYItem?.content.getContent().join(""),
        );
        itemOffset = currentYItem.length - itemOffset;
        currentYItem = findNextContentString(currentYItem.right);
        log(
          "moved to next YItem",
          currentYItem,
          currentYItem?.content.getContent().join(""),
        );
      }

      offset += deltaOp.insert.length;
    }

    groupEnd();
    log("offset is now", offset, {
      cur: transform.doc.nodeAt(offset)?.toString(),
      prev: transform.doc.nodeAt(offset - 1)?.toString(),
      next: transform.doc.nodeAt(offset + 1)?.toString(),
    });

    return offset;
  } catch (error) {
    log(error);
    groupEnd();
    throw error;
  }
}

// Process Y.XmlElement content
function processXmlElement(
  xmlElement: Y.XmlElement,
  schema: any,
  doc: Node,
  currentSnapshot: Y.Snapshot,
  prevSnapshot: Y.Snapshot,
  transform: ChangeTrackingTransform,
  offset: number,
  createAttribution: (type: string, id?: Y.ID) => Attribution,
): number {
  const nodeName = `createNode "${xmlElement.nodeName}" starting at ${offset}`;
  group(nodeName);

  log({
    initialOffset: offset,
    doc: transform.doc.toString(),
    prevNode: transform.doc.nodeAt(Math.max(offset - 1, 0))?.toString(),
    currentNode: transform.doc.nodeAt(offset)?.toString(),
    nextNode: transform.doc
      .nodeAt(Math.min(offset + 1, transform.doc.nodeSize - 2))
      ?.toString(),
  });

  const currentNode = transform.doc.nodeAt(offset);
  const isSameNodeType = currentNode?.type.name === xmlElement.nodeName;
  const visibleInPrev = isDeleted(xmlElement._item, prevSnapshot);
  const visibleInCurrent = isDeleted(xmlElement._item, currentSnapshot);

  if (visibleInCurrent && visibleInPrev) {
    log("visible in both snapshots", currentNode, xmlElement.nodeName);

    if (isSameNodeType) {
      // Update attributes
      const newAttrs = xmlElement.getAttributes(currentSnapshot);
      const oldAttrs = currentNode!.attrs;

      Object.keys(newAttrs)
        .filter((key) => oldAttrs[key] !== newAttrs[key])
        .forEach((key) => {
          const attrId = xmlElement._map.get(key)?.id;
          transform.setNodeAttributeWithAttribution(
            offset,
            key,
            newAttrs[key],
            createAttribution("added", attrId),
          );
        });

      Object.keys(oldAttrs).forEach((key) => {
        if (newAttrs[key] === undefined) {
          const attrId = xmlElement._map.get(key)?.id;
          transform.setNodeAttributeWithAttribution(
            offset,
            key,
            undefined,
            createAttribution("removed", attrId),
          );
        }
      });

      // Process children
      offset = processChildren(
        xmlElement,
        schema,
        doc,
        currentSnapshot,
        prevSnapshot,
        transform,
        offset + 1,
        createAttribution,
      );

      offset += currentNode!.isAtom && !currentNode!.isText ? 0 : 1;
    } else {
      offset = insertXmlElement(
        xmlElement,
        schema,
        currentSnapshot,
        transform,
        offset,
        createAttribution,
      );
    }
  } else if (visibleInCurrent && !visibleInPrev) {
    log("visible in prev, not in current", currentNode, xmlElement.nodeName);

    if (isSameNodeType) {
      transform.deleteWithAttribution(
        offset,
        offset + currentNode!.nodeSize,
        createAttribution("removed", xmlElement._item?.id),
      );
    }
  } else if (!visibleInCurrent && visibleInPrev) {
    log(
      "not visible in prev, visible in current",
      currentNode,
      xmlElement.nodeName,
    );
    offset = insertXmlElement(
      xmlElement,
      schema,
      currentSnapshot,
      transform,
      offset,
      createAttribution,
    );
  } else {
    log("not visible in either snapshot");
    throw new Error(
      `Unexpected case: node "${xmlElement.nodeName}" not visible in either snapshot`,
    );
  }

  log(
    "done with children, offset is now",
    offset,
    transform.doc.toString(),
    transform.doc.nodeAt(offset)?.toString(),
  );
  groupEnd();

  return offset;
}

// Insert XML element as new node
function insertXmlElement(
  xmlElement: Y.XmlElement,
  schema: any,
  snapshot: Y.Snapshot,
  transform: ChangeTrackingTransform,
  offset: number,
  createAttribution: (type: string, id?: Y.ID) => Attribution,
): number {
  const { content } = yXmlFragmentToProsemirrorJSON(xmlElement);
  const node = schema.nodeFromJSON({
    type: xmlElement.nodeName,
    attrs: xmlElement.getAttributes(snapshot),
    content: content.flat(1),
  });

  transform.insertWithAttribution(
    offset,
    node,
    createAttribution("added", xmlElement._item?.id),
  );

  return offset + node.nodeSize;
}

// Process children of XML element
function processChildren(
  xmlElement: Y.XmlElement,
  schema: any,
  doc: Node,
  currentSnapshot: Y.Snapshot,
  prevSnapshot: Y.Snapshot,
  transform: ChangeTrackingTransform,
  offset: number,
  createAttribution: (type: string, id?: Y.ID) => Attribution,
): number {
  const processChild = (currentOffset: number, child: any): number => {
    log("make child", { curOffset: currentOffset, initialOffset: offset });

    if (child.constructor === Y.XmlElement) {
      return processXmlElement(
        child,
        schema,
        doc,
        currentSnapshot,
        prevSnapshot,
        transform,
        currentOffset,
        createAttribution,
      );
    } else if (child.constructor === Y.XmlHook) {
      return currentOffset;
    } else {
      return processXmlText(
        child,
        schema,
        doc,
        currentSnapshot,
        prevSnapshot,
        transform,
        currentOffset,
        createAttribution,
      );
    }
  };

  if (currentSnapshot === undefined || prevSnapshot === undefined) {
    return xmlElement.toArray().reduce(processChild, offset);
  } else {
    return Y.typeListToArraySnapshot(
      xmlElement,
      new Y.Snapshot(prevSnapshot.ds, currentSnapshot.sv),
    ).reduce(processChild, offset);
  }
}

// Main function to get changeset transform
export async function getChangesetTransform({
  snapshot,
  prevSnapshot,
  permanentUserDataMapField = "__tiptapcollab__users",
  field = "default",
  hydrateUserData,
  schema,
  enableDebugging = false,
}: GetChangesetTransformParams): Promise<ChangeTrackingTransform> {
  let historyDoc: Y.Doc;
  let prevSnapshotData: Y.Snapshot;
  let currentSnapshotData: Y.Snapshot;
  let initialDoc: Node;

  // Enable debugging if requested
  if (enableDebugging && typeof window !== "undefined") {
    // Set debug flag in utils
    (globalThis as any).__debugEnabled = true;
  }

  if (
    !(snapshot instanceof Uint8Array) ||
    !(prevSnapshot instanceof Uint8Array)
  ) {
    throw new Error("Expected both snapshots to be v2 updates");
  }

  // Create history document and apply snapshots
  historyDoc = new Y.Doc({ gc: false });
  Y.applyUpdateV2(historyDoc, prevSnapshot);
  prevSnapshotData = Y.snapshot(historyDoc);
  initialDoc = Node.fromJSON(schema, yDocToProsemirrorJSON(historyDoc, field));
  Y.applyUpdateV2(historyDoc, snapshot);
  currentSnapshotData = Y.snapshot(historyDoc);

  log({
    initialPrevSnapshot: prevSnapshot,
    initialSnapshot: snapshot,
    prevSnapshot: prevSnapshotData,
    snapshot: currentSnapshotData,
    historyDoc,
  });

  // Save debug file if debugging is enabled
  if (enableDebugging && typeof window !== "undefined") {
    const blob = new Blob([Y.encodeStateAsUpdate(historyDoc)], {
      type: "application/octet-stream",
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "historyDoc";
    a.style.display = "none";
    document.body.appendChild(a);
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  return new Promise((resolve, reject) => {
    let transform = new ChangeTrackingTransform(initialDoc);
    const permanentUserData = new Y.PermanentUserData(
      historyDoc,
      historyDoc.getMap(permanentUserDataMapField),
    );

    historyDoc.transact((transaction) => {
      try {
        // Process deleted structures
        if (permanentUserData) {
          permanentUserData.dss.forEach((ds) => {
            Y.iterateDeletedStructs(transaction, ds, () => {});
          });
        }

        // Create attribution function
        const createAttribution = (type: string, id?: Y.ID): Attribution => {
          let userData: any;
          log("computing ychange", { id, type });

          if (id && permanentUserData) {
            if (type === "removed") {
              userData = permanentUserData.getUserByDeletedId(id);
            }
            if (type !== "added" && userData) {
              // Use existing userData
            } else {
              userData = permanentUserData.getUserByClientId(id.client);
            }
          }

          const attribution = { id, userId: userData, type };
          log("ychange found", attribution);

          return {
            ...attribution,
            ...(hydrateUserData?.(attribution) || {}),
          };
        };

        // Process document changes
        Y.typeListToArraySnapshot(
          historyDoc.get(field),
          new Y.Snapshot(prevSnapshotData.ds, currentSnapshotData.sv),
        ).reduce((offset, item) => {
          if (
            !item._item.deleted ||
            isDeleted(item._item, currentSnapshotData) ||
            isDeleted(item._item, prevSnapshotData)
          ) {
            return processXmlElement(
              item,
              schema,
              initialDoc,
              currentSnapshotData,
              prevSnapshotData,
              transform,
              offset,
              createAttribution,
            );
          }
          return offset;
        }, 0);

        transform = transform.simplify();

        log(
          "EXPECTED",
          Node.fromJSON(
            schema,
            yDocToProsemirrorJSON(historyDoc, field),
          ).toString(),
        );
        log("OUTPUTTED", transform.doc.toString());
        log("====================================================");

        resolve(transform);
      } catch (error) {
        log("error", error);
        reject(error);
      }
    });
  });
}
