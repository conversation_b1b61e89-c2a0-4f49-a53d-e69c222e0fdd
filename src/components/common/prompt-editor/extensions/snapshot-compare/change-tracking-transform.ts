import { Node, Fragment, Slice } from "@tiptap/pm/model";
import { Transform, replaceStep } from "@tiptap/pm/transform";
import { ChangeTrackingStep } from "./change-tracking-step";
import { Attribution, DiffItem } from "./types";

const noop = (..._args: any[]) => {};

export class ChangeTrackingTransform extends Transform {
  replaceWithAttribution(
    from: number,
    to: number,
    content: Fragment | Node | Node[],
    attribution: Attribution,
  ): this {
    noop("replaceWithAttribution", from, to, content.toString(), attribution);
    noop("doc-before", this.doc.toString());

    const slice = new Slice(Fragment.from(content), 0, 0);
    const step = replaceStep(this.doc, from, to, slice);

    if (step) {
      this.step(ChangeTrackingStep.fromReplaceStep(step as any, attribution));
      noop("doc-after", this.doc.toString());
    }

    return this;
  }

  deleteWithAttribution(
    from: number,
    to: number,
    attribution: Attribution,
  ): this {
    return this.replaceWithAttribution(from, to, [], attribution);
  }

  insertWithAttribution(
    pos: number,
    content: Fragment | Node | Node[],
    attribution: Attribution,
  ): this {
    return this.replaceWithAttribution(pos, pos, content, attribution);
  }

  setNodeAttributeWithAttribution(
    pos: number,
    attr: string,
    value: any,
    attribution: Attribution,
  ): this {
    const node = this.doc.nodeAt(pos);
    if (!node) {
      throw new Error("Node not found");
    }

    return this.replaceWithAttribution(
      pos,
      pos + node.nodeSize,
      node.type.create({ ...node.attrs, [attr]: value }, node.content),
      attribution,
    );
  }

  toTransform(): Transform {
    const transform = new Transform(this.doc);

    this.steps.forEach((step) => {
      if (step instanceof ChangeTrackingStep) {
        transform.step(ChangeTrackingStep.toReplaceStep(step));
      } else {
        transform.step(step);
      }
    });

    return transform;
  }

  simplify(): ChangeTrackingTransform {
    if (!this.docChanged) {
      return this;
    }

    noop("pre-simplify", this);

    const simplified = new ChangeTrackingTransform(this.docs[0]);
    const steps = this.steps.slice();

    while (steps.length) {
      let step = steps.shift()!;

      while (steps.length && step.merge(steps[0])) {
        const nextStep = steps.shift()!;
        step = step.merge(nextStep)!;
      }

      simplified.step(step);
    }

    noop(
      "simplified",
      simplified,
      `merged ${this.steps.length - simplified.steps.length} steps`,
    );
    return simplified;
  }

  toJSON(): any {
    return {
      type: "changeTrackingTransform",
      steps: this.steps.map((step) => step.toJSON()),
      doc: this.doc.toJSON(),
      docs: this.docs.map((doc) => doc.toJSON()),
    };
  }

  toDiff(): DiffItem[] {
    // Convert transform steps to diff items
    let diffs: DiffItem[] = [];

    for (let i = 0; i < this.steps.length; i++) {
      const step = this.steps[i];
      if (step instanceof ChangeTrackingStep) {
        diffs = diffs.concat(this.stepToDiffItems(step, this.docs[i]));
      }
    }

    return diffs;
  }

  private stepToDiffItems(step: ChangeTrackingStep, doc: Node): DiffItem[] {
    const isInlineContent = (fragment: any): boolean => {
      return (
        fragment.childCount === 1 && Boolean(fragment.firstChild?.isInline)
      );
    };

    if (step.from === step.to) {
      // Insert operation
      const content = step.slice.content;
      return isInlineContent(content)
        ? [
            {
              attribution: step.attribution,
              type: "inline-insert",
              from: step.from,
              to: step.from + content.size,
              content,
            },
          ]
        : [
            {
              attribution: step.attribution,
              type: "block-insert",
              from: step.from,
              to: step.from + content.size,
              content,
            },
          ];
    }

    if (step.slice.size === 0) {
      // Delete operation
      const content = doc.slice(step.from, step.to).content;
      return isInlineContent(content)
        ? [
            {
              attribution: step.attribution,
              type: "inline-delete",
              from: step.from,
              to: step.to,
              content,
            },
          ]
        : [
            {
              attribution: step.attribution,
              type: "block-delete",
              from: step.from,
              to: step.to,
              content,
            },
          ];
    }

    // Replace operation - check if it's an update
    const nodeAtPos = doc.nodeAt(step.from);
    if (nodeAtPos) {
      return [
        {
          attribution: step.attribution,
          type: isInlineContent(step.slice.content)
            ? "inline-update"
            : "block-update",
          from: step.from,
          to: step.to,
          content: step.slice.content,
          previousContent: Fragment.from(nodeAtPos),
          previousAttributes: nodeAtPos.attrs,
          attributes: step.slice.content.firstChild?.attrs || {},
        },
      ];
    }

    return [];
  }
}
