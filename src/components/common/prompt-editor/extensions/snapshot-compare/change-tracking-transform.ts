import { Node, Fragment, Slice } from '@tiptap/pm/model'
import { Transform, replaceStep } from '@tiptap/pm/transform'
import { ChangeTrackingStep } from './change-tracking-step'
import { Attribution, DiffItem } from './types'
import { changesToDiff } from './utils'

const noop = () => {}

export class ChangeTrackingTransform extends Transform {
  replaceWithAttribution(
    from: number,
    to: number,
    content: Fragment | Node | Node[],
    attribution: Attribution
  ): this {
    noop('replaceWithAttribution', from, to, content.toString(), attribution)
    noop('doc-before', this.doc.toString())

    const slice = new Slice(Fragment.from(content), 0, 0)
    const step = replaceStep(this.doc, from, to, slice)

    if (step) {
      this.step(ChangeTrackingStep.fromReplaceStep(step, attribution))
      noop('doc-after', this.doc.toString())
    }

    return this
  }

  deleteWithAttribution(from: number, to: number, attribution: Attribution): this {
    return this.replaceWithAttribution(from, to, [], attribution)
  }

  insertWithAttribution(pos: number, content: Fragment | Node | Node[], attribution: Attribution): this {
    return this.replaceWithAttribution(pos, pos, content, attribution)
  }

  setNodeAttributeWithAttribution(
    pos: number,
    attr: string,
    value: any,
    attribution: Attribution
  ): this {
    const node = this.doc.nodeAt(pos)
    if (!node) {
      throw new Error('Node not found')
    }

    return this.replaceWithAttribution(
      pos,
      pos + node.nodeSize,
      node.type.create({ ...node.attrs, [attr]: value }, node.content),
      attribution
    )
  }

  toTransform(): Transform {
    const transform = new Transform(this.doc)
    
    this.steps.forEach(step => {
      if (step instanceof ChangeTrackingStep) {
        transform.step(ChangeTrackingStep.toReplaceStep(step))
      } else {
        transform.step(step)
      }
    })

    return transform
  }

  simplify(): ChangeTrackingTransform {
    if (!this.docChanged) {
      return this
    }

    noop('pre-simplify', this)

    const simplified = new ChangeTrackingTransform(this.docs[0])
    const steps = this.steps.slice()

    while (steps.length) {
      let step = steps.shift()!
      
      while (steps.length && step.merge(steps[0])) {
        const nextStep = steps.shift()!
        step = step.merge(nextStep)!
      }
      
      simplified.step(step)
    }

    noop('simplified', simplified, `merged ${this.steps.length - simplified.steps.length} steps`)
    return simplified
  }

  toJSON(): any {
    return {
      type: 'changeTrackingTransform',
      steps: this.steps.map(step => step.toJSON()),
      doc: this.doc.toJSON(),
      docs: this.docs.map(doc => doc.toJSON())
    }
  }

  toDiff(): DiffItem[] {
    return changesToDiff(this)
  }
}
