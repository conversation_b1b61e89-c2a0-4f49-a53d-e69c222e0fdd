import { Node, Fragment } from "@tiptap/pm/model";
import { Slice } from "@tiptap/pm/model";
import { ChangeTrackingStep } from "./change-tracking-step";
import { ChangeTrackingTransform } from "./change-tracking-transform";
import { DiffItem, Attribution } from "./types";

// Debugging utilities
let debugEnabled = false;

export const log = (...args: any[]) => {
  if (debugEnabled && typeof window !== "undefined") {
    window.console.log(...args);
  }
};

export const group = (...args: any[]) => {
  if (debugEnabled && typeof window !== "undefined") {
    window.console.group(...args);
  }
};

export const groupCollapsed = (...args: any[]) => {
  if (debugEnabled && typeof window !== "undefined") {
    window.console.groupCollapsed(...args);
  }
};

export const groupEnd = () => {
  if (debugEnabled && typeof window !== "undefined") {
    window.console.groupEnd();
  }
};

// Utility functions
export function areObjectsEqual(
  obj1: Record<string, any>,
  obj2: Record<string, any>,
): boolean {
  const keys1 = Object.keys(obj1).sort();
  const keys2 = Object.keys(obj2).sort();

  if (keys1.length !== keys2.length) {
    return false;
  }

  for (let i = 0; i < keys1.length; i++) {
    if (keys1[i] !== keys2[i]) {
      return false;
    }
    if (obj1[keys1[i]] !== obj2[keys1[i]]) {
      return false;
    }
  }

  return true;
}

const DEFAULT_COMPARE_OPTIONS = {
  marks: true,
  attrs: true,
  content: true,
  type: true,
  text: true,
};

export function areNodesEqual(
  node1: Node,
  node2: Node,
  options = DEFAULT_COMPARE_OPTIONS,
): boolean {
  const { marks, attrs, content, type, text } = {
    ...DEFAULT_COMPARE_OPTIONS,
    ...options,
  };

  if (type && node1.type !== node2.type) {
    return false;
  }

  if (text && node1.text !== node2.text) {
    return false;
  }

  if (attrs && !areObjectsEqual(node1.attrs, node2.attrs)) {
    return false;
  }

  if (marks) {
    if (node1.marks.length !== node2.marks.length) {
      return false;
    }

    for (let i = 0; i < node1.marks.length; i++) {
      if (node1.marks[i].type !== node2.marks[i].type) {
        return false;
      }
      if (!areObjectsEqual(node1.marks[i].attrs, node2.marks[i].attrs)) {
        return false;
      }
    }
  }

  return !(content && !areFragmentsEqual(node1.content, node2.content));
}

export function areFragmentsEqual(
  fragment1: Fragment,
  fragment2: Fragment,
  options = DEFAULT_COMPARE_OPTIONS,
): boolean {
  const frag1 = Fragment.from(fragment1);
  const frag2 = Fragment.from(fragment2);

  if (frag1.childCount !== frag2.childCount) {
    return false;
  }

  const childCount = frag1.childCount;
  for (let i = 0; i < childCount; i++) {
    if (!areNodesEqual(frag1.child(i), frag2.child(i), options)) {
      return false;
    }
  }

  return true;
}

export function isInlineContent(fragment: Fragment): boolean {
  return fragment.childCount === 1 && Boolean(fragment.firstChild?.isInline);
}

// Convert step changes to diff items
function stepToDiffItems(step: ChangeTrackingStep, doc: Node): DiffItem[] {
  if (step.from === step.to) {
    // Insert operation
    const content = step.slice.content;
    return isInlineContent(content)
      ? [
          {
            attribution: step.attribution,
            type: "inline-insert",
            from: step.from,
            to: step.from + content.size,
            content,
          },
        ]
      : [
          {
            attribution: step.attribution,
            type: "block-insert",
            from: step.from,
            to: step.from + content.size,
            content,
          },
        ];
  }

  if (step.slice.size === 0) {
    // Delete operation
    const content = doc.slice(step.from, step.to).content;
    return isInlineContent(content)
      ? [
          {
            attribution: step.attribution,
            type: "inline-delete",
            from: step.from,
            to: step.to,
            content,
          },
        ]
      : [
          {
            attribution: step.attribution,
            type: "block-delete",
            from: step.from,
            to: step.to,
            content,
          },
        ];
  }

  // Replace operation - check if it's an update
  const nodeAtPos = doc.nodeAt(step.from);
  if (
    nodeAtPos &&
    areFragmentsEqual(step.slice.content, Fragment.from(nodeAtPos), {
      attrs: false,
    })
  ) {
    return [
      {
        attribution: step.attribution,
        type: isInlineContent(step.slice.content)
          ? "inline-update"
          : "block-update",
        from: step.from,
        to: step.to,
        content: step.slice.content,
        previousContent: Fragment.from(nodeAtPos),
        previousAttributes: nodeAtPos.attrs,
        attributes: step.slice.content.firstChild?.attrs || {},
      },
    ];
  }

  // Complex replace - split into delete + insert
  return stepToDiffItems(
    new ChangeTrackingStep(step.from, step.to, Slice.empty, step.attribution),
    doc,
  ).concat(
    stepToDiffItems(
      new ChangeTrackingStep(
        step.from,
        step.from,
        step.slice,
        step.attribution,
      ),
      doc,
    ),
  );
}

export function changesToDiff(transform: ChangeTrackingTransform): DiffItem[] {
  let diffs: DiffItem[] = [];

  for (let i = 0; i < transform.steps.length; i++) {
    const step = transform.steps[i];
    if (step instanceof ChangeTrackingStep) {
      diffs = diffs.concat(stepToDiffItems(step, transform.docs[i]));
    }
  }

  return optimizeDiffs(diffs);
}

// Optimize diffs by merging adjacent operations
function optimizeDiffs(diffs: DiffItem[]): DiffItem[] {
  for (let i = 0; i < diffs.length; i++) {
    const diff = diffs[i];
    if (diff.type !== "inline-delete" && diff.type !== "block-delete") {
      continue;
    }

    const content = diff.content;
    let currentPos = diff.from;

    for (let j = i + 1; j < diffs.length; j++) {
      const nextDiff = diffs[j];
      if (nextDiff.from > currentPos + 1) {
        break;
      }

      currentPos = nextDiff.to;

      if (
        (nextDiff.type !== "block-insert" &&
          nextDiff.type !== "inline-insert") ||
        nextDiff.attribution.userId !== diff.attribution.userId
      ) {
        break;
      }

      // Try to find common content for optimization
      let bestMatch: Fragment | null = null;
      let bestMatchLength = 0;
      let bestMatchIndex = 0;

      for (let k = 0; k < nextDiff.content.childCount; k++) {
        const child = nextDiff.content.child(k);
        let childContent = child.content;
        if (child.isText) {
          childContent = Fragment.from(child);
        }

        const startMatch =
          childContent.findDiffStart(content) ?? childContent.size;
        if (startMatch > bestMatchLength) {
          bestMatch = childContent;
          bestMatchLength = startMatch;
          bestMatchIndex = k;
        }

        const endMatch = childContent.findDiffEnd(content);
        let endMatchLength = 0;
        if (endMatch) {
          endMatchLength =
            typeof endMatch === "number"
              ? endMatch
              : childContent.size - endMatch.a;
        }

        if (endMatchLength > bestMatchLength) {
          bestMatch = childContent;
          bestMatchLength = endMatchLength;
          bestMatchIndex = k;
        }
      }

      if (bestMatchLength >= bestMatchLength && bestMatch) {
        (nextDiff as any).trimLeft = bestMatchLength;
        (nextDiff as any).trimLeftIndex = bestMatchIndex;

        nextDiff.content.forEach((node, offset, index) => {
          if (index === bestMatchIndex) {
            diff.from = nextDiff.from + offset + bestMatchLength + 1;
          }
        });

        diff.content = content.cut(bestMatchLength);
        break;
      }
    }
  }

  return diffs;
}
