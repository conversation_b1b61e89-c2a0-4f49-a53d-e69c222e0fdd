import { Schema } from "@tiptap/pm/model";
import { computeModuleDiff } from "../module-diff-calculator";

// 创建测试 schema
const testSchema = new Schema({
  nodes: {
    doc: {
      content: "prompt+",
    },
    prompt: {
      content: "paragraph+",
    },
    paragraph: {
      content: "inline*",
    },
    text: {
      group: "inline",
    },
    stringVariable: {
      group: "inline",
      inline: true,
      atom: true,
      attrs: {
        id: { default: "" },
      },
    },
  },
});

describe("Diff 功能集成测试", () => {
  it("应该能够完整处理 StringVariable 节点的 diff 流程", () => {
    // 1. 创建测试文档
    const oldDoc = testSchema.nodeFromJSON({
      type: "doc",
      content: [
        {
          type: "prompt",
          content: [
            {
              type: "paragraph",
              content: [
                {
                  type: "text",
                  text: "Hello ",
                },
                {
                  type: "stringVariable",
                  attrs: { id: "user" },
                },
                {
                  type: "text",
                  text: "!",
                },
              ],
            },
          ],
        },
      ],
    });

    const newDoc = testSchema.nodeFromJSON({
      type: "doc",
      content: [
        {
          type: "prompt",
          content: [
            {
              type: "paragraph",
              content: [
                {
                  type: "text",
                  text: "Hello ",
                },
                {
                  type: "stringVariable",
                  attrs: { id: "user" },
                },
                {
                  type: "text",
                  text: ", welcome to ",
                },
                {
                  type: "stringVariable",
                  attrs: { id: "shop" },
                },
                {
                  type: "text",
                  text: "!",
                },
              ],
            },
          ],
        },
      ],
    });

    // 2. 计算 diff
    const diffData = computeModuleDiff(oldDoc, newDoc);

    // 3. 验证 diff 数据结构
    expect(diffData).toHaveProperty("added");
    expect(diffData).toHaveProperty("removed");
    expect(Array.isArray(diffData.added)).toBe(true);
    expect(Array.isArray(diffData.removed)).toBe(true);

    // 4. 验证能检测到变量节点的变更
    expect(diffData.added.length).toBeGreaterThan(0);

    // 5. 验证新增内容包含变量节点的显示文本
    const addedTexts = diffData.added.map((change) => change.newText).join("");
    expect(addedTexts).toContain("{shop}");
    expect(addedTexts).toContain("welcome to");

    // 6. 验证 diff 数据格式符合扩展要求
    diffData.added.forEach((range) => {
      expect(typeof range.from).toBe("number");
      expect(typeof range.to).toBe("number");
      expect(typeof range.newText).toBe("string");
      expect(range.from).toBeLessThanOrEqual(range.to);
    });

    diffData.removed.forEach((range) => {
      expect(typeof range.from).toBe("number");
      expect(typeof range.to).toBe("number");
      expect(typeof range.originalText).toBe("string");
      expect(range.from).toBeLessThanOrEqual(range.to);
    });
  });

  it("应该能够处理复杂的变量节点变更场景", () => {
    // 测试变量节点的替换场景
    const oldDoc = testSchema.nodeFromJSON({
      type: "doc",
      content: [
        {
          type: "prompt",
          content: [
            {
              type: "paragraph",
              content: [
                {
                  type: "text",
                  text: "Product: ",
                },
                {
                  type: "stringVariable",
                  attrs: { id: "old_product" },
                },
                {
                  type: "text",
                  text: " is available",
                },
              ],
            },
          ],
        },
      ],
    });

    const newDoc = testSchema.nodeFromJSON({
      type: "doc",
      content: [
        {
          type: "prompt",
          content: [
            {
              type: "paragraph",
              content: [
                {
                  type: "text",
                  text: "Product: ",
                },
                {
                  type: "stringVariable",
                  attrs: { id: "new_product" },
                },
                {
                  type: "text",
                  text: " is now available",
                },
              ],
            },
          ],
        },
      ],
    });

    const diffData = computeModuleDiff(oldDoc, newDoc);

    // 验证能检测到变量和文本的变更
    expect(diffData.added.length).toBeGreaterThan(0);
    expect(diffData.removed.length).toBeGreaterThan(0);

    const addedTexts = diffData.added.map((change) => change.newText).join("");
    const removedTexts = diffData.removed
      .map((change) => change.originalText)
      .join("");

    // 验证变量节点的变更
    expect(addedTexts).toContain("{new_product}");
    expect(removedTexts).toContain("{old_product}");

    // 验证文本变更
    expect(addedTexts).toContain("now");
  });

  it("应该正确处理空 diff 的情况", () => {
    const sameDoc = testSchema.nodeFromJSON({
      type: "doc",
      content: [
        {
          type: "prompt",
          content: [
            {
              type: "paragraph",
              content: [
                {
                  type: "text",
                  text: "Hello ",
                },
                {
                  type: "stringVariable",
                  attrs: { id: "user" },
                },
              ],
            },
          ],
        },
      ],
    });

    const diffData = computeModuleDiff(sameDoc, sameDoc);

    expect(diffData.added).toHaveLength(0);
    expect(diffData.removed).toHaveLength(0);
  });
});
