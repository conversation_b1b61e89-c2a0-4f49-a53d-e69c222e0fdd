import { computeModuleDiff } from "../module-diff-calculator";
import { Schema } from "@tiptap/pm/model";

// 创建测试 schema
const testSchema = new Schema({
  nodes: {
    doc: {
      content: "prompt+",
    },
    prompt: {
      content: "paragraph+",
    },
    paragraph: {
      content: "inline*",
    },
    text: {
      group: "inline",
    },
    stringVariable: {
      group: "inline",
      inline: true,
      atom: true,
      attrs: {
        id: { default: "" },
      },
    },
  },
});

describe("变量节点 Diff 集成测试", () => {
  it("应该为变量节点生成正确的 diff 数据", () => {
    // 创建包含变量节点的文档
    const oldDoc = testSchema.nodeFromJSON({
      type: "doc",
      content: [
        {
          type: "prompt",
          content: [
            {
              type: "paragraph",
              content: [
                {
                  type: "text",
                  text: "Hello ",
                },
                {
                  type: "stringVariable",
                  attrs: { id: "user" },
                },
                {
                  type: "text",
                  text: "!",
                },
              ],
            },
          ],
        },
      ],
    });

    const newDoc = testSchema.nodeFromJSON({
      type: "doc",
      content: [
        {
          type: "prompt",
          content: [
            {
              type: "paragraph",
              content: [
                {
                  type: "text",
                  text: "Hello ",
                },
                {
                  type: "stringVariable",
                  attrs: { id: "user" },
                },
                {
                  type: "text",
                  text: ", welcome to ",
                },
                {
                  type: "stringVariable",
                  attrs: { id: "shop" },
                },
                {
                  type: "text",
                  text: "!",
                },
              ],
            },
          ],
        },
      ],
    });

    // 计算 diff
    const diffData = computeModuleDiff(oldDoc, newDoc);

    // 验证 diff 数据结构
    expect(diffData).toHaveProperty("added");
    expect(diffData).toHaveProperty("removed");
    expect(diffData.added.length).toBeGreaterThan(0);

    // 验证变量节点被正确识别
    const addedTexts = diffData.added.map((change) => change.newText).join("");
    expect(addedTexts).toContain("{shop}");
    expect(addedTexts).toContain("welcome to");

    // 验证位置信息正确
    diffData.added.forEach((range) => {
      expect(typeof range.from).toBe("number");
      expect(typeof range.to).toBe("number");
      expect(range.from).toBeLessThanOrEqual(range.to);
    });

    // 验证 diff 数据包含 isVariable 属性
    diffData.added.forEach((change) => {
      expect(change).toHaveProperty("isVariable");
      expect(typeof change.isVariable).toBe("boolean");
    });

    console.log("Generated diff data:", diffData);
    console.log(
      "Added changes with isVariable:",
      diffData.added.map((c) => ({
        newText: c.newText,
        isVariable: c.isVariable,
        nodeType: c.nodeType,
      })),
    );
  });

  it("应该能够识别变量节点的替换", () => {
    const oldDoc = testSchema.nodeFromJSON({
      type: "doc",
      content: [
        {
          type: "prompt",
          content: [
            {
              type: "paragraph",
              content: [
                {
                  type: "text",
                  text: "Product: ",
                },
                {
                  type: "stringVariable",
                  attrs: { id: "old_product" },
                },
              ],
            },
          ],
        },
      ],
    });

    const newDoc = testSchema.nodeFromJSON({
      type: "doc",
      content: [
        {
          type: "prompt",
          content: [
            {
              type: "paragraph",
              content: [
                {
                  type: "text",
                  text: "Product: ",
                },
                {
                  type: "stringVariable",
                  attrs: { id: "new_product" },
                },
              ],
            },
          ],
        },
      ],
    });

    const diffData = computeModuleDiff(oldDoc, newDoc);

    expect(diffData.added.length).toBeGreaterThan(0);
    expect(diffData.removed.length).toBeGreaterThan(0);

    const addedText = diffData.added.map((change) => change.newText).join("");
    const removedText = diffData.removed
      .map((change) => change.originalText)
      .join("");

    expect(addedText).toContain("{new_product}");
    expect(removedText).toContain("{old_product}");

    console.log("Variable replacement diff:", { addedText, removedText });
  });

  it("应该正确处理纯文本的 diff", () => {
    const oldDoc = testSchema.nodeFromJSON({
      type: "doc",
      content: [
        {
          type: "prompt",
          content: [
            {
              type: "paragraph",
              content: [
                {
                  type: "text",
                  text: "Hello world",
                },
              ],
            },
          ],
        },
      ],
    });

    const newDoc = testSchema.nodeFromJSON({
      type: "doc",
      content: [
        {
          type: "prompt",
          content: [
            {
              type: "paragraph",
              content: [
                {
                  type: "text",
                  text: "Hello universe",
                },
              ],
            },
          ],
        },
      ],
    });

    const diffData = computeModuleDiff(oldDoc, newDoc);

    expect(diffData.added.length).toBeGreaterThan(0);
    expect(diffData.removed.length).toBeGreaterThan(0);

    const addedText = diffData.added.map((change) => change.newText).join("");
    const removedText = diffData.removed
      .map((change) => change.originalText)
      .join("");

    expect(addedText).toContain("universe");
    expect(removedText).toContain("world");

    console.log("Text diff:", { addedText, removedText });
  });
});
