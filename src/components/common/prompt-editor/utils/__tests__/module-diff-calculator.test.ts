import { describe, expect, it } from "vitest";
import { getDiffStats, isDiffEmpty } from "../module-diff-calculator";

describe("isDiffEmpty", () => {
  it("应该正确判断空差异", () => {
    const emptyDiff = {
      added: [],
      removed: [],
    };

    expect(isDiffEmpty(emptyDiff)).toBe(true);
  });

  it("应该正确判断非空差异 - 有新增", () => {
    const nonEmptyDiff = {
      added: [{ from: 0, to: 5, newText: "Hello" }],
      removed: [],
    };

    expect(isDiffEmpty(nonEmptyDiff)).toBe(false);
  });

  it("应该正确判断非空差异 - 有删除", () => {
    const nonEmptyDiff = {
      added: [],
      removed: [{ from: 0, to: 5, originalText: "Hello" }],
    };

    expect(isDiffEmpty(nonEmptyDiff)).toBe(false);
  });
});

describe("getDiffStats", () => {
  it("应该正确计算空差异统计", () => {
    const diff = {
      added: [],
      removed: [],
    };

    const stats = getDiffStats(diff);

    expect(stats.addedCount).toBe(0);
    expect(stats.removedCount).toBe(0);
    expect(stats.totalChanges).toBe(0);
  });

  it("应该正确计算混合差异统计", () => {
    const diff = {
      added: [
        { from: 0, to: 5, newText: "Hello" },
        { from: 10, to: 15, newText: "World" },
      ],
      removed: [{ from: 20, to: 25, originalText: "Test" }],
    };

    const stats = getDiffStats(diff);

    expect(stats.addedCount).toBe(2);
    expect(stats.removedCount).toBe(1);
  });

  it("应该正确计算只有新增的统计", () => {
    const diff = {
      added: [
        { from: 0, to: 5, newText: "Hello" },
        { from: 10, to: 15, newText: "World" },
        { from: 20, to: 25, newText: "Test" },
      ],
      removed: [],
    };

    const stats = getDiffStats(diff);

    expect(stats.addedCount).toBe(3);
    expect(stats.removedCount).toBe(0);
    expect(stats.totalChanges).toBe(3);
  });

  it("应该正确计算只有删除的统计", () => {
    const diff = {
      added: [],
      removed: [
        { from: 0, to: 5, originalText: "Hello" },
        { from: 10, to: 15, originalText: "World" },
      ],
    };

    const stats = getDiffStats(diff);

    expect(stats.addedCount).toBe(0);
    expect(stats.removedCount).toBe(2);
    expect(stats.totalChanges).toBe(2);
  });
});
