import { Schema } from "@tiptap/pm/model";
import { computeModuleDiff } from "../module-diff-calculator";

const testSchema = new Schema({
  nodes: {
    doc: {
      content: "prompt+",
    },
    prompt: {
      content: "paragraph+",
    },
    paragraph: {
      content: "inline*",
    },
    text: {
      group: "inline",
    },
    stringVariable: {
      group: "inline",
      inline: true,
      atom: true,
      attrs: {
        id: { default: "" },
      },
    },
  },
});

describe("StringVariable diff 计算问题", () => {
  it("应该检测到 StringVariable 节点的增删", () => {
    const oldDoc = testSchema.nodeFromJSON({
      type: "doc",
      content: [
        {
          type: "prompt",
          content: [
            {
              type: "paragraph",
              content: [
                {
                  type: "text",
                  text: "你好，",
                },
                {
                  type: "stringVariable",
                  attrs: {
                    id: "var-1",
                  },
                },
                {
                  type: "text",
                  text: "！欢迎来到我们的店铺。",
                },
              ],
            },
          ],
        },
      ],
    });
    const newDoc = testSchema.nodeFromJSON({
      type: "doc",
      content: [
        {
          type: "prompt",
          content: [
            {
              type: "paragraph",
              content: [
                {
                  type: "text",
                  text: "你好，",
                },
                {
                  type: "stringVariable",
                  attrs: {
                    id: "var-1",
                  },
                },
                {
                  type: "text",
                  text: "！欢迎来到",
                },
                {
                  type: "stringVariable",
                  attrs: {
                    id: "var-3",
                  },
                },
                {
                  type: "text",
                  text: "，我们为您推荐",
                },
                {
                  type: "stringVariable",
                  attrs: {
                    id: "var-2",
                  },
                },
                {
                  type: "text",
                  text: "。",
                },
              ],
            },
          ],
        },
      ],
    });

    const diffData = computeModuleDiff(oldDoc, newDoc);

    console.log("检测到的变更：", diffData);
    console.log("新增变更数量：", diffData.added.length);
    console.log("删除变更数量：", diffData.removed.length);

    // 验证能检测到变量节点的变化
    expect(diffData.added.length).toBeGreaterThan(0);
    expect(diffData.removed.length).toBeGreaterThan(0);

    // 验证变更内容包含变量节点的显示文本
    const addedTexts = diffData.added.map((change) => change.newText).join("");
    expect(addedTexts).toContain("{var-3}"); // 新增的店铺变量
    expect(addedTexts).toContain("{var-2}"); // 新增的商品变量
  });

  it("应该能提取 StringVariable 节点信息", () => {
    const docWithVariable = testSchema.nodeFromJSON({
      type: "doc",
      content: [
        {
          type: "prompt",
          content: [
            {
              type: "paragraph",
              content: [
                {
                  type: "text",
                  text: "Hello ",
                },
                {
                  type: "stringVariable",
                  attrs: {
                    id: "user-name",
                  },
                },
                {
                  type: "text",
                  text: "!",
                },
              ],
            },
          ],
        },
      ],
    });

    const fragments: any[] = [];

    docWithVariable.descendants((node, pos) => {
      if (node.type.name === "text" && node.text) {
        fragments.push({
          type: "text",
          text: node.text,
          from: pos,
          to: pos + node.nodeSize,
        });
      } else if (node.type.name === "stringVariable") {
        fragments.push({
          type: "stringVariable",
          id: node.attrs.id,
          from: pos,
          to: pos + node.nodeSize,
        });
      }
    });

    // 验证能提取到 StringVariable 节点
    const variableFragments = fragments.filter(
      (f) => f.type === "stringVariable",
    );
    expect(variableFragments).toHaveLength(1);
    expect(variableFragments[0].id).toBe("user-name");
  });
});
