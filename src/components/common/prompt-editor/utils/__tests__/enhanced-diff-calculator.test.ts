import { Schema } from "@tiptap/pm/model";
import { computeModuleDiff } from "../module-diff-calculator";

const enhancedSchema = new Schema({
  nodes: {
    doc: {
      content: "prompt+",
    },
    prompt: {
      content: "paragraph+",
    },
    paragraph: {
      content: "inline*",
    },
    text: {
      group: "inline",
    },
    stringVariable: {
      group: "inline",
      inline: true,
      atom: true,
      attrs: {
        id: { default: "" },
      },
    },
    tableVariable: {
      group: "inline",
      inline: true,
      atom: true,
      attrs: {
        name: { default: "" },
        variableName: { default: "" },
      },
    },
    sharedVariableReference: {
      group: "inline",
      inline: true,
      atom: true,
      attrs: {
        sharedVariableId: { default: "" },
      },
    },
  },
});

describe("增强的 diff 计算器", () => {
  it("应该检测到 StringVariable 节点的增删", () => {
    const oldDoc = enhancedSchema.nodeFromJSON({
      type: "doc",
      content: [
        {
          type: "prompt",
          content: [
            {
              type: "paragraph",
              content: [
                {
                  type: "text",
                  text: "Hello ",
                },
                {
                  type: "stringVariable",
                  attrs: { id: "user" },
                },
                {
                  type: "text",
                  text: "!",
                },
              ],
            },
          ],
        },
      ],
    });

    const newDoc = enhancedSchema.nodeFromJSON({
      type: "doc",
      content: [
        {
          type: "prompt",
          content: [
            {
              type: "paragraph",
              content: [
                {
                  type: "text",
                  text: "Hello ",
                },
                {
                  type: "stringVariable",
                  attrs: { id: "user" },
                },
                {
                  type: "text",
                  text: ", welcome to ",
                },
                {
                  type: "stringVariable",
                  attrs: { id: "shop" },
                },
                {
                  type: "text",
                  text: "!",
                },
              ],
            },
          ],
        },
      ],
    });

    const diffData = computeModuleDiff(oldDoc, newDoc);

    expect(diffData.added.length).toBeGreaterThan(0);

    const addedText = diffData.added.map((change) => change.newText).join("");
    expect(addedText).toContain("{shop}");
    expect(addedText).toContain("welcome to");
  });

  it("应该检测到 TableVariable 节点的变化", () => {
    const oldDoc = enhancedSchema.nodeFromJSON({
      type: "doc",
      content: [
        {
          type: "prompt",
          content: [
            {
              type: "paragraph",
              content: [
                {
                  type: "text",
                  text: "Product: ",
                },
                {
                  type: "tableVariable",
                  attrs: { name: "product_name" },
                },
              ],
            },
          ],
        },
      ],
    });

    const newDoc = enhancedSchema.nodeFromJSON({
      type: "doc",
      content: [
        {
          type: "prompt",
          content: [
            {
              type: "paragraph",
              content: [
                {
                  type: "text",
                  text: "Product: ",
                },
                {
                  type: "tableVariable",
                  attrs: { name: "product_title" },
                },
              ],
            },
          ],
        },
      ],
    });

    const diffData = computeModuleDiff(oldDoc, newDoc, {
      compareByWords: false,
    });

    console.log("TableVariable diff data:", diffData);
    console.log("Added changes:", diffData.added);
    console.log("Removed changes:", diffData.removed);

    expect(diffData.added.length).toBeGreaterThan(0);
    expect(diffData.removed.length).toBeGreaterThan(0);

    const addedText = diffData.added.map((change) => change.newText).join("");
    const removedText = diffData.removed
      .map((change) => change.originalText)
      .join("");

    expect(addedText).toContain("{product_title}");
    expect(removedText).toContain("{product_name}");
  });

  it("应该检测到 SharedVariableReference 节点的变化", () => {
    const oldDoc = enhancedSchema.nodeFromJSON({
      type: "doc",
      content: [
        {
          type: "prompt",
          content: [
            {
              type: "paragraph",
              content: [
                {
                  type: "text",
                  text: "Shared: ",
                },
                {
                  type: "sharedVariableReference",
                  attrs: { sharedVariableId: "shared-1" },
                },
              ],
            },
          ],
        },
      ],
    });

    const newDoc = enhancedSchema.nodeFromJSON({
      type: "doc",
      content: [
        {
          type: "prompt",
          content: [
            {
              type: "paragraph",
              content: [
                {
                  type: "text",
                  text: "Shared: ",
                },
                {
                  type: "sharedVariableReference",
                  attrs: { sharedVariableId: "shared-2" },
                },
              ],
            },
          ],
        },
      ],
    });

    const diffData = computeModuleDiff(oldDoc, newDoc);

    expect(diffData.added.length).toBeGreaterThan(0);
    expect(diffData.removed.length).toBeGreaterThan(0);

    const addedText = diffData.added.map((change) => change.newText).join("");
    const removedText = diffData.removed
      .map((change) => change.originalText)
      .join("");

    expect(addedText).toContain("{shared:shared-2}");
    expect(removedText).toContain("{shared:shared-1}");
  });

  it("应该处理混合节点类型的复杂变化", () => {
    const oldDoc = enhancedSchema.nodeFromJSON({
      type: "doc",
      content: [
        {
          type: "prompt",
          content: [
            {
              type: "paragraph",
              content: [
                {
                  type: "text",
                  text: "Hello ",
                },
                {
                  type: "stringVariable",
                  attrs: { id: "user" },
                },
                {
                  type: "text",
                  text: ", check out ",
                },
                {
                  type: "tableVariable",
                  attrs: { name: "product" },
                },
              ],
            },
          ],
        },
      ],
    });

    const newDoc = enhancedSchema.nodeFromJSON({
      type: "doc",
      content: [
        {
          type: "prompt",
          content: [
            {
              type: "paragraph",
              content: [
                {
                  type: "text",
                  text: "Hi ",
                },
                {
                  type: "stringVariable",
                  attrs: { id: "user" },
                },
                {
                  type: "text",
                  text: ", welcome to ",
                },
                {
                  type: "sharedVariableReference",
                  attrs: { sharedVariableId: "store-info" },
                },
                {
                  type: "text",
                  text: "! Check out ",
                },
                {
                  type: "tableVariable",
                  attrs: { name: "product" },
                },
              ],
            },
          ],
        },
      ],
    });

    const diffData = computeModuleDiff(oldDoc, newDoc);

    expect(diffData.added.length).toBeGreaterThan(0);
    expect(diffData.removed.length).toBeGreaterThan(0);

    const addedText = diffData.added.map((change) => change.newText).join("");
    const removedText = diffData.removed
      .map((change) => change.originalText)
      .join("");

    // 验证文本变化
    expect(addedText).toContain("Hi");
    expect(addedText).toContain("welcome to");
    expect(removedText).toContain("Hello");

    // 验证变量节点变化
    expect(addedText).toContain("{shared:store-info}");
  });
});
