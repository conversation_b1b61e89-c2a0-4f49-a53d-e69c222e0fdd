import { Node as ProseMirrorNode } from "@tiptap/pm/model";
import * as Diff from "diff";

/**
 * 差异数据结构
 */
export interface DiffData {
  /**
   * 新增的文本范围
   */
  added: DiffRange[];
  /**
   * 删除的文本范围
   */
  removed: DiffRange[];
}

/**
 * 差异范围
 */
export interface DiffRange {
  /**
   * 起始位置 (ProseMirror Position)
   */
  from: number;
  /**
   * 结束位置 (ProseMirror Position)
   */
  to: number;
  /**
   * 原始文本
   */
  originalText?: string;
  /**
   * 新文本
   */
  newText?: string;
  /**
   * 节点类型（用于识别变量节点）
   */
  nodeType?: string;
  /**
   * 是否为变量节点
   */
  isVariable?: boolean;
}

/**
 * 节点片段信息（支持文本和变量节点）
 */
interface NodeFragment {
  type: "text" | "stringVariable" | "tableVariable" | "sharedVariableReference";
  text?: string; // 文本节点的内容
  id?: string; // 变量节点的 ID
  displayText?: string; // 变量节点的显示文本（用于 diff 计算）
  from: number; // ProseMirror position
  to: number; // ProseMirror position
}

/**
 * 差异计算选项
 */
export interface DiffOptions {
  /**
   * 是否按词对比（默认按字符对比）
   */
  compareByWords?: boolean;
  /**
   * 是否忽略空白字符差异
   */
  ignoreWhitespace?: boolean;
}

/**
 * 从 ProseMirror 文档中提取节点片段及其位置信息（支持文本和变量节点）
 */
function extractNodeFragments(doc: ProseMirrorNode): NodeFragment[] {
  const fragments: NodeFragment[] = [];

  doc.descendants((node, pos) => {
    if (node.type.name === "text" && node.text) {
      // 文本节点
      fragments.push({
        type: "text",
        text: node.text,
        from: pos,
        to: pos + node.nodeSize,
      });
    } else if (node.type.name === "stringVariable") {
      // 字符串变量节点 - 使用特殊占位符确保作为原子单位处理
      const id = node.attrs.id || "unknown";
      const placeholder = `\uE000${id}\uE001`; // 使用私有使用区字符作为占位符
      const displayText = `{${id}}`;
      fragments.push({
        type: "stringVariable",
        id,
        displayText,
        text: placeholder, // 用于 diff 计算的占位符
        from: pos,
        to: pos + node.nodeSize,
      });
    } else if (node.type.name === "tableVariable") {
      // 表格变量节点
      const name = node.attrs.name || node.attrs.variableName || "unknown";
      const placeholder = `\uE002${name}\uE003`;
      const displayText = `{${name}}`;
      fragments.push({
        type: "tableVariable",
        id: name,
        displayText,
        text: placeholder,
        from: pos,
        to: pos + node.nodeSize,
      });
    } else if (node.type.name === "sharedVariableReference") {
      // 共享变量引用节点
      const id = node.attrs.sharedVariableId || "unknown";
      const placeholder = `\uE004${id}\uE005`;
      const displayText = `{shared:${id}}`;
      fragments.push({
        type: "sharedVariableReference",
        id,
        displayText,
        text: placeholder,
        from: pos,
        to: pos + node.nodeSize,
      });
    }
  });

  return fragments;
}

/**
 * 将节点片段合并为完整文本，同时记录位置映射（支持文本和变量节点）
 */
function buildTextWithMappingFromNodes(fragments: NodeFragment[]): {
  text: string;
  positionMap: Array<{ charIndex: number; proseMirrorPos: number }>;
} {
  let text = "";
  const positionMap: Array<{ charIndex: number; proseMirrorPos: number }> = [];

  fragments.forEach((fragment) => {
    const startCharIndex = text.length;
    let fragmentText = "";

    if (fragment.type === "text") {
      fragmentText = fragment.text || "";
    } else {
      // 对于变量节点，使用占位符进行 diff 计算
      fragmentText = fragment.text || fragment.displayText || "";
    }

    text += fragmentText;

    // 为每个字符记录其对应的 ProseMirror 位置
    // 对于变量节点，所有字符都映射到节点的起始位置
    for (let i = 0; i < fragmentText.length; i++) {
      positionMap.push({
        charIndex: startCharIndex + i,
        proseMirrorPos:
          fragment.type === "text" ? fragment.from + i : fragment.from,
      });
    }
  });

  return { text, positionMap };
}

/**
 * 将字符索引映射为 ProseMirror 位置
 */
function mapCharIndexToProseMirrorPos(
  charIndex: number,
  positionMap: Array<{ charIndex: number; proseMirrorPos: number }>,
): number {
  // 找到最接近的位置映射
  for (let i = 0; i < positionMap.length; i++) {
    if (positionMap[i].charIndex >= charIndex) {
      return positionMap[i].proseMirrorPos;
    }
  }

  // 如果超出范围，返回最后一个位置
  return positionMap.length > 0
    ? positionMap[positionMap.length - 1].proseMirrorPos + 1
    : 0;
}

/**
 * 创建占位符到显示文本的映射
 */
function createPlaceholderMap(fragments: NodeFragment[]): Map<string, string> {
  const map = new Map<string, string>();

  fragments.forEach((fragment) => {
    if (fragment.type !== "text" && fragment.text && fragment.displayText) {
      map.set(fragment.text, fragment.displayText);
    }
  });

  return map;
}

/**
 * 将占位符替换为显示文本（支持部分匹配）
 */
function replacePlaceholdersWithDisplayText(
  text: string,
  placeholderMap: Map<string, string>,
): string {
  let result = text;

  // 尝试替换完整的占位符
  for (const [placeholder, displayText] of placeholderMap) {
    const regex = new RegExp(escapeRegExp(placeholder), "g");
    const newResult = result.replace(regex, displayText);
    if (newResult !== result) {
      result = newResult;
    }
  }

  // 如果没有找到完整匹配，尝试部分匹配
  if (result === text) {
    for (const [placeholder, displayText] of placeholderMap) {
      // 提取占位符中的实际内容（去掉特殊字符）
      const content = placeholder.replace(/[\uE000-\uE005]/g, "");
      // 检查占位符内容是否包含 diff 文本（而不是相反）
      if (content.includes(text)) {
        result = displayText;
        break;
      }
    }
  }

  return result;
}

/**
 * 转义正则表达式特殊字符
 */
function escapeRegExp(string: string): string {
  return string.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
}

/**
 * 在片段中查找变量信息
 */
function findVariableInFragments(
  text: string,
  fragments: NodeFragment[],
): { type: string; id: string } | null {
  for (const fragment of fragments) {
    if (fragment.type !== "text") {
      // 检查占位符文本或显示文本是否匹配
      if (fragment.text === text || fragment.displayText === text) {
        return {
          type: fragment.type,
          id: fragment.id || "unknown",
        };
      }

      // 检查是否是部分匹配（用于处理 diff 库的字符级分解）
      if (fragment.text && fragment.text.includes(text)) {
        return {
          type: fragment.type,
          id: fragment.id || "unknown",
        };
      }

      if (fragment.displayText && fragment.displayText.includes(text)) {
        return {
          type: fragment.type,
          id: fragment.id || "unknown",
        };
      }
    }
  }
  return null;
}

/**
 * 将 diff 变化转换为 DiffData 格式
 */
function convertChangesToDiffData(
  changes: Diff.Change[],
  oldPositionMap: Array<{ charIndex: number; proseMirrorPos: number }>,
  newPositionMap: Array<{ charIndex: number; proseMirrorPos: number }>,
  oldFragments: NodeFragment[],
  newFragments: NodeFragment[],
): DiffData {
  const diffData: DiffData = {
    added: [],
    removed: [],
  };

  // 创建占位符映射
  const oldPlaceholderMap = createPlaceholderMap(oldFragments);
  const newPlaceholderMap = createPlaceholderMap(newFragments);

  let oldCharIndex = 0;
  let newCharIndex = 0;

  for (const change of changes) {
    const value = change.value || "";

    if (change.added) {
      // 新增内容
      const from = mapCharIndexToProseMirrorPos(newCharIndex, newPositionMap);
      const to = mapCharIndexToProseMirrorPos(
        newCharIndex + value.length,
        newPositionMap,
      );

      // 检查是否为变量节点
      const variableInfo = findVariableInFragments(value, newFragments);

      diffData.added.push({
        from,
        to,
        newText: replacePlaceholdersWithDisplayText(value, newPlaceholderMap),
        nodeType: variableInfo?.type,
        isVariable: variableInfo !== null,
      });

      newCharIndex += value.length;
    } else if (change.removed) {
      // 删除内容
      const from = mapCharIndexToProseMirrorPos(oldCharIndex, oldPositionMap);
      const to = mapCharIndexToProseMirrorPos(
        oldCharIndex + value.length,
        oldPositionMap,
      );

      // 检查是否为变量节点
      const variableInfo = findVariableInFragments(value, oldFragments);

      diffData.removed.push({
        from,
        to,
        originalText: replacePlaceholdersWithDisplayText(
          value,
          oldPlaceholderMap,
        ),
        nodeType: variableInfo?.type,
        isVariable: variableInfo !== null,
      });

      oldCharIndex += value.length;
    } else {
      // 未变化内容
      oldCharIndex += value.length;
      newCharIndex += value.length;
    }
  }

  return diffData;
}

/**
 * 合并相邻的删除和新增为修改
 */
function mergeAdjacentChanges(diffData: DiffData): DiffData {
  const result: DiffData = {
    added: [...diffData.added],
    removed: [...diffData.removed],
  };

  return result;
}

/**
 * 计算两个 ProseMirror 文档中 module 节点的差异（支持文本和变量节点）
 */
export function computeModuleDiff(
  oldDoc: ProseMirrorNode,
  newDoc: ProseMirrorNode,
  options: DiffOptions = {
    compareByWords: true,
  },
): DiffData {
  // 提取节点片段（包括文本和变量节点）
  const oldFragments = extractNodeFragments(oldDoc);
  const newFragments = extractNodeFragments(newDoc);

  // 构建文本和位置映射
  const oldTextData = buildTextWithMappingFromNodes(oldFragments);
  const newTextData = buildTextWithMappingFromNodes(newFragments);

  // 如果两个文档的文本完全相同，直接返回空差异
  if (oldTextData.text === newTextData.text) {
    return {
      added: [],
      removed: [],
    };
  }

  // 使用 diff 库进行文本对比
  const changes = options.compareByWords
    ? Diff.diffWords(oldTextData.text, newTextData.text)
    : Diff.diffChars(oldTextData.text, newTextData.text);

  // 转换为 DiffData 格式
  let diffData = convertChangesToDiffData(
    changes,
    oldTextData.positionMap,
    newTextData.positionMap,
    oldFragments,
    newFragments,
  );

  // 合并相邻变化
  diffData = mergeAdjacentChanges(diffData);

  return diffData;
}

/**
 * 检查差异数据是否为空（无变化）
 */
export function isDiffEmpty(diffData: DiffData): boolean {
  return diffData.added.length === 0 && diffData.removed.length === 0;
}

/**
 * 获取差异统计信息
 */
export function getDiffStats(diffData: DiffData): {
  addedCount: number;
  removedCount: number;
  totalChanges: number;
} {
  return {
    addedCount: diffData.added.length,
    removedCount: diffData.removed.length,
    totalChanges: diffData.added.length + diffData.removed.length,
  };
}
