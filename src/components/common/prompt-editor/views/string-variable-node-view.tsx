import {
  Hover<PERSON><PERSON>,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";
import { cn } from "@/lib/utils";
import { NodeViewProps, NodeViewWrapper } from "@tiptap/react";
import { AlertTriangle } from "lucide-react";
import { LevelBadge, StringVariableTypeBadge } from "../../prompt";
import { useVariableContext } from "../contexts";
import type { StringVariableAttrs } from "../types";
import { isVariableVisible } from "../utils/variable-filter";

// 基于字符串生成固定颜色的函数
function generateVariableColor(name: string) {
  let hash = 0;
  for (let i = 0; i < name.length; i++) {
    hash = name.charCodeAt(i) + ((hash << 5) - hash);
  }

  // 颜色组合
  const colors = [
    {
      bg: "bg-yellow-200/60",
      text: "text-yellow-800",
      hover: "hover:bg-yellow-200/80",
    },
    {
      bg: "bg-pink-200/60",
      text: "text-pink-800",
      hover: "hover:bg-pink-200/80",
    },
    {
      bg: "bg-blue-200/60",
      text: "text-blue-800",
      hover: "hover:bg-blue-200/80",
    },
    {
      bg: "bg-orange-200/60",
      text: "text-orange-800",
      hover: "hover:bg-orange-200/80",
    },
    {
      bg: "bg-purple-200/60",
      text: "text-purple-800",
      hover: "hover:bg-purple-200/80",
    },
    {
      bg: "bg-amber-200/60",
      text: "text-amber-800",
      hover: "hover:bg-amber-200/80",
    },
    {
      bg: "bg-zinc-200/60",
      text: "text-zinc-800",
      hover: "hover:bg-zinc-200/80",
    },
    {
      bg: "bg-stone-200/60",
      text: "text-stone-800",
      hover: "hover:bg-stone-200/80",
    },
    {
      bg: "bg-cyan-200/60",
      text: "text-cyan-800",
      hover: "hover:bg-cyan-200/80",
    },
  ];

  return colors[Math.abs(hash) % colors.length];
}

export const StringVariableNodeView = ({ node }: NodeViewProps) => {
  const { id } = node.attrs as StringVariableAttrs;
  const { findVariableById, currentEditContext } = useVariableContext();

  // 通过ID从context获取变量数据
  const variable = findVariableById(id);

  // 如果找不到变量数据，显示错误状态
  if (!variable) {
    return (
      <NodeViewWrapper className="inline-block">
        <span className="inline-flex items-center gap-0.5 px-1 py-0 mx-0.5 text-sm rounded-lg bg-red-200/60 text-red-800">
          <span className="leading-none opacity-60">{"{"}</span>
          <span>变量 {id} 不存在</span>
          <span className="leading-none opacity-60">{"}"}</span>
        </span>
      </NodeViewWrapper>
    );
  }

  // 检查变量在当前上下文中是否合法
  const isValid =
    variable.originalData &&
    "level" in variable.originalData &&
    variable.originalData.level
      ? isVariableVisible(
          variable.originalData.level,
          variable.originalData.scopeDetail,
          currentEditContext,
        )
      : true; // 如果没有等级信息，默认为合法

  const displayName = variable.name || "变量";
  const colors = generateVariableColor(displayName);

  // 如果变量不合法，显示错误状态
  if (!isValid) {
    return (
      <NodeViewWrapper className="inline-block">
        <HoverCard openDelay={400}>
          <HoverCardTrigger asChild>
            <span className="inline-flex items-center gap-0.5 px-1 py-0 mx-0.5 text-sm rounded-lg cursor-pointer bg-red-200/60 text-red-800">
              <AlertTriangle className="size-3" />
              <span className="leading-none opacity-60">{"{"}</span>
              <span>{displayName}</span>
              <span className="leading-none opacity-60">{"}"}</span>
            </span>
          </HoverCardTrigger>
          <HoverCardContent
            className="w-64 p-3 max-h-96 overflow-auto"
            side="bottom"
            align="start"
          >
            <div className="space-y-2">
              {/* 错误提示 */}
              <div className="flex items-center gap-2 text-red-600">
                <AlertTriangle className="size-4" />
                <span className="font-medium text-sm">
                  变量在当前影响范围不可用
                </span>
              </div>

              {/* 变量基本信息 */}
              <div className="flex items-center gap-2">
                <span className="font-medium text-sm truncate">
                  {displayName}
                </span>
                <div className="text-xs text-muted-foreground truncate">
                  {variable.promptName}
                </div>
              </div>

              {/* 属性信息 */}
              <div className="flex items-center gap-2 text-gray-600">
                {variable.originalData && "type" in variable.originalData && (
                  <StringVariableTypeBadge type={variable.originalData.type} />
                )}
                {variable.originalData &&
                  "level" in variable.originalData &&
                  variable.originalData.level && (
                    <LevelBadge level={variable.originalData.level} />
                  )}
              </div>

              {/* 定义和示例 */}
              {(variable.definition ||
                (variable.originalData &&
                  "example" in variable.originalData &&
                  variable.originalData.example)) && (
                <div className="space-y-2">
                  {variable.definition && (
                    <div>
                      <span className="text-xs text-gray-700">定义：</span>
                      <p className="text-xs text-gray-600 mt-1">
                        {variable.definition}
                      </p>
                    </div>
                  )}
                  {variable.originalData &&
                    "example" in variable.originalData &&
                    variable.originalData.example && (
                      <div>
                        <span className="text-xs font-medium text-gray-700">
                          示例：
                        </span>
                        <p className="text-xs text-gray-600 font-mono bg-gray-50 p-1 rounded mt-1">
                          {variable.originalData.example}
                        </p>
                      </div>
                    )}
                </div>
              )}
            </div>
          </HoverCardContent>
        </HoverCard>
      </NodeViewWrapper>
    );
  }

  return (
    <NodeViewWrapper className="inline-block">
      <HoverCard openDelay={400}>
        <HoverCardTrigger asChild>
          <span
            className={cn(
              "inline-flex items-center gap-0.5 px-1 py-0 mx-0.5 text-sm rounded-lg cursor-pointer transition-all duration-200",
              colors.bg,
              colors.text,
              colors.hover,
            )}
          >
            <span className="leading-none opacity-60">{"{"}</span>
            <span>{displayName}</span>
            <span className="leading-none opacity-60">{"}"}</span>
          </span>
        </HoverCardTrigger>
        <HoverCardContent
          className="w-64 p-3 max-h-96 overflow-auto"
          side="bottom"
          align="start"
        >
          <div className="space-y-2">
            {/* 头部 */}
            <div className="flex items-center gap-2">
              <span className="font-medium text-sm truncate">
                {displayName}
              </span>
              <div className="text-xs text-muted-foreground truncate">
                {variable.promptName}
              </div>
            </div>

            {/* 状态信息 */}
            <div className="flex items-center gap-2 text-green-600">
              <span className="text-xs font-medium">
                ✓ 变量在当前上下文中可用
              </span>
            </div>

            {/* 属性信息 - 横向排列 */}
            <div className="flex items-center gap-2 text-gray-600">
              {variable.originalData && "type" in variable.originalData && (
                <StringVariableTypeBadge type={variable.originalData.type} />
              )}
              {variable.originalData &&
                "level" in variable.originalData &&
                variable.originalData.level && (
                  <LevelBadge level={variable.originalData.level} />
                )}
            </div>
            {/* 定义和示例 */}
            {(variable.definition ||
              (variable.originalData &&
                "example" in variable.originalData &&
                variable.originalData.example)) && (
              <div className="space-y-2">
                {variable.definition && (
                  <div>
                    <span className="text-xs text-gray-700">定义：</span>
                    <p className="text-xs text-gray-600 mt-1">
                      {variable.definition}
                    </p>
                  </div>
                )}
                {variable.originalData &&
                  "example" in variable.originalData &&
                  variable.originalData.example && (
                    <div>
                      <span className="text-xs font-medium text-gray-700">
                        示例：
                      </span>
                      <p className="text-xs text-gray-600 font-mono bg-gray-50 p-1 rounded mt-1">
                        {variable.originalData.example}
                      </p>
                    </div>
                  )}
              </div>
            )}
          </div>
        </HoverCardContent>
      </HoverCard>
    </NodeViewWrapper>
  );
};
