import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import { GripVertical, MoreHorizontal } from "lucide-react";

export interface PromptEditorSkeletonProps {
  className?: string;
  /** 段落数量 */
  paragraphCount?: number;
  /** 是否显示为可编辑状态 */
  editable?: boolean;
}

export const PromptEditorContentSkeleton = ({
  paragraphCount = 1,
}: PromptEditorSkeletonProps) => {
  return (
    <div className="relative w-full bg-background outline-none overflow-hidden">
      <div className="h-full m-1 overflow-auto">
        <div className="prose prose-sm mx-auto focus:outline-none min-h-full max-w-none">
          <div className="overflow-auto">
            <div className="relative py-4">
              {/* 段落骨架 */}
              {Array.from({ length: paragraphCount }).map((_, index) => (
                <div key={index} className="mx-0 my-1 px-4">
                  {/* 模拟不同长度的段落 */}
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-3/4" />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export const PromptEditorSkeleton = ({
  className,
  paragraphCount = 1,
  editable = true,
}: PromptEditorSkeletonProps) => {
  return (
    <div className={cn("p-1 bg-gray-100 rounded-lg", className)}>
      <div
        className={cn(
          "border border-gray-200 rounded-lg bg-background shadow-xs",
          "focus-within:border-ring focus-within:ring-ring/50 focus-within:ring-[3px]",
        )}
      >
        {/* 顶部标题栏骨架 */}
        <div className="flex items-center justify-between px-3 py-2 border-b border-border h-[47px]">
          <div className="flex items-center gap-2">
            {editable && <GripVertical className="h-4 w-4 text-gray-400" />}
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-5 w-16 rounded-full" />
          </div>

          <div className="flex items-center gap-2">
            {editable && (
              <div className="w-8 h-8 flex items-center justify-center">
                <MoreHorizontal className="h-4 w-4 text-gray-400" />
              </div>
            )}
          </div>
        </div>

        {/* 编辑器内容骨架 */}
        <PromptEditorContentSkeleton paragraphCount={paragraphCount} />
      </div>
    </div>
  );
};
