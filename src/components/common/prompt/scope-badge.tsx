import { userAtom } from "@/atoms/auth";
import { Tooltip } from "@/components/common/tooltip";
import { cn } from "@/lib/utils";
import { CategoryService } from "@/services/category-service";
import { Level } from "@/types/api/prompt";
import { Category } from "@/types/api/shop";
import { safeParseJson } from "@/utils";
import { useQuery } from "@tanstack/react-query";
import { useAtomValue } from "jotai";
import { MapPin } from "lucide-react";
import { useMemo } from "react";

export interface ScopeBadgeProps {
  level: Level;
  scopeDetail?: string | null;
  className?: string;
}

/**
 * 影响范围徽章组件
 * 根据等级和范围详情显示相应的范围信息
 */
export const ScopeBadge = ({
  level,
  scopeDetail,
  className,
}: ScopeBadgeProps) => {
  const { data: user } = useAtomValue(userAtom);

  // 获取类目数据
  const { data: categories } = useQuery<Category[]>({
    queryKey: ["categories"],
    queryFn: CategoryService.selectCategoryList,
    enabled: level === Level.Category,
  });

  // 解析 scopeDetail
  const parsedScopeDetail = useMemo(() => {
    if (!scopeDetail) return [];

    return safeParseJson<string[]>(scopeDetail, []);
  }, [scopeDetail]);

  // 获取显示内容
  const displayContent = useMemo(() => {
    if (level === Level.Product) {
      return "产品级";
    }

    if (parsedScopeDetail.length === 0) {
      return level === Level.Shop ? "店铺级" : "类目级";
    }

    if (level === Level.Shop) {
      // 显示店铺名称
      const shopNames = parsedScopeDetail
        .map((shopId) => {
          const shop = user?.shopList?.find((s) => s.id.toString() === shopId);

          return shop?.name;
        })
        .filter(Boolean);

      if (shopNames.length === 0) {
        return "店铺级";
      }

      if (shopNames.length === 1) {
        return shopNames[0];
      }

      return `${shopNames[0]} 等${shopNames.length}个店铺`;
    }

    if (level === Level.Category) {
      // 显示类目名称
      const flatten = (cats: Category[]): Category[] => {
        const result: Category[] = [];

        cats.forEach((cat) => {
          result.push(cat);
          if (cat.child && cat.child.length > 0) {
            result.push(...flatten(cat.child));
          }
        });

        return result;
      };

      const flatCategories = categories ? flatten(categories) : [];

      const categoryNames = parsedScopeDetail
        .map((categoryId) => {
          const category = flatCategories.find(
            (c) => c.categoryId === categoryId,
          );
          return category?.categoryName;
        })
        .filter(Boolean);

      if (categoryNames.length === 0) {
        return "类目级";
      }

      if (categoryNames.length === 1) {
        return categoryNames[0];
      }

      return `${categoryNames[0]} 等${categoryNames.length}个类目`;
    }

    return "未知范围";
  }, [level, parsedScopeDetail, user?.shopList, categories]);

  // 获取样式
  const badgeVariant = useMemo(() => {
    switch (level) {
      case Level.Shop:
        return "bg-red-100 text-red-800";
      case Level.Category:
        return "bg-orange-100 text-orange-800";
      case Level.Product:
        return "bg-blue-100 text-blue-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  }, [level]);

  // 获取 tooltip 内容
  const tooltipContent = useMemo(() => {
    if (level === Level.Product) {
      return null;
    }

    if (parsedScopeDetail.length === 0) {
      return null;
    }

    if (level === Level.Shop) {
      const shopNames = parsedScopeDetail
        .map((shopId) => {
          const shop = user?.shopList?.find((s) => s.id.toString() === shopId);
          return shop?.name;
        })
        .filter(Boolean);

      if (shopNames.length === 0) {
        return null;
      }

      return shopNames.join("\n");
    }

    if (level === Level.Category) {
      const flatten = (cats: Category[]): Category[] => {
        const result: Category[] = [];

        cats.forEach((cat) => {
          result.push(cat);
          if (cat.child && cat.child.length > 0) {
            result.push(...flatten(cat.child));
          }
        });

        return result;
      };

      const flatCategories = categories ? flatten(categories) : [];

      const categoryNames = parsedScopeDetail
        .map((categoryId) => {
          const category = flatCategories.find(
            (c) => c.categoryId === categoryId,
          );
          return category?.categoryName;
        })
        .filter(Boolean);

      if (categoryNames.length === 0) {
        return "类目级影响范围";
      }

      return categoryNames.join("\n");
    }

    return;
  }, [level, parsedScopeDetail, user?.shopList, categories]);

  return (
    <Tooltip disabled={tooltipContent == null} content={tooltipContent}>
      <span
        className={cn(
          "px-2 py-1 inline-flex items-center gap-x-1 text-xs rounded-full",
          badgeVariant,
          className,
        )}
      >
        <MapPin className="shrink-0 size-3" />
        {displayContent}
      </span>
    </Tooltip>
  );
};
