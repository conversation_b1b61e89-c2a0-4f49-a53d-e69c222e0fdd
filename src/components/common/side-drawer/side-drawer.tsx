import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import { ReactNode, useCallback } from "react";

export interface SideDrawerProps {
  /**
   * 抽屉是否打开
   */
  open: boolean;
  /**
   * 抽屉标题
   */
  title: string;
  /**
   * 抽屉描述（可选）
   */
  description?: string;
  /**
   * 抽屉内容
   */
  children: ReactNode;
  /**
   * 抽屉最小宽度，默认为66.7vw
   */
  minWidth?: string;
  /**
   * 抽屉最大宽度，默认为1200px
   */
  maxWidth?: string;
  /**
   * 是否禁用默认行为
   */
  modal?: boolean;
  /**
   * 关闭抽屉的回调函数
   */
  onClose: () => void;
}

export const SideDrawer = ({
  open,
  title,
  description,
  children,
  minWidth = "66.7vw",
  maxWidth = "1200px",
  modal = true,
  onClose,
}: SideDrawerProps) => {
  const avoidDefaultDomBehavior = useCallback(
    (e: Event) => {
      if (modal) {
        e.preventDefault();
      }
    },
    [modal],
  );

  return (
    <Sheet open={open} onOpenChange={onClose}>
      <SheetContent
        className="flex flex-col p-0"
        style={{ minWidth, maxWidth }}
        onPointerDownOutside={avoidDefaultDomBehavior}
        onInteractOutside={avoidDefaultDomBehavior}
        onOpenAutoFocus={avoidDefaultDomBehavior}
        onEscapeKeyDown={avoidDefaultDomBehavior}
      >
        <div className="flex flex-col h-full">
          <SheetHeader>
            <SheetTitle className="m-0 text-base font-semibold leading-tight">
              {title}
            </SheetTitle>
          </SheetHeader>
          <SheetDescription className="sr-only">{description}</SheetDescription>
          <div className="flex-1 min-h-0">{children}</div>
        </div>
      </SheetContent>
    </Sheet>
  );
};
