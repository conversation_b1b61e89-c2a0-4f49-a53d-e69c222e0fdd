import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Save } from "lucide-react";
import { ReactNode } from "react";
import { SideDrawer, SideDrawerProps } from "./side-drawer";

export interface SideDrawerWithFormProps
  extends Omit<SideDrawerProps, "children"> {
  /**
   * 表单内容
   */
  children: ReactNode;
  /**
   * 取消按钮文本
   */
  cancelText?: string;
  /**
   * 保存按钮文本
   */
  saveText?: string;
  /**
   * 保存按钮回调
   */
  onSave: () => void;
  /**
   * 是否禁用保存按钮
   */
  saveDisabled?: boolean;
  /**
   * 自定义底部操作区
   */
  footerActions?: ReactNode;
}

export const SideDrawerWithForm = ({
  children,
  saveDisabled = false,
  footerActions,
  cancelText = "取消",
  saveText = "保存",
  onClose,
  onSave,
  ...drawerProps
}: SideDrawerWithFormProps) => {
  return (
    <SideDrawer {...drawerProps} onClose={onClose}>
      <div className="flex flex-col h-full">
        <ScrollArea className="flex-1 min-h-0" horizontal={true}>
          {children}
        </ScrollArea>

        <div className="flex items-center justify-end gap-3 p-4 border-t border-t-border">
          {footerActions || (
            <>
              <Button type="button" variant="outline" onClick={onClose}>
                {cancelText}
              </Button>
              <Button type="submit" onClick={onSave} disabled={saveDisabled}>
                <Save size={14} />
                {saveText}
              </Button>
            </>
          )}
        </div>
      </div>
    </SideDrawer>
  );
};
