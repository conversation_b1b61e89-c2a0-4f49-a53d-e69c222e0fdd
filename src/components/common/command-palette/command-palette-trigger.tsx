import { Button } from "@/components/ui/button";
import { CommandShortcut } from "@/components/ui/command";
import { Search } from "lucide-react";
import { useTranslation } from "react-i18next";
import { useCommandPalette } from "./command-palette-provider";

const isMac =
  typeof navigator !== "undefined" &&
  navigator.platform.toUpperCase().indexOf("MAC") >= 0;

export const CommandPaletteTrigger = () => {
  const { t } = useTranslation();
  const { toggle } = useCommandPalette();

  return (
    <Button
      variant="outline"
      size="sm"
      onClick={toggle}
      className="relative h-8 justify-start rounded-[0.5rem] bg-background text-sm font-normal text-muted-foreground shadow-none w-40"
    >
      <div className="flex items-center w-full">
        <Search className="mr-2 h-4 w-4" />
        <span>{t("command.palette.command")}</span>
        <CommandShortcut className="pointer-events-none absolute right-[0.3rem] top-[0.3rem] hidden h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 py-0.5 font-mono text-[10px] font-medium opacity-100 sm:flex">
          <span className="text-xs">{isMac ? "⌘" : "Ctrl"}</span>K
        </CommandShortcut>
      </div>
    </Button>
  );
};
