import {
  MenuItem,
  useMenuConfig,
} from "@/components/common/app-sidebar/menu-config";
import {
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { useNavigate } from "@tanstack/react-router";
import {
  LucideIcon,
  MessageSquare,
  MessagesSquare,
  WandSparkles,
} from "lucide-react";
import Pinyin from "pinyin-match";
import { useMemo, useState } from "react";
import { useTranslation } from "react-i18next";

interface CommandItem {
  id: string;
  title: string;
  subtitle?: string;
  icon: LucideIcon;
  route: string;
  search?: Record<string, string>;
}

interface CommandPaletteProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

// 递归提取所有菜单项
const extractMenuItems = (
  items: MenuItem[],
  parentTitle?: string,
): CommandItem[] => {
  const result: CommandItem[] = [];

  items.forEach((item) => {
    // 如果有子菜单，递归处理
    if (item.children && item.children.length > 0) {
      result.push(...extractMenuItems(item.children, item.text));
    } else {
      // 只添加最终的菜单项（没有子菜单的项）
      result.push({
        id: item.to,
        title: item.text,
        subtitle: parentTitle,
        icon: item.icon,
        route: item.to,
      });
    }
  });

  return result;
};

const CommandPaletteItem = ({ command, onSelect }) => (
  <CommandItem
    value={`${command.title} ${command.subtitle || ""}`}
    onSelect={() => onSelect(command)}
    className="flex items-center gap-2"
  >
    <command.icon className="h-4 w-4" />
    <div className="flex flex-col">
      <span>{command.title}</span>
      {command.subtitle && (
        <span className="text-xs text-muted-foreground">
          {command.subtitle}
        </span>
      )}
    </div>
  </CommandItem>
);

export const CommandPalette = ({ open, onOpenChange }: CommandPaletteProps) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { main: mainMenus, footer: footerMenus } = useMenuConfig();
  const [searchValue, setSearchValue] = useState("");

  const commands = useMemo(() => {
    const allMenuItems = [...mainMenus, ...footerMenus];
    return extractMenuItems(allMenuItems);
  }, [mainMenus, footerMenus]);

  // 自定义过滤函数，支持拼音搜索
  const customFilter = (value: string, search: string) => {
    // 如果没有搜索词，显示所有项目
    if (!search.trim()) return 1;

    // 支持直接文本匹配
    if (value.toLowerCase().includes(search.toLowerCase())) {
      return 1;
    }

    // 支持拼音匹配
    try {
      const matchResult = Pinyin.match(value, search);
      return matchResult !== false ? 1 : 0;
    } catch {
      return 0;
    }
  };

  // 生成动态命令（消息ID和会话ID）
  const dynamicCommands = useMemo(() => {
    const commands: CommandItem[] = [];

    if (searchValue.startsWith("msg_")) {
      commands.push({
        id: `message-${searchValue}`,
        title: `跳转到消息：${searchValue}`,
        subtitle: "消息详情",
        icon: MessageSquare,
        route: "/chat",
        search: { msgId: searchValue },
      });
      commands.push({
        id: `effect-evaluation-${searchValue}`,
        title: `调试消息：${searchValue}`,
        subtitle: "效果调试",
        icon: WandSparkles,
        route: `/effect-evaluation/${searchValue}`,
      });
    }

    if (searchValue.startsWith("conv_")) {
      commands.push({
        id: `conversation-${searchValue}`,
        title: `跳转到会话：${searchValue}`,
        subtitle: "会话详情",
        icon: MessagesSquare,
        route: "/chat",
        search: { conversationId: searchValue },
      });
    }

    return commands;
  }, [searchValue]);

  const handleSelect = (command: CommandItem) => {
    onOpenChange(false);
    setSearchValue(""); // 重置搜索值
    navigate({
      to: command.route,
      search: command.search,
    });
  };

  // 当对话框关闭时重置搜索值
  const handleOpenChange = (open: boolean) => {
    if (!open) {
      setSearchValue("");
    }
    onOpenChange(open);
  };

  return (
    <CommandDialog
      open={open}
      onOpenChange={handleOpenChange}
      className="sm:max-w-4xl max-h-[80vh]"
      filter={customFilter}
    >
      <CommandInput
        placeholder={t("command.palette.search.placeholder")}
        value={searchValue}
        onValueChange={setSearchValue}
      />
      <CommandList className="max-h-[500px]">
        <CommandEmpty>{t("command.palette.no.results")}</CommandEmpty>

        {/* 动态命令组（消息ID和会话ID） */}
        {dynamicCommands.length > 0 && (
          <CommandGroup heading={t("command.palette.quick.jump")}>
            {dynamicCommands.map((command) => (
              <CommandPaletteItem
                key={command.id}
                command={command}
                onSelect={handleSelect}
              />
            ))}
          </CommandGroup>
        )}

        {/* 导航命令组 */}
        <CommandGroup heading={t("command.palette.navigation")}>
          {commands.map((command) => (
            <CommandPaletteItem
              key={command.id}
              command={command}
              onSelect={handleSelect}
            />
          ))}
        </CommandGroup>
      </CommandList>
    </CommandDialog>
  );
};
