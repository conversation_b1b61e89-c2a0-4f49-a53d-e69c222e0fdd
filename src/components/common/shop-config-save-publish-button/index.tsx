import { shopAtom } from "@/atoms/shop";
import { ConfigDiffDialog } from "@/components/common/config-diff-viewer/config-diff-dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogPortal,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { EvaluationConfigService } from "@/services/evaluation-config-service";
import { ShopConfigService } from "@/services/config-shop";
import { ConfigVersionStatus } from "@/types/api/version";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useAtomValue } from "jotai";
import { useState, useCallback } from "react";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";
import { UseFormReturn } from "react-hook-form";

interface PropsType {
  configId: string;
  shopId: string;
  form: UseFormReturn<any>;
  getRestoredData: (data: any) => any;
  onFinish?: () => void;
  refetch?: () => void;
}

export const SaveAndPublishButton = ({
  configId,
  shopId,
  form,
  getRestoredData,
  refetch,
}: PropsType) => {
  const { t } = useTranslation();
  const [showDiffDialog, setShowDiffDialog] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const shop = useAtomValue(shopAtom);

  const { data: versionList } = useQuery({
    queryKey: ["getVersionList", shop?.id],
    queryFn: () =>
      EvaluationConfigService.getVersionList(
        {
          version: "",
        },
        { shopId: shop?.id },
      ),
    enabled: showDiffDialog,
  });

  // 找出线上版本
  const onlineVersion = versionList?.find(
    (version) => version.onlineStatus === ConfigVersionStatus.ONLINE,
  );

  // 保存配置
  const { mutate: saveConfig } = useMutation({
    mutationFn: async (data: any) => {
      const shopConfigParams = getRestoredData(data);
      return ShopConfigService.saveShopConfig({
        configId,
        shopConfig: JSON.stringify(shopConfigParams),
        shopId,
      });
    },
    onSuccess: (res) => {
      if (res.success) {
        refetch?.();
        toast.success("发布成功");
        // 保存成功后显示差异对话框
        setShowDiffDialog(false);
      } else {
        toast.error("发布失败");
      }
      setIsSaving(false);
    },
    onError: (error) => {
      toast.error(
        "发布失败: " + (error instanceof Error ? error.message : "未知错误"),
      );
      setIsSaving(false);
    },
  });

  // 处理表单验证和保存
  const handleValidateAndSave = useCallback(async () => {
    if (isSaving) return; // 防止重复提交
    try {
      await form.handleSubmit(
        async () => {
          setShowDiffDialog(true);
        },
        (err) => {
          console.error(err);

          const getErrorMessage = (errors: any): string => {
            const errorMessages: string[] = [];
            const extractErrors = (obj: any, path = "") => {
              if (obj && typeof obj === "object") {
                if (obj.message) {
                  errorMessages.push(obj.message);
                } else {
                  Object.keys(obj).forEach((key) => {
                    const newPath = path ? `${path}.${key}` : key;
                    extractErrors(obj[key], newPath);
                  });
                }
              }
            };
            extractErrors(errors);
            return errorMessages.length > 0
              ? errorMessages.join("; ")
              : "表单验证失败，请检查输入";
          };
          const errorMessage = getErrorMessage(err);
          toast.error(errorMessage);
          setIsSaving(false);
        },
      )();
    } catch (error) {
      setIsSaving(false);
    }
  }, [form, isSaving]);

  const handleProceedToConfirm = () => {
    setShowConfirmDialog(true);
  };

  const renderDiffDialogFooter = (
    <div className="flex items-center justify-end gap-2">
      <Button variant="outline" onClick={() => setShowDiffDialog(false)}>
        {t("common.delete.confirm.cancel")}
      </Button>
      <Button onClick={handleProceedToConfirm}>{t("publish.online")}</Button>
    </div>
  );

  return (
    <>
      <Button
        onClick={handleValidateAndSave}
        loading={isSaving}
        disabled={isSaving}
      >
        发布
      </Button>

      <ConfigDiffDialog
        open={showDiffDialog}
        localJSON={JSON.stringify(getRestoredData(form.getValues()))}
        currentVersionId={configId}
        baseVersionId={onlineVersion?.configId}
        showVersionSelector={false}
        footer={renderDiffDialogFooter}
        onOpenChange={setShowDiffDialog}
      >
        <AlertDialog
          open={showConfirmDialog}
          onOpenChange={setShowConfirmDialog}
        >
          <AlertDialogPortal>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>{t("publish.confirm")}</AlertDialogTitle>
                <AlertDialogDescription>
                  {t("publish.confirm.content")}
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>
                  {t("common.delete.confirm.cancel")}
                </AlertDialogCancel>
                <AlertDialogAction
                  onClick={() => {
                    saveConfig(form.getValues());
                  }}
                >
                  {t("publish.online")}
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialogPortal>
        </AlertDialog>
      </ConfigDiffDialog>
    </>
  );
};
