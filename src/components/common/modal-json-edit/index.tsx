import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON><PERSON><PERSON><PERSON>,
  DialogFooter,
} from "@/components/ui/dialog";
import { useTranslation } from "react-i18next";
import { useMemo, useState } from "react";
import { Editor } from "@/components/common/code-mirror";
import React<PERSON><PERSON> from "react-json-view";

interface JsonEditProps {
  open: boolean;
  onCancel: () => void;
  onSubmit: (jsonData: any) => void;
  data: string | null;
  title?: string;
  type: "look" | "edit";
}

export const ModalJsonEdit = ({
  open,
  onCancel,
  onSubmit,
  data,
  title = "编辑JSON",
  type = "edit",
}: JsonEditProps) => {
  const { t } = useTranslation();
  const [isEdit, setIsEdit] = useState(false);
  const [editJson, setEditJson] = useState<any>({});

  const dataJson = useMemo(() => {
    if (open && data) {
      try {
        return JSON.parse(data);
      } catch (error) {
        console.error("JSON解析错误", error);
        return {};
      }
    }
    return {};
  }, [open, data]);

  const handleSubmit = () => {
    onSubmit(editJson);
    setIsEdit(false);
  };

  return (
    <Dialog open={open} onOpenChange={onCancel}>
      <DialogContent className="w-[800px] max-w-full">
        <DialogTitle className="flex items-center">{title}</DialogTitle>
        <div className="min-h-[400px] max-h-[600px] overflow-auto">
          {isEdit ? (
            <Editor
              value={dataJson ? JSON.stringify(dataJson, null, 2) : "{}"}
              onChange={(e) => {
                try {
                  setEditJson(JSON.parse(e));
                } catch (error) {
                  console.error("JSON解析错误", error);
                }
              }}
            />
          ) : (
            <ReactJson src={dataJson} collapsed={1} enableClipboard={false} />
          )}
        </div>
        <DialogFooter>
          {isEdit ? (
            <>
              <Button type="button" onClick={handleSubmit}>
                {t("common.confirm")}
              </Button>
              <Button
                type="button"
                onClick={() => {
                  setIsEdit(false);
                }}
                variant="outline"
              >
                {t("common.cancel")}
              </Button>
            </>
          ) : (
            <>
              {type === "edit" ? (
                <Button
                  type="button"
                  onClick={() => {
                    setIsEdit(true);
                    setEditJson(dataJson);
                  }}
                >
                  {t("common.edit")}
                </Button>
              ) : (
                ""
              )}
            </>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
