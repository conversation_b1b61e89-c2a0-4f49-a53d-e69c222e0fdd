import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";
import { MemoizedMarkdownBlock } from "@/components/ui/markdown-content";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Skeleton } from "@/components/ui/skeleton";
import { PromptVersionService } from "@/services/prompt-version-service";
import { PromptVersion } from "@/types/api/prompt-version";
import { useQuery } from "@tanstack/react-query";
import { useTranslation } from "react-i18next";
import { getPromptVersionDetails } from "./helper";
import { PromptVersionStatusView } from "./prompt-version-status-view";

export function PromptVersionHoverCard({
  version,
  side = "right",
  sideOffset = 20,
  ...props
}: {
  version: PromptVersion;
  side?: "left" | "right";
  sideOffset?: number;
}) {
  const { t } = useTranslation();

  // 获取基准版本详情
  const { data: baseVersionData, isLoading: isBaseVersionLoading } = useQuery({
    queryKey: ["getPromptVersionDetail", version.basePromptVersionId],
    queryFn: () => {
      if (!version.basePromptVersionId) {
        return null;
      }
      return PromptVersionService.getPromptVersionDetail({
        promptVersionId: version.basePromptVersionId,
      });
    },
    enabled: !!version.basePromptVersionId,
  });

  const renderDetails = () => {
    const details = getPromptVersionDetails(version, t, false);

    return details.map((detail, index) => (
      <div key={index} className="flex justify-between mt-1 text-xs">
        <span className="text-muted-foreground">{detail.label}</span>
        <span>{detail.value}</span>
      </div>
    ));
  };

  const renderBaseVersionInfo = () => {
    if (!version.basePromptVersionId) {
      return (
        <div className="text-xs text-muted-foreground">
          {t("version.base")}: {t("version.base.none")}
        </div>
      );
    }

    if (isBaseVersionLoading) {
      return (
        <div className="space-y-2">
          <div className="text-xs font-medium text-muted-foreground">
            {t("version.base")}
          </div>
          <div className="space-y-1">
            <Skeleton className="h-3 w-24" />
            <Skeleton className="h-3 w-32" />
            <Skeleton className="h-3 w-16" />
          </div>
        </div>
      );
    }

    if (!baseVersionData) {
      return (
        <div className="space-y-1">
          <div className="text-xs font-medium text-muted-foreground">
            {t("version.base")}
          </div>
          <div className="text-xs text-muted-foreground">
            {t("version.base.get.failed")}
          </div>
        </div>
      );
    }

    return (
      <div className="space-y-1">
        <div className="text-xs font-medium text-muted-foreground">
          {t("version.base")}
        </div>
        <div className="flex justify-between text-xs">
          <span className="text-muted-foreground">{t("version.num")}</span>
          <span>{baseVersionData.version}</span>
        </div>
        <div className="flex justify-between text-xs">
          <span className="text-muted-foreground">{t("version.name")}</span>
          <span
            className="truncate max-w-32"
            title={baseVersionData.versionName}
          >
            {baseVersionData.versionName}
          </span>
        </div>
        <div className="flex justify-between text-xs items-center">
          <span className="text-muted-foreground">{t("version.status")}</span>
          <PromptVersionStatusView version={baseVersionData} />
        </div>
      </div>
    );
  };

  return (
    <HoverCard openDelay={800}>
      <HoverCardTrigger asChild>
        <div className="flex flex-col w-full gap-1 cursor-default">
          <div className="flex w-full justify-between items-center gap-1">
            <div className="flex-1 min-w-0">
              <span className="break-all line-clamp-2" data-version-name>
                {version.versionName}
              </span>
            </div>
            <PromptVersionStatusView version={version} />
          </div>
          <div className="data-option-remark line-clamp-2 text-xs text-muted-foreground whitespace-normal">
            <MemoizedMarkdownBlock content={version.remark} />
          </div>
        </div>
      </HoverCardTrigger>
      <HoverCardContent
        className="px-0 py-4 w-80"
        side={side}
        align="start"
        sideOffset={sideOffset}
        onClick={(e) => e.stopPropagation()}
        {...props}
      >
        <ScrollArea className="flex flex-col px-4 max-h-[300px]">
          <div className="flex flex-col gap-3">
            <div className="flex items-center justify-between">
              <h4 className="font-medium">{version.version}</h4>
              <PromptVersionStatusView version={version} />
            </div>

            {version.remark && (
              <div className="flex-col text-sm border-l-2 border-primary p-2 bg-gray-50 rounded-br-md rounded-tr-md">
                <MemoizedMarkdownBlock content={version.remark} />
              </div>
            )}

            <div className="mt-1 text-xs">{renderDetails()}</div>

            {/* 基准版本信息区域 */}
            <div className="border-t pt-2">{renderBaseVersionInfo()}</div>
          </div>
        </ScrollArea>
      </HoverCardContent>
    </HoverCard>
  );
}
