import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Skeleton } from "@/components/ui/skeleton";
import { PromptVersionService } from "@/services/prompt-version-service";
import type { DraftVersion } from "@/features/prompt/management/types";
import { useQuery } from "@tanstack/react-query";
import { useTranslation } from "react-i18next";
import dayjs from "dayjs";
import { FileEdit } from "lucide-react";
import { PromptVersionStatusView } from "./prompt-version-status-view";

interface DraftVersionHoverCardProps {
  draftVersion: DraftVersion;
  side?: "left" | "right";
  sideOffset?: number;
  children: React.ReactNode;
}

export function DraftVersionHoverCard({
  draftVersion,
  side = "right",
  sideOffset = 20,
  children,
  ...props
}: DraftVersionHoverCardProps) {
  const { t } = useTranslation();

  // 获取基准版本详情（如果存在）
  const { data: baseVersionData, isLoading: isBaseVersionLoading } = useQuery({
    queryKey: ["getPromptVersionDetail", draftVersion.baseVersionId],
    queryFn: () => {
      if (!draftVersion.baseVersionId) {
        return null;
      }
      return PromptVersionService.getPromptVersionDetail({
        promptVersionId: draftVersion.baseVersionId,
      });
    },
    enabled: !!draftVersion.baseVersionId,
  });

  const renderDraftDetails = () => {
    const details = [
      {
        label: "草稿ID",
        value: draftVersion.id.slice(0, 8) + "...",
      },
      {
        label: "创建时间",
        value: dayjs
          .unix(+draftVersion.createdAt)
          .format("YYYY-MM-DD HH:mm:ss"),
      },
      {
        label: "更新时间",
        value: dayjs
          .unix(+draftVersion.updatedAt)
          .format("YYYY-MM-DD HH:mm:ss"),
      },
    ];

    if (draftVersion.draftName) {
      details.unshift({
        label: "草稿名称",
        value: draftVersion.draftName,
      });
    }

    return details.map((detail, index) => (
      <div key={index} className="flex justify-between mt-1 text-xs">
        <span className="text-muted-foreground">{detail.label}</span>
        <span className="truncate max-w-32" title={detail.value}>
          {detail.value}
        </span>
      </div>
    ));
  };

  const renderBaseVersionInfo = () => {
    if (!draftVersion.baseVersionId) {
      return (
        <div className="text-xs text-muted-foreground">
          {t("version.base")}: 无基准版本
        </div>
      );
    }

    if (isBaseVersionLoading) {
      return (
        <div className="space-y-2">
          <div className="text-xs font-medium text-muted-foreground">
            {t("version.base")}
          </div>
          <div className="space-y-1">
            <Skeleton className="h-3 w-24" />
            <Skeleton className="h-3 w-32" />
            <Skeleton className="h-3 w-16" />
          </div>
        </div>
      );
    }

    if (!baseVersionData) {
      return (
        <div className="space-y-1">
          <div className="text-xs font-medium text-muted-foreground">
            {t("version.base")}
          </div>
          <div className="text-xs text-muted-foreground">
            基准版本信息获取失败
          </div>
        </div>
      );
    }

    return (
      <div className="space-y-1">
        <div className="text-xs font-medium text-muted-foreground">
          {t("version.base")}
        </div>
        <div className="flex justify-between text-xs">
          <span className="text-muted-foreground">版本号</span>
          <span>{baseVersionData.version}</span>
        </div>
        <div className="flex justify-between text-xs">
          <span className="text-muted-foreground">版本名称</span>
          <span
            className="truncate max-w-32"
            title={baseVersionData.versionName}
          >
            {baseVersionData.versionName}
          </span>
        </div>
        <div className="flex justify-between text-xs items-center">
          <span className="text-muted-foreground">状态</span>
          <PromptVersionStatusView version={baseVersionData} />
        </div>
      </div>
    );
  };

  return (
    <HoverCard openDelay={800}>
      <HoverCardTrigger asChild>{children}</HoverCardTrigger>
      <HoverCardContent
        className="px-0 py-4 w-80"
        side={side}
        align="start"
        sideOffset={sideOffset}
        onClick={(e) => e.stopPropagation()}
        {...props}
      >
        <ScrollArea className="flex flex-col px-4 max-h-[300px]">
          <div className="flex flex-col gap-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <FileEdit className="size-4 text-orange-500" />
                <h4 className="font-medium text-orange-700">
                  {draftVersion.versionName}
                </h4>
              </div>
              <div className="px-2 py-1 text-xs text-orange-600 border border-orange-300 bg-orange-50 rounded">
                草稿
              </div>
            </div>

            <div className="mt-1 text-xs">{renderDraftDetails()}</div>

            {/* 基准版本信息区域 */}
            <div className="border-t pt-2">{renderBaseVersionInfo()}</div>
          </div>
        </ScrollArea>
      </HoverCardContent>
    </HoverCard>
  );
}
