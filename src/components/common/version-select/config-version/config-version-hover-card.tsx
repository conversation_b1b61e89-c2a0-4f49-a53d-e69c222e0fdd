import {
  Hover<PERSON>ard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";
import { MemoizedMarkdownBlock } from "@/components/ui/markdown-content";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Version } from "@/types/api/version";
import { useTranslation } from "react-i18next";
import { ConfigVersionStatusView } from "./config-version-status";
import { getConfigVersionDetails, getConfigVersionName } from "./helper";

export function ConfigVersionHoverCard({
  version,
  side = "right",
  sideOffset,
  ...props
}: {
  version: Version;
  side?: "left" | "right";
  sideOffset?: number;
}) {
  const { t } = useTranslation();
  const renderDetails = () => {
    const details = getConfigVersionDetails(version, t, false);

    return details.map((detail, index) => (
      <div key={index} className="flex justify-between mt-1 text-xs">
        <span className="text-muted-foreground">{detail.label}</span>
        <span>{detail.value}</span>
      </div>
    ));
  };

  return (
    <HoverCard openDelay={800}>
      <HoverCardTrigger asChild>
        <div className="flex flex-col w-full gap-1 cursor-default">
          <div className="flex w-full justify-between items-center gap-1">
            <div className="flex-1 min-w-0">
              <span className="break-all line-clamp-2" data-version-name>
                {getConfigVersionName(version)}
              </span>
            </div>
            <ConfigVersionStatusView version={version} />
          </div>
          <div className="data-option-remark line-clamp-2 text-xs text-muted-foreground whitespace-normal">
            <MemoizedMarkdownBlock content={version.remark} />
          </div>
        </div>
      </HoverCardTrigger>
      <HoverCardContent
        className="px-0 py-4 w-80"
        side={side}
        align="start"
        sideOffset={sideOffset}
        onClick={(e) => e.stopPropagation()}
        {...props}
      >
        <ScrollArea className="flex flex-col px-4 max-h-[300px]">
          <div className="flex flex-col gap-2">
            <div className="flex items-center justify-between">
              <h4 className="font-medium">{version.version}</h4>
              <ConfigVersionStatusView version={version} />
            </div>

            {version.remark && (
              <div className="flex-col text-sm border-l-2 border-primary p-2 bg-gray-50 rounded-br-md rounded-tr-md">
                <MemoizedMarkdownBlock content={version.remark} />
              </div>
            )}

            <div className="mt-1 text-xs">
              <div className="flex justify-between">
                <span className="text-muted-foreground">
                  {t("version.config.id")}
                </span>
                <span>{version.configId}</span>
              </div>
              {renderDetails()}
            </div>
          </div>
        </ScrollArea>
      </HoverCardContent>
    </HoverCard>
  );
}
