import "@/styles/globals.css";
import "@ant-design/v5-patch-for-react-19";
import * as Sen<PERSON> from "@sentry/react";
import { RouterProvider } from "@tanstack/react-router";
import dayjs, { Dayjs } from "dayjs";
import zh from "dayjs/locale/zh-cn";
import calendar from "dayjs/plugin/calendar";
import updateLocale from "dayjs/plugin/updateLocale";
import { StrictMode } from "react";
import ReactDOM from "react-dom/client";
import "virtual:svg-icons-register";
import packageJson from "../package.json";
import "./locales/i18n";
import { routeContext, router } from "./router";
import { getQueryClient } from "./services/query-client";
import "./zod-v4-patch";
import { usePermission } from "./hooks/use-permission";

dayjs.locale("zh-cn", zh);
dayjs.extend(calendar);
dayjs.extend(updateLocale);
dayjs.updateLocale("zh-cn", {
  weekStart: 1,
  calendar: {
    lastDay: "[昨天] HH:mm:ss",
    sameDay: "HH:mm:ss",
    nextDay: "[明天] HH:mm:ss",
    lastWeek: function (this: Dayjs, now) {
      if (this.isSame(now, "week")) {
        return this.format("[本周] dddd HH:mm:ss");
      } else {
        return this.format("[上周] dddd HH:mm:ss");
      }
    },
    nextWeek: "[下周] dddd HH:mm:ss",
    sameElse: "YYYY-MM-DD HH:mm:ss",
  },
});

async function enableMocking() {
  // 只在开发环境且明确启用了 MSW 环境变量时启动 Mock
  if (import.meta.env.DEV && import.meta.env.VITE_ENABLE_MSW === "true") {
    const { worker } = await import("./mocks/browser");

    return worker.start({
      onUnhandledRequest: "bypass",
    });
  }

  return Promise.resolve();
}

declare module "@tanstack/react-router" {
  interface Register {
    router: typeof router;
  }
}

import.meta.env.PROD &&
  (function () {
    Sentry.init({
      dsn: "https://<EMAIL>/5",
      release: packageJson.version,
      integrations: [
        Sentry.browserTracingIntegration(),
        Sentry.replayIntegration({
          maskAllText: false,
          blockAllMedia: false,
        }),
      ],
      tracesSampleRate: 1.0,
      replaysSessionSampleRate: 0.1,
      replaysOnErrorSampleRate: 1.0,
      tracePropagationTargets: ["localhost"],
      beforeSend(event) {
        const queryClient = getQueryClient();
        const userInfo = queryClient.getQueryData<any>(["user"]);

        if (userInfo) {
          event.user = userInfo;
        }

        return event;
      },
    });
  })();

function InnerApp() {
  const { hasPermission } = usePermission();

  return (
    <RouterProvider
      router={router}
      context={{ ...routeContext, hasPermission }}
    />
  );
}

// 先启动 MSW，然后渲染应用
enableMocking().then(() => {
  ReactDOM.createRoot(document.getElementById("root")!).render(
    <StrictMode>
      <InnerApp />
    </StrictMode>,
  );
});
