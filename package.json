{"name": "vite-project", "private": true, "version": "0.0.939", "type": "module", "scripts": {"dev": "set NODE_OPTIONS=--max_old_space_size=1028 && vite --mode development", "dev:mock": "set NODE_OPTIONS=--max_old_space_size=1028 && cross-env VITE_ENABLE_MSW=true vite --mode development", "publish:prod": "vite build --mode production", "publish:test": "vite build --mode development", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest --run", "test:ui": "vitest --ui", "test:coverage": "vitest run --coverage", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "cy:open": "cypress open", "cy:run": "cypress run", "test:e2e": "start-server-and-test dev http://localhost:5173 cy:run", "svgr": "svgr --typescript --no-index --no-dimensions --icon --memo --out-dir src/components/Icons src/components/Icons", "prepare": "husky"}, "dependencies": {"@ant-design/icons": "^5.3.7", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@codemirror/lang-json": "^6.0.1", "@codemirror/state": "^6.5.2", "@codemirror/view": "^6.36.4", "@dagrejs/dagre": "^1.1.4", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-context-menu": "^2.2.15", "@stellaris/react-diff-viewer-continued": "^4.1.3", "@tanstack/query-core": "^5.74.3", "@tanstack/react-query": "^5.74.3", "@tanstack/react-query-devtools": "^5.74.3", "@tanstack/react-router": "^1.121.0", "@tanstack/react-router-devtools": "^1.121.0", "@tanstack/react-table": "^8.21.3", "@tanstack/react-virtual": "^3.13.6", "@tiptap/core": "^2.22.3", "@tiptap/extension-bullet-list": "^2.22.3", "@tiptap/extension-character-count": "^2.22.3", "@tiptap/extension-code-block-lowlight": "^2.22.3", "@tiptap/extension-dropcursor": "^2.22.3", "@tiptap/extension-gapcursor": "^2.22.3", "@tiptap/extension-history": "^2.22.3", "@tiptap/extension-horizontal-rule": "^2.22.3", "@tiptap/extension-list-item": "^2.22.3", "@tiptap/extension-node-range": "^2.22.3", "@tiptap/extension-ordered-list": "^2.22.3", "@tiptap/extension-paragraph": "^2.22.3", "@tiptap/extension-table": "^2.22.3", "@tiptap/extension-table-cell": "^2.22.3", "@tiptap/extension-table-header": "^2.22.3", "@tiptap/extension-table-row": "^2.22.3", "@tiptap/extension-text": "^2.22.3", "@tiptap/pm": "^2.22.3", "@tiptap/react": "^2.22.3", "@tiptap/suggestion": "^2.22.3", "@types/ali-oss": "^6.16.11", "@types/lodash-es": "^4.17.12", "@types/react-portal": "^4.0.7", "@uiw/codemirror-theme-github": "^4.22.1", "@uiw/codemirror-theme-xcode": "^4.23.10", "@uiw/react-codemirror": "^4.23.10", "@xyflow/react": "^12.6.4", "ahooks": "^3.8.4", "ali-oss": "^6.20.0", "antd": "^5.24.6", "benz-amr-recorder": "^1.1.5", "bunshi": "^2.1.5", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.1.1", "codemirror": "^6.0.1", "compromise": "^14.14.4", "copy-to-clipboard": "^3.3.3", "date-fns": "^4.1.0", "dayjs": "^1.11.11", "diff": "^8.0.2", "easy-json-schema": "0.0.2-beta", "fast-json-patch": "^3.1.1", "file-saver": "^2.0.5", "framer-motion": "^12.4.7", "github-markdown-css": "^5.6.1", "howler": "^2.2.4", "i18next": "^24.2.2", "immer": "^10.1.1", "jotai": "^2.12.5", "jotai-cache": "^0.5.0", "jotai-devtools": "^0.12.0", "jotai-effect": "^2.0.4", "jotai-history": "^0.4.3", "jotai-immer": "^0.4.1", "jotai-minidb": "^0.0.8", "jotai-tanstack-query": "^0.9.0", "js-cookie": "^3.0.5", "json-logic-js": "^2.0.5", "jszip": "^3.10.1", "ky": "^1.2.4", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "lottie-react": "^2.4.1", "lowlight": "^3.3.0", "lucide-react": "^0.525.0", "marked": "^15.0.11", "mobx": "^6.12.3", "mobx-devtools-mst": "^0.9.30", "mobx-react-lite": "^4.0.7", "mobx-state-tree": "^5.4.2", "moment": "^2.30.1", "msw": "^2.7.3", "nanoid": "^5.1.5", "next-themes": "^0.4.6", "pinyin-match": "^1.2.8", "prismjs": "^1.30.0", "radix-ui": "^1.4.2", "rc-image": "^7.12.0", "re-resizable": "^6.11.2", "react": "19.0.0", "react-cookies": "^0.1.1", "react-copy-to-clipboard": "^5.1.0", "react-day-picker": "^9.7.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "19.0.0", "react-error-boundary": "^5.0.0", "react-highlight-words": "^0.21.0", "react-hook-form": "^7.51.4", "react-i18next": "^15.4.1", "react-iframe": "^1.8.5", "react-images-uploading": "^3.1.7", "react-infinite-scroll-component": "^6.1.0", "react-json-view": "^1.21.3", "react-lettered-avatar": "^1.0.2", "react-markdown": "^9.0.1", "react-player": "^2.16.0", "react-portal": "^4.3.0", "react-resizable-panels": "^2.0.19", "react-sortablejs": "^6.1.4", "recharts": "^3.0.0", "remark-gfm": "^4.0.0", "rollup-plugin-visualizer": "^5.12.0", "socket.io-client": "^4.8.1", "sonner": "^2.0.5", "tailwind-merge": "^3.0.2", "tippy.js": "^6.3.7", "tw-animate-css": "^1.2.5", "typewriter-effect": "^2.21.0", "uuid": "^9.0.1", "vaul": "^1.1.2", "vite-plugin-html": "^3.2.2", "xlsx": "^0.18.5", "zod": "4.0.0-beta.20250505T195954"}, "devDependencies": {"@faker-js/faker": "^9.5.0", "@sentry/react": "^7.116.0", "@sentry/vite-plugin": "^2.16.1", "@stellaris/vite-plugin-tencent-oss": "^0.1.3", "@storybook/addon-essentials": "^8.6.0", "@storybook/addon-interactions": "^8.6.0", "@storybook/addon-links": "^8.6.7", "@storybook/addon-onboarding": "^8.6.0", "@storybook/addon-styling": "^1.3.7", "@storybook/blocks": "^8.6.0", "@storybook/react": "^8.6.0", "@storybook/react-vite": "^8.6.0", "@storybook/test": "^8.6.0", "@svgr/cli": "^8.1.0", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.8", "@tanstack/router-plugin": "^1.121.0", "@testing-library/cypress": "^10.0.3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^14.6.1", "@tiptap/extension-unique-id": "^3.0.7", "@types/diff": "^8.0.0", "@types/js-cookie": "^3.0.6", "@types/json-logic-js": "^2.0.8", "@types/lodash": "^4.17.4", "@types/node": "^20.12.12", "@types/prismjs": "^1.26.5", "@types/react": "^19.0.8", "@types/react-cookies": "^0.1.4", "@types/react-dom": "^19.0.3", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-react": "^4.2.1", "@vitest/ui": "^3.0.7", "cross-env": "^7.0.3", "cypress": "^14.1.0", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.7", "eslint-plugin-storybook": "^0.11.3", "fake-indexeddb": "^6.0.1", "husky": "^9.1.7", "jsdom": "^26.0.0", "lint-staged": "^15.5.1", "msw-storybook-addon": "^2.0.4", "node-sass": "^9.0.0", "prettier": "^3.5.2", "sass": "^1.83.0", "sass-loader": "^16.0.3", "storybook": "^8.6.0", "svgr": "^2.0.0", "tailwindcss": "^4.1.4", "typescript": "^5.7.3", "vite": "^6.3.5", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-svgr": "^4.3.0", "vitest": "^3.2.3"}, "lint-staged": {"*.{js,jsx,ts,tsx,css,json,md}": ["prettier --write"], "*.{js,jsx,ts,tsx,json}": ["eslint --fix"]}, "packageManager": "pnpm@10.12.4+sha512.5ea8b0deed94ed68691c9bad4c955492705c5eeb8a87ef86bc62c74a26b037b08ff9570f108b2e4dbd1dd1a9186fea925e527f141c648e85af45631074680184", "msw": {"workerDirectory": ["public"]}}