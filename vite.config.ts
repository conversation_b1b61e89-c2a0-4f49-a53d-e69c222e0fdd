import { sentryVitePlugin } from "@sentry/vite-plugin";
import vitePluginTencentOss from "@stellaris/vite-plugin-tencent-oss";
import tailwindcss from "@tailwindcss/vite";
import { tanstackRouter } from "@tanstack/router-plugin/vite";
import react from "@vitejs/plugin-react";
import jotaiDebugLabel from "jotai/babel/plugin-debug-label";
import jotaiReactRefresh from "jotai/babel/plugin-react-refresh";
import path from "path";
import { visualizer } from "rollup-plugin-visualizer";
import { defineConfig, loadEnv } from "vite";
import { createHtmlPlugin } from "vite-plugin-html";
import { createSvgIconsPlugin } from "vite-plugin-svg-icons";
import svgr from "vite-plugin-svgr";
import packageJSON from "./package.json";

// const proxyHost = "https://test.endpoint.dongchacat.cn/";
// const proxyHost = "https://endpoint.dongchacat.cn/";
// const proxyHost = "http://************:8081/";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), "");
  const proxyHost = env.VITE_APP_API_ENDPOINT;
  // CDN 部署配置
  const cdnBaseUrl = env.VITE_CDN_BASE_URL;

  return {
    base: cdnBaseUrl || "/",
    plugins: [
      tanstackRouter({
        target: "react",
        autoCodeSplitting: true,
        verboseFileRoutes: false,
      }),
      react({
        babel: {
          plugins: [jotaiDebugLabel, jotaiReactRefresh],
          presets: ["jotai/babel/preset"],
        },
      }),
      tailwindcss(),
      svgr(),
      createSvgIconsPlugin({
        // 指定需要缓存的图标文件夹
        iconDirs: [path.resolve(process.cwd(), "src/assets/icons")],
        // 指定symbolId格式
        symbolId: "icon-[dir]-[name]",
      }),
      createHtmlPlugin({
        inject: {
          data: {
            title: "index", // 出现在模版中的 <%- title %>
            injectScript: `<script>
            window.MyQA_version = 'v${packageJSON.version}';
            </script>`, // 出现在模版中的<%- injectScript %>
          },
        },
      }),
      visualizer({
        // 打包完成后自动打开浏览器，显示产物体积报告
        open: true,
      }),
      sentryVitePlugin({
        authToken: process.env.SENTRY_AUTH_TOKEN,
        org: "sentry",
        project: "myqa-web",
        url: "https://sentry.dongchacat.cn",
        disable: env.VITE_APP_ENV !== "production",
      }),
      vitePluginTencentOss({
        region: env.REGION,
        secretId: env.SECRET_ID,
        secretKey: env.SECRET_KEY,
        bucket: env.BUCKET,
        overwrite: true,
        ignore: "**/*.js.map",
        lastCommitGlobs: [
          "**/*.html", // 最后上传所有 HTML 文件
        ],
      }),
    ],
    esbuild: {
      pure: ["console.log"],
      drop: ["debugger"],
    },
    build: {
      sourcemap: true,
      assetsDir: "static",
      rollupOptions: {
        output: {
          manualChunks: {
            "socket-io": ["socket.io-client"],
            "lucide-icons": ["lucide-react"],
          },
        },
      },
    },
    assetsInclude: ["**/*.lottie", "**/*.wav"],
    resolve: {
      alias: {
        "@": "/src",
      },
    },
    server: {
      host: "0.0.0.0",
      port: 9000,
      proxy: {
        "/api": {
          target: proxyHost,
          changeOrigin: true,
        },
        "/muyan/api": {
          target: proxyHost,
          changeOrigin: true,
        },
        "/message": {
          target: proxyHost,
          changeOrigin: true,
        },
        "/myuser": proxyHost,
        "/evaluation": proxyHost,
        "/socket.io": {
          target: proxyHost,
          changeOrigin: true,
          ws: true,
        },
      },
    },
  };
});
